<view class="ai-query-container data-v-57280228"><view data-event-opts="{{[['tap',[['reconnectMCP',['$event']]]]]}}" class="{{['connection-status','data-v-57280228',connectionStatusClass]}}" bindtap="__e">{{''+connectionStatusText+''}}</view><view class="container data-v-57280228"><text class="title data-v-57280228">AI智能问数</text><view class="search-container data-v-57280228"><input class="search-input data-v-57280228" type="text" placeholder="去年营业额前十的门店同比情况" data-event-opts="{{[['keypress',[['onKeyPress',['$event']]]],['input',[['__set_model',['','searchQuery','$event',[]]]]]]}}" value="{{searchQuery}}" bindkeypress="__e" bindinput="__e"/><button class="{{['search-btn','voice-btn','data-v-57280228',(isRecording)?'recording':'']}}" title="{{isRecording?'正在聆听...点击停止':'语音输入'}}" data-event-opts="{{[['tap',[['toggleVoiceRecognition',['$event']]]]]}}" bindtap="__e"><text class="voice-icon data-v-57280228">🎤</text></button><button data-event-opts="{{[['tap',[['performSearch',['$event']]]]]}}" class="search-btn data-v-57280228" bindtap="__e"><text class="search-icon data-v-57280228">🔍</text></button><block wx:if="{{isRecording&&isH5}}"><canvas class="voice-wave-canvas data-v-57280228" id="voiceWaveCanvas"></canvas></block></view><block wx:if="{{isLoading}}"><view class="loading data-v-57280228"><view class="loading-content data-v-57280228"><view class="spinner data-v-57280228"></view><text class="loading-text data-v-57280228">{{''+loadingMessage}}<text class="loading-dots data-v-57280228"></text></text><text class="loading-sub-text data-v-57280228">{{loadingSubMessage}}</text></view></view></block><block wx:if="{{showResult}}"><view class="result-container data-v-57280228"><text class="result-title data-v-57280228">📊 查询结果</text><rich-text class="result-content data-v-57280228" nodes="{{formattedResult}}"></rich-text></view></block><view class="section data-v-57280228"><text class="section-title data-v-57280228">热门搜索</text><view class="tags-container data-v-57280228"><block wx:for="{{hotTags}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['searchTag',['$0'],[[['hotTags','',index]]]]]]]}}" class="tag data-v-57280228" bindtap="__e">{{''+tag+''}}</view></block></view></view><view class="section data-v-57280228"><text class="section-title data-v-57280228">历史搜索</text><view class="history-container data-v-57280228"><block wx:for="{{searchHistory}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['searchTag',['$0'],[[['searchHistory','',index]]]]]]]}}" class="history-item data-v-57280228" bindtap="__e">{{''+item+''}}</view></block></view></view></view></view>