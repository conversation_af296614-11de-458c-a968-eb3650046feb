{
	"pages": [{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "主页"
			}
		}
	],
	// 分包文件夹
	"subPackages": [{
		// 分包目录-我的
		"root": "pages-mine",
		"pages": []
	}],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"app-plus": {
			"background": "#efeff4"
		}
	},
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "测试", //模式名称
			"path": "pages/index/index", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	}
}