import Vue from 'vue'
import App from './App'
import http from './api/http.js'
import uView from '@/uni_modules/uview-ui'

// Mock uni object for browser environment
if (typeof uni === 'undefined') {
  window.uni = {
    getStorageSync: (key) => {
      return localStorage.getItem(key) || ''
    },
    setStorageSync: (key, value) => {
      localStorage.setItem(key, value)
    },
    showLoading: (options) => {
      console.log('showLoading:', options)
    },
    hideLoading: () => {
      console.log('hideLoading')
    },
    showToast: (options) => {
      console.log('showToast:', options)
      alert(options.title || options.message || 'Toast')
    },
    request: (options) => {
      // Use fetch API as fallback
      const config = {
        method: options.method || 'GET',
        headers: options.header || {},
      }

      if (options.data && (options.method === 'POST' || options.method === 'PUT')) {
        if (options.header && options.header['content-type'] && options.header['content-type'].includes('application/json')) {
          config.body = JSON.stringify(options.data)
        } else {
          const formData = new URLSearchParams()
          Object.keys(options.data).forEach(key => {
            formData.append(key, options.data[key])
          })
          config.body = formData
        }
      }

      fetch(options.url, config)
        .then(response => response.json())
        .then(data => {
          if (options.success) {
            options.success({ data })
          }
        })
        .catch(error => {
          console.error('Request failed:', error)
          if (options.fail) {
            options.fail({ errMsg: error.message })
          }
        })
        .finally(() => {
          if (options.complete) {
            options.complete()
          }
        })
    }
  }
}

Vue.use(uView)
Vue.prototype.http = http

App.mpType = 'app'


const app = new Vue({
	...App
})
app.$mount()



