import Vue from 'vue'
import App from './App'
import http from './api/http.js'
import uView from '@/uni_modules/uview-ui'

Vue.use(uView)
Vue.prototype.http = http

App.mpType = 'app'


const app = new Vue({
	...App
})
app.$mount()


// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
import App from './App.vue'
export function createApp() {
	const app = createSSRApp(App)
	return {
		app
	}
}
// #endif
