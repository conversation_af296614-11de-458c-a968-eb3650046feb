(global["webpackChunkjavamcpview"]=global["webpackChunkjavamcpview"]||[]).push([["pages/index/index"],{268:function(e,t,n){"use strict";n(923);var o,a,s=function(){var e=this,t=e.$createElement;e._self._c},i=[],r=n(575),c=n(766),u=n.n(c),l=n(480)["default"],d={data:function(){return{mcpApiBase:"http://localhost:8081",mcpConnected:!1,connectionStatus:"disconnected",searchQuery:"",isLoading:!1,showResult:!1,queryResult:"",loadingMessage:"正在智能分析您的问题",loadingSubMessage:"AI正在理解查询意图",loadingMessages:[{main:"正在智能分析您的问题",sub:"AI正在理解查询意图"},{main:"正在连接数据库",sub:"建立与SQL Server的安全连接"},{main:"正在查询数据",sub:"从StoreRevenue表中检索相关数据"},{main:"正在处理结果",sub:"对查询结果进行智能分析和格式化"}],loadingInterval:null,isRecording:!1,aliyunAppKey:"DzVzUYbM1vG83nxm",aliyunToken:"74902900e45c44a497f830c29522ede0",aliyunWs:null,aliyunRecorder:null,aliyunAudioContext:null,aliyunStream:null,aliyunAnalyser:null,aliyunWaveAnimationId:null,silenceStartTime:null,silenceThreshold:.02,silenceTimeout:1e3,hotTags:["本月平台收入总共多少","本月平台收入明细","上个月平台收入","平台收入排名前5","去年营业额","今年营业额","门店营业额排名"],searchHistory:["本月平台收入总共多少","列一个本月平台收入的明细给我","上个月的平台收入总共有多少","看看去年的营业额","今年的营业额","门店营业额前十门店营业额","去年营业额前十门店同比情况","门店日期间3天利润趋势"],isH5:!1,_waveAnimId:null}},computed:{connectionStatusClass:function(){return"connection-status ".concat(this.connectionStatus)},connectionStatusText:function(){var e={connected:"🟢 已连接",disconnected:"🔴 未连接",connecting:"🟡 连接中..."};return e[this.connectionStatus]||"🔴 未连接"},formattedResult:function(){if(!this.queryResult)return"";var e=this.queryResult.replace(/\n/g,"<br>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/`(.*?)`/g,'<code style="background: #f1f3f4; padding: 2px 4px; border-radius: 3px;">$1</code>');return e=e.replace(/(\d+\.?\d*)(元|万元|亿元)/g,'<span style="color: #4A90E2; font-weight: bold;">$1$2</span>'),'<div style="line-height: 1.8;">'.concat(e,"</div>")}},methods:{connectToMCP:function(){var e=this;return(0,r["default"])(u().mark(function t(){var n,o,a;return u().wrap(function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.updateConnectionStatus("connecting"),console.log("正在连接MCP服务器..."),t.next=1,l.request({url:"".concat(e.mcpApiBase,"/mcp/connect"),method:"POST",header:{"Content-Type":"application/json"}});case 1:if(n=t.sent,o=Array.isArray(n)?n[1]:n,console.log("xxx",o),!(o&&o.data&&o.data.success)){t.next=2;break}e.mcpConnected=!0,e.updateConnectionStatus("connected"),console.log("MCP服务器连接成功"),t.next=3;break;case 2:throw new Error(o&&o.data&&o.data.message||"连接失败");case 3:t.next=5;break;case 4:t.prev=4,a=t["catch"](0),console.error("MCP连接错误:",a),e.mcpConnected=!1,e.updateConnectionStatus("disconnected"),setTimeout(function(){l.showModal({title:"连接失败",content:"MCP服务器连接失败: ".concat(a.message,"\n\n请确保:\n1. MCP服务器正在运行 (端口8080)\n2. MCP客户端正在运行 (端口8081)"),showCancel:!1})},1e3);case 5:case"end":return t.stop()}},t,null,[[0,4]])}))()},updateConnectionStatus:function(e){this.connectionStatus=e},reconnectMCP:function(){var e=this;return(0,r["default"])(u().mark(function t(){return u().wrap(function(t){while(1)switch(t.prev=t.next){case 0:if(e.mcpConnected){t.next=1;break}return console.log("尝试重新连接MCP..."),t.next=1,e.connectToMCP();case 1:case"end":return t.stop()}},t)}))()},performSearch:function(){var e=this;return(0,r["default"])(u().mark(function t(){var n;return u().wrap(function(t){while(1)switch(t.prev=t.next){case 0:if(n=e.searchQuery.trim(),n){t.next=1;break}return l.showToast({title:"请输入查询内容",icon:"none"}),t.abrupt("return");case 1:if(e.mcpConnected){t.next=2;break}return l.showToast({title:"MCP服务器未连接，请稍后重试",icon:"none"}),t.abrupt("return");case 2:return t.next=3,e.executeQuery(n);case 3:case"end":return t.stop()}},t)}))()},searchTag:function(e){var t=this;return(0,r["default"])(u().mark(function n(){return u().wrap(function(n){while(1)switch(n.prev=n.next){case 0:return t.searchQuery=e,n.next=1,t.executeQuery(e);case 1:case"end":return n.stop()}},n)}))()},executeQuery:function(e){var t=this;return(0,r["default"])(u().mark(function n(){var o,a,s,i,r,c;return u().wrap(function(n){while(1)switch(n.prev=n.next){case 0:return t.isLoading=!0,t.showResult=!1,t.showLoadingWithMessages(),console.log("开始执行查询:",e),n.prev=1,n.next=2,l.request({url:"".concat(t.mcpApiBase,"/mcp/tools/business-query?query=").concat(encodeURIComponent(e)),method:"POST",header:{"Content-Type":"application/json",Accept:"application/json"}});case 2:o=n.sent,a=Array.isArray(o)?o[1]:o,console.log("API响应状态:",a),a&&a.data&&a.data.success&&a.data.data&&a.data.data.content?(s=a.data.data.content[0],s&&s.text?(t.queryResult=s.text,t.showResult=!0,t.addToHistory(e),console.log("查询成功，结果已显示")):(t.queryResult="查询结果为空，请尝试其他问题",t.showResult=!0)):(i=a&&a.data&&(a.data.message||a.data.error)||"查询失败，请稍后重试",t.queryResult="❌ ".concat(i),t.showResult=!0),n.next=4;break;case 3:n.prev=3,c=n["catch"](1),console.error("查询错误:",c),r="",r=c.message&&c.message.includes("Failed to fetch")?"❌ 无法连接到MCP服务，请检查服务是否正在运行":c.message&&c.message.includes("HTTP 404")?"❌ API接口不存在，请检查MCP客户端是否正确启动":c.message&&c.message.includes("HTTP 500")?"❌ 服务器内部错误，请检查MCP服务器日志":"❌ 查询出错: ".concat(c.message),t.queryResult="".concat(r,'\n\t\t\t\t<div style="margin-top: 10px; font-size: 12px; color: #666;">\n\t\t\t\t\t<strong>故障排除建议：</strong><br>\n\t\t\t\t\t1. 确保MCP服务器正在运行 (端口8080)<br>\n\t\t\t\t\t2. 确保MCP客户端正在运行 (端口8081)<br>\n\t\t\t\t\t3. 检查网络连接<br>\n\t\t\t\t\t4. 刷新页面重新连接\n\t\t\t\t</div>'),t.showResult=!0;case 4:return n.prev=4,t.hideLoading(),n.finish(4);case 5:case"end":return n.stop()}},n,null,[[1,3,4,5]])}))()},showLoadingWithMessages:function(){var e=this,t=0;this.updateLoadingMessage(this.loadingMessages[t]),this.loadingInterval=setInterval(function(){t=(t+1)%e.loadingMessages.length,e.updateLoadingMessage(e.loadingMessages[t])},1500)},updateLoadingMessage:function(e){this.loadingMessage=e.main,this.loadingSubMessage=e.sub},hideLoading:function(){this.isLoading=!1,this.loadingInterval&&(clearInterval(this.loadingInterval),this.loadingInterval=null)},addToHistory:function(e){this.searchHistory=this.searchHistory.filter(function(t){return t!==e}),this.searchHistory.unshift(e),this.searchHistory.length>10&&(this.searchHistory=this.searchHistory.slice(0,10)),l.setStorageSync("searchHistory",JSON.stringify(this.searchHistory))},loadSearchHistory:function(){try{var e=l.getStorageSync("searchHistory");e&&(this.searchHistory=JSON.parse(e))}catch(t){console.error("加载搜索历史失败:",t)}},onKeyPress:function(e){13===e.keyCode&&this.performSearch()},toggleVoiceRecognition:function(){this.isRecording?this.stopVoiceRecognition():this.startVoiceRecognition()},startVoiceRecognition:function(){var e=this,t=l.getSystemInfoSync(),n=t.platform;if("devtools"===n||"android"===n||"ios"===n){this.isRecording=!0;var o=requirePlugin("WechatSI");return o.getRecordRecognitionManager().start({lang:"zh_CN",duration:6e4}),o.getRecordRecognitionManager().onRecognize=function(t){t.result&&(e.searchQuery=t.result,e.stopVoiceRecognition(),e.performSearch())},o.getRecordRecognitionManager().onStop=function(t){t.result&&(e.searchQuery=t.result,e.performSearch()),e.stopVoiceRecognition()},void(o.getRecordRecognitionManager().onError=function(t){e.stopVoiceRecognition(),l.showToast({title:"语音识别失败",icon:"none"})})}l.showToast({title:"当前平台暂不支持语音识别",icon:"none",duration:2e3})},stopVoiceRecognition:function(){this.isRecording=!1;try{var e=requirePlugin("WechatSI");e.getRecordRecognitionManager().stop()}catch(t){}},_startWaveAnim:function(){var e=this,t=document.getElementById("voiceWaveCanvas");if(t){var n=t.getContext("2d"),o=t.width=120,a=t.height=40,s=0,i=function(){n.clearRect(0,0,o,a),n.save(),n.strokeStyle="#4A90E2",n.lineWidth=3,n.beginPath();for(var t=0;t<=o;t+=2){var r=a/2+Math.sin(t/12+s)*(10+8*Math.sin(s/2));0===t?n.moveTo(t,r):n.lineTo(t,r)}n.stroke(),n.restore(),s+=.08,e.isRecording&&(e._waveAnimId=requestAnimationFrame(i))};i()}}},mounted:function(){console.log("AI智能问数页面已加载"),console.log("MCP API地址:",this.mcpApiBase),this.isH5="undefined"!==typeof window&&!!window.SpeechRecognition},onLoad:function(e){this.connectToMCP(),this.loadSearchHistory()},onUnload:function(){this.loadingInterval&&clearInterval(this.loadingInterval),this.isRecording&&this.stopVoiceRecognition()}},h=d,g=n(573),p=n.n(g),f=(p(),n(535)),m=(0,f["default"])(h,s,i,!1,null,"30061629",null,!1,o,a),v=m.exports,y=n(372)["default"],R=n(480)["createPage"];y.__webpack_require_UNI_MP_PLUGIN__=n,R(v)},573:function(){}},function(e){var t=function(t){return e(e.s=t)};e.O(0,["common/vendor"],function(){return t(268)});e.O()}]);