<view class="ai-query-container data-v-30061629"><view data-event-opts="{{[['tap',[['reconnectMCP',['$event']]]]]}}" class="{{['connection-status','data-v-30061629',connectionStatusClass]}}" bindtap="__e">{{''+connectionStatusText+''}}</view><view class="container data-v-30061629"><text class="title data-v-30061629">AI智能问数</text><view class="search-container data-v-30061629"><input class="search-input data-v-30061629" type="text" placeholder="去年营业额前十的门店同比情况" data-event-opts="{{[['keypress',[['onKeyPress',['$event']]]],['input',[['__set_model',['','searchQuery','$event',[]]]]]]}}" value="{{searchQuery}}" bindkeypress="__e" bindinput="__e"/><button class="{{['search-btn','voice-btn','data-v-30061629',(isRecording)?'recording':'']}}" title="{{isRecording?'正在聆听...点击停止':'语音输入'}}" data-event-opts="{{[['tap',[['toggleVoiceRecognition',['$event']]]]]}}" bindtap="__e"><text class="voice-icon data-v-30061629">🎤</text></button><button data-event-opts="{{[['tap',[['performSearch',['$event']]]]]}}" class="search-btn data-v-30061629" bindtap="__e"><text class="search-icon data-v-30061629">🔍</text></button><block wx:if="{{isRecording&&isH5}}"><canvas class="voice-wave-canvas data-v-30061629" id="voiceWaveCanvas"></canvas></block></view><block wx:if="{{isLoading}}"><view class="loading data-v-30061629"><view class="loading-content data-v-30061629"><view class="spinner data-v-30061629"></view><text class="loading-text data-v-30061629">{{''+loadingMessage}}<text class="loading-dots data-v-30061629"></text></text><text class="loading-sub-text data-v-30061629">{{loadingSubMessage}}</text></view></view></block><block wx:if="{{showResult}}"><view class="result-container data-v-30061629"><text class="result-title data-v-30061629">📊 查询结果</text><rich-text class="result-content data-v-30061629" nodes="{{formattedResult}}"></rich-text></view></block><view class="section data-v-30061629"><text class="section-title data-v-30061629">热门搜索</text><view class="tags-container data-v-30061629"><block wx:for="{{hotTags}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['searchTag',['$0'],[[['hotTags','',index]]]]]]]}}" class="tag data-v-30061629" bindtap="__e">{{''+tag+''}}</view></block></view></view><view class="section data-v-30061629"><text class="section-title data-v-30061629">历史搜索</text><view class="history-container data-v-30061629"><block wx:for="{{searchHistory}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['searchTag',['$0'],[[['searchHistory','',index]]]]]]]}}" class="history-item data-v-30061629" bindtap="__e">{{''+item+''}}</view></block></view></view></view></view>