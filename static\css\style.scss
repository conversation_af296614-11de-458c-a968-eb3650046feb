  page {
		background-color: #ffffff;
	}
  button::after{border: none !important;background-color: transparent;}
  /* // 宽度 */
  .w10 {
    width: 100%;
  }

  .w95 {
    width: 95%;
  }

  .w9 {
    width: 90%;
  }

  .w85 {
    width: 85%;
  }

  .w8 {
    width: 80%;
  }

  .w75 {
    width: 75%;
  }

  .w7 {
    width: 70%;
  }

  .w65 {
    width: 65%;
  }

  .w6 {
    width: 60%;
  }

  .w55 {
    width: 55%;
  }

  .w5 {
    width: 50%;
  }

  .w45 {
    width: 45%;
  }

  .w4 {
    width: 40%;
  }

  .w35 {
    width: 35%;
  }

  .w3 {
    width: 30%;
  }

  .w25 {
    width: 25%;
  }

  .w2 {
    width: 20%;
  }

  .w15 {
    width: 15%;
  }

  .w1 {
    width: 10%;
  }

  /* //字体大小 */
  .f10 {
    font-size: 20rpx;
  }

  .f12 {
    font-size: 24rpx;
  }

  .f14 {
    font-size: 28rpx;
  }

  .f16 {
    font-size: 1rem;
  }

  .f18 {
    font-size: 36rpx;
  }

  .f20 {
    font-size: 40rpx;
  }

  .f22 {
    font-size: 44rpx;
  }

  .f24 {
    font-size: 48rpx;
  }

  .f26 {
    font-size: 52rpx;
  }

  .f28 {
    font-size: 56rpx;
  }

  .f30 {
    font-size: 60rpx;
  }

  .f32 {
    font-size: 64rpx;
  }

  .f34 {
    font-size: 68rpx;
  }

  .f36 {
    font-size: 72rpx;
  }

  /* //背景颜色 */
  .bcf {
    background-color: #fff;
  }

  /* // 字体颜色 */
  .cf {
    color: #fff;
  }

  .ce {
    color: #eee;
  }

  .red {
    color: red;
  }

  .blue {
    color: blue;
  }

  .c0 {
    color: #000;
  }

  .c3 {
    color: #333;
  }

  .c6 {
    color: #666;
  }

  .c9 {
    color: #999;
  }

  .c70 {
    color: #707070;
  }

  .c51 {
    color: #515151;
  }
  
  .cf0b438 {
    color: #f0b438;
  }
  /* // 字体粗细 */
  .fb {
    font-weight: bold;
  }

  .fb4 {
    font-weight: 400;
  }

  /* //字体行高 */
  .lh20 {
    line-height: 40rpx;
  }
  
  .lh24{
    line-height: 48rpx;
  }
  
  .lh30 {
    line-height: 60rpx;
  }

  .lh35 {
    line-height: 70rpx;
  }

  .lh40 {
    line-height: 80rpx;
  }

  .lh45 {
    line-height: 90rpx;
  }

  .lh50 {
    line-height: 100rpx;
  }
  .lh60 {
    line-height: 120rpx;
  }
  .lh70 {
    line-height: 140rpx;
  }
  .lh80 {
    line-height: 160rpx;
  }
  .lh90 {
    line-height: 180rpx;
  }
  .lh100 {
    line-height: 200rpx;
  }
  // 首行缩进
  .t-index2 {
    text-indent: 32rpx;
  }
  /* //字体间距 */
  .lsp4 {
    letter-spacing: 4rpx;
  }
  .lsp8 {
    letter-spacing: 8rpx;
  }
  
  .lsp10 {
    letter-spacing: 10rpx;
  }
  
  .lsp20 {
    letter-spacing: 20rpx;
  }

  .lsp40 {
    letter-spacing: 40rpx;
  }

  /* // 背景颜色 */
  .bacf {
    background-color: #fff;
  }

  .text-c {
    text-align: center;
  }
  //自动换行
  .overflow-wrap {
    overflow-wrap: break-word;
  }
  .text-r {
    text-align: right;
  }

  .text-l {
    text-align: left;
  }

  .fr {
    float: right;
  }

  .fl {
    float: left;
  }

  .mg-at {
    margin: auto;
  }

  .flex-col {
    display: flex;
    flex-direction: column;
  }
  
  .flex-col-c {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .flac-row {
    display: flex;
    align-items: center;
  }

  .flac-row-c {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .flac-row-b {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .flac-row-a {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  
  .border{
    border: 2rpx solid #eee;
  }
  
  .border-top {
    border-top: 2rpx solid #eee;
  }
  
  .border-bottom {
    border-bottom: 2rpx solid #eee;
  }
  
  .ellipsis{
    white-space: nowrap;overflow: hidden;text-overflow: ellipsis;
  }
  .fixed-bottom {
    position: fixed;bottom: 0;left:0;
  }
  .radius5 {
    border-radius: 10rpx;
  }
  .radius8 {
    border-radius: 16rpx;
  }
  .radius10 {
    border-radius: 20rpx;
  }
  .radius15 {
    border-radius: 30rpx;
  }
  .radius20 {
    border-radius: 40rpx;
  }