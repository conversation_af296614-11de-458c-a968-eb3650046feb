.cfdd472 {
  color: #fdd472;
}

.c1e1848 {
  color: #1e1848;
}

.bac1e1848 {
  background-color: #1e1848;
}

.header {
    color:#fff;
    background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1));
    box-shadow: 0rpx 12rpx 13rpx 0rpx rgba(38, 36, 128,0.47);
  }


.button-login,.btnStyle {
  color: #f6cc70;
  // background: linear-gradient(-90deg, rgba(38, 56, 128, 1), rgba(58, 100, 100, 1));
  background: #1e1848;
	// box-shadow: 0rpx 0rpx 13rpx 0rpx rgba(164, 217, 228, 0.2);
}
// .button-hover {
//     color: #f6cc70;
//     background: #1e1848;
// }
  
  .yzm {
    color: #1e1848;
    border: 1rpx solid #1e1848;
  }
.agreement,.agreement1{
    color: #1e1848;
  }
  
/deep/.uni-datetime-picker--btn, 
  /deep/.uni-calendar-item--before-checked, 
  /deep/.uni-calendar-item--after-checked {
    background-color: #1e1848 !important;
    border: 2rpx solid #F6F7FC !important;
  }
   /deep/.uni-calendar-item--multiple {
     background-color: #FFF5EE;
     border: 2rpx solid #F6F7FC !important;
   }
   
   /deep/.uni-section .uni-section-header__decoration {
   	background-color: #f6cc70 !important;
   }
   
   /deep/.u-button--square {
     border-radius: 0 !important;
   }
   
   /deep/.uni-section__content-title,/deep/.uni-list-item__content-title {
     font-size: 32rpx !important;
   }
   /deep/.uni-list-item__extra-text,/deep/.uni-list-item__content-note{
       font-size: 28rpx !important;
   }
   
   /deep/.uni-searchbar__cancel {
     color:#fff !important;
   }
   
   /deep/.u-button {
      border-radius: 20rpx !important;
   }