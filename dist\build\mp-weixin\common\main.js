(global["webpackChunkjavamcpview"]=global["webpackChunkjavamcpview"]||[]).push([["common/main"],{90:function(){},798:function(e,n,t){"use strict";var o,r,c,u,p=t(775),i=t(923),a={onLaunch:function(){console.log("App Launch")},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")}},l=a,f=t(90),b=t.n(f),s=(b(),t(535)),O=(0,s["default"])(l,o,r,!1,null,null,null,!1,c,u),w=O.exports,g=(t(817),t(372)["default"]),j=t(480)["createApp"];function m(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);n&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),t.push.apply(t,o)}return t}function v(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?m(Object(t),!0).forEach(function(n){(0,p["default"])(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):m(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}g.__webpack_require_UNI_MP_PLUGIN__=t,i["default"].config.productionTip=!1,w.mpType="app";var y=new i["default"](v({},w));j(y).$mount()}},function(e){var n=function(n){return e(e.s=n)};e.O(0,["common/vendor"],function(){return n(798)});e.O()}]);