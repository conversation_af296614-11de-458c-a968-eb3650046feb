{"version": 3, "file": "pages/index/index.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuDA;EACAA,IAAA,WAAAA,KAAA;IACA;MACA;MACA;MACAC,UAAA;MACA;MACA;MACAC,YAAA;MACAC,gBAAA;MAAA;;MAEA;MACAC,WAAA;MACAC,SAAA;MACAC,UAAA;MACAC,WAAA;MAEA;MACAC,cAAA;MACAC,iBAAA;MACAC,eAAA;QACAC,IAAA;QACAC,GAAA;MACA,GACA;QACAD,IAAA;QACAC,GAAA;MACA,GACA;QACAD,IAAA;QACAC,GAAA;MACA,GACA;QACAD,IAAA;QACAC,GAAA;MACA,EACA;MACAC,eAAA;MAEA;MACAC,WAAA;MACAC,YAAA;MACAC,WAAA;MACAC,QAAA;MACAC,cAAA;MACAC,kBAAA;MACAC,YAAA;MACAC,cAAA;MACAC,qBAAA;MACAC,gBAAA;MACAC,gBAAA;MACAC,cAAA;MAEA;MACAC,OAAA,GACA,cACA,YACA,WACA,YACA,SACA,SACA,UACA;MAEA;MACAC,aAAA,GACA,cACA,kBACA,iBACA,YACA,UACA,gBACA,iBACA,cACA;MAEAC,IAAA;MACAC,WAAA;IACA;EACA;EAEAC,QAAA;IACAC,qBAAA,WAAAA,sBAAA;MACA,4BAAAC,MAAA,MAAA7B,gBAAA;IACA;IAEA8B,oBAAA,WAAAA,qBAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,MAAA/B,gBAAA;IACA;IAEAgC,eAAA,WAAAA,gBAAA;MACA,UAAA5B,WAAA;MAEA,IAAA6B,SAAA,QAAA7B,WAAA,CACA8B,OAAA,gBACAA,OAAA,0CACAA,OAAA,8BACAA,OAAA,aACA;;MAEA;MACAD,SAAA,GAAAA,SAAA,CAAAC,OAAA,0BACA;MAEA,2CAAAL,MAAA,CAAAI,SAAA;IACA;EACA;EAEAE,OAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MAAA,OAAAC,mFAAA,cAAAC,sEAAA,UAAAE,QAAA;QAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,GAAA,EAAAC,QAAA,EAAAC,EAAA;QAAA,OAAAP,sEAAA,WAAAS,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEAZ,KAAA,CAAAc,sBAAA;cACAC,OAAA,CAAAC,GAAA;cAAAL,QAAA,CAAAE,IAAA;cAAA,OAEAI,GAAA,CAAAC,OAAA;gBACAC,GAAA,KAAA3B,MAAA,CAAAQ,KAAA,CAAAvC,UAAA;gBACA2D,MAAA;gBACAC,MAAA;kBACA;gBACA;cACA;YAAA;cAAAhB,kBAAA,GAAAM,QAAA,CAAAW,IAAA;cAAAhB,mBAAA,GAAAiB,gFAAA,CAAAlB,kBAAA;cANAE,GAAA,GAAAD,mBAAA;cAAAE,QAAA,GAAAF,mBAAA;cAAA,MAQAE,QAAA,IAAAA,QAAA,CAAAhD,IAAA,IAAAgD,QAAA,CAAAhD,IAAA,CAAAgE,OAAA;gBAAAb,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAb,KAAA,CAAAtC,YAAA;cACAsC,KAAA,CAAAc,sBAAA;cACAC,OAAA,CAAAC,GAAA;cAAAL,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAA,MAEA,IAAAY,KAAA,CAAAjB,QAAA,IAAAA,QAAA,CAAAhD,IAAA,IAAAgD,QAAA,CAAAhD,IAAA,CAAAkE,OAAA;YAAA;cAAAf,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAH,EAAA,GAAAE,QAAA;cAIAI,OAAA,CAAAY,KAAA,aAAAlB,EAAA;cACAT,KAAA,CAAAtC,YAAA;cACAsC,KAAA,CAAAc,sBAAA;cAEAc,UAAA;gBACAX,GAAA,CAAAY,SAAA;kBACAC,KAAA;kBACAC,OAAA,oDAAAvC,MAAA,CAAAiB,EAAA,CAAAiB,OAAA;kBACAM,UAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAArB,QAAA,CAAAsB,IAAA;UAAA;QAAA,GAAA7B,OAAA;MAAA;IAEA;IAEAU,sBAAA,WAAAA,uBAAAoB,MAAA;MACA,KAAAvE,gBAAA,GAAAuE,MAAA;IACA;IAEAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAAnC,mFAAA,cAAAC,sEAAA,UAAAmC,SAAA;QAAA,OAAAnC,sEAAA,WAAAoC,SAAA;UAAA,kBAAAA,SAAA,CAAA1B,IAAA,GAAA0B,SAAA,CAAAzB,IAAA;YAAA;cAAA,IACAuB,MAAA,CAAA1E,YAAA;gBAAA4E,SAAA,CAAAzB,IAAA;gBAAA;cAAA;cACAE,OAAA,CAAAC,GAAA;cAAAsB,SAAA,CAAAzB,IAAA;cAAA,OACAuB,MAAA,CAAArC,YAAA;YAAA;YAAA;cAAA,OAAAuC,SAAA,CAAAL,IAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IAEA;IAEA;IACAE,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MAAA,OAAAvC,mFAAA,cAAAC,sEAAA,UAAAuC,SAAA;QAAA,IAAAC,KAAA;QAAA,OAAAxC,sEAAA,WAAAyC,SAAA;UAAA,kBAAAA,SAAA,CAAA/B,IAAA,GAAA+B,SAAA,CAAA9B,IAAA;YAAA;cACA6B,KAAA,GAAAF,MAAA,CAAA5E,WAAA,CAAAgF,IAAA;cAAA,IACAF,KAAA;gBAAAC,SAAA,CAAA9B,IAAA;gBAAA;cAAA;cACAI,GAAA,CAAA4B,SAAA;gBACAf,KAAA;gBACAgB,IAAA;cACA;cAAA,OAAAH,SAAA,CAAAI,MAAA;YAAA;cAAA,IAIAP,MAAA,CAAA9E,YAAA;gBAAAiF,SAAA,CAAA9B,IAAA;gBAAA;cAAA;cACAI,GAAA,CAAA4B,SAAA;gBACAf,KAAA;gBACAgB,IAAA;cACA;cAAA,OAAAH,SAAA,CAAAI,MAAA;YAAA;cAAAJ,SAAA,CAAA9B,IAAA;cAAA,OAIA2B,MAAA,CAAAQ,YAAA,CAAAN,KAAA;YAAA;YAAA;cAAA,OAAAC,SAAA,CAAAV,IAAA;UAAA;QAAA,GAAAQ,QAAA;MAAA;IACA;IAEAQ,SAAA,WAAAA,UAAAP,KAAA;MAAA,IAAAQ,MAAA;MAAA,OAAAjD,mFAAA,cAAAC,sEAAA,UAAAiD,SAAA;QAAA,OAAAjD,sEAAA,WAAAkD,SAAA;UAAA,kBAAAA,SAAA,CAAAxC,IAAA,GAAAwC,SAAA,CAAAvC,IAAA;YAAA;cACAqC,MAAA,CAAAtF,WAAA,GAAA8E,KAAA;cAAAU,SAAA,CAAAvC,IAAA;cAAA,OACAqC,MAAA,CAAAF,YAAA,CAAAN,KAAA;YAAA;YAAA;cAAA,OAAAU,SAAA,CAAAnB,IAAA;UAAA;QAAA,GAAAkB,QAAA;MAAA;IACA;IAEAH,YAAA,WAAAA,aAAAN,KAAA;MAAA,IAAAW,MAAA;MAAA,OAAApD,mFAAA,cAAAC,sEAAA,UAAAoD,SAAA;QAAA,IAAAC,mBAAA,EAAAC,mBAAA,EAAAjD,GAAA,EAAAC,QAAA,EAAAuB,OAAA,EAAA0B,QAAA,EAAAC,YAAA,EAAAC,GAAA;QAAA,OAAAzD,sEAAA,WAAA0D,SAAA;UAAA,kBAAAA,SAAA,CAAAhD,IAAA,GAAAgD,SAAA,CAAA/C,IAAA;YAAA;cACAwC,MAAA,CAAAxF,SAAA;cACAwF,MAAA,CAAAvF,UAAA;cACAuF,MAAA,CAAAQ,uBAAA;cAEA9C,OAAA,CAAAC,GAAA,YAAA0B,KAAA;cAAAkB,SAAA,CAAAhD,IAAA;cAAAgD,SAAA,CAAA/C,IAAA;cAAA,OAGAI,GAAA,CAAAC,OAAA;gBACAC,GAAA,KAAA3B,MAAA,CAAA6D,MAAA,CAAA5F,UAAA,sCAAA+B,MAAA,CAAAsE,kBAAA,CAAApB,KAAA;gBACAtB,MAAA;gBACAC,MAAA;kBACA;kBACA;gBACA;cACA;YAAA;cAAAkC,mBAAA,GAAAK,SAAA,CAAAtC,IAAA;cAAAkC,mBAAA,GAAAjC,gFAAA,CAAAgC,mBAAA;cAPAhD,GAAA,GAAAiD,mBAAA;cAAAhD,QAAA,GAAAgD,mBAAA;cASAzC,OAAA,CAAAC,GAAA,aAAAR,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAhD,IAAA,IAAAgD,QAAA,CAAAhD,IAAA,CAAAgE,OAAA,IAAAhB,QAAA,CAAAhD,IAAA,CAAAA,IAAA,IAAAgD,QAAA,CAAAhD,IAAA,CAAAA,IAAA,CAAAuE,OAAA;gBACAA,OAAA,GAAAvB,QAAA,CAAAhD,IAAA,CAAAA,IAAA,CAAAuE,OAAA;gBACA,IAAAA,OAAA,IAAAA,OAAA,CAAAgC,IAAA;kBACAV,MAAA,CAAAtF,WAAA,GAAAgE,OAAA,CAAAgC,IAAA;kBACAV,MAAA,CAAAvF,UAAA;kBACAuF,MAAA,CAAAW,YAAA,CAAAtB,KAAA;kBACA3B,OAAA,CAAAC,GAAA;gBACA;kBACAqC,MAAA,CAAAtF,WAAA;kBACAsF,MAAA,CAAAvF,UAAA;gBACA;cACA;gBACA2F,QAAA,GAAAjD,QAAA,IAAAA,QAAA,CAAAhD,IAAA,KAAAgD,QAAA,CAAAhD,IAAA,CAAAkE,OAAA,IAAAlB,QAAA,CAAAhD,IAAA,CAAAmE,KAAA;gBACA0B,MAAA,CAAAtF,WAAA,aAAAyB,MAAA,CAAAiE,QAAA;gBACAJ,MAAA,CAAAvF,UAAA;cACA;cAAA8F,SAAA,CAAA/C,IAAA;cAAA;YAAA;cAAA+C,SAAA,CAAAhD,IAAA;cAAA+C,GAAA,GAAAC,SAAA;cAGA7C,OAAA,CAAAY,KAAA,UAAAgC,GAAA;cAEAD,YAAA;cACA,IAAAC,GAAA,CAAAjC,OAAA,IAAAiC,GAAA,CAAAjC,OAAA,CAAAuC,QAAA;gBACAP,YAAA;cACA,WAAAC,GAAA,CAAAjC,OAAA,IAAAiC,GAAA,CAAAjC,OAAA,CAAAuC,QAAA;gBACAP,YAAA;cACA,WAAAC,GAAA,CAAAjC,OAAA,IAAAiC,GAAA,CAAAjC,OAAA,CAAAuC,QAAA;gBACAP,YAAA;cACA;gBACAA,YAAA,uCAAAlE,MAAA,CAAAmE,GAAA,CAAAjC,OAAA;cACA;cAEA2B,MAAA,CAAAtF,WAAA,MAAAyB,MAAA,CAAAkE,YAAA,6dAOA;cACAL,MAAA,CAAAvF,UAAA;YAAA;cAAA8F,SAAA,CAAAhD,IAAA;cAEAyC,MAAA,CAAAa,WAAA;cAAA,OAAAN,SAAA,CAAAO,MAAA;YAAA;YAAA;cAAA,OAAAP,SAAA,CAAA3B,IAAA;UAAA;QAAA,GAAAqB,QAAA;MAAA;IAEA;IAEA;IACAO,uBAAA,WAAAA,wBAAA;MAAA,IAAAO,MAAA;MACA,IAAAC,YAAA;MACA,KAAAC,oBAAA,MAAApG,eAAA,CAAAmG,YAAA;MAEA,KAAAhG,eAAA,GAAAkG,WAAA;QACAF,YAAA,IAAAA,YAAA,QAAAD,MAAA,CAAAlG,eAAA,CAAAsG,MAAA;QACAJ,MAAA,CAAAE,oBAAA,CAAAF,MAAA,CAAAlG,eAAA,CAAAmG,YAAA;MACA;IACA;IAEAC,oBAAA,WAAAA,qBAAA5C,OAAA;MACA,KAAA1D,cAAA,GAAA0D,OAAA,CAAAvD,IAAA;MACA,KAAAF,iBAAA,GAAAyD,OAAA,CAAAtD,GAAA;IACA;IAEA8F,WAAA,WAAAA,YAAA;MACA,KAAArG,SAAA;MACA,SAAAQ,eAAA;QACAoG,aAAA,MAAApG,eAAA;QACA,KAAAA,eAAA;MACA;IACA;IAEA;IACA2F,YAAA,WAAAA,aAAAtB,KAAA;MACA;MACA,KAAAvD,aAAA,QAAAA,aAAA,CAAAuF,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,KAAAjC,KAAA;MAAA;;MAEA;MACA,KAAAvD,aAAA,CAAAyF,OAAA,CAAAlC,KAAA;;MAEA;MACA,SAAAvD,aAAA,CAAAqF,MAAA;QACA,KAAArF,aAAA,QAAAA,aAAA,CAAA0F,KAAA;MACA;;MAEA;MACA5D,GAAA,CAAA6D,cAAA,kBAAAC,IAAA,CAAAC,SAAA,MAAA7F,aAAA;IACA;IAEA8F,iBAAA,WAAAA,kBAAA;MACA;QACA,IAAAC,OAAA,GAAAjE,GAAA,CAAAkE,cAAA;QACA,IAAAD,OAAA;UACA,KAAA/F,aAAA,GAAA4F,IAAA,CAAAK,KAAA,CAAAF,OAAA;QACA;MACA,SAAAvD,KAAA;QACAZ,OAAA,CAAAY,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACA0D,UAAA,WAAAA,WAAAC,CAAA;MACA,IAAAA,CAAA,CAAAC,OAAA;QAAA;QACA,KAAAhD,aAAA;MACA;IACA;IAEA;IACAiD,sBAAA,WAAAA,uBAAA;MACA,SAAAlH,WAAA;QACA,KAAAmH,oBAAA;MACA;QACA,KAAAC,qBAAA;MACA;IACA;IAEAA,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,UAAA,GAAA3E,GAAA,CAAA4E,iBAAA;MACA,IAAAC,QAAA,GAAAF,UAAA,CAAAE,QAAA;MA6BA,IAAAA,QAAA,mBAAAA,QAAA,kBAAAA,QAAA;QACA,KAAAxH,WAAA;QACA,IAAAyH,MAAA,GAAAC,aAAA;QACAD,MAAA,CAAAE,2BAAA,GAAAC,KAAA;UACAC,IAAA;UACAC,QAAA;QACA;QACAL,MAAA,CAAAE,2BAAA,GAAAI,WAAA,aAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,MAAA;YACAZ,MAAA,CAAA/H,WAAA,GAAA0I,GAAA,CAAAC,MAAA;YACAZ,MAAA,CAAAF,oBAAA;YACAE,MAAA,CAAApD,aAAA;UACA;QACA;QACAwD,MAAA,CAAAE,2BAAA,GAAAO,MAAA,aAAAF,GAAA;UACA,IAAAA,GAAA,CAAAC,MAAA;YACAZ,MAAA,CAAA/H,WAAA,GAAA0I,GAAA,CAAAC,MAAA;YACAZ,MAAA,CAAApD,aAAA;UACA;UACAoD,MAAA,CAAAF,oBAAA;QACA;QACAM,MAAA,CAAAE,2BAAA,GAAAQ,OAAA,aAAAH,GAAA;UACAX,MAAA,CAAAF,oBAAA;UACAxE,GAAA,CAAA4B,SAAA;YAAAf,KAAA;YAAAgB,IAAA;UAAA;QACA;QACA;MACA;;MAEA;MACA7B,GAAA,CAAA4B,SAAA;QACAf,KAAA;QACAgB,IAAA;QACAsD,QAAA;MACA;IACA;IAEAX,oBAAA,WAAAA,qBAAA;MACA,KAAAnH,WAAA;MAiBA;QACA,IAAAyH,MAAA,GAAAC,aAAA;QACAD,MAAA,CAAAE,2BAAA,GAAAhE,IAAA;MACA,SAAAqD,CAAA;IAEA;IAEA;IACAoB,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,MAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,MAAA;MACA,IAAAG,GAAA,GAAAH,MAAA,CAAAI,UAAA;MACA,IAAAC,CAAA,GAAAL,MAAA,CAAAM,KAAA;MACA,IAAAC,CAAA,GAAAP,MAAA,CAAAQ,MAAA;MACA,IAAAC,CAAA;MACA,IAAAC,KAAA,YAAAA,KAAA;QACAP,GAAA,CAAAQ,SAAA,OAAAN,CAAA,EAAAE,CAAA;QACAJ,GAAA,CAAAS,IAAA;QACAT,GAAA,CAAAU,WAAA;QACAV,GAAA,CAAAW,SAAA;QACAX,GAAA,CAAAY,SAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,IAAAX,CAAA,EAAAW,CAAA;UACA,IAAAC,CAAA,GAAAV,CAAA,OAAAW,IAAA,CAAAC,GAAA,CAAAH,CAAA,QAAAP,CAAA,cAAAS,IAAA,CAAAC,GAAA,CAAAV,CAAA;UACA,IAAAO,CAAA,QAAAb,GAAA,CAAAiB,MAAA,CAAAJ,CAAA,EAAAC,CAAA,OACAd,GAAA,CAAAkB,MAAA,CAAAL,CAAA,EAAAC,CAAA;QACA;QACAd,GAAA,CAAAmB,MAAA;QACAnB,GAAA,CAAAoB,OAAA;QACAd,CAAA;QACA,IAAAV,MAAA,CAAArI,WAAA,EAAAqI,MAAA,CAAAtH,WAAA,GAAA+I,qBAAA,CAAAd,KAAA;MACA;MACAA,KAAA;IACA;EACA;EAEAe,OAAA,WAAAA,QAAA;IACAtH,OAAA,CAAAC,GAAA;IACAD,OAAA,CAAAC,GAAA,oBAAAvD,UAAA;IACA;IACA,KAAA2B,IAAA,UAAAkJ,MAAA,sBAAAA,MAAA,CAAAC,iBAAA;EACA;EAEAC,MAAA,WAAAA,OAAAC,OAAA;IACA;IACA,KAAA1I,YAAA;;IAEA;IACA,KAAAkF,iBAAA;EACA;EAEAyD,QAAA,WAAAA,SAAA;IACA;IACA,SAAArK,eAAA;MACAoG,aAAA,MAAApG,eAAA;IACA;;IAEA;IACA,SAAAC,WAAA;MACA,KAAAmH,oBAAA;IACA;EACA;AACA,G;;;;;;;;;;AClhBA,uC;;;;;;;;;;;;;;;;;;ACAmB;AACnB;AACAkD,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AACrC;AACqB;AAC1CG,UAAU,CAACD,8DAAI,CAAC,C;;;;;;;;;;;;;;;;ACL8G;AAC9H;AACA,CAAyD;AACL;AACpD,CAAyF;;;AAGzF;AACmI;AACnI,gBAAgB,4IAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACA,+DAAe,iB;;;;;;;;;;;;;ACvBkf,CAAC,+DAAe,ydAAG,EAAC,C;;;;;;;;;;;;;;;;;ACAmO,CAAC,+DAAe,urBAAG,EAAC,C", "sources": ["webpack:///./src/pages/index/index.vue?6014", "uni-app:///src/pages/index/index.vue", "webpack:///./src/pages/index/index.vue?fa30", "uni-app:///src/main.js", null, "webpack:///./src/pages/index/index.vue?fb26", "webpack:///./src/pages/index/index.vue?5046"], "sourcesContent": ["var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "<template>\r\n\t<view class=\"ai-query-container\">\r\n\t\t<!-- 连接状态指示器 -->\r\n\t\t<view class=\"connection-status\" :class=\"connectionStatusClass\" @click=\"reconnectMCP\">\r\n\t\t\t{{ connectionStatusText }}\r\n\t\t</view>\r\n\r\n\t\t<view class=\"container\">\r\n\t\t\t<text class=\"title\">AI智能问数</text>\r\n\r\n\t\t\t<view class=\"search-container\">\r\n\t\t\t\t<input type=\"text\" class=\"search-input\" placeholder=\"去年营业额前十的门店同比情况\" v-model=\"searchQuery\"\r\n\t\t\t\t\t@keypress=\"onKeyPress\" />\r\n\t\t\t\t<button class=\"search-btn voice-btn\" :class=\"{ 'recording': isRecording }\"\r\n\t\t\t\t\t@click=\"toggleVoiceRecognition\" :title=\"isRecording ? '正在聆听...点击停止' : '语音输入'\">\r\n\t\t\t\t\t<text class=\"voice-icon\">🎤</text>\r\n\t\t\t\t</button>\r\n\t\t\t\t<button class=\"search-btn\" @click=\"performSearch\">\r\n\t\t\t\t\t<text class=\"search-icon\">🔍</text>\r\n\t\t\t\t</button>\r\n\t\t\t\t<canvas v-if=\"isRecording && isH5\" id=\"voiceWaveCanvas\" class=\"voice-wave-canvas\"></canvas>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 查询等待提示 -->\r\n\t\t\t<view class=\"loading\" v-if=\"isLoading\">\r\n\t\t\t\t<view class=\"loading-content\">\r\n\t\t\t\t\t<view class=\"spinner\"></view>\r\n\t\t\t\t\t<text class=\"loading-text\">\r\n\t\t\t\t\t\t{{ loadingMessage }}<text class=\"loading-dots\"></text>\r\n\t\t\t\t\t</text>\r\n\t\t\t\t\t<text class=\"loading-sub-text\">{{ loadingSubMessage }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 查询结果 -->\r\n\t\t\t<view class=\"result-container\" v-if=\"showResult\">\r\n\t\t\t\t<text class=\"result-title\">📊 查询结果</text>\r\n\t\t\t\t<rich-text class=\"result-content\" :nodes=\"formattedResult\"></rich-text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<text class=\"section-title\">热门搜索</text>\r\n\t\t\t\t<view class=\"tags-container\">\r\n\t\t\t\t\t<view class=\"tag\" v-for=\"(tag, index) in hotTags\" :key=\"index\" @click=\"searchTag(tag)\">\r\n\t\t\t\t\t\t{{ tag }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<text class=\"section-title\">历史搜索</text>\r\n\t\t\t\t<view class=\"history-container\">\r\n\t\t\t\t\t<view class=\"history-item\" v-for=\"(item, index) in searchHistory\" :key=\"index\"\r\n\t\t\t\t\t\t@click=\"searchTag(item)\">\r\n\t\t\t\t\t\t{{ item }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// MCP连接相关\r\n\t\t\t\t// 本地测试地址\r\n\t\t\t\tmcpApiBase: 'http://localhost:8081',\r\n\t\t\t\t// 线上地址\r\n\t\t\t\t// mcpApiBase: 'https://b55b-183-250-154-98.ngrok-free.app',\r\n\t\t\t\tmcpConnected: false,\r\n\t\t\t\tconnectionStatus: 'disconnected', // connected, disconnected, connecting\r\n\r\n\t\t\t\t// 搜索相关\r\n\t\t\t\tsearchQuery: '',\r\n\t\t\t\tisLoading: false,\r\n\t\t\t\tshowResult: false,\r\n\t\t\t\tqueryResult: '',\r\n\r\n\t\t\t\t// 加载动画相关\r\n\t\t\t\tloadingMessage: '正在智能分析您的问题',\r\n\t\t\t\tloadingSubMessage: 'AI正在理解查询意图',\r\n\t\t\t\tloadingMessages: [{\r\n\t\t\t\t\t\tmain: '正在智能分析您的问题',\r\n\t\t\t\t\t\tsub: 'AI正在理解查询意图'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tmain: '正在连接数据库',\r\n\t\t\t\t\t\tsub: '建立与SQL Server的安全连接'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tmain: '正在查询数据',\r\n\t\t\t\t\t\tsub: '从StoreRevenue表中检索相关数据'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tmain: '正在处理结果',\r\n\t\t\t\t\t\tsub: '对查询结果进行智能分析和格式化'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tloadingInterval: null,\r\n\r\n\t\t\t\t// 语音识别相关\r\n\t\t\t\tisRecording: false,\r\n\t\t\t\taliyunAppKey: 'DzVzUYbM1vG83nxm',\r\n\t\t\t\taliyunToken: '74902900e45c44a497f830c29522ede0',\r\n\t\t\t\taliyunWs: null,\r\n\t\t\t\taliyunRecorder: null,\r\n\t\t\t\taliyunAudioContext: null,\r\n\t\t\t\taliyunStream: null,\r\n\t\t\t\taliyunAnalyser: null,\r\n\t\t\t\taliyunWaveAnimationId: null,\r\n\t\t\t\tsilenceStartTime: null,\r\n\t\t\t\tsilenceThreshold: 0.02,\r\n\t\t\t\tsilenceTimeout: 1000,\r\n\r\n\t\t\t\t// 热门搜索标签\r\n\t\t\t\thotTags: [\r\n\t\t\t\t\t'本月平台收入总共多少',\r\n\t\t\t\t\t'本月平台收入明细',\r\n\t\t\t\t\t'上个月平台收入',\r\n\t\t\t\t\t'平台收入排名前5',\r\n\t\t\t\t\t'去年营业额',\r\n\t\t\t\t\t'今年营业额',\r\n\t\t\t\t\t'门店营业额排名'\r\n\t\t\t\t],\r\n\r\n\t\t\t\t// 搜索历史\r\n\t\t\t\tsearchHistory: [\r\n\t\t\t\t\t'本月平台收入总共多少',\r\n\t\t\t\t\t'列一个本月平台收入的明细给我',\r\n\t\t\t\t\t'上个月的平台收入总共有多少',\r\n\t\t\t\t\t'看看去年的营业额',\r\n\t\t\t\t\t'今年的营业额',\r\n\t\t\t\t\t'门店营业额前十门店营业额',\r\n\t\t\t\t\t'去年营业额前十门店同比情况',\r\n\t\t\t\t\t'门店日期间3天利润趋势'\r\n\t\t\t\t],\r\n\r\n\t\t\t\tisH5: false,\r\n\t\t\t\t_waveAnimId: null,\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tcomputed: {\r\n\t\t\tconnectionStatusClass() {\r\n\t\t\t\treturn `connection-status ${this.connectionStatus}`;\r\n\t\t\t},\r\n\r\n\t\t\tconnectionStatusText() {\r\n\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t'connected': '🟢 已连接',\r\n\t\t\t\t\t'disconnected': '🔴 未连接',\r\n\t\t\t\t\t'connecting': '🟡 连接中...'\r\n\t\t\t\t};\r\n\t\t\t\treturn statusMap[this.connectionStatus] || '🔴 未连接';\r\n\t\t\t},\r\n\r\n\t\t\tformattedResult() {\r\n\t\t\t\tif (!this.queryResult) return '';\r\n\r\n\t\t\t\tlet formatted = this.queryResult\r\n\t\t\t\t\t.replace(/\\n/g, '<br>')\r\n\t\t\t\t\t.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n\t\t\t\t\t.replace(/\\*(.*?)\\*/g, '<em>$1</em>')\r\n\t\t\t\t\t.replace(/`(.*?)`/g,\r\n\t\t\t\t\t\t'<code style=\"background: #f1f3f4; padding: 2px 4px; border-radius: 3px;\">$1</code>');\r\n\r\n\t\t\t\t// 数字高亮\r\n\t\t\t\tformatted = formatted.replace(/(\\d+\\.?\\d*)(元|万元|亿元)/g,\r\n\t\t\t\t\t'<span style=\"color: #4A90E2; font-weight: bold;\">$1$2</span>');\r\n\r\n\t\t\t\treturn `<div style=\"line-height: 1.8;\">${formatted}</div>`;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// MCP连接管理\r\n\t\t\tasync connectToMCP() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthis.updateConnectionStatus('connecting');\r\n\t\t\t\t\tconsole.log('正在连接MCP服务器...');\r\n\r\n\t\t\t\t\tconst [err, response] = await uni.request({\r\n\t\t\t\t\t\turl: `${this.mcpApiBase}/mcp/connect`,\r\n\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tif (response && response.data && response.data.success) {\r\n\t\t\t\t\t\tthis.mcpConnected = true;\r\n\t\t\t\t\t\tthis.updateConnectionStatus('connected');\r\n\t\t\t\t\t\tconsole.log('MCP服务器连接成功');\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthrow new Error((response && response.data && response.data.message) || '连接失败');\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('MCP连接错误:', error);\r\n\t\t\t\t\tthis.mcpConnected = false;\r\n\t\t\t\t\tthis.updateConnectionStatus('disconnected');\r\n\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '连接失败',\r\n\t\t\t\t\t\t\tcontent: `MCP服务器连接失败: ${error.message}\\n\\n请确保:\\n1. MCP服务器正在运行 (端口8080)\\n2. MCP客户端正在运行 (端口8081)`,\r\n\t\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tupdateConnectionStatus(status) {\r\n\t\t\t\tthis.connectionStatus = status;\r\n\t\t\t},\r\n\r\n\t\t\tasync reconnectMCP() {\r\n\t\t\t\tif (!this.mcpConnected) {\r\n\t\t\t\t\tconsole.log('尝试重新连接MCP...');\r\n\t\t\t\t\tawait this.connectToMCP();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 搜索功能\r\n\t\t\tasync performSearch() {\r\n\t\t\t\tconst query = this.searchQuery.trim();\r\n\t\t\t\tif (!query) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入查询内容',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!this.mcpConnected) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: 'MCP服务器未连接，请稍后重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tawait this.executeQuery(query);\r\n\t\t\t},\r\n\r\n\t\t\tasync searchTag(query) {\r\n\t\t\t\tthis.searchQuery = query;\r\n\t\t\t\tawait this.executeQuery(query);\r\n\t\t\t},\r\n\r\n\t\t\tasync executeQuery(query) {\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\tthis.showResult = false;\r\n\t\t\t\tthis.showLoadingWithMessages();\r\n\r\n\t\t\t\tconsole.log('开始执行查询:', query);\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst [err, response] = await uni.request({\r\n\t\t\t\t\t\turl: `${this.mcpApiBase}/mcp/tools/business-query?query=${encodeURIComponent(query)}`,\r\n\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t'Content-Type': 'application/json',\r\n\t\t\t\t\t\t\t'Accept': 'application/json'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tconsole.log('API响应状态:', response);\r\n\r\n\t\t\t\t\tif (response && response.data && response.data.success && response.data.data && response.data.data.content) {\r\n\t\t\t\t\t\tconst content = response.data.data.content[0];\r\n\t\t\t\t\t\tif (content && content.text) {\r\n\t\t\t\t\t\t\tthis.queryResult = content.text;\r\n\t\t\t\t\t\t\tthis.showResult = true;\r\n\t\t\t\t\t\t\tthis.addToHistory(query);\r\n\t\t\t\t\t\t\tconsole.log('查询成功，结果已显示');\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.queryResult = '查询结果为空，请尝试其他问题';\r\n\t\t\t\t\t\t\tthis.showResult = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconst errorMsg = (response && response.data && (response.data.message || response.data.error)) || '查询失败，请稍后重试';\r\n\t\t\t\t\t\tthis.queryResult = `❌ ${errorMsg}`;\r\n\t\t\t\t\t\tthis.showResult = true;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('查询错误:', error);\r\n\r\n\t\t\t\t\tlet errorMessage = '';\r\n\t\t\t\t\tif (error.message && error.message.includes('Failed to fetch')) {\r\n\t\t\t\t\t\terrorMessage = '❌ 无法连接到MCP服务，请检查服务是否正在运行';\r\n\t\t\t\t\t} else if (error.message && error.message.includes('HTTP 404')) {\r\n\t\t\t\t\t\terrorMessage = '❌ API接口不存在，请检查MCP客户端是否正确启动';\r\n\t\t\t\t\t} else if (error.message && error.message.includes('HTTP 500')) {\r\n\t\t\t\t\t\terrorMessage = '❌ 服务器内部错误，请检查MCP服务器日志';\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\terrorMessage = `❌ 查询出错: ${error.message}`;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthis.queryResult = `${errorMessage}\r\n\t\t\t\t\t<div style=\"margin-top: 10px; font-size: 12px; color: #666;\">\r\n\t\t\t\t\t\t<strong>故障排除建议：</strong><br>\r\n\t\t\t\t\t\t1. 确保MCP服务器正在运行 (端口8080)<br>\r\n\t\t\t\t\t\t2. 确保MCP客户端正在运行 (端口8081)<br>\r\n\t\t\t\t\t\t3. 检查网络连接<br>\r\n\t\t\t\t\t\t4. 刷新页面重新连接\r\n\t\t\t\t\t</div>`;\r\n\t\t\t\t\tthis.showResult = true;\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 加载动画管理\r\n\t\t\tshowLoadingWithMessages() {\r\n\t\t\t\tlet currentIndex = 0;\r\n\t\t\t\tthis.updateLoadingMessage(this.loadingMessages[currentIndex]);\r\n\r\n\t\t\t\tthis.loadingInterval = setInterval(() => {\r\n\t\t\t\t\tcurrentIndex = (currentIndex + 1) % this.loadingMessages.length;\r\n\t\t\t\t\tthis.updateLoadingMessage(this.loadingMessages[currentIndex]);\r\n\t\t\t\t}, 1500);\r\n\t\t\t},\r\n\r\n\t\t\tupdateLoadingMessage(message) {\r\n\t\t\t\tthis.loadingMessage = message.main;\r\n\t\t\t\tthis.loadingSubMessage = message.sub;\r\n\t\t\t},\r\n\r\n\t\t\thideLoading() {\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tif (this.loadingInterval) {\r\n\t\t\t\t\tclearInterval(this.loadingInterval);\r\n\t\t\t\t\tthis.loadingInterval = null;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 历史记录管理\r\n\t\t\taddToHistory(query) {\r\n\t\t\t\t// 避免重复\r\n\t\t\t\tthis.searchHistory = this.searchHistory.filter(item => item !== query);\r\n\r\n\t\t\t\t// 添加到开头\r\n\t\t\t\tthis.searchHistory.unshift(query);\r\n\r\n\t\t\t\t// 限制历史记录数量\r\n\t\t\t\tif (this.searchHistory.length > 10) {\r\n\t\t\t\t\tthis.searchHistory = this.searchHistory.slice(0, 10);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 保存到本地存储\r\n\t\t\t\tuni.setStorageSync('searchHistory', JSON.stringify(this.searchHistory));\r\n\t\t\t},\r\n\r\n\t\t\tloadSearchHistory() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst history = uni.getStorageSync('searchHistory');\r\n\t\t\t\t\tif (history) {\r\n\t\t\t\t\t\tthis.searchHistory = JSON.parse(history);\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('加载搜索历史失败:', error);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 键盘事件\r\n\t\t\tonKeyPress(e) {\r\n\t\t\t\tif (e.keyCode === 13) { // Enter键\r\n\t\t\t\t\tthis.performSearch();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 语音识别功能（简化版，uniapp环境下可能需要特殊处理）\r\n\t\t\ttoggleVoiceRecognition() {\r\n\t\t\t\tif (this.isRecording) {\r\n\t\t\t\t\tthis.stopVoiceRecognition();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.startVoiceRecognition();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tstartVoiceRecognition() {\r\n\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\r\n\t\t\t\tconst platform = systemInfo.platform;\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (typeof window !== 'undefined' && (window.SpeechRecognition || window.webkitSpeechRecognition)) {\r\n\t\t\t\t\tthis.isRecording = true;\r\n\t\t\t\t\tconst SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\r\n\t\t\t\t\tthis._recognition = new SpeechRecognition();\r\n\t\t\t\t\tthis._recognition.lang = 'zh-CN';\r\n\t\t\t\t\tthis._recognition.continuous = false;\r\n\t\t\t\t\tthis._recognition.interimResults = false;\r\n\t\t\t\t\tthis._recognition.onresult = (event) => {\r\n\t\t\t\t\t\tconst text = event.results[0][0].transcript;\r\n\t\t\t\t\t\tthis.searchQuery = text;\r\n\t\t\t\t\t\tthis.stopVoiceRecognition();\r\n\t\t\t\t\t\tthis.performSearch();\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis._recognition.onerror = (event) => {\r\n\t\t\t\t\t\tthis.stopVoiceRecognition();\r\n\t\t\t\t\t\tuni.showToast({ title: '语音识别失败', icon: 'none' });\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis._recognition.onend = () => {\r\n\t\t\t\t\t\tthis.stopVoiceRecognition();\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis._recognition.start();\r\n\t\t\t\t\t// 启动波形动画\r\n\t\t\t\t\tthis.$nextTick(() => { this._startWaveAnim(); });\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tif (platform === 'devtools' || platform === 'android' || platform === 'ios') {\r\n\t\t\t\t\tthis.isRecording = true;\r\n\t\t\t\t\tconst plugin = requirePlugin('WechatSI');\r\n\t\t\t\t\tplugin.getRecordRecognitionManager().start({\r\n\t\t\t\t\t\tlang: 'zh_CN',\r\n\t\t\t\t\t\tduration: 60000\r\n\t\t\t\t\t});\r\n\t\t\t\t\tplugin.getRecordRecognitionManager().onRecognize = (res) => {\r\n\t\t\t\t\t\tif (res.result) {\r\n\t\t\t\t\t\t\tthis.searchQuery = res.result;\r\n\t\t\t\t\t\t\tthis.stopVoiceRecognition();\r\n\t\t\t\t\t\t\tthis.performSearch();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t};\r\n\t\t\t\t\tplugin.getRecordRecognitionManager().onStop = (res) => {\r\n\t\t\t\t\t\tif (res.result) {\r\n\t\t\t\t\t\t\tthis.searchQuery = res.result;\r\n\t\t\t\t\t\t\tthis.performSearch();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.stopVoiceRecognition();\r\n\t\t\t\t\t};\r\n\t\t\t\t\tplugin.getRecordRecognitionManager().onError = (res) => {\r\n\t\t\t\t\t\tthis.stopVoiceRecognition();\r\n\t\t\t\t\t\tuni.showToast({ title: '语音识别失败', icon: 'none' });\r\n\t\t\t\t\t};\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// 其他平台\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '当前平台暂不支持语音识别',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tstopVoiceRecognition() {\r\n\t\t\t\tthis.isRecording = false;\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (this._recognition) {\r\n\t\t\t\t\tthis._recognition.stop();\r\n\t\t\t\t\tthis._recognition = null;\r\n\t\t\t\t}\r\n\t\t\t\tif (this._waveAnimId) {\r\n\t\t\t\t\tcancelAnimationFrame(this._waveAnimId);\r\n\t\t\t\t\tthis._waveAnimId = null;\r\n\t\t\t\t\tconst canvas = document.getElementById('voiceWaveCanvas');\r\n\t\t\t\t\tif (canvas) {\r\n\t\t\t\t\t\tconst ctx = canvas.getContext('2d');\r\n\t\t\t\t\t\tctx.clearRect(0, 0, canvas.width, canvas.height);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst plugin = requirePlugin('WechatSI');\r\n\t\t\t\t\tplugin.getRecordRecognitionManager().stop();\r\n\t\t\t\t} catch (e) {}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\r\n\t\t\t// H5端波形动画\r\n\t\t\t_startWaveAnim() {\r\n\t\t\t\tconst canvas = document.getElementById('voiceWaveCanvas');\r\n\t\t\t\tif (!canvas) return;\r\n\t\t\t\tconst ctx = canvas.getContext('2d');\r\n\t\t\t\tconst W = canvas.width = 120;\r\n\t\t\t\tconst H = canvas.height = 40;\r\n\t\t\t\tlet t = 0;\r\n\t\t\t\tconst draw = () => {\r\n\t\t\t\t\tctx.clearRect(0, 0, W, H);\r\n\t\t\t\t\tctx.save();\r\n\t\t\t\t\tctx.strokeStyle = '#4A90E2';\r\n\t\t\t\t\tctx.lineWidth = 3;\r\n\t\t\t\t\tctx.beginPath();\r\n\t\t\t\t\tfor (let x = 0; x <= W; x += 2) {\r\n\t\t\t\t\t\tconst y = H/2 + Math.sin((x/12) + t) * (10 + 8*Math.sin(t/2));\r\n\t\t\t\t\t\tif (x === 0) ctx.moveTo(x, y);\r\n\t\t\t\t\t\telse ctx.lineTo(x, y);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tctx.stroke();\r\n\t\t\t\t\tctx.restore();\r\n\t\t\t\t\tt += 0.08;\r\n\t\t\t\t\tif (this.isRecording) this._waveAnimId = requestAnimationFrame(draw);\r\n\t\t\t\t};\r\n\t\t\t\tdraw();\r\n\t\t\t},\r\n\t\t},\r\n\r\n\t\tmounted() {\r\n\t\t\tconsole.log('AI智能问数页面已加载');\r\n\t\t\tconsole.log('MCP API地址:', this.mcpApiBase);\r\n\t\t\t// 判断平台\r\n\t\t\tthis.isH5 = typeof window !== 'undefined' && !!window.SpeechRecognition;\r\n\t\t},\r\n\r\n\t\tonLoad(options) {\r\n\t\t\t// 自动建立MCP连接\r\n\t\t\tthis.connectToMCP();\r\n\r\n\t\t\t// 加载搜索历史\r\n\t\t\tthis.loadSearchHistory();\r\n\t\t},\r\n\r\n\t\tonUnload() {\r\n\t\t\t// 清理定时器\r\n\t\t\tif (this.loadingInterval) {\r\n\t\t\t\tclearInterval(this.loadingInterval);\r\n\t\t\t}\r\n\r\n\t\t\t// 停止语音识别\r\n\t\t\tif (this.isRecording) {\r\n\t\t\t\tthis.stopVoiceRecognition();\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style  scoped>\r\n\t.ai-query-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tpadding: 20px;\r\n\t}\r\n\r\n\t.connection-status {\r\n\t\tposition: fixed;\r\n\t\ttop: 60px;\r\n\t\tright: 20px;\r\n\t\tpadding: 8px 16px;\r\n\t\tborder-radius: 20px;\r\n\t\tfont-size: 12px;\r\n\t\tfont-weight: 500;\r\n\t\tz-index: 1000;\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&.connected {\r\n\t\t\tbackground: #d4edda;\r\n\t\t\tcolor: #155724;\r\n\t\t\tborder: 1px solid #c3e6cb;\r\n\t\t}\r\n\r\n\t\t&.disconnected {\r\n\t\t\tbackground: #f8d7da;\r\n\t\t\tcolor: #721c24;\r\n\t\t\tborder: 1px solid #f5c6cb;\r\n\t\t}\r\n\r\n\t\t&.connecting {\r\n\t\t\tbackground: #fff3cd;\r\n\t\t\tcolor: #856404;\r\n\t\t\tborder: 1px solid #ffeaa7;\r\n\t\t}\r\n\t}\r\n\r\n\t.container {\r\n\t\tmax-width: 800px;\r\n\t\tmargin: 0 auto;\r\n\t\tbackground: rgba(255, 255, 255, 0.95);\r\n\t\tborder-radius: 20px;\r\n\t\tpadding: 40px;\r\n\t\tbox-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t}\r\n\r\n\t.title {\r\n\t\tdisplay: block;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 32px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #4A90E2;\r\n\t\tmargin-bottom: 40px;\r\n\t\tletter-spacing: 2px;\r\n\t}\r\n\r\n\t.search-container {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 50px;\r\n\t\tpadding: 8px 16px;\r\n\t\tbox-shadow: 0 4px 16px rgba(74, 144, 226, 0.08);\r\n\t\tmargin-bottom: 40px;\r\n\t\tposition: relative;\r\n\t\tmin-height: 56px;\r\n\t}\r\n\r\n\t.search-input {\r\n\t\tflex: 1;\r\n\t\tmin-width: 0;\r\n\t\tborder: none;\r\n\t\toutline: none;\r\n\t\tfont-size: 16px;\r\n\t\tbackground: transparent;\r\n\t\tpadding: 8px 0 8px 0;\r\n\t\tmargin-right: 12px;\r\n\t\tbox-shadow: none;\r\n\t\tborder-radius: 0;\r\n\t}\r\n\r\n\t.search-btn {\r\n\t\twidth: 40px;\r\n\t\theight: 40px;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder: none;\r\n\t\tbackground: #e6edfa;\r\n\t\tcolor: #4A90E2;\r\n\t\tmargin-left: 8px;\r\n\t\ttransition: background 0.2s, color 0.2s;\r\n\t\tfont-size: 18px;\r\n\t\tcursor: pointer;\r\n\t\tbox-shadow: 0 2px 8px rgba(74, 144, 226, 0.08);\r\n\t}\r\n\r\n\t.search-btn:active, .search-btn:hover {\r\n\t\tbackground: #4A90E2;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.search-btn.voice-btn {\r\n\t\tbackground: #fff;\r\n\t\tborder: 2px solid #4A90E2;\r\n\t\tcolor: #4A90E2;\r\n\t}\r\n\r\n\t.search-btn.voice-btn.recording {\r\n\t\tbackground: #4A90E2;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.voice-wave-canvas {\r\n\t\tposition: absolute;\r\n\t\tright: 100px;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t\tz-index: 2;\r\n\t\tbackground: transparent;\r\n\t\tborder-radius: 8px;\r\n\t\tpointer-events: none;\r\n\t\theight: 40px;\r\n\t\twidth: 120px;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.section {\r\n\t\tmargin-bottom: 30px;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 15px;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.tags-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 10px;\r\n\t}\r\n\r\n\t.tag {\r\n\t\tpadding: 8px 16px;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder: 1px solid #e9ecef;\r\n\t\tborder-radius: 20px;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #495057;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t\twhite-space: nowrap;\r\n\r\n\t\t&:hover {\r\n\t\t\tbackground: #4A90E2;\r\n\t\t\tcolor: white;\r\n\t\t\tborder-color: #4A90E2;\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t}\r\n\t}\r\n\r\n\t.history-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 8px;\r\n\t}\r\n\r\n\t.history-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 8px 16px;\r\n\t\tbackground: #f8f9fa;\r\n\t\tborder: 1px solid #e9ecef;\r\n\t\tborder-radius: 20px;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #495057;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&:hover {\r\n\t\t\tbackground: #e3f2fd;\r\n\t\t\tborder-color: #4A90E2;\r\n\t\t}\r\n\r\n\t\t&::before {\r\n\t\t\tcontent: \"🕐\";\r\n\t\t\tmargin-right: 8px;\r\n\t\t}\r\n\t}\r\n\r\n\t.loading {\r\n\t\ttext-align: center;\r\n\t\tpadding: 25px;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tborder-radius: 15px;\r\n\t\tmargin: 20px 0;\r\n\t\tcolor: white;\r\n\t\tbox-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\tborder: 1px solid rgba(255, 255, 255, 0.2);\r\n\t}\r\n\r\n\t.loading-content {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tflex-direction: column;\r\n\t\tgap: 15px;\r\n\t}\r\n\r\n\t.spinner {\r\n\t\twidth: 40px;\r\n\t\theight: 40px;\r\n\t\tborder: 4px solid rgba(255, 255, 255, 0.3);\r\n\t\tborder-top: 4px solid white;\r\n\t\tborder-radius: 50%;\r\n\t\tanimation: spin 1s linear infinite;\r\n\t}\r\n\r\n\t.loading-text {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 500;\r\n\t\tletter-spacing: 0.5px;\r\n\t}\r\n\r\n\t.loading-sub-text {\r\n\t\tfont-size: 12px;\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t.loading-dots {\r\n\t\tdisplay: inline-block;\r\n\t\tanimation: dots 1.5s infinite;\r\n\t}\r\n\r\n\t.result-container {\r\n\t\tmargin: 20px 0;\r\n\t\tpadding: 25px;\r\n\t\tbackground: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n\t\tborder-radius: 15px;\r\n\t\tborder-left: 4px solid #4A90E2;\r\n\t\tbox-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\r\n\t\tborder: 1px solid #e9ecef;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\r\n\t\t&::before {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\theight: 3px;\r\n\t\t\tbackground: linear-gradient(90deg, #4A90E2, #667eea, #764ba2);\r\n\t\t}\r\n\t}\r\n\r\n\t.result-title {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 18px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.result-content {\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t@keyframes spin {\r\n\t\t0% {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes dots {\r\n\r\n\t\t0%,\r\n\t\t20% {\r\n\t\t\tcontent: '';\r\n\t\t}\r\n\r\n\t\t40% {\r\n\t\t\tcontent: '.';\r\n\t\t}\r\n\r\n\t\t60% {\r\n\t\t\tcontent: '..';\r\n\t\t}\r\n\r\n\t\t80%,\r\n\t\t100% {\r\n\t\t\tcontent: '...';\r\n\t\t}\r\n\t}\r\n\r\n\t/* 响应式设计 */\r\n\t@media (max-width: 768px) {\r\n\t\t.connection-status {\r\n\t\t\ttop: 44px;\r\n\t\t\tpadding: 6px 10px;\r\n\t\t\tfont-size: 11px;\r\n\t\t}\r\n\t\t.container {\r\n\t\t\tpadding: 20px;\r\n\t\t\tmargin: 10px;\r\n\t\t}\r\n\t\t\r\n\t\t.title {\r\n\t\t\tfont-size: 24px;\r\n\t\t}\r\n\t\t\r\n\t\t.tags-container {\r\n\t\t\tgap: 8px;\r\n\t\t}\r\n\t\t\r\n\t\t.tag {\r\n\t\t\tfont-size: 12px;\r\n\t\t\tpadding: 6px 12px;\r\n\t\t}\r\n\t}\r\n\r\n\t.search-btn.voice-btn.recording {\r\n\t\tanimation: voice-breath 1.2s infinite alternate;\r\n\t\tbox-shadow: 0 0 0 8px rgba(74,144,226,0.12);\r\n\t}\r\n\r\n\t@keyframes voice-breath {\r\n\t\t0% { transform: scale(1); box-shadow: 0 0 0 8px rgba(74,144,226,0.12); }\r\n\t\t100% { transform: scale(1.12); box-shadow: 0 0 0 16px rgba(74,144,226,0.18); }\r\n\t}\r\n</style>", "// extracted by mini-css-extract-plugin", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-12[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-12[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-12[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-12[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\""], "names": ["data", "mcpApiBase", "mcpConnected", "connectionStatus", "searchQuery", "isLoading", "showResult", "query<PERSON><PERSON>ult", "loadingMessage", "loadingSubMessage", "loadingMessages", "main", "sub", "loadingInterval", "isRecording", "aliyunAppKey", "aliyunToken", "aliyunWs", "aliyunRecorder", "aliyunAudioContext", "aliyunStream", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aliyunWaveAnimationId", "silenceStartTime", "silenceT<PERSON><PERSON>old", "silenceTimeout", "hotTags", "searchHistory", "isH5", "_waveAnimId", "computed", "connectionStatusClass", "concat", "connectionStatusText", "statusMap", "formattedResult", "formatted", "replace", "methods", "connectToMCP", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$uni$request", "_yield$uni$request2", "err", "response", "_t", "wrap", "_context", "prev", "next", "updateConnectionStatus", "console", "log", "uni", "request", "url", "method", "header", "sent", "_slicedToArray", "success", "Error", "message", "error", "setTimeout", "showModal", "title", "content", "showCancel", "stop", "status", "reconnectMCP", "_this2", "_callee2", "_context2", "performSearch", "_this3", "_callee3", "query", "_context3", "trim", "showToast", "icon", "abrupt", "execute<PERSON>uery", "searchTag", "_this4", "_callee4", "_context4", "_this5", "_callee5", "_yield$uni$request3", "_yield$uni$request4", "errorMsg", "errorMessage", "_t2", "_context5", "showLoadingWithMessages", "encodeURIComponent", "text", "addToHistory", "includes", "hideLoading", "finish", "_this6", "currentIndex", "updateLoadingMessage", "setInterval", "length", "clearInterval", "filter", "item", "unshift", "slice", "setStorageSync", "JSON", "stringify", "loadSearchHistory", "history", "getStorageSync", "parse", "onKeyPress", "e", "keyCode", "toggleVoiceRecognition", "stopVoiceRecognition", "startVoiceRecognition", "_this7", "systemInfo", "getSystemInfoSync", "platform", "plugin", "requirePlugin", "getRecordRecognitionManager", "start", "lang", "duration", "onRecognize", "res", "result", "onStop", "onError", "_startWaveAnim", "_this8", "canvas", "document", "getElementById", "ctx", "getContext", "W", "width", "H", "height", "t", "draw", "clearRect", "save", "strokeStyle", "lineWidth", "beginPath", "x", "y", "Math", "sin", "moveTo", "lineTo", "stroke", "restore", "requestAnimationFrame", "mounted", "window", "SpeechRecognition", "onLoad", "options", "onUnload", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "Page", "createPage"], "sourceRoot": ""}