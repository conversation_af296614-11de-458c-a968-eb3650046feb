{"version": 3, "file": "common/main.js", "mappings": ";;;;;;;;;;AACA;EACAA,QAAA,WAAAA,SAAA;IACAC,OAAA,CAAAC,GAAA;EACA;EACAC,MAAA,WAAAA,OAAA;IACAF,OAAA,CAAAC,GAAA;EACA;EACAE,MAAA,WAAAA,OAAA;IACAH,OAAA,CAAAC,GAAA;EACA;AACA,G;;;;;;;;;;ACXA,uC;;;;;;;;;;;;;;;ACAA;AACA;AACA,CAAuD;AACL;AAClD,CAA+D;;;AAG/D;AAC6H;AAC7H,gBAAgB,4IAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,+DAAe,iB;;;;;;;;;;;;;ACvBkd,CAAC,+DAAe,udAAG,EAAC,C;;;;;;;;;;;;;;;;;ACA+L,CAAC,+DAAe,6pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACArrB;AACnB;AACAG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAAsB;AACzD;AACS;AAEhCC,2CAAG,CAACE,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhCF,4CAAG,CAACG,MAAM,GAAG,KAAK;AAElB,IAAMC,GAAG,GAAG,IAAIL,2CAAG,CAAAM,aAAA,KACdL,4CAAG,CACP,CAAC;AACFM,SAAA,CAAAF,GAAG,EAACG,MAAM,CAAC,CAAC,C", "sources": ["uni-app:///src/App.vue", "webpack:///./src/App.vue?92c4", null, "webpack:///./src/App.vue?0d0f", "webpack:///./src/App.vue?732d", "uni-app:///src/main.js"], "sourcesContent": ["<script>\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n</style>\n", "// extracted by mini-css-extract-plugin", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-12[0].rules[0].use[0]!../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[1]!../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-12[0].rules[0].use[2]!../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[3]!../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-12[0].rules[0].use[0]!../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[1]!../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-12[0].rules[0].use[2]!../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[3]!../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue'\r\nimport App from './App'\r\nimport './uni.promisify.adaptor'\r\n\r\nVue.config.productionTip = false\r\n\r\nApp.mpType = 'app'\r\n\r\nconst app = new Vue({\r\n  ...App\r\n})\r\napp.$mount()"], "names": ["onLaunch", "console", "log", "onShow", "onHide", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "App", "config", "productionTip", "mpType", "app", "_objectSpread", "createApp", "$mount"], "sourceRoot": ""}