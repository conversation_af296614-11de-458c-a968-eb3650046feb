/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-12[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/index/index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/

.ai-query-container.data-v-57280228 {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 20px;
}
.connection-status.data-v-57280228 {
	position: fixed;
	top: 60px;
	right: 20px;
	padding: 8px 16px;
	border-radius: 20px;
	font-size: 12px;
	font-weight: 500;
	z-index: 1000;
	transition: all 0.3s ease;
&.connected {
		background: #d4edda;
		color: #155724;
		border: 1px solid #c3e6cb;
}
&.disconnected {
		background: #f8d7da;
		color: #721c24;
		border: 1px solid #f5c6cb;
}
&.connecting {
		background: #fff3cd;
		color: #856404;
		border: 1px solid #ffeaa7;
}
}
.container.data-v-57280228 {
	max-width: 800px;
	margin: 0 auto;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20px;
	padding: 40px;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
	-webkit-backdrop-filter: blur(10px);
	        backdrop-filter: blur(10px);
}
.title.data-v-57280228 {
	display: block;
	text-align: center;
	font-size: 32px;
	font-weight: 600;
	color: #4A90E2;
	margin-bottom: 40px;
	letter-spacing: 2px;
}
.search-container.data-v-57280228 {
	display: flex;
	align-items: center;
	background: #fff;
	border-radius: 50px;
	padding: 8px 16px;
	box-shadow: 0 4px 16px rgba(74, 144, 226, 0.08);
	margin-bottom: 40px;
	position: relative;
	min-height: 56px;
}
.search-input.data-v-57280228 {
	flex: 1;
	min-width: 0;
	border: none;
	outline: none;
	font-size: 16px;
	background: transparent;
	padding: 8px 0 8px 0;
	margin-right: 12px;
	box-shadow: none;
	border-radius: 0;
}
.search-btn.data-v-57280228 {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	border: none;
	background: #e6edfa;
	color: #4A90E2;
	margin-left: 8px;
	transition: background 0.2s, color 0.2s;
	font-size: 18px;
	cursor: pointer;
	box-shadow: 0 2px 8px rgba(74, 144, 226, 0.08);
}
.search-btn.data-v-57280228:active, .search-btn.data-v-57280228:hover {
	background: #4A90E2;
	color: #fff;
}
.search-btn.voice-btn.data-v-57280228 {
	background: #fff;
	border: 2px solid #4A90E2;
	color: #4A90E2;
}
.search-btn.voice-btn.recording.data-v-57280228 {
	background: #4A90E2;
	color: #fff;
}
.voice-wave-canvas.data-v-57280228 {
	position: absolute;
	right: 100px;
	top: 50%;
	-webkit-transform: translateY(-50%);
	        transform: translateY(-50%);
	z-index: 2;
	background: transparent;
	border-radius: 8px;
	pointer-events: none;
	height: 40px;
	width: 120px;
	display: block;
}
.section.data-v-57280228 {
	margin-bottom: 30px;
}
.section-title.data-v-57280228 {
	display: block;
	font-size: 16px;
	color: #666;
	margin-bottom: 15px;
	font-weight: 500;
}
.tags-container.data-v-57280228 {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}
.tag.data-v-57280228 {
	padding: 8px 16px;
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 20px;
	font-size: 14px;
	color: #495057;
	cursor: pointer;
	transition: all 0.3s ease;
	white-space: nowrap;
&:hover {
		background: #4A90E2;
		color: white;
		border-color: #4A90E2;
		-webkit-transform: translateY(-2px);
		        transform: translateY(-2px);
}
}
.history-container.data-v-57280228 {
	display: flex;
	flex-direction: column;
	gap: 8px;
}
.history-item.data-v-57280228 {
	display: flex;
	align-items: center;
	padding: 8px 16px;
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 20px;
	font-size: 14px;
	color: #495057;
	cursor: pointer;
	transition: all 0.3s ease;
&:hover {
		background: #e3f2fd;
		border-color: #4A90E2;
}
&::before {
		content: "🕐";
		margin-right: 8px;
}
}
.loading.data-v-57280228 {
	text-align: center;
	padding: 25px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 15px;
	margin: 20px 0;
	color: white;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
	-webkit-backdrop-filter: blur(10px);
	        backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}
.loading-content.data-v-57280228 {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	gap: 15px;
}
.spinner.data-v-57280228 {
	width: 40px;
	height: 40px;
	border: 4px solid rgba(255, 255, 255, 0.3);
	border-top: 4px solid white;
	border-radius: 50%;
	-webkit-animation: spin-data-v-57280228 1s linear infinite;
	        animation: spin-data-v-57280228 1s linear infinite;
}
.loading-text.data-v-57280228 {
	font-size: 16px;
	font-weight: 500;
	letter-spacing: 0.5px;
}
.loading-sub-text.data-v-57280228 {
	font-size: 12px;
	opacity: 0.8;
}
.loading-dots.data-v-57280228 {
	display: inline-block;
	-webkit-animation: dots-data-v-57280228 1.5s infinite;
	        animation: dots-data-v-57280228 1.5s infinite;
}
.result-container.data-v-57280228 {
	margin: 20px 0;
	padding: 25px;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 15px;
	border-left: 4px solid #4A90E2;
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
	border: 1px solid #e9ecef;
	position: relative;
	overflow: hidden;
&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 3px;
		background: linear-gradient(90deg, #4A90E2, #667eea, #764ba2);
}
}
.result-title.data-v-57280228 {
	display: block;
	font-size: 18px;
	font-weight: 600;
	color: #333;
	margin-bottom: 15px;
}
.result-content.data-v-57280228 {
	font-size: 14px;
	line-height: 1.6;
	color: #666;
}
@-webkit-keyframes spin-data-v-57280228 {
0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
}
100% {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
}
}
@keyframes spin-data-v-57280228 {
0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
}
100% {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
}
}
@-webkit-keyframes dots-data-v-57280228 {
0%,
	20% {
		content: '';
}
40% {
		content: '.';
}
60% {
		content: '..';
}
80%,
	100% {
		content: '...';
}
}
@keyframes dots-data-v-57280228 {
0%,
	20% {
		content: '';
}
40% {
		content: '.';
}
60% {
		content: '..';
}
80%,
	100% {
		content: '...';
}
}

/* 响应式设计 */
@media (max-width: 768px) {
.connection-status.data-v-57280228 {
		top: 44px;
		padding: 6px 10px;
		font-size: 11px;
}
.container.data-v-57280228 {
		padding: 20px;
		margin: 10px;
}
.title.data-v-57280228 {
		font-size: 24px;
}
.tags-container.data-v-57280228 {
		gap: 8px;
}
.tag.data-v-57280228 {
		font-size: 12px;
		padding: 6px 12px;
}
}
.search-btn.voice-btn.recording.data-v-57280228 {
	-webkit-animation: voice-breath-data-v-57280228 1.2s infinite alternate;
	        animation: voice-breath-data-v-57280228 1.2s infinite alternate;
	box-shadow: 0 0 0 8px rgba(74,144,226,0.12);
}
@-webkit-keyframes voice-breath-data-v-57280228 {
0% { -webkit-transform: scale(1); transform: scale(1); box-shadow: 0 0 0 8px rgba(74,144,226,0.12);
}
100% { -webkit-transform: scale(1.12); transform: scale(1.12); box-shadow: 0 0 0 16px rgba(74,144,226,0.18);
}
}
@keyframes voice-breath-data-v-57280228 {
0% { -webkit-transform: scale(1); transform: scale(1); box-shadow: 0 0 0 8px rgba(74,144,226,0.12);
}
100% { -webkit-transform: scale(1.12); transform: scale(1.12); box-shadow: 0 0 0 16px rgba(74,144,226,0.18);
}
}

