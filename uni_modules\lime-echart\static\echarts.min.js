!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).echarts={})}(this,function(t){"use strict";var _=function(t,e){return(_=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)};function u(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}_(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var x=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},b=new function(){this.browser=new x,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(b.wxa=!0,b.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?b.worker=!0:"undefined"==typeof navigator||0===navigator.userAgent.indexOf("Node.js")?(b.node=!0,b.svgSupported=!0):(J=navigator.userAgent,oe=(Wt=b).browser,rt=J.match(/Firefox\/([\d.]+)/),G=J.match(/MSIE\s([\d.]+)/)||J.match(/Trident\/.+?rv:(([\d.]+))/),Q=J.match(/Edge?\/([\d.]+)/),J=/micromessenger/i.test(J),rt&&(oe.firefox=!0,oe.version=rt[1]),G&&(oe.ie=!0,oe.version=G[1]),Q&&(oe.edge=!0,oe.version=Q[1],oe.newEdge=18<+Q[1].split(".")[0]),J&&(oe.weChat=!0),Wt.svgSupported="undefined"!=typeof SVGRect,Wt.touchEventsSupported="ontouchstart"in window&&!oe.ie&&!oe.edge,Wt.pointerEventsSupported="onpointerdown"in window&&(oe.edge||oe.ie&&11<=+oe.version),Wt.domSupported="undefined"!=typeof document,rt=document.documentElement.style,Wt.transform3dSupported=(oe.ie&&"transition"in rt||oe.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in rt)&&!("OTransition"in rt),Wt.transformSupported=Wt.transform3dSupported||oe.ie&&9<=+oe.version);var K="12px sans-serif";var w,S,T=function(t){var e={};if("undefined"!=typeof JSON)for(var n=0;n<t.length;n++){var i=String.fromCharCode(n+32),r=(t.charCodeAt(n)-20)/100;e[i]=r}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),H={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(w||(n=H.createCanvas(),w=n&&n.getContext("2d")),w)return S!==e&&(S=w.font=e||K),w.measureText(t);t=t||"",e=e||K;var n=/((?:\d+)?\.?\d*)px/.exec(e),i=n&&+n[1]||12,r=0;if(0<=e.indexOf("mono"))r=i*t.length;else for(var o=0;o<t.length;o++){var a=T[t[o]];r+=null==a?i:a*i}return{width:r}},loadImage:function(t,e,n){var i=new Image;return i.onload=e,i.onerror=n,i.src=t,i}};function C(t){for(var e in H)t[e]&&(H[e]=t[e])}var A=lt(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(t,e){return t["[object "+e+"]"]=!0,t},{}),P=lt(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(t,e){return t["[object "+e+"Array]"]=!0,t},{}),W=Object.prototype.toString,G=Array.prototype,U=G.forEach,q=G.filter,Z=G.slice,$=G.map,Q=function(){}.constructor,J=Q?Q.prototype:null,tt="__proto__",et=2311;function nt(){return et++}function it(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function y(t){if(null==t||"object"!=typeof t)return t;var e=t,n=W.call(t);if("[object Array]"===n){if(!At(t))for(var e=[],i=0,r=t.length;i<r;i++)e[i]=y(t[i])}else if(P[n]){if(!At(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=t[i]}}}else if(!A[n]&&!At(t)&&!mt(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==tt&&(e[a]=y(t[a]));return e}function d(t,e,n){if(!L(e)||!L(t))return n?y(e):t;for(var i in e){var r,o;e.hasOwnProperty(i)&&i!==tt&&(r=t[i],!L(o=e[i])||!L(r)||F(o)||F(r)||mt(o)||mt(r)||gt(o)||gt(r)||At(o)||At(r)?!n&&i in t||(t[i]=y(e[i])):d(r,o,n))}return t}function O(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==tt&&(t[n]=e[n]);return t}function N(t,e,n){for(var i=ht(e),r=0;r<i.length;r++){var o=i[r];(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}return t}var rt=H.createCanvas;function I(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function ot(t,e){var n,i=t.prototype;function r(){}for(n in r.prototype=e.prototype,t.prototype=new r,i)i.hasOwnProperty(n)&&(t.prototype[n]=i[n]);(t.prototype.constructor=t).superClass=e}function at(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else N(t,e,n)}function st(t){return!!t&&"string"!=typeof t&&"number"==typeof t.length}function R(t,e,n){if(t&&e)if(t.forEach&&t.forEach===U)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function z(t,e,n){if(!t)return[];if(!e)return Mt(t);if(t.map&&t.map===$)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function lt(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function ut(t,e,n){if(!t)return[];if(!e)return Mt(t);if(t.filter&&t.filter===q)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function ht(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e);return n}var ct=J&&k(J.bind)?J.call.bind(J.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(Z.call(arguments)))}};function pt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(Z.call(arguments)))}}function F(t){return Array.isArray?Array.isArray(t):"[object Array]"===W.call(t)}function k(t){return"function"==typeof t}function V(t){return"string"==typeof t}function dt(t){return"[object String]"===W.call(t)}function ft(t){return"number"==typeof t}function L(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function gt(t){return!!A[W.call(t)]}function yt(t){return!!P[W.call(t)]}function mt(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function vt(t){return null!=t.colorStops}function _t(t){return null!=t.image}function xt(t){return"[object RegExp]"===W.call(t)}function wt(t){return t!=t}function bt(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]}function E(t,e){return null!=t?t:e}function St(t,e,n){return null!=t?t:null!=e?e:n}function Mt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return Z.apply(t,e)}function Tt(t){var e;return"number"==typeof t?[t,t,t,t]:2===(e=t.length)?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function Ct(t,e){if(!t)throw new Error(e)}function It(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var kt="__ec_primitive__";function Dt(t){t[kt]=!0}function At(t){return t[kt]}Lt.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},Lt.prototype.has=function(t){return this.data.hasOwnProperty(t)},Lt.prototype.get=function(t){return this.data[t]},Lt.prototype.set=function(t,e){return this.data[t]=e,this},Lt.prototype.keys=function(){return ht(this.data)},Lt.prototype.forEach=function(t){var e,n=this.data;for(e in n)n.hasOwnProperty(e)&&t(n[e],e)};var Pt=Lt;function Lt(){this.data={}}var Ot="function"==typeof Map;Et.prototype.hasKey=function(t){return this.data.has(t)},Et.prototype.get=function(t){return this.data.get(t)},Et.prototype.set=function(t,e){return this.data.set(t,e),e},Et.prototype.each=function(n,i){this.data.forEach(function(t,e){n.call(i,t,e)})},Et.prototype.keys=function(){var t=this.data.keys();return Ot?Array.from(t):t},Et.prototype.removeKey=function(t){this.data.delete(t)};var Rt=Et;function Et(t){var n=F(t),i=(this.data=new(Ot?Map:Pt),this);function e(t,e){n?i.set(t,e):i.set(e,t)}t instanceof Et?t.each(e):t&&R(t,e)}function B(t){return new Rt(t)}function Bt(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];for(var r=t.length,i=0;i<e.length;i++)n[i+r]=e[i];return n}function Nt(t,e){var n,t=Object.create?Object.create(t):((n=function(){}).prototype=t,new n);return e&&O(t,e),t}function zt(t){t=t.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function Ft(t,e){return t.hasOwnProperty(e)}function Vt(){}var Ht=180/Math.PI,Wt=Object.freeze({__proto__:null,HashMap:Rt,RADIAN_TO_DEGREE:Ht,assert:Ct,bind:ct,clone:y,concatArray:Bt,createCanvas:rt,createHashMap:B,createObject:Nt,curry:pt,defaults:N,disableUserSelect:zt,each:R,eqNaN:wt,extend:O,filter:ut,find:function(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]},guid:nt,hasOwn:Ft,indexOf:I,inherits:ot,isArray:F,isArrayLike:st,isBuiltInObject:gt,isDom:mt,isFunction:k,isGradientObject:vt,isImagePatternObject:_t,isNumber:ft,isObject:L,isPrimitive:At,isRegExp:xt,isString:V,isStringSafe:dt,isTypedArray:yt,keys:ht,logError:it,map:z,merge:d,mergeAll:function(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=d(n,t[i],e);return n},mixin:at,noop:Vt,normalizeCssArray:Tt,reduce:lt,retrieve:bt,retrieve2:E,retrieve3:St,setAsPrimitive:Dt,slice:Mt,trim:It});function Gt(t,e){return[t=null==t?0:t,e=null==e?0:e]}function Ut(t){return[t[0],t[1]]}function Xt(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Yt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function qt(t){return Math.sqrt(Zt(t))}function Zt(t){return t[0]*t[0]+t[1]*t[1]}function jt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function Kt(t,e){var n=qt(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function $t(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Qt=$t;function Jt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var te=Jt;function ee(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function ne(t,e,n){var i=e[0],e=e[1];return t[0]=n[0]*i+n[2]*e+n[4],t[1]=n[1]*i+n[3]*e+n[5],t}function ie(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function re(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var oe=Object.freeze({__proto__:null,add:Xt,applyTransform:ne,clone:Ut,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},create:Gt,dist:Qt,distSquare:te,distance:$t,distanceSquare:Jt,div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},len:qt,lenSquare:Zt,length:qt,lengthSquare:Zt,lerp:ee,max:re,min:ie,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:Kt,scale:jt,scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},set:function(t,e,n){return t[0]=e,t[1]=n,t},sub:Yt}),ae=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},se=(le.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new ae(e,t),"dragstart",t.event))},le.prototype._drag=function(t){var e,n,i,r,o=this._draggingTarget;o&&(e=t.offsetX,n=t.offsetY,i=e-this._x,r=n-this._y,this._x=e,this._y=n,o.drift(i,r,t),this.handler.dispatchToElement(new ae(o,t),"drag",t.event),i=this.handler.findHover(e,n,o).target,r=this._dropTarget,o!==(this._dropTarget=i))&&(r&&i!==r&&this.handler.dispatchToElement(new ae(r,t),"dragleave",t.event),i)&&i!==r&&this.handler.dispatchToElement(new ae(i,t),"dragenter",t.event)},le.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new ae(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new ae(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},le);function le(t){(this.handler=t).on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}he.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),n&&t){var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;o={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},e=r[t].length-1,i=r[t][e];i&&i.callAtLast?r[t].splice(e,0,o):r[t].push(o)}return this},he.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},he.prototype.off=function(t,e){var n=this._$handlers;if(n)if(t)if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];else this._$handlers={};return this},he.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}r&&r.afterTrigger&&r.afterTrigger(t)}return this},he.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}r&&r.afterTrigger&&r.afterTrigger(t)}return this};var ue=he;function he(t){t&&(this._$eventProcessor=t)}var ce=Math.log(2);function pe(t,e,n,i,r,o){var a,s=i+"-"+r,l=t.length;if(o.hasOwnProperty(s))return o[s];if(1===e)return a=Math.round(Math.log((1<<l)-1&~r)/ce),t[n][a];for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,d=0;p<l;p++){var f=1<<p;f&r||(c+=(d%2?-1:1)*t[n][p]*pe(t,e-1,h,u,r|f,o),d++)}return o[s]=c}function de(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=pe(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*pe(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var fe="___zrEVENTSAVED",ge=[];function ye(t,e,n,i,r){if(e.getBoundingClientRect&&b.domSupported&&!me(e)){var o=e[fe]||(e[fe]={}),e=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left,h=h.top;a.push(p,h),l=l&&o&&p===o[c]&&h===o[1+c],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?de(s,a):de(a,s))}(function(t,e){var n=e.markers;if(!n){n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,l=o%2,u=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",r[u]+":0",i[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}}return n}(e,o),o,r);if(e)return e(t,n,i),!0}return!1}function me(t){return"CANVAS"===t.nodeName.toUpperCase()}var ve=/([&<>"'])/g,_e={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function xe(t){return null==t?"":(t+"").replace(ve,function(t,e){return _e[e]})}var we=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,be=[],Se=b.browser.firefox&&+b.browser.version.split(".")[0]<39;function Me(t,e,n,i){return n=n||{},i?Te(t,e,n):Se&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):Te(t,e,n),n}function Te(t,e,n){if(b.domSupported&&t.getBoundingClientRect){var i,r=e.clientX,e=e.clientY;if(me(t))return i=t.getBoundingClientRect(),n.zrX=r-i.left,void(n.zrY=e-i.top);if(ye(be,t,r,e))return n.zrX=be[0],void(n.zrY=be[1])}n.zrX=n.zrY=0}function Ce(t){return t||window.event}function Ie(t,e,n){var i;return null==(e=Ce(e)).zrX&&((i=e.type)&&0<=i.indexOf("touch")?(i=("touchend"!==i?e.targetTouches:e.changedTouches)[0])&&Me(t,i,e,n):(Me(t,e,e,n),i=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,t=t.deltaY;return null!=n&&null!=t?3*(0!==t?Math.abs(t):Math.abs(n))*(0<t||!(t<0)&&0<n?-1:1):e}(e),e.zrDelta=i?i/120:-(e.detail||0)/3),t=e.button,null==e.which&&void 0!==t&&we.test(e.type))&&(e.which=1&t?1:2&t?3:4&t?2:0),e}var ke=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0},De=(Ae.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},Ae.prototype.clear=function(){return this._track.length=0,this},Ae.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=Me(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},Ae.prototype._recognize=function(t){for(var e in Le)if(Le.hasOwnProperty(e)){e=Le[e](this._track,t);if(e)return e}},Ae);function Ae(){this._track=[]}function Pe(t){var e=t[1][0]-t[0][0],t=t[1][1]-t[0][1];return Math.sqrt(e*e+t*t)}var Le={pinch:function(t,e){var n,i=t.length;if(i)return n=(t[i-1]||{}).points,(i=(t[i-2]||{}).points||n)&&1<i.length&&n&&1<n.length?(i=Pe(n)/Pe(i),isFinite(i)||(i=1),e.pinchScale=i,i=[(n[0][0]+n[1][0])/2,(n[0][1]+n[1][1])/2],e.pinchX=i[0],e.pinchY=i[1],{type:"pinch",target:t[0].target,event:e}):void 0}};function Oe(){return[1,0,0,1,0,0]}function Re(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Ee(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Be(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],n=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=n,t}function Ne(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function ze(t,e,n,i){void 0===i&&(i=[0,0]);var r=e[0],o=e[2],a=e[4],s=e[1],l=e[3],e=e[5],u=Math.sin(n),n=Math.cos(n);return t[0]=r*n+s*u,t[1]=-r*u+s*n,t[2]=o*n+l*u,t[3]=-o*u+n*l,t[4]=n*(a-i[0])+u*(e-i[1])+i[0],t[5]=n*(e-i[1])-u*(a-i[0])+i[1],t}function Fe(t,e,n){var i=n[0],n=n[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function Ve(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],e=e[5],s=n*a-o*i;return s?(t[0]=a*(s=1/s),t[1]=-o*s,t[2]=-i*s,t[3]=n*s,t[4]=(i*e-a*r)*s,t[5]=(o*r-n*e)*s,t):null}var He=Object.freeze({__proto__:null,clone:function(t){var e=Oe();return Ee(e,t),e},copy:Ee,create:Oe,identity:Re,invert:Ve,mul:Be,rotate:ze,scale:Fe,translate:Ne}),M=(e.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},e.prototype.clone=function(){return new e(this.x,this.y)},e.prototype.set=function(t,e){return this.x=t,this.y=e,this},e.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},e.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},e.prototype.scale=function(t){this.x*=t,this.y*=t},e.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},e.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},e.prototype.dot=function(t){return this.x*t.x+this.y*t.y},e.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},e.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},e.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},e.prototype.distance=function(t){var e=this.x-t.x,t=this.y-t.y;return Math.sqrt(e*e+t*t)},e.prototype.distanceSquare=function(t){var e=this.x-t.x,t=this.y-t.y;return e*e+t*t},e.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},e.prototype.transform=function(t){var e,n;if(t)return e=this.x,n=this.y,this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this},e.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},e.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},e.set=function(t,e,n){t.x=e,t.y=n},e.copy=function(t,e){t.x=e.x,t.y=e.y},e.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},e.lenSquare=function(t){return t.x*t.x+t.y*t.y},e.dot=function(t,e){return t.x*e.x+t.y*e.y},e.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},e.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},e.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},e.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},e.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},e);function e(t,e){this.x=t||0,this.y=e||0}var We=Math.min,Ge=Math.max,Ue=new M,Xe=new M,Ye=new M,qe=new M,Ze=new M,je=new M,X=(Ke.prototype.union=function(t){var e=We(t.x,this.x),n=We(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Ge(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Ge(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},Ke.prototype.applyTransform=function(t){Ke.applyTransform(this,this,t)},Ke.prototype.calculateTransform=function(t){var e=t.width/this.width,n=t.height/this.height,i=Oe();return Ne(i,i,[-this.x,-this.y]),Fe(i,i,[e,n]),Ne(i,i,[t.x,t.y]),i},Ke.prototype.intersect=function(t,e){if(!t)return!1;t instanceof Ke||(t=Ke.create(t));var n,i,r,o,a,s,l,u,h=this,c=h.x,p=h.x+h.width,d=h.y,h=h.y+h.height,f=t.x,g=t.x+t.width,y=t.y,t=t.y+t.height,m=!(p<f||g<c||h<y||t<d);return e&&(n=1/0,i=0,r=Math.abs(p-f),o=Math.abs(g-c),a=Math.abs(h-y),s=Math.abs(t-d),l=Math.min(r,o),u=Math.min(a,s),p<f||g<c?i<l&&(i=l,r<o?M.set(je,-r,0):M.set(je,o,0)):l<n&&(n=l,r<o?M.set(Ze,r,0):M.set(Ze,-o,0)),h<y||t<d?i<u&&(i=u,a<s?M.set(je,0,-a):M.set(je,0,s)):l<n&&(n=l,a<s?M.set(Ze,0,a):M.set(Ze,0,-s))),e&&M.copy(e,m?Ze:je),m},Ke.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},Ke.prototype.clone=function(){return new Ke(this.x,this.y,this.width,this.height)},Ke.prototype.copy=function(t){Ke.copy(this,t)},Ke.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},Ke.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},Ke.prototype.isZero=function(){return 0===this.width||0===this.height},Ke.create=function(t){return new Ke(t.x,t.y,t.width,t.height)},Ke.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},Ke.applyTransform=function(t,e,n){var i,r,o,a;n?n[1]<1e-5&&-1e-5<n[1]&&n[2]<1e-5&&-1e-5<n[2]?(i=n[0],r=n[3],o=n[4],a=n[5],t.x=e.x*i+o,t.y=e.y*r+a,t.width=e.width*i,t.height=e.height*r,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height)):(Ue.x=Ye.x=e.x,Ue.y=qe.y=e.y,Xe.x=qe.x=e.x+e.width,Xe.y=Ye.y=e.y+e.height,Ue.transform(n),qe.transform(n),Xe.transform(n),Ye.transform(n),t.x=We(Ue.x,Xe.x,Ye.x,qe.x),t.y=We(Ue.y,Xe.y,Ye.y,qe.y),o=Ge(Ue.x,Xe.x,Ye.x,qe.x),a=Ge(Ue.y,Xe.y,Ye.y,qe.y),t.width=o-t.x,t.height=a-t.y):t!==e&&Ke.copy(t,e)},Ke);function Ke(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}var $e="silent";function Qe(){ke(this.event)}u(en,Je=ue),en.prototype.dispose=function(){},en.prototype.setCursor=function(){};var Je,tn=en;function en(){var t=null!==Je&&Je.apply(this,arguments)||this;return t.handler=null,t}var nn,rn=function(t,e){this.x=t,this.y=e},on=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],an=new X(0,0,0,0),sn=(u(ln,nn=ue),ln.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(R(on,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},ln.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=hn(this,e,n),r=this._hovered,o=r.target,i=(o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target),this._hovered=i?new rn(e,n):this.findHover(e,n)),e=i.target,n=this.proxy;n.setCursor&&n.setCursor(e?e.cursor:"default"),o&&e!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(i,"mousemove",t),e&&e!==o&&this.dispatchToElement(i,"mouseover",t)},ln.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},ln.prototype.resize=function(){this._hovered=new rn(0,0)},ln.prototype.dispatch=function(t,e){t=this[t];t&&t.call(this,e)},ln.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},ln.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},ln.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o={type:e,event:n,target:(t=t).target,topTarget:t.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:Qe};i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget||i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)}))}},ln.prototype.findHover=function(t,e,n){var i=this.storage.getDisplayList(),r=new rn(t,e);if(un(i,r,t,e,n),this._pointerSize&&!r.target){for(var o=[],a=this._pointerSize,s=a/2,l=new X(t-s,e-s,a,a),u=i.length-1;0<=u;u--){var h=i[u];h===n||h.ignore||h.ignoreCoarsePointer||h.parent&&h.parent.ignoreCoarsePointer||(an.copy(h.getBoundingRect()),h.transform&&an.applyTransform(h.transform),an.intersect(l)&&o.push(h))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,d=0;d<s;d+=4)for(var f=0;f<p;f+=c)if(un(o,r,t+d*Math.cos(f),e+d*Math.sin(f),n),r.target)return r}return r},ln.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new De);var n=this._gestureMgr,i=("start"===e&&n.clear(),n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom));"end"===e&&n.clear(),i&&(e=i.type,t.gestureEvent=e,(n=new rn).target=i.target,this.dispatchToElement(n,e,i.event))},ln);function ln(t,e,n,i,r){var o=nn.call(this)||this;return o._hovered=new rn(0,0),o.storage=t,o.painter=e,o.painterRoot=i,o._pointerSize=r,n=n||new tn,o.proxy=null,o.setHandlerProxy(n),o._draggingMgr=new se(o),o}function un(t,e,n,i,r){for(var o=t.length-1;0<=o;o--){var a=t[o],s=void 0;if(a!==r&&!a.ignore&&(s=function(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(!(o=i.ignoreClip?!0:o)){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1}i.silent&&(r=!0);a=i.__hostTarget,i=a||i.parent}return!r||$e}return!1}(a,n,i))&&(e.topTarget||(e.topTarget=a),s!==$e)){e.target=a;break}}}function hn(t,e,n){t=t.painter;return e<0||e>t.getWidth()||n<0||n>t.getHeight()}R(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){sn.prototype[a]=function(t){var e,n,i=t.zrX,r=t.zrY,o=hn(this,i,r);if("mouseup"===a&&o||(n=(e=this.findHover(i,r)).target),"mousedown"===a)this._downEl=n,this._downPoint=[t.zrX,t.zrY],this._upEl=n;else if("mouseup"===a)this._upEl=n;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<Qt(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}});var cn=32,pn=7;function dn(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;var o=t,a=e,s=r;for(s--;a<s;){var l=o[a];o[a++]=o[s],o[s--]=l}}else for(;r<n&&0<=i(t[r],t[r-1]);)r++;return r-e}function fn(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=1+o;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<u;)t[s+u]=t[s+u-1],u--}t[s]=a}}function gn(t,e,n,i,r,o){var a=0,s=0,l=1;if(0<o(t,e[n+r])){for(s=i-r;l<s&&0<o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)(l=1+((a=l)<<1))<=0&&(l=s);i=a,a=r-(l=s<l?s:l),l=r-i}for(a++;a<l;){var u=a+(l-a>>>1);0<o(t,e[n+u])?a=u+1:l=u}return l}function yn(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)(l=1+((a=l)<<1))<=0&&(l=s);var u=a,a=r-(l=s<l?s:l),l=r-u}else{for(s=i-r;l<s&&0<=o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function mn(A,P){var L,O,R=pn,E=0,B=[];function e(t){var e=L[t],n=O[t],i=L[t+1],r=O[t+1],t=(O[t]=n+r,t===E-3&&(L[t+1]=L[t+2],O[t+1]=O[t+2]),E--,yn(A[i],A,e,n,0,P));if(e+=t,0!=(n-=t)&&0!==(r=gn(A[e+n-1],A,i,r,r-1,P)))if(n<=r){var o=e,a=n,t=i,s=r,l=0;for(l=0;l<a;l++)B[l]=A[o+l];var u=0,h=t,c=o;if(A[c++]=A[h++],0==--s)for(l=0;l<a;l++)A[c+l]=B[u+l];else if(1===a){for(l=0;l<s;l++)A[c+l]=A[h+l];A[c+s]=B[u]}else{for(var p,d,f,g=R;;){d=p=0,f=!1;do{if(P(A[h],B[u])<0){if(A[c++]=A[h++],d++,(p=0)==--s){f=!0;break}}else if(A[c++]=B[u++],p++,d=0,1==--a){f=!0;break}}while((p|d)<g);if(f)break;do{if(0!==(p=yn(A[h],B,u,a,0,P))){for(l=0;l<p;l++)A[c+l]=B[u+l];if(c+=p,u+=p,(a-=p)<=1){f=!0;break}}if(A[c++]=A[h++],0==--s){f=!0;break}if(0!==(d=gn(B[u],A,h,s,0,P))){for(l=0;l<d;l++)A[c+l]=A[h+l];if(c+=d,h+=d,0===(s-=d)){f=!0;break}}if(A[c++]=B[u++],1==--a){f=!0;break}}while(g--,pn<=p||pn<=d);if(f)break;g<0&&(g=0),g+=2}if((R=g)<1&&(R=1),1===a){for(l=0;l<s;l++)A[c+l]=A[h+l];A[c+s]=B[u]}else{if(0===a)throw new Error;for(l=0;l<a;l++)A[c+l]=B[u+l]}}}else{var y=e,m=n,v=i,_=r,x=0;for(x=0;x<_;x++)B[x]=A[v+x];var w=y+m-1,b=_-1,S=v+_-1,M=0,T=0;if(A[S--]=A[w--],0==--m)for(M=S-(_-1),x=0;x<_;x++)A[M+x]=B[x];else if(1===_){for(T=(S-=m)+1,M=(w-=m)+1,x=m-1;0<=x;x--)A[T+x]=A[M+x];A[S]=B[b]}else{for(var C=R;;){var I=0,k=0,D=!1;do{if(P(B[b],A[w])<0){if(A[S--]=A[w--],I++,(k=0)==--m){D=!0;break}}else if(A[S--]=B[b--],k++,I=0,1==--_){D=!0;break}}while((I|k)<C);if(D)break;do{if(0!==(I=m-yn(B[b],A,y,m,m-1,P))){for(m-=I,T=(S-=I)+1,M=(w-=I)+1,x=I-1;0<=x;x--)A[T+x]=A[M+x];if(0===m){D=!0;break}}if(A[S--]=B[b--],1==--_){D=!0;break}if(0!==(k=_-gn(A[w],B,0,_,_-1,P))){for(_-=k,T=(S-=k)+1,M=(b-=k)+1,x=0;x<k;x++)A[T+x]=B[M+x];if(_<=1){D=!0;break}}if(A[S--]=A[w--],0==--m){D=!0;break}}while(C--,pn<=I||pn<=k);if(D)break;C<0&&(C=0),C+=2}if((R=C)<1&&(R=1),1===_){for(T=(S-=m)+1,M=(w-=m)+1,x=m-1;0<=x;x--)A[T+x]=A[M+x];A[S]=B[b]}else{if(0===_)throw new Error;for(M=S-(_-1),x=0;x<_;x++)A[M+x]=B[x]}}}}return L=[],O=[],{mergeRuns:function(){for(;1<E;){var t=E-2;if(1<=t&&O[t-1]<=O[t]+O[t+1]||2<=t&&O[t-2]<=O[t]+O[t-1])O[t-1]<O[t+1]&&t--;else if(O[t]>O[t+1])break;e(t)}},forceMergeRuns:function(){for(;1<E;){var t=E-2;0<t&&O[t-1]<O[t+1]&&t--,e(t)}},pushRun:function(t,e){L[E]=t,O[E]=e,E+=1}}}function vn(t,e,n,i){var r=(i=i||t.length)-(n=n||0);if(!(r<2)){var o=0;if(r<cn)fn(t,n,i,n+(o=dn(t,n,i,e)),e);else{var a,s=mn(t,e),l=function(t){for(var e=0;cn<=t;)e|=1&t,t>>=1;return t+e}(r);do{}while((o=dn(t,n,i,e))<l&&(fn(t,n,n+(a=l<(a=r)?l:r),n+o,e),o=a),s.pushRun(n,o),s.mergeRuns(),n+=o,0!==(r-=o));s.forceMergeRuns()}}}var _n=1,xn=4,wn=!1;function bn(){wn||(wn=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Sn(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}Tn.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},Tn.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},Tn.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,vn(n,Sn)},Tn.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),r=(o=r).getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty|=_n),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else{i=t;e&&e.length?i.__clipPaths=e:i.__clipPaths&&0<i.__clipPaths.length&&(i.__clipPaths=[]),isNaN(i.z)&&(bn(),i.z=0),isNaN(i.z2)&&(bn(),i.z2=0),isNaN(i.zlevel)&&(bn(),i.zlevel=0),this._displayList[this._displayListLen++]=i}i=t.getDecalElement&&t.getDecalElement(),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextGuideLine()),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextContent());i&&this._updateAndAddDisplayable(i,e,n)}},Tn.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},Tn.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=I(this._roots,t);0<=i&&this._roots.splice(i,1)}},Tn.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},Tn.prototype.getRoots=function(){return this._roots},Tn.prototype.dispose=function(){this._displayList=null,this._roots=null};var Mn=Tn;function Tn(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Sn}var Cn=b.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},In={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:n*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-In.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*In.bounceIn(2*t):.5*In.bounceOut(2*t-1)+.5}},kn=Math.pow,Dn=Math.sqrt,An=1e-8,Pn=Dn(3),Ln=1/3,On=Gt(),Rn=Gt(),En=Gt();function Bn(t){return-An<t&&t<An}function Nn(t){return An<t||t<-An}function zn(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function Fn(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function Vn(t,e,n,i,r,o){var a,s,i=i+3*(e-n)-t,n=3*(n-2*e+t),e=3*(e-t),t=t-r,r=n*n-3*i*e,l=n*e-9*i*t,t=e*e-3*n*t,u=0;return Bn(r)&&Bn(l)?Bn(n)?o[0]=0:0<=(a=-e/n)&&a<=1&&(o[u++]=a):Bn(e=l*l-4*r*t)?(s=-(t=l/r)/2,0<=(a=-n/i+t)&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s)):0<e?(e=r*n+1.5*i*(-l-(t=Dn(e))),0<=(a=(-n-((t=(t=r*n+1.5*i*(-l+t))<0?-kn(-t,Ln):kn(t,Ln))+(e=e<0?-kn(-e,Ln):kn(e,Ln))))/(3*i))&&a<=1&&(o[u++]=a)):(t=(2*r*n-3*i*l)/(2*Dn(r*r*r)),e=Math.acos(t)/3,a=(-n-2*(l=Dn(r))*(t=Math.cos(e)))/(3*i),s=(-n+l*(t+Pn*Math.sin(e)))/(3*i),r=(-n+l*(t-Pn*Math.sin(e)))/(3*i),0<=a&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s),0<=r&&r<=1&&(o[u++]=r)),u}function Hn(t,e,n,i,r){var o,a=6*n-12*e+6*t,i=9*e+3*i-3*t-9*n,n=3*e-3*t,e=0;return Bn(i)?Nn(a)&&0<=(o=-n/a)&&o<=1&&(r[e++]=o):Bn(t=a*a-4*i*n)?r[0]=-a/(2*i):0<t&&(t=(-a-(n=Dn(t)))/(2*i),0<=(o=(-a+n)/(2*i))&&o<=1&&(r[e++]=o),0<=t)&&t<=1&&(r[e++]=t),e}function Wn(t,e,n,i,r,o){var a=(e-t)*r+t,e=(n-e)*r+e,n=(i-n)*r+n,s=(e-a)*r+a,e=(n-e)*r+e,r=(e-s)*r+s;o[0]=t,o[1]=a,o[2]=s,o[3]=r,o[4]=r,o[5]=e,o[6]=n,o[7]=i}function Gn(t,e,n,i,r,o,a,s,l,u,h){var c,p,d,f,g=.005,y=1/0;On[0]=l,On[1]=u;for(var m=0;m<1;m+=.05)Rn[0]=zn(t,n,r,a,m),Rn[1]=zn(e,i,o,s,m),(d=te(On,Rn))<y&&(c=m,y=d);for(var y=1/0,v=0;v<32&&!(g<1e-4);v++)p=c+g,Rn[0]=zn(t,n,r,a,f=c-g),Rn[1]=zn(e,i,o,s,f),d=te(Rn,On),0<=f&&d<y?(c=f,y=d):(En[0]=zn(t,n,r,a,p),En[1]=zn(e,i,o,s,p),f=te(En,On),p<=1&&f<y?(c=p,y=f):g*=.5);return h&&(h[0]=zn(t,n,r,a,c),h[1]=zn(e,i,o,s,c)),Dn(y)}function Un(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function Xn(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function Yn(t,e,n){n=t+n-2*e;return 0==n?.5:(t-e)/n}function qn(t,e,n,i,r){var o=(e-t)*i+t,e=(n-e)*i+e,i=(e-o)*i+o;r[0]=t,r[1]=o,r[2]=i,r[3]=i,r[4]=e,r[5]=n}function Zn(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;On[0]=a,On[1]=s;for(var p=0;p<1;p+=.05)Rn[0]=Un(t,n,r,p),Rn[1]=Un(e,i,o,p),(y=te(On,Rn))<c&&(u=p,c=y);for(var c=1/0,d=0;d<32&&!(h<1e-4);d++){var f=u-h,g=u+h,y=(Rn[0]=Un(t,n,r,f),Rn[1]=Un(e,i,o,f),te(Rn,On));0<=f&&y<c?(u=f,c=y):(En[0]=Un(t,n,r,g),En[1]=Un(e,i,o,g),f=te(En,On),g<=1&&f<c?(u=g,c=f):h*=.5)}return l&&(l[0]=Un(t,n,r,u),l[1]=Un(e,i,o,u)),Dn(c)}var jn=/cubic-bezier\(([0-9,\.e ]+)\)/;function Kn(t){t=t&&jn.exec(t);if(t){var e,t=t[1].split(","),n=+It(t[0]),i=+It(t[1]),r=+It(t[2]),o=+It(t[3]);if(!isNaN(n+i+r+o))return e=[],function(t){return t<=0?0:1<=t?1:Vn(0,n,r,1,t,e)&&zn(0,i,o,1,e[0])}}}Qn.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n,o=(r<0&&(r=0),r=Math.min(r,1),this.easingFunc),o=o?o(r):r;if(this.onframe(o),1===r){if(!this.loop)return!0;this._startTime=t-i%n,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},Qn.prototype.pause=function(){this._paused=!0},Qn.prototype.resume=function(){this._paused=!1},Qn.prototype.setEasing=function(t){this.easing=t,this.easingFunc=k(t)?t:In[t]||Kn(t)};var $n=Qn;function Qn(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Vt,this.ondestroy=t.ondestroy||Vt,this.onrestart=t.onrestart||Vt,t.easing&&this.setEasing(t.easing)}var Jn=function(t){this.value=t},ti=(ei.prototype.insert=function(t){t=new Jn(t);return this.insertEntry(t),t},ei.prototype.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},ei.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},ei.prototype.len=function(){return this._len},ei.prototype.clear=function(){this.head=this.tail=null,this._len=0},ei);function ei(){this._len=0}ii.prototype.put=function(t,e){var n,i,r=this._list,o=this._map,a=null;return null==o[t]&&(i=r.len(),n=this._lastRemovedEntry,i>=this._maxSize&&0<i&&(i=r.head,r.remove(i),delete o[i.key],a=i.value,this._lastRemovedEntry=i),n?n.value=e:n=new Jn(e),n.key=t,r.insertEntry(n),o[t]=n),a},ii.prototype.get=function(t){var t=this._map[t],e=this._list;if(null!=t)return t!==e.tail&&(e.remove(t),e.insertEntry(t)),t.value},ii.prototype.clear=function(){this._list.clear(),this._map={}},ii.prototype.len=function(){return this._list.len()};var ni=ii;function ii(t){this._list=new ti,this._maxSize=10,this._map={},this._maxSize=t}var ri={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function oi(t){return(t=Math.round(t))<0?0:255<t?255:t}function ai(t){return t<0?0:1<t?1:t}function si(t){return t.length&&"%"===t.charAt(t.length-1)?oi(parseFloat(t)/100*255):oi(parseInt(t,10))}function li(t){return t.length&&"%"===t.charAt(t.length-1)?ai(parseFloat(t)/100):ai(parseFloat(t))}function ui(t,e,n){return n<0?n+=1:1<n&&--n,6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function hi(t,e,n){return t+(e-t)*n}function ci(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function pi(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var di=new ni(20),fi=null;function gi(t,e){fi&&pi(fi,e),fi=di.put(t,fi||e.slice())}function yi(t,e){if(t){e=e||[];var n=di.get(t);if(n)return pi(e,n);n=(t+="").replace(/ /g,"").toLowerCase();if(n in ri)return pi(e,ri[n]),gi(t,e),e;var i=n.length;if("#"===n.charAt(0))return 4===i||5===i?0<=(r=parseInt(n.slice(1,4),16))&&r<=4095?(ci(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===i?parseInt(n.slice(4),16)/15:1),gi(t,e),e):void ci(e,0,0,0,1):7===i||9===i?0<=(r=parseInt(n.slice(1,7),16))&&r<=16777215?(ci(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===i?parseInt(n.slice(7),16)/255:1),gi(t,e),e):void ci(e,0,0,0,1):void 0;var r=n.indexOf("("),o=n.indexOf(")");if(-1!==r&&o+1===i){var i=n.substr(0,r),a=n.substr(r+1,o-(r+1)).split(","),s=1;switch(i){case"rgba":if(4!==a.length)return 3===a.length?ci(e,+a[0],+a[1],+a[2],1):ci(e,0,0,0,1);s=li(a.pop());case"rgb":return 3<=a.length?(ci(e,si(a[0]),si(a[1]),si(a[2]),3===a.length?s:li(a[3])),gi(t,e),e):void ci(e,0,0,0,1);case"hsla":return 4!==a.length?void ci(e,0,0,0,1):(a[3]=li(a[3]),mi(a,e),gi(t,e),e);case"hsl":return 3!==a.length?void ci(e,0,0,0,1):(mi(a,e),gi(t,e),e);default:return}}ci(e,0,0,0,1)}}function mi(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=li(t[1]),r=li(t[2]),i=r<=.5?r*(i+1):r+i-r*i,r=2*r-i;return ci(e=e||[],oi(255*ui(r,i,n+1/3)),oi(255*ui(r,i,n)),oi(255*ui(r,i,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function vi(t,e){var n=yi(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,255<n[i]?n[i]=255:n[i]<0&&(n[i]=0);return bi(n,4===n.length?"rgba":"rgb")}}function _i(t,e,n){var i,r,o;if(e&&e.length&&0<=t&&t<=1)return n=n||[],t=t*(e.length-1),i=Math.floor(t),o=Math.ceil(t),r=e[i],e=e[o],n[0]=oi(hi(r[0],e[0],o=t-i)),n[1]=oi(hi(r[1],e[1],o)),n[2]=oi(hi(r[2],e[2],o)),n[3]=ai(hi(r[3],e[3],o)),n}var xi=_i;function wi(t,e,n){var i,r,o,a;if(e&&e.length&&0<=t&&t<=1)return t=t*(e.length-1),i=Math.floor(t),r=Math.ceil(t),a=yi(e[i]),e=yi(e[r]),a=bi([oi(hi(a[0],e[0],o=t-i)),oi(hi(a[1],e[1],o)),oi(hi(a[2],e[2],o)),ai(hi(a[3],e[3],o))],"rgba"),n?{color:a,leftIndex:i,rightIndex:r,value:t}:a}var n=wi;function bi(t,e){var n;if(t&&t.length)return n=t[0]+","+t[1]+","+t[2],"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}function Si(t,e){t=yi(t);return t?(.299*t[0]+.587*t[1]+.114*t[2])*t[3]/255+(1-t[3])*e:0}var Mi=new ni(100);function Ti(t){var e;return V(t)?((e=Mi.get(t))||(e=vi(t,-.1),Mi.put(t,e)),e):vt(t)?((e=O({},t)).colorStops=z(t.colorStops,function(t){return{offset:t.offset,color:vi(t.color,-.1)}}),e):t}xi=Object.freeze({__proto__:null,fastLerp:_i,fastMapToColor:xi,lerp:wi,lift:vi,liftColor:Ti,lum:Si,mapToColor:n,modifyAlpha:function(t,e){if((t=yi(t))&&null!=e)return t[3]=ai(e),bi(t,"rgba")},modifyHSL:function(t,e,n,i){var r=yi(t);if(t)return r=function(t){var e,n,i,r,o,a,s,l,u,h;if(t)return h=t[0]/255,e=t[1]/255,n=t[2]/255,s=Math.min(h,e,n),r=((i=Math.max(h,e,n))+s)/2,0==(u=i-s)?a=o=0:(a=r<.5?u/(i+s):u/(2-i-s),s=((i-h)/6+u/2)/u,l=((i-e)/6+u/2)/u,u=((i-n)/6+u/2)/u,h===i?o=u-l:e===i?o=1/3+s-u:n===i&&(o=2/3+l-s),o<0&&(o+=1),1<o&&--o),h=[360*o,a,r],null!=t[3]&&h.push(t[3]),h}(r),null!=e&&(r[0]=(t=e,(t=Math.round(t))<0?0:360<t?360:t)),null!=n&&(r[1]=li(n)),null!=i&&(r[2]=li(i)),bi(mi(r),"rgba")},parse:yi,random:function(){return bi([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},stringify:bi,toHex:function(t){if(t=yi(t))return((1<<24)+(t[0]<<16)+(t[1]<<8)+ +t[2]).toString(16).slice(1)}});b.hasGlobalWindow&&k(window.btoa);var Ci=Array.prototype.slice;function Ii(t,e,n){return(e-t)*n+t}function ki(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=Ii(e[o],n[o],i);return t}function Di(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function Ai(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function Pi(t){if(st(t)){var e=t.length;if(st(t[0])){for(var n=[],i=0;i<e;i++)n.push(Ci.call(t[i]));return n}return Ci.call(t)}return t}function Li(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function Oi(t){return 4===t||5===t}function Ri(t){return 1===t||2===t}var Ei=[0,0,0,0],Bi=(Ni.prototype.isFinished=function(){return this._finished},Ni.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},Ni.prototype.needsAnimate=function(){return 1<=this.keyframes.length},Ni.prototype.getAdditiveTrack=function(){return this._additiveTrack},Ni.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i,r=this.keyframes,o=r.length,a=!1,s=6,l=e,u=(st(e)?(1==(s=i=st((i=e)&&i[0])?2:1)&&!ft(e[0])||2==i&&!ft(e[0][0]))&&(a=!0):ft(e)&&!wt(e)?s=0:V(e)?isNaN(+e)?(i=yi(e))&&(l=i,s=3):s=0:vt(e)&&((u=O({},l)).colorStops=z(e.colorStops,function(t){return{offset:t.offset,color:yi(t.color)}}),"linear"===e.type?s=4:"radial"===e.type&&(s=5),l=u),0===o?this.valType=s:s===this.valType&&6!==s||(a=!0),this.discrete=this.discrete||a,{time:t,value:l,rawValue:e,percent:0});return n&&(u.easing=n,u.easingFunc=k(n)?n:In[n]||Kn(n)),r.push(u),u},Ni.prototype.prepare=function(t,e){for(var n=this.keyframes,i=(this._needsSort&&n.sort(function(t,e){return t.time-e.time}),this.valType),r=n.length,o=n[r-1],a=this.discrete,s=Ri(i),l=Oi(i),u=0;u<r;u++){var h=n[u],c=h.value,p=o.value;if(h.percent=h.time/t,!a)if(s&&u!==r-1){x=_=v=m=y=g=f=d=h=void 0;var d=p,f=i,g=h=c,y=d;if(g.push&&y.push){var h=g.length,m=y.length;if(h!==m)if(m<h)g.length=m;else for(var v=h;v<m;v++)g.push(1===f?y[v]:Ci.call(y[v]));for(var _=g[0]&&g[0].length,v=0;v<g.length;v++)if(1===f)isNaN(g[v])&&(g[v]=y[v]);else for(var x=0;x<_;x++)isNaN(g[v][x])&&(g[v][x]=y[v][x])}}else if(l){T=M=S=b=w=h=d=void 0;for(var d=c.colorStops,h=p.colorStops,w=d.length,b=h.length,S=b<w?h:d,h=Math.min(w,b),M=S[h-1]||{color:[0,0,0,0],offset:0},T=h;T<Math.max(w,b);T++)S.push({offset:M.offset,color:M.color.slice()})}}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;for(var C=n[0].value,u=0;u<r;u++)0===i?n[u].additiveValue=n[u].value-C:3===i?n[u].additiveValue=Di([],n[u].value,C,-1):Ri(i)&&(n[u].additiveValue=(1===i?Di:Ai)([],n[u].value,C,-1))}},Ni.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,o,a=null!=this._additiveTrack,s=a?"additiveValue":"value",l=this.valType,u=this.keyframes,h=u.length,c=this.propName,p=3===l,d=this._lastFr,f=Math.min;if(1===h)n=i=u[0];else{if(e<0)g=0;else if(e<this._lastFrP){for(var g=f(d+1,h-1);0<=g&&!(u[g].percent<=e);g--);g=f(g,h-2)}else{for(g=d;g<h&&!(u[g].percent>e);g++);g=f(g-1,h-2)}i=u[g+1],n=u[g]}n&&i&&(this._lastFr=g,this._lastFrP=e,d=i.percent-n.percent,r=0==d?1:f((e-n.percent)/d,1),i.easingFunc&&(r=i.easingFunc(r)),f=a?this._additiveValue:p?Ei:t[c],(Ri(l)||p)&&(f=f||(this._additiveValue=[])),this.discrete?t[c]=(r<1?n:i).rawValue:Ri(l)?(1===l?ki:function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=Ii(e[a][s],n[a][s],i)}})(f,n[s],i[s],r):Oi(l)?(d=n[s],o=i[s],t[c]={type:(l=4===l)?"linear":"radial",x:Ii(d.x,o.x,r),y:Ii(d.y,o.y,r),colorStops:z(d.colorStops,function(t,e){e=o.colorStops[e];return{offset:Ii(t.offset,e.offset,r),color:Li(ki([],t.color,e.color,r))}}),global:o.global},l?(t[c].x2=Ii(d.x2,o.x2,r),t[c].y2=Ii(d.y2,o.y2,r)):t[c].r=Ii(d.r,o.r,r)):p?(ki(f,n[s],i[s],r),a||(t[c]=Li(f))):(l=Ii(n[s],i[s],r),a?this._additiveValue=l:t[c]=l),a)&&this._addToTarget(t)}},Ni.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;0===e?t[n]=t[n]+i:3===e?(yi(t[n],Ei),Di(Ei,Ei,i,1),t[n]=Li(Ei)):1===e?Di(t[n],t[n],i,1):2===e&&Ai(t[n],t[n],i,1)},Ni);function Ni(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}i.prototype.getMaxTime=function(){return this._maxTime},i.prototype.getDelay=function(){return this._delay},i.prototype.getLoop=function(){return this._loop},i.prototype.getTarget=function(){return this._target},i.prototype.changeTarget=function(t){this._target=t},i.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,ht(e),n)},i.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o];if(!(l=r[a])){var s,l=r[a]=new Bi(a),u=void 0,h=this._getAdditiveTrack(a);if(h?(u=(s=(s=h.keyframes)[s.length-1])&&s.value,3===h.valType&&(u=u&&Li(u))):u=this._target[a],null==u)continue;0<t&&l.addKeyframe(0,Pi(u),i),this._trackKeys.push(a)}l.addKeyframe(t,Pi(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},i.prototype.pause=function(){this._clip.pause(),this._paused=!0},i.prototype.resume=function(){this._clip.resume(),this._paused=!1},i.prototype.isPaused=function(){return!!this._paused},i.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},i.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},i.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},i.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},i.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},i.prototype.start=function(t){if(!(0<this._started)){this._started=1;for(var e,o=this,a=[],n=this._maxTime||0,i=0;i<this._trackKeys.length;i++){var r=this._trackKeys[i],s=this._tracks[r],r=this._getAdditiveTrack(r),l=s.keyframes,u=l.length;s.prepare(n,r),s.needsAnimate()&&(!this._allowDiscrete&&s.discrete?((r=l[u-1])&&(o._target[s.propName]=r.rawValue),s.setFinished()):a.push(s))}return a.length||this._force?(e=new $n({life:n,loop:this._loop,delay:this._delay||0,onframe:function(t){o._started=2;var e=o._additiveAnimators;if(e){for(var n=!1,i=0;i<e.length;i++)if(e[i]._clip){n=!0;break}n||(o._additiveAnimators=null)}for(i=0;i<a.length;i++)a[i].step(o._target,t);var r=o._onframeCbs;if(r)for(i=0;i<r.length;i++)r[i](o._target,t)},ondestroy:function(){o._doneCallback()}}),this._clip=e,this.animation&&this.animation.addClip(e),t&&e.setEasing(t)):this._doneCallback(),this}},i.prototype.stop=function(t){var e;this._clip&&(e=this._clip,t&&e.onframe(1),this._abortedCallback())},i.prototype.delay=function(t){return this._delay=t,this},i.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},i.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},i.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},i.prototype.getClip=function(){return this._clip},i.prototype.getTrack=function(t){return this._tracks[t]},i.prototype.getTracks=function(){var e=this;return z(this._trackKeys,function(t){return e._tracks[t]})},i.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}for(var a=!0,r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},i.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];o&&!o.isFinished()&&(o=(o=o.keyframes)[n?0:o.length-1])&&(t[r]=Pi(o.rawValue))}}},i.prototype.__changeFinalValue=function(t,e){e=e||ht(t);for(var n=0;n<e.length;n++){var i,r=e[n],o=this._tracks[r];o&&1<(i=o.keyframes).length&&(i=i.pop(),o.addKeyframe(i.time,t[r]),o.prepare(this._maxTime,o.getAdditiveTrack()))}};var zi=i;function i(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,(this._loop=e)&&i?it("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}function Fi(){return(new Date).getTime()}u(Wi,Vi=ue),Wi.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?((this._tail.next=t).prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},Wi.prototype.addAnimator=function(t){t.animation=this;t=t.getClip();t&&this.addClip(t)},Wi.prototype.removeClip=function(t){var e,n;t.animation&&(e=t.prev,n=t.next,e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null)},Wi.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},Wi.prototype.update=function(t){for(var e=Fi()-this._pausedTime,n=e-this._time,i=this._head;i;)var r=i.next,i=(i.step(e,n)&&(i.ondestroy(),this.removeClip(i)),r);this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},Wi.prototype._startLoop=function(){var e=this;this._running=!0,Cn(function t(){e._running&&(Cn(t),e._paused||e.update())})},Wi.prototype.start=function(){this._running||(this._time=Fi(),this._pausedTime=0,this._startLoop())},Wi.prototype.stop=function(){this._running=!1},Wi.prototype.pause=function(){this._paused||(this._pauseStart=Fi(),this._paused=!0)},Wi.prototype.resume=function(){this._paused&&(this._pausedTime+=Fi()-this._pauseStart,this._paused=!1)},Wi.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},Wi.prototype.isFinished=function(){return null==this._head},Wi.prototype.animate=function(t,e){e=e||{},this.start();t=new zi(t,e.loop);return this.addAnimator(t),t};var Vi,Hi=Wi;function Wi(t){var e=Vi.call(this)||this;return e._running=!1,e._time=0,e._pausedTime=0,e._pauseStart=0,e._paused=!1,e.stage=(t=t||{}).stage||{},e}var Gi,Ui=b.domSupported,Xi=(Gi={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:n=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:z(n,function(t){var e=t.replace("mouse","pointer");return Gi.hasOwnProperty(e)?e:t})}),Yi=["mousemove","mouseup"],qi=["pointermove","pointerup"],Zi=!1;function ji(t){t=t.pointerType;return"pen"===t||"touch"===t}function Ki(t){t&&(t.zrByTouch=!0)}function $i(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var Qi=function(t,e){this.stopPropagation=Vt,this.stopImmediatePropagation=Vt,this.preventDefault=Vt,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},Ji={mousedown:function(t){t=Ie(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=Ie(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=Ie(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){$i(this,(t=Ie(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){Zi=!0,t=Ie(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){Zi||(t=Ie(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){Ki(t=Ie(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Ji.mousemove.call(this,t),Ji.mousedown.call(this,t)},touchmove:function(t){Ki(t=Ie(this.dom,t)),this.handler.processGesture(t,"change"),Ji.mousemove.call(this,t)},touchend:function(t){Ki(t=Ie(this.dom,t)),this.handler.processGesture(t,"end"),Ji.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&Ji.click.call(this,t)},pointerdown:function(t){Ji.mousedown.call(this,t)},pointermove:function(t){ji(t)||Ji.mousemove.call(this,t)},pointerup:function(t){Ji.mouseup.call(this,t)},pointerout:function(t){ji(t)||Ji.mouseout.call(this,t)}},tr=(R(["click","dblclick","contextmenu"],function(e){Ji[e]=function(t){t=Ie(this.dom,t),this.trigger(e,t)}}),{pointermove:function(t){ji(t)||tr.mousemove.call(this,t)},pointerup:function(t){tr.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}});function er(i,r){var o=r.domHandlers;b.pointerEventsSupported?R(Xi.pointer,function(e){ir(r,e,function(t){o[e].call(i,t)})}):(b.touchEventsSupported&&R(Xi.touch,function(n){ir(r,n,function(t){var e;o[n].call(i,t),(e=r).touching=!0,null!=e.touchTimer&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)})}),R(Xi.mouse,function(e){ir(r,e,function(t){t=Ce(t),r.touching||o[e].call(i,t)})}))}function nr(i,r){function t(n){ir(r,n,function(t){var e;t=Ce(t),$i(i,t.target)||(e=t,t=Ie(i.dom,new Qi(i,e),!0),r.domHandlers[n].call(i,t))},{capture:!0})}b.pointerEventsSupported?R(qi,t):b.touchEventsSupported||R(Yi,t)}function ir(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,t.domTarget.addEventListener(e,n,i)}function rr(t){var e,n,i,r,o,a=t.mounted;for(e in a)a.hasOwnProperty(e)&&(n=t.domTarget,r=a[i=e],o=t.listenerOpts[e],n.removeEventListener(i,r,o));t.mounted={}}var or,ar=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},sr=(u(lr,or=ue),lr.prototype.dispose=function(){rr(this._localHandlerScope),Ui&&rr(this._globalHandlerScope)},lr.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},lr.prototype.__togglePointerCapture=function(t){var e;this.__mayPointerCapture=null,Ui&&+this.__pointerCapturing^+t&&(this.__pointerCapturing=t,e=this._globalHandlerScope,t?nr(this,e):rr(e))},lr);function lr(t,e){var n=or.call(this)||this;return n.__pointerCapturing=!1,n.dom=t,n.painterRoot=e,n._localHandlerScope=new ar(t,Ji),Ui&&(n._globalHandlerScope=new ar(document,tr)),er(n,n._localHandlerScope),n}var n=1,ur=n=b.hasGlobalWindow?Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1):n,hr="#333",cr="#ccc",pr=Re;function dr(t){return 5e-5<t||t<-5e-5}var fr=[],gr=[],yr=Oe(),mr=Math.abs,vr=(_r.prototype.getLocalTransform=function(t){return _r.getLocalTransform(this,t)},_r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},_r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},_r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},_r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},_r.prototype.needLocalTransform=function(){return dr(this.rotation)||dr(this.x)||dr(this.y)||dr(this.scaleX-1)||dr(this.scaleY-1)||dr(this.skewX)||dr(this.skewY)},_r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||Oe(),e?this.getLocalTransform(n):pr(n),t&&(e?Be(n,t,n):Ee(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&(pr(n),this.invTransform=null)},_r.prototype._resolveGlobalScaleRatio=function(t){var e,n,i=this.globalScaleRatio;null!=i&&1!==i&&(this.getGlobalScale(fr),n=((fr[1]-(n=fr[1]<0?-1:1))*i+n)/fr[1]||0,t[0]*=i=((fr[0]-(e=fr[0]<0?-1:1))*i+e)/fr[0]||0,t[1]*=i,t[2]*=n,t[3]*=n),this.invTransform=this.invTransform||Oe(),Ve(this.invTransform,t)},_r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},_r.prototype.setLocalTransform=function(t){var e,n,i,r;t&&(r=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],e=Math.atan2(t[1],t[0]),n=Math.PI/2+e-Math.atan2(t[3],t[2]),i=Math.sqrt(i)*Math.cos(n),r=Math.sqrt(r),this.skewX=n,this.skewY=0,this.rotation=-e,this.x=+t[4],this.y=+t[5],this.scaleX=r,this.scaleY=i,this.originX=0,this.originY=0)},_r.prototype.decomposeTransform=function(){var t,e,n;this.transform&&(e=this.parent,t=this.transform,e&&e.transform&&(e.invTransform=e.invTransform||Oe(),Be(gr,e.invTransform,t),t=gr),e=this.originX,n=this.originY,(e||n)&&(yr[4]=e,yr[5]=n,Be(gr,t,yr),gr[4]-=e,gr[5]-=n,t=gr),this.setLocalTransform(t))},_r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},_r.prototype.transformCoordToLocal=function(t,e){t=[t,e],e=this.invTransform;return e&&ne(t,t,e),t},_r.prototype.transformCoordToGlobal=function(t,e){t=[t,e],e=this.transform;return e&&ne(t,t,e),t},_r.prototype.getLineScale=function(){var t=this.transform;return t&&1e-10<mr(t[0]-1)&&1e-10<mr(t[3]-1)?Math.sqrt(mr(t[0]*t[3]-t[2]*t[1])):1},_r.prototype.copyTransform=function(t){for(var e=this,n=t,i=0;i<xr.length;i++){var r=xr[i];e[r]=n[r]}},_r.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,l=t.rotation||0,u=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,t=t.skewY?Math.tan(-t.skewY):0;return n||i||a||s?(e[4]=-(a=n+a)*r-c*(s=i+s)*o,e[5]=-s*o-t*a*r):e[4]=e[5]=0,e[0]=r,e[3]=o,e[1]=t*r,e[2]=c*o,l&&ze(e,e,l),e[4]+=n+u,e[5]+=i+h,e},_r.initDefaultProps=((n=_r.prototype).scaleX=n.scaleY=n.globalScaleRatio=1,void(n.x=n.y=n.originX=n.originY=n.skewX=n.skewY=n.rotation=n.anchorX=n.anchorY=0)),_r);function _r(){}var xr=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];var wr={};function br(t,e){var n=wr[e=e||K],i=(n=n||(wr[e]=new ni(500))).get(t);return null==i&&(i=H.measureText(t,e).width,n.put(t,i)),i}function Sr(t,e,n,i){t=br(t,e),e=Ir(e),n=Tr(0,t,n),i=Cr(0,e,i);return new X(n,i,t,e)}function Mr(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return Sr(r[0],e,n,i);for(var o=new X(0,0,0,0),a=0;a<r.length;a++){var s=Sr(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function Tr(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function Cr(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function Ir(t){return br("国",t)}function kr(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function Dr(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=kr(i[0],n.width),u+=kr(i[1],n.height),c=h=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var Ar,Pr="__zr_normal__",Lr=xr.concat(["ignore"]),Or=lt(xr,function(t,e){return t[e]=!0,t},{ignore:!1}),Rr={},Er=new X(0,0,0,0),n=(r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e,n,i,r,o,a,s,l,u,h,c=this._textContent;!c||c.ignore&&!t||(this.textConfig||(this.textConfig={}),l=(t=this.textConfig).local,i=n=void 0,r=!1,(e=c.innerTransformable).parent=l?this:null,h=!1,e.copyTransform(c),null!=t.position&&(u=Er,t.layoutRect?u.copy(t.layoutRect):u.copy(this.getBoundingRect()),l||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Rr,t,u):Dr(Rr,t,u),e.x=Rr.x,e.y=Rr.y,n=Rr.align,i=Rr.verticalAlign,o=t.origin)&&null!=t.rotation&&(s=a=void 0,s="center"===o?(a=.5*u.width,.5*u.height):(a=kr(o[0],u.width),kr(o[1],u.height)),h=!0,e.originX=-e.x+a+(l?0:u.x),e.originY=-e.y+s+(l?0:u.y)),null!=t.rotation&&(e.rotation=t.rotation),(o=t.offset)&&(e.x+=o[0],e.y+=o[1],h||(e.originX=-o[0],e.originY=-o[1])),a=null==t.inside?"string"==typeof t.position&&0<=t.position.indexOf("inside"):t.inside,s=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),h=u=l=void 0,a&&this.canBeInsideText()?(l=t.insideFill,u=t.insideStroke,null!=l&&"auto"!==l||(l=this.getInsideTextFill()),null!=u&&"auto"!==u||(u=this.getInsideTextStroke(l),h=!0)):(l=t.outsideFill,u=t.outsideStroke,null!=l&&"auto"!==l||(l=this.getOutsideFill()),null!=u&&"auto"!==u||(u=this.getOutsideStroke(l),h=!0)),(l=l||"#000")===s.fill&&u===s.stroke&&h===s.autoStroke&&n===s.align&&i===s.verticalAlign||(r=!0,s.fill=l,s.stroke=u,s.autoStroke=h,s.align=n,s.verticalAlign=i,c.setDefaultTextStyle(s)),c.__dirty|=_n,r&&c.dirtyStyle(!0))},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?cr:hr},r.prototype.getOutsideStroke=function(t){for(var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&yi(e),i=(n=n||[255,255,255,1])[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,bi(n,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},O(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(L(t))for(var n=ht(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;i.getLoop()||r&&r!==Pr||(r=(r=i.targetName)?e[r]:e,i.saveTo(r))}},r.prototype._innerSaveToNormal=function(t){var e=(e=this._normalState)||(this._normalState={});t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Lr)},r.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},r.prototype.hasState=function(){return 0<this.currentStates.length},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(Pr,!1,t)},r.prototype.useState=function(t,e,n,i){var r=t===Pr,o=this.hasState();if(o||!r){var a,o=this.currentStates,s=this.stateTransition;if(!(0<=I(o,t))||!e&&1!==o.length){if((a=(a=this.stateProxy&&!r?this.stateProxy(t):a)||this.states&&this.states[t])||r)return r||this.saveCurrentToNormalState(a),(o=!!(a&&a.hoverLayer||i))&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,a,this._normalState,e,!n&&!this.__inHover&&s&&0<s.duration,s),i=this._textContent,s=this._textGuide,i&&i.useState(t,e,n,o),s&&s.useState(t,e,n,o),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!o&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~_n),a;it("State "+t+" not exists.")}}},r.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(!a){for(s=0;s<o;s++){var l=t[s],u=void 0;(u=(u=this.stateProxy?this.stateProxy(l,t):u)||this.states[l])&&i.push(u)}var h=i[o-1],h=!!(h&&h.hoverLayer||n),n=(h&&this._toggleHoverLayerFlag(!0),this._mergeStates(i)),c=this.stateTransition,n=(this.saveCurrentToNormalState(n),this._applyStateObj(t.join(","),n,this._normalState,!1,!e&&!this.__inHover&&c&&0<c.duration,c),this._textContent),c=this._textGuide;n&&n.useStates(t,e,h),c&&c.useStates(t,e,h),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~_n)}}else this.clearStates()},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e,t=I(this.currentStates,t);0<=t&&((e=this.currentStates.slice()).splice(t,1),this.useStates(e))},r.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),t=I(i,t),r=0<=I(i,e);0<=t?r?i.splice(t,1):i[t]=e:n&&!r&&i.push(e),this.useStates(i)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];O(n,r),r.textConfig&&O(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},r.prototype._applyStateObj=function(t,e,n,i,r,o){for(var a=!(e&&i),s=(e&&e.textConfig?(this.textConfig=O({},(i?this:n).textConfig),O(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig),{}),l=!1,u=0;u<Lr.length;u++){var h=Lr[u],c=r&&Or[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],d=p.targetName;p.getLoop()||p.__changeFinalValue(d?(e||n)[d]:e||n)}l&&this._transitionState(t,s,o)},r.prototype._attachComponent=function(t){var e;t.__zr&&!t.__hostTarget||t!==this&&((e=this.__zr)&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this)},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new vr,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),O(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=_n;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,n){var i=t?this[t]:this,i=new zi(i,e,n);return t&&(i.targetName=t),this.addAnimator(i,t),i},r.prototype.addAnimator=function(n,t){var e=this.__zr,i=this;n.during(function(){i.updateDuringAnimation(t)}).done(function(){var t=i.animators,e=I(t,n);0<=e&&t.splice(e,1)}),this.animators.push(n),e&&e.animation.addAnimator(n),e&&e.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},r.prototype.animateTo=function(t,e,n){Nr(this,t,e,n)},r.prototype.animateFrom=function(t,e,n){Nr(this,t,e,n,!0)},r.prototype._transitionState=function(t,e,n,i){for(var r=Nr(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=((Ar=r.prototype).type="element",Ar.name="",Ar.ignore=Ar.silent=Ar.isGroup=Ar.draggable=Ar.dragging=Ar.ignoreClip=Ar.__inHover=!1,Ar.__dirty=_n,void(Object.defineProperty&&(Br("position","_legacyPos","x","y"),Br("scale","_legacyScale","scaleX","scaleY"),Br("origin","_legacyOrigin","originX","originY")))),r);function r(t){this.id=et++,this.animators=[],this.currentStates=[],this.states={},this._init(t)}function Br(t,e,n,i){function r(e,t){Object.defineProperty(t,0,{get:function(){return e[n]},set:function(t){e[n]=t}}),Object.defineProperty(t,1,{get:function(){return e[i]},set:function(t){e[i]=t}})}Object.defineProperty(Ar,t,{get:function(){var t;return this[e]||(t=this[e]=[],r(this,t)),this[e]},set:function(t){this[n]=t[0],this[i]=t[1],this[e]=t,r(this,t)}})}function Nr(t,e,n,i,r){function o(){u=!0,--l<=0&&(u?h&&h():c&&c())}function a(){--l<=0&&(u?h&&h():c&&c())}var s=[],l=(!function t(e,n,i,r,o,a,s,l){var u=ht(r);var h=o.duration;var c=o.delay;var p=o.additive;var d=o.setToFinal;var f=!L(a);var g=e.animators;var y=[];for(var m=0;m<u.length;m++){var v=u[m],_=r[v];null!=_&&null!=i[v]&&(f||a[v])?!L(_)||st(_)||vt(_)?y.push(v):n?l||(i[v]=_,e.updateDuringAnimation(n)):t(e,v,i[v],_,o,a&&a[v],s,l):l||(i[v]=_,e.updateDuringAnimation(n),y.push(v))}var x=y.length;if(!p&&x)for(var w,b=0;b<g.length;b++)(S=g[b]).targetName===n&&S.stopTracks(y)&&(w=I(g,S),g.splice(w,1));o.force||(y=ut(y,function(t){return!Vr(r[t],i[t])}),x=y.length);if(0<x||o.force&&!s.length){var S,M=void 0,T=void 0,C=void 0;if(l){T={},d&&(M={});for(b=0;b<x;b++){v=y[b];T[v]=i[v],d?M[v]=r[v]:i[v]=r[v]}}else if(d){C={};for(b=0;b<x;b++){v=y[b];C[v]=Pi(i[v]),Fr(i,r,v)}}(S=new zi(i,!1,!1,p?ut(g,function(t){return t.targetName===n}):null)).targetName=n,o.scope&&(S.scope=o.scope),d&&M&&S.whenWithKeys(0,M,y),C&&S.whenWithKeys(0,C,y),S.whenWithKeys(null==h?500:h,l?T:r,y).delay(c||0),e.addAnimator(S,n),s.push(S)}}(t,"",t,e,n=n||{},i,s,r),s.length),u=!1,h=n.done,c=n.aborted;l||h&&h(),0<s.length&&n.during&&s[0].during(function(t,e){n.during(e)});for(var p=0;p<s.length;p++){var d=s[p];d.done(o),d.aborted(a),n.force&&d.duration(n.duration),d.start(n.easing)}return s}function zr(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function Fr(t,e,n){if(st(e[n]))if(st(t[n])||(t[n]=[]),yt(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),zr(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(st(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?zr(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else zr(o,r,a);o.length=r.length}else t[n]=e[n]}function Vr(t,e){return t===e||st(t)&&st(e)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}(t,e)}at(n,ue),at(n,vr);u(Gr,Hr=n),Gr.prototype.childrenRef=function(){return this._children},Gr.prototype.children=function(){return this._children.slice()},Gr.prototype.childAt=function(t){return this._children[t]},Gr.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},Gr.prototype.childCount=function(){return this._children.length},Gr.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},Gr.prototype.addBefore=function(t,e){var n;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&0<=(e=(n=this._children).indexOf(e))&&(n.splice(e,0,t),this._doAdd(t)),this},Gr.prototype.replace=function(t,e){t=I(this._children,t);return 0<=t&&this.replaceAt(e,t),this},Gr.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];return t&&t!==this&&t.parent!==this&&t!==i&&(n[e]=t,i.parent=null,(n=this.__zr)&&i.removeSelfFromZr(n),this._doAdd(t)),this},Gr.prototype._doAdd=function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},Gr.prototype.remove=function(t){var e=this.__zr,n=this._children,i=I(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},Gr.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},Gr.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},Gr.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},Gr.prototype.addSelfToZr=function(t){Hr.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].addSelfToZr(t)},Gr.prototype.removeSelfFromZr=function(t){Hr.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].removeSelfFromZr(t)},Gr.prototype.getBoundingRect=function(t){for(var e=new X(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a,s=n[o];s.ignore||s.invisible||(a=s.getBoundingRect(),(s=s.getLocalTransform(i))?(X.applyTransform(e,a,s),(r=r||e.clone()).union(e)):(r=r||a.clone()).union(a))}return r||e};var Hr,Wr=Gr;function Gr(t){var e=Hr.call(this)||this;return e.isGroup=!0,e._children=[],e.attr(t),e}Wr.prototype.type="group";var Ur={},Xr={};Zr.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},Zr.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},Zr.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},Zr.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(t){if("string"==typeof t)return Si(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=Si(e[r].color,1);return(n/=i)<.4}}return!1}(t))},Zr.prototype.getBackgroundColor=function(){return this._backgroundColor},Zr.prototype.setDarkMode=function(t){this._darkMode=t},Zr.prototype.isDarkMode=function(){return this._darkMode},Zr.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},Zr.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},Zr.prototype.flush=function(){this._disposed||this._flush(!1)},Zr.prototype._flush=function(t){var e,n=Fi(),t=(this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately()),Fi());e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:t-n})):0<this._sleepAfterStill&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill)&&this.animation.stop()},Zr.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},Zr.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},Zr.prototype.refreshHover=function(){this._needsRefreshHover=!0},Zr.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},Zr.prototype.resize=function(t){this._disposed||(this.painter.resize((t=t||{}).width,t.height),this.handler.resize())},Zr.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},Zr.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},Zr.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},Zr.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},Zr.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},Zr.prototype.on=function(t,e,n){return this._disposed||this.handler.on(t,e,n),this},Zr.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},Zr.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},Zr.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Wr&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},Zr.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete Xr[t])};var Yr,qr=Zr;function Zr(t,e,n){var i,r=this,o=(this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t,new Mn),a=n.renderer||"canvas",a=(Ur[a]||(a=ht(Ur)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect,new Ur[a](e,o,n,t)),e=n.ssr||a.ssrOnly,t=(this.storage=o,this.painter=a,b.node||b.worker||e?null:new sr(a.getViewportRoot(),a.root)),s=n.useCoarsePointer;(null==s||"auto"===s?b.touchEventsSupported:!!s)&&(i=E(n.pointerSize,44)),this.handler=new sn(o,a,t,a.root,i),this.animation=new Hi({stage:{update:e?null:function(){return r._flush(!0)}}}),e||this.animation.start()}function jr(t,e){t=new qr(et++,t,e);return Xr[t.id]=t}function Kr(t,e){Ur[t]=e}function $r(t){Yr=t}var Qr=Object.freeze({__proto__:null,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in Xr)Xr.hasOwnProperty(t)&&Xr[t].dispose();Xr={}},getElementSSRData:function(t){if("function"==typeof Yr)return Yr(t)},getInstance:function(t){return Xr[t]},init:jr,registerPainter:Kr,registerSSRDataGetter:$r,version:"5.6.0"}),Jr=20;function to(t,e,n,i){var r=e[0],e=e[1],o=n[0],n=n[1],a=e-r,s=n-o;if(0==a)return 0==s?o:(o+n)/2;if(i)if(0<a){if(t<=r)return o;if(e<=t)return n}else{if(r<=t)return o;if(t<=e)return n}else{if(t===r)return o;if(t===e)return n}return(t-r)/a*s+o}function eo(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return V(t)?t.replace(/^\s+|\s+$/g,"").match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function no(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),Jr),t=(+t).toFixed(e),n?t:+t}function io(t){if(t=+t,isNaN(t))return 0;if(1e-14<t)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return ro(t)}function ro(t){var t=t.toString().toLowerCase(),e=t.indexOf("e"),n=0<e?+t.slice(e+1):0,e=0<e?e:t.length,t=t.indexOf(".");return Math.max(0,(t<0?0:e-1-t)-n)}function oo(t,e){var n=Math.log,i=Math.LN10,t=Math.floor(n(t[1]-t[0])/i),n=Math.round(n(Math.abs(e[1]-e[0]))/i),e=Math.min(Math.max(-t+n,0),20);return isFinite(e)?e:20}function ao(t){var e=2*Math.PI;return(t%e+e)%e}function so(t){return-1e-4<t&&t<1e-4}var lo=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function uo(t){var e,n;return t instanceof Date?t:V(t)?(e=lo.exec(t))?e[8]?(n=+e[4]||0,"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))):new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0):new Date(NaN):null==t?new Date(NaN):new Date(Math.round(t))}function ho(t){return Math.pow(10,co(t))}function co(t){var e;return 0===t?0:(e=Math.floor(Math.log(t)/Math.LN10),10<=t/Math.pow(10,e)&&e++,e)}function po(t,e){var n=co(t),i=Math.pow(10,n),r=t/i,e=e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10;return t=e*i,-20<=n?+t.toFixed(n<0?-n:0):t}function fo(t){var e=parseFloat(t);return e==t&&(0!==e||!V(t)||t.indexOf("x")<=0)?e:NaN}function go(t){return!isNaN(fo(t))}function yo(){return Math.round(9*Math.random())}function mo(t,e){return null==t?e:null==e?t:t*e/function t(e,n){return 0===n?e:t(n,e%n)}(t,e)}function f(t){throw new Error(t)}function vo(t,e,n){return(e-t)*n+t}var _o="series\0";function xo(t){return t instanceof Array?t:null==t?[]:[t]}function wo(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var bo=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function So(t){return!L(t)||F(t)||t instanceof Date?t:t.value}function Mo(t,n,e){var o,a,s,l,r,u,i,h,c,p,d="normalMerge"===e,f="replaceMerge"===e,g="replaceAll"===e,y=(t=t||[],n=(n||[]).slice(),B()),e=(R(n,function(t,e){L(t)||(n[e]=null)}),function(t,e,n){var i=[];if("replaceAll"!==n)for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||Do(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,y,e));return(d||f)&&(o=e,a=t,s=y,R(l=n,function(t,e){var n,i,r;t&&null!=t.id&&(n=Co(t.id),null!=(i=s.get(n)))&&(Ct(!(r=o[i]).newOption,'Duplicated option on id "'+n+'".'),r.newOption=t,r.existing=a[i],l[e]=null)})),d&&(r=e,R(u=n,function(t,e){if(t&&null!=t.name)for(var n=0;n<r.length;n++){var i=r[n].existing;if(!r[n].newOption&&i&&(null==i.id||null==t.id)&&!Do(t)&&!Do(i)&&To("name",i,t))return r[n].newOption=t,void(u[e]=null)}})),d||f?(h=e,c=f,R(n,function(t){if(t){for(var e,n=0;(e=h[n])&&(e.newOption||Do(e.existing)||e.existing&&null!=t.id&&!To("id",t,e.existing));)n++;e?(e.newOption=t,e.brandNew=c):h.push({newOption:t,brandNew:c,existing:null,keyInfo:null}),n++}})):g&&(i=e,R(n,function(t){i.push({newOption:t,brandNew:!0,existing:null,keyInfo:null})})),t=e,p=B(),R(t,function(t){var e=t.existing;e&&p.set(e.id,t)}),R(t,function(t){var e=t.newOption;Ct(!e||null==e.id||!p.get(e.id)||p.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&p.set(e.id,t),t.keyInfo||(t.keyInfo={})}),R(t,function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(L(i)){if(r.name=null!=i.name?Co(i.name):n?n.name:_o+e,n)r.id=Co(n.id);else if(null!=i.id)r.id=Co(i.id);else for(var o=0;r.id="\0"+r.name+"\0"+o++,p.get(r.id););p.set(r.id,t)}}),e}function To(t,e,n){e=Io(e[t],null),n=Io(n[t],null);return null!=e&&null!=n&&e===n}function Co(t){return Io(t,"")}function Io(t,e){return null==t?e:V(t)?t:ft(t)||dt(t)?t+"":e}function ko(t){t=t.name;return!(!t||!t.indexOf(_o))}function Do(t){return t&&null!=t.id&&0===Co(t.id).indexOf("\0_ec_\0")}function Ao(t,r,o){R(t,function(t){var e,n,i=t.newOption;L(i)&&(t.keyInfo.mainType=r,t.keyInfo.subType=(e=r,i=i,t=t.existing,n=o,i.type||(t?t.subType:n.determineSubType(e,i))))})}function Po(e,t){return null!=t.dataIndexInside?t.dataIndexInside:null!=t.dataIndex?F(t.dataIndex)?z(t.dataIndex,function(t){return e.indexOfRawIndex(t)}):e.indexOfRawIndex(t.dataIndex):null!=t.name?F(t.name)?z(t.name,function(t){return e.indexOfName(t)}):e.indexOfName(t.name):void 0}function Lo(){var e="__ec_inner_"+Oo++;return function(t){return t[e]||(t[e]={})}}var Oo=yo();function Ro(n,t,i){var t=Eo(t,i),e=t.mainTypeSpecified,r=t.queryOptionMap,o=t.others,a=i?i.defaultMainType:null;return!e&&a&&r.set(a,{}),r.each(function(t,e){t=No(n,e,t,{useDefault:a===e,enableAll:!i||null==i.enableAll||i.enableAll,enableNone:!i||null==i.enableNone||i.enableNone});o[e+"Models"]=t.models,o[e+"Model"]=t.models[0]}),o}function Eo(t,i){var e=V(t)?((e={})[t+"Index"]=0,e):t,r=B(),o={},a=!1;return R(e,function(t,e){var n;"dataIndex"===e||"dataIndexInside"===e?o[e]=t:(n=(e=e.match(/^(\w+)(Index|Id|Name)$/)||[])[1],e=(e[2]||"").toLowerCase(),!n||!e||i&&i.includeMainTypes&&I(i.includeMainTypes,n)<0||(a=a||!!n,(r.get(n)||r.set(n,{}))[e]=t))}),{mainTypeSpecified:a,queryOptionMap:r,others:o}}var Bo={useDefault:!0,enableAll:!1,enableNone:!1};function No(t,e,n,i){i=i||Bo;var r=n.index,o=n.id,n=n.name,a={models:null,specified:null!=r||null!=o||null!=n};return a.specified?"none"===r||!1===r?(Ct(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),a.models=[]):("all"===r&&(Ct(i.enableAll,'`"all"` is not a valid value on index option.'),r=o=n=null),a.models=t.queryComponents({mainType:e,index:r,id:o,name:n})):(r=void 0,a.models=i.useDefault&&(r=t.getComponent(e))?[r]:[]),a}function zo(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}var Fo=".",Vo="___EC__COMPONENT__CONTAINER___",Ho="___EC__EXTENDED_CLASS___";function Wo(t){var e={main:"",sub:""};return t&&(t=t.split(Fo),e.main=t[0]||"",e.sub=t[1]||""),e}function Go(t){(t.$constructor=t).extend=function(t){var e,n,i,r=this;function o(){return n.apply(this,arguments)||this}return k(i=r)&&/^class\s/.test(Function.prototype.toString.call(i))?(u(o,n=r),e=o):ot(e=function(){(t.$constructor||r).apply(this,arguments)},this),O(e.prototype,t),e[Ho]=!0,e.extend=this.extend,e.superCall=Yo,e.superApply=qo,e.superClass=r,e}}function Uo(t,e){t.extend=e.extend}var Xo=Math.round(10*Math.random());function Yo(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function qo(t,e,n){return this.superClass.prototype[e].apply(t,n)}function Zo(t){var r={};t.registerClass=function(t){var e,n=t.type||t.prototype.type;return n&&(Ct(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(e=n),'componentType "'+e+'" illegal'),(e=Wo(t.prototype.type=n)).sub?e.sub!==Vo&&(function(t){var e=r[t.main];e&&e[Vo]||(e=r[t.main]={___EC__COMPONENT__CONTAINER___:!0});return e}(e)[e.sub]=t):r[e.main]=t),t},t.getClass=function(t,e,n){var i=r[t];if(i&&i[Vo]&&(i=e?i[e]:null),n&&!i)throw new Error(e?"Component "+t+"."+(e||"")+" is used but not imported.":t+".type should be specified.");return i},t.getClassesByMainType=function(t){var t=Wo(t),n=[],t=r[t.main];return t&&t[Vo]?R(t,function(t,e){e!==Vo&&n.push(t)}):n.push(t),n},t.hasClass=function(t){t=Wo(t);return!!r[t.main]},t.getAllClassMainTypes=function(){var n=[];return R(r,function(t,e){n.push(e)}),n},t.hasSubTypes=function(t){t=Wo(t),t=r[t.main];return t&&t[Vo]}}function jo(a,s){for(var t=0;t<a.length;t++)a[t][1]||(a[t][1]=a[t][0]);return s=s||!1,function(t,e,n){for(var i={},r=0;r<a.length;r++){var o=a[r][1];e&&0<=I(e,o)||n&&I(n,o)<0||null!=(o=t.getShallow(o,s))&&(i[a[r][0]]=o)}return i}}var Ko=jo([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),$o=(Qo.prototype.getAreaStyle=function(t,e){return Ko(this,t,e)},Qo);function Qo(){}var Jo=new ni(50);function ta(t,e,n,i,r){return t?"string"==typeof t?(e&&e.__zrImageSrc===t||!n||(n={hostEl:n,cb:i,cbPayload:r},(i=Jo.get(t))?na(e=i.image)||i.pending.push(n):((e=H.loadImage(t,ea,ea)).__zrImageSrc=t,Jo.put(t,e.__cachedImgObj={image:e,pending:[n]}))),e):t:e}function ea(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function na(t){return t&&t.width&&t.height}var ia=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function ra(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=oa(e,n,i,r);for(var a=0,s=o.length;a<s;a++)o[a]=aa(o[a],r);return o.join("\n")}function oa(t,e,n,i){for(var r=O({},i=i||{}),o=(r.font=e,n=E(n,"..."),r.maxIterations=E(i.maxIterations,2),r.minChar=E(i.minChar,0)),a=(r.cnCharWidth=br("国",e),r.ascCharWidth=br("a",e)),s=(r.placeholder=E(i.placeholder,""),t=Math.max(0,t-1)),l=0;l<o&&a<=s;l++)s-=a;i=br(n,e);return s<i&&(n="",i=0),s=t-i,r.ellipsis=n,r.ellipsisWidth=i,r.contentWidth=s,r.containerWidth=t,r}function aa(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var o=br(t,i);if(!(o<=n)){for(var a=0;;a++){if(o<=r||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?function(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}(t,r,e.ascCharWidth,e.cnCharWidth):0<o?Math.floor(t.length*r/o):0,o=br(t=t.substr(0,s),i)}""===t&&(t=e.placeholder)}return t}var sa=function(){},la=function(t){this.tokens=[],t&&(this.tokens=t)},ua=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]};function ha(t,e){var n=new ua;if(null!=t&&(t+=""),t){for(var i,r=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==r?null:{width:r,accumWidth:0,breakAll:"breakAll"===a},l=ia.lastIndex=0;null!=(i=ia.exec(t));){var u=i.index;l<u&&ca(n,t.substring(l,u),e,s),ca(n,i[2],e,s,i[1]),l=ia.lastIndex}l<t.length&&ca(n,t.substring(l,t.length),e,s);var h,c=[],p=0,d=0,f=e.padding,g="truncate"===a,y="truncate"===e.lineOverflow;t:for(var m=0;m<n.lines.length;m++){for(var v=n.lines[m],_=0,x=0,w=0;w<v.tokens.length;w++){var b=(D=v.tokens[w]).styleName&&e.rich[D.styleName]||{},S=D.textPadding=b.padding,M=S?S[1]+S[3]:0,T=D.font=b.font||e.font,C=(D.contentHeight=Ir(T),E(b.height,D.contentHeight));if(D.innerHeight=C,S&&(C+=S[0]+S[2]),D.height=C,D.lineHeight=St(b.lineHeight,e.lineHeight,C),D.align=b&&b.align||e.align,D.verticalAlign=b&&b.verticalAlign||"middle",y&&null!=o&&p+D.lineHeight>o){0<w?(v.tokens=v.tokens.slice(0,w),P(v,x,_),n.lines=n.lines.slice(0,m+1)):n.lines=n.lines.slice(0,m);break t}var I,S=b.width,k=null==S||"auto"===S;"string"==typeof S&&"%"===S.charAt(S.length-1)?(D.percentWidth=S,c.push(D),D.contentWidth=br(D.text,T)):(k&&(S=(S=b.backgroundColor)&&S.image)&&(I=void 0,na(S="string"==typeof(h=S)?(I=Jo.get(h))&&I.image:h))&&(D.width=Math.max(D.width,S.width*C/S.height)),null!=(I=g&&null!=r?r-x:null)&&I<D.width?!k||I<M?(D.text="",D.width=D.contentWidth=0):(D.text=ra(D.text,I-M,T,e.ellipsis,{minChar:e.truncateMinChar}),D.width=D.contentWidth=br(D.text,T)):D.contentWidth=br(D.text,T)),D.width+=M,x+=D.width,b&&(_=Math.max(_,D.lineHeight))}P(v,x,_)}n.outerWidth=n.width=E(r,d),n.outerHeight=n.height=E(o,p),n.contentHeight=p,n.contentWidth=d,f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]);for(m=0;m<c.length;m++){var D,A=(D=c[m]).percentWidth;D.width=parseInt(A,10)/100*n.width}}return n;function P(t,e,n){t.width=e,t.lineHeight=n,p+=n,d=Math.max(d,e)}}function ca(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;i?(n=(t=l.padding)?t[1]+t[3]:0,null!=l.width&&"auto"!==l.width?(t=kr(l.width,i.width)+n,0<u.length&&t+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=t):(t=da(e,h,i.width,i.breakAll,i.accumWidth),i.accumWidth=t.accumWidth+n,a=t.linesWidths,o=t.lines)):o=e.split("\n");for(var p=0;p<o.length;p++){var d,f,g=o[p],y=new sa;y.styleName=r,y.text=g,y.isLineHolder=!g&&!s,"number"==typeof l.width?y.width=l.width:y.width=a?a[p]:br(g,h),p||c?u.push(new la([y])):1===(f=(d=(u[u.length-1]||(u[0]=new la)).tokens).length)&&d[0].isLineHolder?d[0]=y:!g&&f&&!s||d.push(y)}}var pa=lt(",&?/;] ".split(""),function(t,e){return t[e]=!0,t},{});function da(t,e,n,i,r){for(var o,a=[],s=[],l="",u="",h=0,c=0,p=0;p<t.length;p++){var d,f,g=t.charAt(p);"\n"===g?(u&&(l+=u,c+=h),a.push(l),s.push(c),u=l="",c=h=0):(d=br(g,e),f=!(i||(f=void 0,!(32<=(f=(f=o=g).charCodeAt(0))&&f<=591||880<=f&&f<=4351||4608<=f&&f<=5119||7680<=f&&f<=8303))||!!pa[o]),(a.length?n<c+d:n<r+c+d)?c?(l||u)&&(c=f?(l||(l=u,u="",c=h=0),a.push(l),s.push(c-h),u+=g,l="",h+=d):(u&&(l+=u,u="",h=0),a.push(l),s.push(c),l=g,d)):f?(a.push(u),s.push(h),u=g,h=d):(a.push(g),s.push(d)):(c+=d,f?(u+=g,h+=d):(u&&(l+=u,u="",h=0),l+=g)))}return a.length||l||(l=t,u="",h=0),u&&(l+=u),l&&(a.push(l),s.push(c)),1===a.length&&(c+=r),{accumWidth:c,lines:a,linesWidths:s}}var fa,ga="__zr_style_"+Math.round(10*Math.random()),ya={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},ma={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}},va=(ya[ga]=!0,["z","z2","invisible"]),_a=["invisible"],n=(u(o,fa=n),o.prototype._init=function(t){for(var e=ht(t),n=0;n<e.length;n++){var i=e[n];"style"===i?this.useStyle(t[i]):fa.prototype.attrKV.call(this,i,t[i])}this.style||this.useStyle({})},o.prototype.beforeBrush=function(){},o.prototype.afterBrush=function(){},o.prototype.innerBeforeBrush=function(){},o.prototype.innerAfterBrush=function(){},o.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){xa.copy(t.getBoundingRect()),t.transform&&xa.applyTransform(t.transform);return wa.width=e,wa.height=n,!xa.intersect(wa)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},o.prototype.contain=function(t,e){return this.rectContain(t,e)},o.prototype.traverse=function(t,e){t.call(e,this)},o.prototype.rectContain=function(t,e){t=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(t[0],t[1])},o.prototype.getPaintRect=function(){var t,e,n,i,r,o=this._paintRect;return this._paintRect&&!this.__dirty||(r=this.transform,t=this.getBoundingRect(),e=(i=this.style).shadowBlur||0,n=i.shadowOffsetX||0,i=i.shadowOffsetY||0,o=this._paintRect||(this._paintRect=new X(0,0,0,0)),r?X.applyTransform(o,t,r):o.copy(t),(e||n||i)&&(o.width+=2*e+Math.abs(n),o.height+=2*e+Math.abs(i),o.x=Math.min(o.x,o.x+n-e),o.y=Math.min(o.y,o.y+i-e)),r=this.dirtyRectTolerance,o.isZero())||(o.x=Math.floor(o.x-r),o.y=Math.floor(o.y-r),o.width=Math.ceil(o.width+1+2*r),o.height=Math.ceil(o.height+1+2*r)),o},o.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new X(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},o.prototype.getPrevPaintRect=function(){return this._prevPaintRect},o.prototype.animateStyle=function(t){return this.animate("style",t)},o.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},o.prototype.attrKV=function(t,e){"style"!==t?fa.prototype.attrKV.call(this,t,e):this.style?this.setStyle(e):this.useStyle(e)},o.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:O(this.style,t),this.dirtyStyle(),this},o.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},o.prototype.dirty=function(){this.dirtyStyle()},o.prototype.styleChanged=function(){return!!(2&this.__dirty)},o.prototype.styleUpdated=function(){this.__dirty&=-3},o.prototype.createStyle=function(t){return Nt(ya,t)},o.prototype.useStyle=function(t){t[ga]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},o.prototype.isStyleObject=function(t){return t[ga]},o.prototype._innerSaveToNormal=function(t){fa.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.style&&!e.style&&(e.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,e,va)},o.prototype._applyStateObj=function(t,e,n,i,r,o){fa.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.style?r?i?a=e.style:(a=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(a,e.style)):(a=this._mergeStyle(this.createStyle(),(i?this:n).style),this._mergeStyle(a,e.style)):s&&(a=n.style),a)if(r){var l=this.style;if(this.style=this.createStyle(s?{}:l),s)for(var u=ht(l),h=0;h<u.length;h++)(p=u[h])in a&&(a[p]=a[p],this.style[p]=l[p]);for(var c=ht(a),h=0;h<c.length;h++){var p=c[h];this.style[p]=this.style[p]}this._transitionState(t,{style:a},o,this.getAnimationStyleProps())}else this.useStyle(a);for(var d=this.__inHover?_a:va,h=0;h<d.length;h++){p=d[h];e&&null!=e[p]?this[p]=e[p]:s&&null!=n[p]&&(this[p]=n[p])}},o.prototype._mergeStates=function(t){for(var e,n=fa.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.style&&this._mergeStyle(e=e||{},r.style)}return e&&(n.style=e),n},o.prototype._mergeStyle=function(t,e){return O(t,e),t},o.prototype.getAnimationStyleProps=function(){return ma},o.initDefaultProps=((n=o.prototype).type="displayable",n.invisible=!1,n.z=0,n.z2=0,n.zlevel=0,n.culling=!1,n.cursor="pointer",n.rectHover=!1,n.incremental=!1,n._rect=null,n.dirtyRectTolerance=0,void(n.__dirty=2|_n)),o);function o(t){return fa.call(this,t)||this}var xa=new X(0,0,0,0),wa=new X(0,0,0,0);var ba=Math.min,Sa=Math.max,Ma=Math.sin,Ta=Math.cos,Ca=2*Math.PI,Ia=Gt(),ka=Gt(),Da=Gt();function Aa(t,e,n,i,r,o){r[0]=ba(t,n),r[1]=ba(e,i),o[0]=Sa(t,n),o[1]=Sa(e,i)}var Pa=[],La=[];var Y={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Oa=[],Ra=[],Ea=[],Ba=[],Na=[],za=[],Fa=Math.min,Va=Math.max,Ha=Math.cos,Wa=Math.sin,Ga=Math.abs,Ua=Math.PI,Xa=2*Ua,Ya="undefined"!=typeof Float32Array,qa=[];function Za(t){return Math.round(t/Ua*1e8)/1e8%2*Ua}a.prototype.increaseVersion=function(){this._version++},a.prototype.getVersion=function(){return this._version},a.prototype.setScale=function(t,e,n){0<(n=n||0)&&(this._ux=Ga(n/ur/t)||0,this._uy=Ga(n/ur/e)||0)},a.prototype.setDPR=function(t){this.dpr=t},a.prototype.setContext=function(t){this._ctx=t},a.prototype.getContext=function(){return this._ctx},a.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},a.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},a.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Y.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},a.prototype.lineTo=function(t,e){var n=Ga(t-this._xi),i=Ga(e-this._yi),r=n>this._ux||i>this._uy;return this.addData(Y.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r?(this._xi=t,this._yi=e,this._pendingPtDist=0):(r=n*n+i*i)>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=r),this},a.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(Y.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},a.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(Y.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},a.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),qa[0]=i,qa[1]=r,s=o,(l=Za((a=qa)[0]))<0&&(l+=Xa),h=l-a[0],u=a[1],u+=h,!s&&Xa<=u-l?u=l+Xa:s&&Xa<=l-u?u=l-Xa:!s&&u<l?u=l+(Xa-Za(l-u)):s&&l<u&&(u=l-(Xa-Za(u-l))),a[0]=l,a[1]=u;var a,s,l,u,h=(r=qa[1])-(i=qa[0]);return this.addData(Y.A,t,e,n,n,i,h,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=Ha(r)*n+t,this._yi=Wa(r)*n+e,this},a.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},a.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Y.R,t,e,n,i),this},a.prototype.closePath=function(){this._drawPendingPt(),this.addData(Y.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},a.prototype.fill=function(t){t&&t.fill(),this.toStatic()},a.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},a.prototype.len=function(){return this._len},a.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!Ya||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},a.prototype.appendPath=function(t){for(var e=(t=t instanceof Array?t:[t]).length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();Ya&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},a.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},a.prototype._drawPendingPt=function(){0<this._pendingPtDist&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},a.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},a.prototype.toStatic=function(){var t;this._saveData&&(this._drawPendingPt(),(t=this.data)instanceof Array)&&(t.length=this._len,Ya)&&11<this._len&&(this.data=new Float32Array(t))},a.prototype.getBoundingRect=function(){Ea[0]=Ea[1]=Na[0]=Na[1]=Number.MAX_VALUE,Ba[0]=Ba[1]=za[0]=za[1]=-Number.MAX_VALUE;for(var t,e=this.data,n=0,i=0,r=0,o=0,a=0;a<this._len;){var B=e[a++],N=1===a;switch(N&&(r=n=e[a],o=i=e[a+1]),B){case Y.M:n=r=e[a++],i=o=e[a++],Na[0]=r,Na[1]=o,za[0]=r,za[1]=o;break;case Y.L:Aa(n,i,e[a],e[a+1],Na,za),n=e[a++],i=e[a++];break;case Y.C:W=H=m=y=V=g=f=d=p=c=F=z=h=u=l=s=void 0;var s=n,l=i,u=e[a++],h=e[a++],z=e[a++],F=e[a++],c=e[a],p=e[a+1],d=Na,f=za,g=Hn,V=zn,y=g(s,u,z,c,Pa);d[0]=1/0,d[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var m=0;m<y;m++){var H=V(s,u,z,c,Pa[m]);d[0]=ba(H,d[0]),f[0]=Sa(H,f[0])}for(y=g(l,h,F,p,La),m=0;m<y;m++){var W=V(l,h,F,p,La[m]);d[1]=ba(W,d[1]),f[1]=Sa(W,f[1])}d[0]=ba(s,d[0]),f[0]=Sa(s,f[0]),d[0]=ba(c,d[0]),f[0]=Sa(c,f[0]),d[1]=ba(l,d[1]),f[1]=Sa(l,f[1]),d[1]=ba(p,d[1]),f[1]=Sa(p,f[1]),n=e[a++],i=e[a++];break;case Y.Q:g=n,L=i,M=e[a++],x=e[a++],S=e[a],v=e[a+1],b=Na,T=za,t=w=t=_=void 0,_=Un,t=Sa(ba((w=Yn)(g,M,S),1),0),w=Sa(ba(w(L,x,v),1),0),M=_(g,M,S,t),t=_(L,x,v,w),b[0]=ba(g,S,M),b[1]=ba(L,v,t),T[0]=Sa(g,S,M),T[1]=Sa(L,v,t),n=e[a++],i=e[a++];break;case Y.A:var v,_=e[a++],x=e[a++],w=e[a++],b=e[a++],S=e[a++],M=e[a++]+S,T=(a+=1,!e[a++]),C=(N&&(r=Ha(S)*w+_,o=Wa(S)*b+x),E=v=U=G=R=O=L=P=A=D=k=I=C=void 0,_),I=x,k=w,D=b,A=S,P=M,L=T,O=Na,R=za,G=ie,U=re;if((v=Math.abs(A-P))%Ca<1e-4&&1e-4<v)O[0]=C-k,O[1]=I-D,R[0]=C+k,R[1]=I+D;else{Ia[0]=Ta(A)*k+C,Ia[1]=Ma(A)*D+I,ka[0]=Ta(P)*k+C,ka[1]=Ma(P)*D+I,G(O,Ia,ka),U(R,Ia,ka),(A%=Ca)<0&&(A+=Ca),(P%=Ca)<0&&(P+=Ca),P<A&&!L?P+=Ca:A<P&&L&&(A+=Ca),L&&(v=P,P=A,A=v);for(var E=0;E<P;E+=Math.PI/2)A<E&&(Da[0]=Ta(E)*k+C,Da[1]=Ma(E)*D+I,G(O,Da,O),U(R,Da,R))}n=Ha(M)*w+_,i=Wa(M)*b+x;break;case Y.R:Aa(r=n=e[a++],o=i=e[a++],r+e[a++],o+e[a++],Na,za);break;case Y.Z:n=r,i=o}ie(Ea,Ea,Na),re(Ba,Ba,za)}return 0===a&&(Ea[0]=Ea[1]=Ba[0]=Ba[1]=0),new X(Ea[0],Ea[1],Ba[0]-Ea[0],Ba[1]-Ea[1])},a.prototype._calculateLength=function(){for(var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0,l=(this._pathSegLen||(this._pathSegLen=[]),this._pathSegLen),u=0,h=0,c=0;c<e;){var p=t[c++],d=1===c,f=(d&&(a=r=t[c],s=o=t[c+1]),-1);switch(p){case Y.M:r=a=t[c++],o=s=t[c++];break;case Y.L:var g=t[c++],y=(_=t[c++])-o;(Ga(I=g-r)>n||Ga(y)>i||c===e-1)&&(f=Math.sqrt(I*I+y*y),r=g,o=_);break;case Y.C:var m=t[c++],v=t[c++],g=t[c++],_=t[c++],x=t[c++],w=t[c++],f=function(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1/l,d=1;d<=l;d++){var f=d*p,g=zn(t,n,r,a,f),f=zn(e,i,o,s,f),y=g-u,m=f-h;c+=Math.sqrt(y*y+m*m),u=g,h=f}return c}(r,o,m,v,g,_,x,w,10),r=x,o=w;break;case Y.Q:f=function(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1/a,c=1;c<=a;c++){var p=c*h,d=Un(t,n,r,p),p=Un(e,i,o,p),f=d-s,g=p-l;u+=Math.sqrt(f*f+g*g),s=d,l=p}return u}(r,o,m=t[c++],v=t[c++],g=t[c++],_=t[c++],10),r=g,o=_;break;case Y.A:var x=t[c++],w=t[c++],b=t[c++],S=t[c++],M=t[c++],T=t[c++],C=T+M;c+=1,d&&(a=Ha(M)*b+x,s=Wa(M)*S+w),f=Va(b,S)*Fa(Xa,Math.abs(T)),r=Ha(C)*b+x,o=Wa(C)*S+w;break;case Y.R:a=r=t[c++],s=o=t[c++];f=2*t[c++]+2*t[c++];break;case Y.Z:var I=a-r,y=s-o;f=Math.sqrt(I*I+y*y),r=a,o=s}0<=f&&(u+=l[h++]=f)}return this._pathLen=u},a.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h=this.data,B=this._ux,N=this._uy,z=this._len,c=e<1,p=0,d=0,f=0;if(!c||(this._pathSegLen||this._calculateLength(),a=this._pathSegLen,s=e*this._pathLen))t:for(var g=0;g<z;){var y=h[g++],F=1===g;switch(F&&(n=r=h[g],i=o=h[g+1]),y!==Y.L&&0<f&&(t.lineTo(l,u),f=0),y){case Y.M:n=r=h[g++],i=o=h[g++],t.moveTo(r,o);break;case Y.L:var m=h[g++],v=h[g++],_=Ga(m-r),x=Ga(v-o);if(B<_||N<x){if(c){if(s<p+(E=a[d++])){var w=(s-p)/E;t.lineTo(r*(1-w)+m*w,o*(1-w)+v*w);break t}p+=E}t.lineTo(m,v),r=m,o=v,f=0}else{_=_*_+x*x;f<_&&(l=m,u=v,f=_)}break;case Y.C:var b=h[g++],S=h[g++],M=h[g++],T=h[g++],x=h[g++],_=h[g++];if(c){if(s<p+(E=a[d++])){Wn(r,b,M,x,w=(s-p)/E,Oa),Wn(o,S,T,_,w,Ra),t.bezierCurveTo(Oa[1],Ra[1],Oa[2],Ra[2],Oa[3],Ra[3]);break t}p+=E}t.bezierCurveTo(b,S,M,T,x,_),r=x,o=_;break;case Y.Q:b=h[g++],S=h[g++],M=h[g++],T=h[g++];if(c){if(s<p+(E=a[d++])){qn(r,b,M,w=(s-p)/E,Oa),qn(o,S,T,w,Ra),t.quadraticCurveTo(Oa[1],Ra[1],Oa[2],Ra[2]);break t}p+=E}t.quadraticCurveTo(b,S,M,T),r=M,o=T;break;case Y.A:var C=h[g++],I=h[g++],k=h[g++],D=h[g++],A=h[g++],P=h[g++],L=h[g++],V=!h[g++],H=D<k?k:D,O=.001<Ga(k-D),R=A+P,W=!1;if(c&&(s<p+(E=a[d++])&&(R=A+P*(s-p)/E,W=!0),p+=E),O&&t.ellipse?t.ellipse(C,I,k,D,L,A,R,V):t.arc(C,I,H,A,R,V),W)break t;F&&(n=Ha(A)*k+C,i=Wa(A)*D+I),r=Ha(R)*k+C,o=Wa(R)*D+I;break;case Y.R:n=r=h[g],i=o=h[g+1],m=h[g++],v=h[g++];var E,P=h[g++],O=h[g++];if(c){if(s<p+(E=a[d++])){L=s-p;t.moveTo(m,v),t.lineTo(m+Fa(L,P),v),0<(L-=P)&&t.lineTo(m+P,v+Fa(L,O)),0<(L-=O)&&t.lineTo(m+Va(P-L,0),v+O),0<(L-=P)&&t.lineTo(m,v+Va(O-L,0));break t}p+=E}t.rect(m,v,P,O);break;case Y.Z:if(c){if(s<p+(E=a[d++])){w=(s-p)/E;t.lineTo(r*(1-w)+n*w,o*(1-w)+i*w);break t}p+=E}t.closePath(),r=n,o=i}}},a.prototype.clone=function(){var t=new a,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},a.CMD=Y,a.initDefaultProps=((su=a.prototype)._saveData=!0,su._ux=0,su._uy=0,su._pendingPtDist=0,void(su._version=0));var ja=a;function a(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}function Ka(t,e,n,i,r,o,a){var s;if(0!==r)return s=0,!(e+(r=r)<a&&i+r<a||a<e-r&&a<i-r||t+r<o&&n+r<o||o<t-r&&o<n-r)&&(t===n?Math.abs(o-t)<=r/2:(o=(s=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n))*o/(s*s+1)<=r/2*r/2)}var $a=2*Math.PI;function Qa(t){return(t%=$a)<0&&(t+=$a),t}var Ja=2*Math.PI;function ts(t,e,n,i,r,o){return e<o&&i<o||o<e&&o<i||i===e?0:(n=(o=(o-e)/(i-e))*(n-t)+t)===r?1/0:r<n?1!=o&&0!=o?i<e?1:-1:i<e?.5:-.5:0}var es=ja.CMD,ns=2*Math.PI,is=1e-4;var rs=[-1,-1,-1],os=[-1,-1];function as(t,e,n,i,r,o,a,s,l,u){if(e<u&&i<u&&o<u&&s<u||u<e&&u<i&&u<o&&u<s)return 0;var h=Vn(e,i,o,s,u,rs);if(0===h)return 0;for(var c,p=0,d=-1,f=void 0,g=void 0,y=0;y<h;y++){var m=rs[y],v=0===m||1===m?.5:1;zn(t,n,r,a,m)<l||(d<0&&(d=Hn(e,i,o,s,os),os[1]<os[0]&&1<d&&(c=void 0,c=os[0],os[0]=os[1],os[1]=c),f=zn(e,i,o,s,os[0]),1<d)&&(g=zn(e,i,o,s,os[1])),2===d?m<os[0]?p+=f<e?v:-v:m<os[1]?p+=g<f?v:-v:p+=s<g?v:-v:m<os[0]?p+=f<e?v:-v:p+=s<f?v:-v)}return p}function ss(t,e,n,i,r,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;c=rs,h=(l=e)-2*(u=i)+(h=o),u=2*(u-l),l-=s=s,s=0,Bn(h)?Nn(u)&&0<=(p=-l/u)&&p<=1&&(c[s++]=p):Bn(l=u*u-4*h*l)?0<=(p=-u/(2*h))&&p<=1&&(c[s++]=p):0<l&&(d=(-u-(l=Dn(l)))/(2*h),0<=(p=(-u+l)/(2*h))&&p<=1&&(c[s++]=p),0<=d)&&d<=1&&(c[s++]=d);var l,u,h,c,p,d,f=s;if(0===f)return 0;var g=Yn(e,i,o);if(0<=g&&g<=1){for(var y=0,m=Un(e,i,o,g),v=0;v<f;v++){var _=0===rs[v]||1===rs[v]?.5:1;Un(t,n,r,rs[v])<a||(rs[v]<g?y+=m<e?_:-_:y+=o<m?_:-_)}return y}return _=0===rs[0]||1===rs[0]?.5:1,Un(t,n,r,rs[0])<a?0:o<e?_:-_}function ls(t,e,n,i,r){for(var o,a=t.data,s=t.len(),l=0,u=0,h=0,c=0,p=0,d=0;d<s;){var f=a[d++],g=1===d;switch(f===es.M&&1<d&&(n||(l+=ts(u,h,c,p,i,r))),g&&(c=u=a[d],p=h=a[d+1]),f){case es.M:u=c=a[d++],h=p=a[d++];break;case es.L:if(n){if(Ka(u,h,a[d],a[d+1],e,i,r))return!0}else l+=ts(u,h,a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case es.C:if(n){if(function(t,e,n,i,r,o,a,s,l,u,h){if(0!==l)return!(e+(l=l)<h&&i+l<h&&o+l<h&&s+l<h||h<e-l&&h<i-l&&h<o-l&&h<s-l||t+l<u&&n+l<u&&r+l<u&&a+l<u||u<t-l&&u<n-l&&u<r-l&&u<a-l)&&Gn(t,e,n,i,r,o,a,s,u,h,null)<=l/2}(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=as(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case es.Q:if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return!(e+(a=a)<l&&i+a<l&&o+a<l||l<e-a&&l<i-a&&l<o-a||t+a<s&&n+a<s&&r+a<s||s<t-a&&s<n-a&&s<r-a)&&Zn(t,e,n,i,r,o,s,l,null)<=a/2}(u,h,a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=ss(u,h,a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case es.A:var y=a[d++],m=a[d++],v=a[d++],_=a[d++],x=a[d++],w=a[d++],b=(d+=1,!!(1-a[d++])),S=Math.cos(x)*v+y,M=Math.sin(x)*_+m,T=(g?(c=S,p=M):l+=ts(u,h,S,M,i,r),(i-y)*_/v+y);if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return a=a,s-=t,l-=e,!(n<(t=Math.sqrt(s*s+l*l))-a||t+a<n)&&(Math.abs(i-r)%Ja<1e-4||((r=o?(e=i,i=Qa(r),Qa(e)):(i=Qa(i),Qa(r)))<i&&(r+=Ja),(t=Math.atan2(l,s))<0&&(t+=Ja),i<=t&&t<=r)||i<=t+Ja&&t+Ja<=r)}(y,m,_,x,x+w,b,e,T,r))return!0}else l+=function(t,e,n,i,r,o,a,s){if(n<(s-=e)||s<-n)return 0;var e=Math.sqrt(n*n-s*s);if(rs[0]=-e,rs[1]=e,(n=Math.abs(i-r))<1e-4)return 0;if(ns-1e-4<=n)return r=ns,h=o?1:-1,a>=rs[i=0]+t&&a<=rs[1]+t?h:0;r<i&&(e=i,i=r,r=e),i<0&&(i+=ns,r+=ns);for(var l=0,u=0;u<2;u++){var h,c=rs[u];a<c+t&&(h=o?1:-1,i<=(c=(c=Math.atan2(s,c))<0?ns+c:c)&&c<=r||i<=c+ns&&c+ns<=r)&&(l+=h=c>Math.PI/2&&c<1.5*Math.PI?-h:h)}return l}(y,m,_,x,x+w,b,T,r);u=Math.cos(x+w)*v+y,h=Math.sin(x+w)*_+m;break;case es.R:c=u=a[d++],p=h=a[d++];if(S=c+a[d++],M=p+a[d++],n){if(Ka(c,p,S,p,e,i,r)||Ka(S,p,S,M,e,i,r)||Ka(S,M,c,M,e,i,r)||Ka(c,M,c,p,e,i,r))return!0}else l=(l+=ts(S,p,S,M,i,r))+ts(c,M,c,p,i,r);break;case es.Z:if(n){if(Ka(u,h,c,p,e,i,r))return!0}else l+=ts(u,h,c,p,i,r);u=c,h=p}}return n||(t=h,o=p,Math.abs(t-o)<is)||(l+=ts(u,h,c,p,i,r)||0),0!==l}var us,hs=N({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},ya),cs={style:N({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},ma.style)},ps=xr.concat(["invisible","culling","z","z2","zlevel","parent"]),j=(u(s,us=n),s.prototype.update=function(){var e=this,t=(us.prototype.update.call(this),this.style);if(t.decal){var n,i=this._decalEl=this._decalEl||new s,r=(i.buildPath===s.prototype.buildPath&&(i.buildPath=function(t){e.buildPath(t,e.shape)}),i.silent=!0,i.style);for(n in t)r[n]!==t[n]&&(r[n]=t[n]);r.fill=t.fill?t.decal:null,r.decal=null,r.shadowColor=null,t.strokeFirst&&(r.stroke=null);for(var o=0;o<ps.length;++o)i[ps[o]]=this[ps[o]];i.__dirty|=_n}else this._decalEl&&(this._decalEl=null)},s.prototype.getDecalElement=function(){return this._decalEl},s.prototype._init=function(t){var e=ht(t),n=(this.shape=this.getDefaultShape(),this.getDefaultStyle());n&&this.useStyle(n);for(var i=0;i<e.length;i++){var r=e[i],o=t[r];"style"===r?this.style?O(this.style,o):this.useStyle(o):"shape"===r?O(this.shape,o):us.prototype.attrKV.call(this,r,o)}this.style||this.useStyle({})},s.prototype.getDefaultStyle=function(){return null},s.prototype.getDefaultShape=function(){return{}},s.prototype.canBeInsideText=function(){return this.hasFill()},s.prototype.getInsideTextFill=function(){var t,e=this.style.fill;if("none"!==e){if(V(e))return.5<(t=Si(e,0))?hr:.2<t?"#eee":cr;if(e)return cr}return hr},s.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(V(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())==Si(t,0)<.4)return e}},s.prototype.buildPath=function(t,e,n){},s.prototype.pathUpdated=function(){this.__dirty&=~xn},s.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},s.prototype.createPathProxy=function(){this.path=new ja(!1)},s.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))},s.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},s.prototype.getBoundingRect=function(){var t,e,n=this._rect,i=this.style,r=!n;return r&&(t=!1,this.path||(t=!0,this.createPathProxy()),e=this.path,(t||this.__dirty&xn)&&(e.beginPath(),this.buildPath(e,this.shape,!1),this.pathUpdated()),n=e.getBoundingRect()),this._rect=n,this.hasStroke()&&this.path&&0<this.path.len()?(t=this._rectStroke||(this._rectStroke=n.clone()),(this.__dirty||r)&&(t.copy(n),e=i.strokeNoScale?this.getLineScale():1,r=i.lineWidth,this.hasFill()||(i=this.strokeContainThreshold,r=Math.max(r,null==i?4:i)),1e-10<e)&&(t.width+=r/e,t.height+=r/e,t.x-=r/e/2,t.y-=r/e/2),t):n},s.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){n=this.path;if(this.hasStroke()){i=r.lineWidth,r=r.strokeNoScale?this.getLineScale():1;if(1e-10<r&&(this.hasFill()||(i=Math.max(i,this.strokeContainThreshold)),ls(n,i/r,!0,t,e)))return!0}if(this.hasFill())return ls(n,0,!1,t,e)}return!1},s.prototype.dirtyShape=function(){this.__dirty|=xn,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},s.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},s.prototype.animateShape=function(t){return this.animate("shape",t)},s.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},s.prototype.attrKV=function(t,e){"shape"===t?this.setShape(e):us.prototype.attrKV.call(this,t,e)},s.prototype.setShape=function(t,e){var n=(n=this.shape)||(this.shape={});return"string"==typeof t?n[t]=e:O(n,t),this.dirtyShape(),this},s.prototype.shapeChanged=function(){return!!(this.__dirty&xn)},s.prototype.createStyle=function(t){return Nt(hs,t)},s.prototype._innerSaveToNormal=function(t){us.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.shape&&!e.shape&&(e.shape=O({},this.shape))},s.prototype._applyStateObj=function(t,e,n,i,r,o){us.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.shape?r?i?a=e.shape:(a=O({},n.shape),O(a,e.shape)):(a=O({},(i?this:n).shape),O(a,e.shape)):s&&(a=n.shape),a)if(r){this.shape=O({},this.shape);for(var l={},u=ht(a),h=0;h<u.length;h++){var c=u[h];"object"==typeof a[c]?this.shape[c]=a[c]:l[c]=a[c]}this._transitionState(t,{shape:l},o)}else this.shape=a,this.dirtyShape()},s.prototype._mergeStates=function(t){for(var e,n=us.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.shape&&this._mergeStyle(e=e||{},r.shape)}return e&&(n.shape=e),n},s.prototype.getAnimationStyleProps=function(){return cs},s.prototype.isZeroArea=function(){return!1},s.extend=function(n){u(r,i=s),r.prototype.getDefaultStyle=function(){return y(n.style)},r.prototype.getDefaultShape=function(){return y(n.shape)};var i,t,e=r;function r(t){var e=i.call(this,t)||this;return n.init&&n.init.call(e,t),e}for(t in n)"function"==typeof n[t]&&(e.prototype[t]=n[t]);return e},s.initDefaultProps=((su=s.prototype).type="path",su.strokeContainThreshold=5,su.segmentIgnoreThreshold=0,su.subPixelOptimize=!1,su.autoBatch=!1,void(su.__dirty=2|_n|xn)),s);function s(t){return us.call(this,t)||this}var ds,fs=N({strokeFirst:!0,font:K,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},hs),gs=(u(ys,ds=n),ys.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&0<t.lineWidth},ys.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},ys.prototype.createStyle=function(t){return Nt(fs,t)},ys.prototype.setBoundingRect=function(t){this._rect=t},ys.prototype.getBoundingRect=function(){var t,e=this.style;return this._rect||(null!=(t=e.text)?t+="":t="",(t=Mr(t,e.font,e.textAlign,e.textBaseline)).x+=e.x||0,t.y+=e.y||0,this.hasStroke()&&(e=e.lineWidth,t.x-=e/2,t.y-=e/2,t.width+=e,t.height+=e),this._rect=t),this._rect},ys.initDefaultProps=void(ys.prototype.dirtyRectTolerance=10),ys);function ys(){return null!==ds&&ds.apply(this,arguments)||this}gs.prototype.type="tspan";var ms=N({x:0,y:0},ya),vs={style:N({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},ma.style)};u(ws,_s=n),ws.prototype.createStyle=function(t){return Nt(ms,t)},ws.prototype._getSize=function(t){var e,n=this.style,i=n[t];return null!=i?i:(i=(i=n.image)&&"string"!=typeof i&&i.width&&i.height?n.image:this.__image)?null==(e=n[n="width"===t?"height":"width"])?i[t]:i[t]/i[n]*e:0},ws.prototype.getWidth=function(){return this._getSize("width")},ws.prototype.getHeight=function(){return this._getSize("height")},ws.prototype.getAnimationStyleProps=function(){return vs},ws.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new X(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect};var _s,xs=ws;function ws(){return null!==_s&&_s.apply(this,arguments)||this}xs.prototype.type="image";var bs=Math.round;function Ss(t,e,n){var i,r,o;if(e)return i=e.x1,r=e.x2,o=e.y1,e=e.y2,t.x1=i,t.x2=r,t.y1=o,t.y2=e,(n=n&&n.lineWidth)&&(bs(2*i)===bs(2*r)&&(t.x1=t.x2=Ts(i,n,!0)),bs(2*o)===bs(2*e))&&(t.y1=t.y2=Ts(o,n,!0)),t}function Ms(t,e,n){var i,r,o;if(e)return i=e.x,r=e.y,o=e.width,e=e.height,t.x=i,t.y=r,t.width=o,t.height=e,(n=n&&n.lineWidth)&&(t.x=Ts(i,n,!0),t.y=Ts(r,n,!0),t.width=Math.max(Ts(i+o,n,!1)-t.x,0===o?0:1),t.height=Math.max(Ts(r+e,n,!1)-t.y,0===e?0:1)),t}function Ts(t,e,n){var i;return e?((i=bs(2*t))+bs(e))%2==0?i/2:(i+(n?1:-1))/2:t}var Cs,Is=function(){this.x=0,this.y=0,this.width=0,this.height=0},ks={},Ds=(u(As,Cs=j),As.prototype.getDefaultShape=function(){return new Is},As.prototype.buildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g;this.subPixelOptimize?(n=(a=Ms(ks,e,this.style)).x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a):(n=e.x,i=e.y,r=e.width,o=e.height),e.r?(a=t,p=(e=e).x,d=e.y,f=e.width,g=e.height,e=e.r,f<0&&(p+=f,f=-f),g<0&&(d+=g,g=-g),"number"==typeof e?s=l=u=h=e:e instanceof Array?1===e.length?s=l=u=h=e[0]:2===e.length?(s=u=e[0],l=h=e[1]):3===e.length?(s=e[0],l=h=e[1],u=e[2]):(s=e[0],l=e[1],u=e[2],h=e[3]):s=l=u=h=0,f<s+l&&(s*=f/(c=s+l),l*=f/c),f<u+h&&(u*=f/(c=u+h),h*=f/c),g<l+u&&(l*=g/(c=l+u),u*=g/c),g<s+h&&(s*=g/(c=s+h),h*=g/c),a.moveTo(p+s,d),a.lineTo(p+f-l,d),0!==l&&a.arc(p+f-l,d+l,l,-Math.PI/2,0),a.lineTo(p+f,d+g-u),0!==u&&a.arc(p+f-u,d+g-u,u,0,Math.PI/2),a.lineTo(p+h,d+g),0!==h&&a.arc(p+h,d+g-h,h,Math.PI/2,Math.PI),a.lineTo(p,d+s),0!==s&&a.arc(p+s,d+s,s,Math.PI,1.5*Math.PI)):t.rect(n,i,r,o)},As.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},As);function As(t){return Cs.call(this,t)||this}Ds.prototype.type="rect";var Ps,Ls={fill:"#000"},Os={style:N({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},ma.style)},Rs=(u(Es,Ps=n),Es.prototype.childrenRef=function(){return this._children},Es.prototype.update=function(){Ps.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var e=this._children[t];e.zlevel=this.zlevel,e.z=this.z,e.z2=this.z2,e.culling=this.culling,e.cursor=this.cursor,e.invisible=this.invisible}},Es.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):Ps.prototype.updateTransform.call(this)},Es.prototype.getLocalTransform=function(t){var e=this.innerTransformable;return e?e.getLocalTransform(t):Ps.prototype.getLocalTransform.call(this,t)},Es.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),Ps.prototype.getComputedTransform.call(this)},Es.prototype._updateSubTexts=function(){var t;this._childCursor=0,Vs(t=this.style),R(t.rich,Vs),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},Es.prototype.addSelfToZr=function(t){Ps.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=t},Es.prototype.removeSelfFromZr=function(t){Ps.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=null},Es.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new X(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),o=o.getLocalTransform(n);o?(t.copy(a),t.applyTransform(o),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},Es.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||Ls},Es.prototype.setTextContent=function(t){},Es.prototype._mergeStyle=function(t,e){var n,i;return e&&(n=e.rich,i=t.rich||n&&{},O(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i)),t},Es.prototype._mergeRich=function(t,e){for(var n=ht(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},O(t[r],e[r])}},Es.prototype.getAnimationStyleProps=function(){return Os},Es.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),(this._children[this._childCursor++]=e).__zr=this.__zr,e.parent=this,e},Es.prototype._updatePlainTexts=function(){for(var t,e=this.style,n=e.font||K,i=e.padding,r=function(t,e){null!=t&&(t+="");var n,i=e.overflow,r=e.padding,o=e.font,a="truncate"===i,s=Ir(o),l=E(e.lineHeight,s),u=!!e.backgroundColor,h="truncate"===e.lineOverflow,c=e.width,i=(n=null==c||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?da(t,e.font,c,"breakAll"===i,0).lines:[]).length*l,p=E(e.height,i);if(p<i&&h&&(h=Math.floor(p/l),n=n.slice(0,h)),t&&a&&null!=c)for(var d=oa(c,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),f=0;f<n.length;f++)n[f]=aa(n[f],d);for(var h=p,g=0,f=0;f<n.length;f++)g=Math.max(br(n[f],o),g);return null==c&&(c=g),t=g,r&&(h+=r[0]+r[2],t+=r[1]+r[3],c+=r[1]+r[3]),{lines:n,height:p,outerWidth:t=u?c:t,outerHeight:h,lineHeight:l,calculatedLineHeight:s,contentWidth:g,contentHeight:i,width:c}}(Us(e),e),o=Xs(e),a=!!e.backgroundColor,s=r.outerHeight,l=r.outerWidth,u=r.contentWidth,h=r.lines,c=r.lineHeight,p=this._defaultStyle,d=e.x||0,f=e.y||0,g=e.align||p.align||"left",y=e.verticalAlign||p.verticalAlign||"top",m=d,v=Cr(f,r.contentHeight,y),_=((o||i)&&(t=Tr(d,l,g),f=Cr(f,s,y),o)&&this._renderBackground(e,e,t,f,l,s),v+=c/2,i&&(m=Gs(d,g,i),"top"===y?v+=i[0]:"bottom"===y&&(v-=i[2])),0),o=!1,x=(Ws(("fill"in e?e:(o=!0,p)).fill)),w=(Hs("stroke"in e?e.stroke:a||p.autoStroke&&!o?null:(_=2,p.stroke))),b=0<e.textShadowBlur,S=null!=e.width&&("truncate"===e.overflow||"break"===e.overflow||"breakAll"===e.overflow),M=r.calculatedLineHeight,T=0;T<h.length;T++){var C=this._getOrCreateChild(gs),I=C.createStyle();C.useStyle(I),I.text=h[T],I.x=m,I.y=v,g&&(I.textAlign=g),I.textBaseline="middle",I.opacity=e.opacity,I.strokeFirst=!0,b&&(I.shadowBlur=e.textShadowBlur||0,I.shadowColor=e.textShadowColor||"transparent",I.shadowOffsetX=e.textShadowOffsetX||0,I.shadowOffsetY=e.textShadowOffsetY||0),I.stroke=w,I.fill=x,w&&(I.lineWidth=e.lineWidth||_,I.lineDash=e.lineDash,I.lineDashOffset=e.lineDashOffset||0),I.font=n,Fs(I,e),v+=c,S&&C.setBoundingRect(new X(Tr(I.x,e.width,I.textAlign),Cr(I.y,M,I.textBaseline),u,M))}},Es.prototype._updateRichTexts=function(){for(var t=this.style,e=ha(Us(t),t),n=e.width,i=e.outerWidth,r=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,l=this._defaultStyle,u=t.align||l.align,l=t.verticalAlign||l.verticalAlign,a=Tr(a,i,u),u=Cr(s,r,l),h=a,c=u,p=(o&&(h+=o[3],c+=o[0]),h+n),d=(Xs(t)&&this._renderBackground(t,t,a,u,i,r),!!t.backgroundColor),f=0;f<e.lines.length;f++){for(var g=e.lines[f],y=g.tokens,m=y.length,v=g.lineHeight,_=g.width,x=0,w=h,b=p,S=m-1,M=void 0;x<m&&(!(M=y[x]).align||"left"===M.align);)this._placeToken(M,t,v,c,w,"left",d),_-=M.width,w+=M.width,x++;for(;0<=S&&"right"===(M=y[S]).align;)this._placeToken(M,t,v,c,b,"right",d),_-=M.width,b-=M.width,S--;for(w+=(n-(w-h)-(p-b)-_)/2;x<=S;)M=y[x],this._placeToken(M,t,v,c,w+M.width/2,"center",d),w+=M.width,x++;c+=v}},Es.prototype._placeToken=function(t,e,n,i,r,o,a){var s=e.rich[t.styleName]||{},l=(s.text=t.text,t.verticalAlign),u=i+n/2;"top"===l?u=i+t.height/2:"bottom"===l&&(u=i+n-t.height/2);!t.isLineHolder&&Xs(s)&&this._renderBackground(s,e,"right"===o?r-t.width:"center"===o?r-t.width/2:r,u-t.height/2,t.width,t.height);var l=!!s.backgroundColor,i=t.textPadding,n=(i&&(r=Gs(r,o,i),u-=t.height/2-i[0]-t.innerHeight/2),this._getOrCreateChild(gs)),i=n.createStyle(),h=(n.useStyle(i),this._defaultStyle),c=!1,p=0,d=Ws(("fill"in s?s:"fill"in e?e:(c=!0,h)).fill),l=Hs("stroke"in s?s.stroke:"stroke"in e?e.stroke:l||a||h.autoStroke&&!c?null:(p=2,h.stroke)),a=0<s.textShadowBlur||0<e.textShadowBlur,c=(i.text=t.text,i.x=r,i.y=u,a&&(i.shadowBlur=s.textShadowBlur||e.textShadowBlur||0,i.shadowColor=s.textShadowColor||e.textShadowColor||"transparent",i.shadowOffsetX=s.textShadowOffsetX||e.textShadowOffsetX||0,i.shadowOffsetY=s.textShadowOffsetY||e.textShadowOffsetY||0),i.textAlign=o,i.textBaseline="middle",i.font=t.font||K,i.opacity=St(s.opacity,e.opacity,1),Fs(i,s),l&&(i.lineWidth=St(s.lineWidth,e.lineWidth,p),i.lineDash=E(s.lineDash,e.lineDash),i.lineDashOffset=e.lineDashOffset||0,i.stroke=l),d&&(i.fill=d),t.contentWidth),h=t.contentHeight;n.setBoundingRect(new X(Tr(i.x,c,i.textAlign),Cr(i.y,h,i.textBaseline),c,h))},Es.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u,h=t.backgroundColor,c=t.borderWidth,p=t.borderColor,d=h&&h.image,f=h&&!d,g=t.borderRadius,y=this,g=((f||t.lineHeight||c&&p)&&((a=this._getOrCreateChild(Ds)).useStyle(a.createStyle()),a.style.fill=null,(l=a.shape).x=n,l.y=i,l.width=r,l.height=o,l.r=g,a.dirtyShape()),f?((u=a.style).fill=h||null,u.fillOpacity=E(t.fillOpacity,1)):d&&((s=this._getOrCreateChild(xs)).onload=function(){y.dirtyStyle()},(l=s.style).image=h.image,l.x=n,l.y=i,l.width=r,l.height=o),c&&p&&((u=a.style).lineWidth=c,u.stroke=p,u.strokeOpacity=E(t.strokeOpacity,1),u.lineDash=t.borderDash,u.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill())&&a.hasStroke()&&(u.strokeFirst=!0,u.lineWidth*=2),(a||s).style);g.shadowBlur=t.shadowBlur||0,g.shadowColor=t.shadowColor||"transparent",g.shadowOffsetX=t.shadowOffsetX||0,g.shadowOffsetY=t.shadowOffsetY||0,g.opacity=St(t.opacity,e.opacity,1)},Es.makeFont=function(t){var e,n="";return(n=null!=(e=t).fontSize||e.fontFamily||e.fontWeight?[t.fontStyle,t.fontWeight,"string"!=typeof(e=t.fontSize)||-1===e.indexOf("px")&&-1===e.indexOf("rem")&&-1===e.indexOf("em")?isNaN(+e)?"12px":e+"px":e,t.fontFamily||"sans-serif"].join(" "):n)&&It(n)||t.textFont||t.font},Es);function Es(t){var e=Ps.call(this)||this;return e.type="text",e._children=[],e._defaultStyle=Ls,e.attr(t),e}var Bs={left:!0,right:1,center:1},Ns={top:1,bottom:1,middle:1},zs=["fontStyle","fontWeight","fontSize","fontFamily"];function Fs(t,e){for(var n=0;n<zs.length;n++){var i=zs[n],r=e[i];null!=r&&(t[i]=r)}}function Vs(t){var e;t&&(t.font=Rs.makeFont(t),e=t.align,t.align=null==(e="middle"===e?"center":e)||Bs[e]?e:"left",e=t.verticalAlign,t.verticalAlign=null==(e="center"===e?"middle":e)||Ns[e]?e:"top",t.padding)&&(t.padding=Tt(t.padding))}function Hs(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Ws(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Gs(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Us(t){t=t.text;return null!=t&&(t+=""),t}function Xs(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var D=Lo(),Ys=1,qs={},Zs=Lo(),js=Lo(),Ks=0,$s=1,Qs=2,Js=["emphasis","blur","select"],tl=["normal","emphasis","blur","select"],el="highlight",nl="downplay",il="select",rl="unselect",ol="toggleSelect";function al(t){return null!=t&&"none"!==t}function sl(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function ll(t){sl(t,"emphasis",Qs)}function ul(t){t.hoverState===Qs&&sl(t,"normal",Ks)}function hl(t){sl(t,"blur",$s)}function cl(t){t.hoverState===$s&&sl(t,"normal",Ks)}function pl(t){t.selected=!0}function dl(t){t.selected=!1}function fl(t,e,n){e(t,n)}function gl(t,e,n){fl(t,e,n),t.isGroup&&t.traverse(function(t){fl(t,e,n)})}function yl(t,e,n){var i=0<=I(t.currentStates,e),r=t.style.opacity,t=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveTo(o,e)}return o}(t,["opacity"],e,{opacity:1}),e=(n=n||{}).style||{};return null==e.opacity&&(n=O({},n),e=O({opacity:i?r:.1*t.opacity},e),n.style=e),n}function ml(t,e){var n,i,r,o,a,s=this.states[t];if(this.style){if("emphasis"===t)return n=this,i=s,e=(e=e)&&0<=I(e,"select"),a=!1,n instanceof j&&(r=Zs(n),o=e&&r.selectFill||r.normalFill,e=e&&r.selectStroke||r.normalStroke,al(o)||al(e))&&("inherit"===(r=(i=i||{}).style||{}).fill?(a=!0,i=O({},i),(r=O({},r)).fill=o):!al(r.fill)&&al(o)?(a=!0,i=O({},i),(r=O({},r)).fill=Ti(o)):!al(r.stroke)&&al(e)&&(a||(i=O({},i),r=O({},r)),r.stroke=Ti(e)),i.style=r),i&&null==i.z2&&(a||(i=O({},i)),o=n.z2EmphasisLift,i.z2=n.z2+(null!=o?o:10)),i;if("blur"===t)return yl(this,t,s);if("select"===t)return e=this,(r=s)&&null==r.z2&&(r=O({},r),a=e.z2SelectLift,r.z2=e.z2+(null!=a?a:9)),r}return s}function vl(t){t.stateProxy=ml;var e=t.getTextContent(),t=t.getTextGuideLine();e&&(e.stateProxy=ml),t&&(t.stateProxy=ml)}function _l(t,e){Cl(t,e)||t.__highByOuter||gl(t,ll)}function xl(t,e){Cl(t,e)||t.__highByOuter||gl(t,ul)}function wl(t,e){t.__highByOuter|=1<<(e||0),gl(t,ll)}function bl(t,e){(t.__highByOuter&=~(1<<(e||0)))||gl(t,ul)}function Sl(t){gl(t,cl)}function Ml(t){gl(t,pl)}function Tl(t){gl(t,dl)}function Cl(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Il(r){var e=r.getModel(),o=[],a=[];e.eachComponent(function(t,e){var n=js(e),t="series"===t,i=t?r.getViewOfSeriesModel(e):r.getViewOfComponentModel(e);t||a.push(i),n.isBlured&&(i.group.traverse(function(t){cl(t)}),t)&&o.push(e),n.isBlured=!1}),R(a,function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(o,!1,e)})}function kl(t,o,a,s){var l,u,h,n=s.getModel();function c(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&Sl(i)}}a=a||"coordinateSystem",null!=t&&o&&"none"!==o&&(l=n.getSeriesByIndex(t),(u=l.coordinateSystem)&&u.master&&(u=u.master),h=[],n.eachSeries(function(t){var e=l===t,n=t.coordinateSystem,n=(n=n&&n.master?n.master:n)&&u?n===u:e;if(!("series"===a&&!e||"coordinateSystem"===a&&!n||"series"===o&&e)){if(s.getViewOfSeriesModel(t).group.traverse(function(t){t.__highByOuter&&e&&"self"===o||hl(t)}),st(o))c(t.getData(),o);else if(L(o))for(var i=ht(o),r=0;r<i.length;r++)c(t.getData(i[r]),o[i[r]]);h.push(t),js(t).isBlured=!0}}),n.eachComponent(function(t,e){"series"!==t&&(t=s.getViewOfComponentModel(e))&&t.toggleBlurSeries&&t.toggleBlurSeries(h,!0,n)}))}function Dl(t,e,n){var i;null!=t&&null!=e&&(t=n.getModel().getComponent(t,e))&&(js(t).isBlured=!0,i=n.getViewOfComponentModel(t))&&i.focusBlurEnabled&&i.group.traverse(function(t){hl(t)})}function Al(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;t=i.getModel().getComponent(t,e);if(!t)return r;e=i.getViewOfComponentModel(t);if(!e||!e.findHighDownDispatchers)return r;for(var o,a=e.findHighDownDispatchers(n),s=0;s<a.length;s++)if("self"===D(a[s]).focus){o=!0;break}return{focusSelf:o,dispatchers:a}}function Pl(i){R(i.getAllData(),function(t){var e=t.data,n=t.type;e.eachItemGraphicEl(function(t,e){(i.isSelected(e,n)?Ml:Tl)(t)})})}function Ll(t,e,n){Bl(t,!0),gl(t,vl);t=D(t),null!=e?(t.focus=e,t.blurScope=n):t.focus&&(t.focus=null)}function Ol(t,e,n,i){i?Bl(t,!1):Ll(t,e,n)}var Rl=["emphasis","blur","select"],El={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function Bl(t,e){var e=!1===e,n=t;t.highDownSilentOnTouch&&(n.__highDownSilentOnTouch=t.highDownSilentOnTouch),e&&!n.__highDownDispatcher||(n.__highByOuter=n.__highByOuter||0,n.__highDownDispatcher=!e)}function Nl(t){return!(!t||!t.__highDownDispatcher)}function zl(t){t=t.type;return t===il||t===rl||t===ol}function Fl(t){t=t.type;return t===el||t===nl}var Vl=ja.CMD,Hl=[[],[],[]],Wl=Math.sqrt,Gl=Math.atan2;var Ul=Math.sqrt,Xl=Math.sin,Yl=Math.cos,ql=Math.PI;function Zl(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function jl(t,e){return(t[0]*e[0]+t[1]*e[1])/(Zl(t)*Zl(e))}function Kl(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(jl(t,e))}function $l(t,e,n,i,r,o,a,s,l,u,h){var l=l*(ql/180),c=Yl(l)*(t-n)/2+Xl(l)*(e-i)/2,p=-1*Xl(l)*(t-n)/2+Yl(l)*(e-i)/2,d=c*c/(a*a)+p*p/(s*s),d=(1<d&&(a*=Ul(d),s*=Ul(d)),(r===o?-1:1)*Ul((a*a*(s*s)-a*a*(p*p)-s*s*(c*c))/(a*a*(p*p)+s*s*(c*c)))||0),r=d*a*p/s,d=d*-s*c/a,t=(t+n)/2+Yl(l)*r-Xl(l)*d,n=(e+i)/2+Xl(l)*r+Yl(l)*d,e=Kl([1,0],[(c-r)/a,(p-d)/s]),i=[(c-r)/a,(p-d)/s],c=[(-1*c-r)/a,(-1*p-d)/s],r=Kl(i,c);jl(i,c)<=-1&&(r=ql),(r=1<=jl(i,c)?0:r)<0&&(p=Math.round(r/ql*1e6)/1e6,r=2*ql+p%2*ql),h.addData(u,t,n,a,s,e,r,l,o)}var Ql=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,Jl=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;u(nu,tu=j),nu.prototype.applyTransform=function(t){};var tu,eu=nu;function nu(){return null!==tu&&tu.apply(this,arguments)||this}function iu(t){return null!=t.setData}function ru(t,e){var S=function(t){var e=new ja;if(t){var n,i=0,r=0,o=i,a=r,s=ja.CMD,l=t.match(Ql);if(l){for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,d=h.match(Jl)||[],f=d.length,g=0;g<f;g++)d[g]=parseFloat(d[g]);for(var y=0;y<f;){var m=void 0,v=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=i,T=r,C=void 0,I=void 0;switch(c){case"l":i+=d[y++],r+=d[y++],p=s.L,e.addData(p,i,r);break;case"L":i=d[y++],r=d[y++],p=s.L,e.addData(p,i,r);break;case"m":i+=d[y++],r+=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=d[y++],r=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=d[y++],p=s.L,e.addData(p,i,r);break;case"H":i=d[y++],p=s.L,e.addData(p,i,r);break;case"v":r+=d[y++],p=s.L,e.addData(p,i,r);break;case"V":r=d[y++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,d[y++],d[y++],d[y++],d[y++],d[y++],d[y++]),i=d[y-2],r=d[y-1];break;case"c":p=s.C,e.addData(p,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r),i+=d[y-2],r+=d[y-1];break;case"S":m=i,v=r,C=e.len(),I=e.data,n===s.C&&(m+=i-I[C-4],v+=r-I[C-3]),p=s.C,M=d[y++],T=d[y++],i=d[y++],r=d[y++],e.addData(p,m,v,M,T,i,r);break;case"s":m=i,v=r,C=e.len(),I=e.data,n===s.C&&(m+=i-I[C-4],v+=r-I[C-3]),p=s.C,M=i+d[y++],T=r+d[y++],i+=d[y++],r+=d[y++],e.addData(p,m,v,M,T,i,r);break;case"Q":M=d[y++],T=d[y++],i=d[y++],r=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=d[y++]+i,T=d[y++]+r,i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":m=i,v=r,C=e.len(),I=e.data,n===s.Q&&(m+=i-I[C-4],v+=r-I[C-3]),i=d[y++],r=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"t":m=i,v=r,C=e.len(),I=e.data,n===s.Q&&(m+=i-I[C-4],v+=r-I[C-3]),i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"A":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],$l(M=i,T=r,i=d[y++],r=d[y++],b,S,_,x,w,p=s.A,e);break;case"a":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],$l(M=i,T=r,i+=d[y++],r+=d[y++],b,S,_,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}e.toStatic()}}return e}(t),t=O({},e);return t.buildPath=function(t){var e;iu(t)?(t.setData(S.data),(e=t.getContext())&&t.rebuildPath(e,1)):S.rebuildPath(e=t,1)},t.applyTransform=function(t){var e=S,n=t;if(n){for(var i,r,o,a,s=e.data,l=e.len(),u=Vl.M,h=Vl.C,c=Vl.L,p=Vl.R,d=Vl.A,f=Vl.Q,g=0,y=0;g<l;){switch(i=s[g++],y=g,r=0,i){case u:case c:r=1;break;case h:r=3;break;case f:r=2;break;case d:var m=n[4],v=n[5],_=Wl(n[0]*n[0]+n[1]*n[1]),x=Wl(n[2]*n[2]+n[3]*n[3]),w=Gl(-n[1]/x,n[0]/_);s[g]*=_,s[g++]+=m,s[g]*=x,s[g++]+=v,s[g++]*=_,s[g++]*=x,s[g++]+=w,s[g++]+=w,y=g+=2;break;case p:a[0]=s[g++],a[1]=s[g++],ne(a,a,n),s[y++]=a[0],s[y++]=a[1],a[0]+=s[g++],a[1]+=s[g++],ne(a,a,n),s[y++]=a[0],s[y++]=a[1]}for(o=0;o<r;o++){var b=Hl[o];b[0]=s[g++],b[1]=s[g++],ne(b,b,n),s[y++]=b[0],s[y++]=b[1]}}e.increaseVersion()}this.dirtyShape()},t}var ou,au=function(){this.cx=0,this.cy=0,this.r=0},su=(u(lu,ou=j),lu.prototype.getDefaultShape=function(){return new au},lu.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},lu);function lu(t){return ou.call(this,t)||this}su.prototype.type="circle";var uu,hu=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},cu=(u(pu,uu=j),pu.prototype.getDefaultShape=function(){return new hu},pu.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=e.rx,e=e.ry,o=.5522848*r,a=.5522848*e;t.moveTo(n-r,i),t.bezierCurveTo(n-r,i-a,n-o,i-e,n,i-e),t.bezierCurveTo(n+o,i-e,n+r,i-a,n+r,i),t.bezierCurveTo(n+r,i+a,n+o,i+e,n,i+e),t.bezierCurveTo(n-o,i+e,n-r,i+a,n-r,i),t.closePath()},pu);function pu(t){return uu.call(this,t)||this}cu.prototype.type="ellipse";var du=Math.PI,fu=2*du,gu=Math.sin,yu=Math.cos,mu=Math.acos,vu=Math.atan2,_u=Math.abs,xu=Math.sqrt,wu=Math.max,bu=Math.min,Su=1e-4;function Mu(t,e,n,i,r,o,a){var s=t-n,l=e-i,a=(a?o:-o)/xu(s*s+l*l),l=a*l,a=-a*s,s=t+l,t=e+a,e=n+l,n=i+a,i=(s+e)/2,u=(t+n)/2,h=e-s,c=n-t,p=h*h+c*c,o=r-o,s=s*n-e*t,n=(c<0?-1:1)*xu(wu(0,o*o*p-s*s)),e=(s*c-h*n)/p,t=(-s*h-c*n)/p,d=(s*c+h*n)/p,s=(-s*h+c*n)/p,h=e-i,c=t-u,n=d-i,p=s-u;return n*n+p*p<h*h+c*c&&(e=d,t=s),{cx:e,cy:t,x0:-l,y0:-a,x1:e*(r/o-1),y1:t*(r/o-1)}}function Tu(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g,y,m,v,_,x,w,b,S,M,T,C,I,k,D,A,P,L=wu(e.r,0),O=wu(e.r0||0,0),R=0<L;(R||0<O)&&(R||(L=O,O=0),L<O&&(R=L,L=O,O=R),R=e.startAngle,n=e.endAngle,isNaN(R)||isNaN(n)||(i=e.cx,r=e.cy,o=!!e.clockwise,m=_u(n-R),Su<(a=fu<m&&m%fu)&&(m=a),Su<L?fu-Su<m?(t.moveTo(i+L*yu(R),r+L*gu(R)),t.arc(i,r,L,R,n,!o),Su<O&&(t.moveTo(i+O*yu(n),r+O*gu(n)),t.arc(i,r,O,n,R,o))):(S=b=w=x=_=v=c=h=I=C=T=M=u=l=s=a=void 0,p=L*yu(R),d=L*gu(R),f=O*yu(n),g=O*gu(n),(y=Su<m)&&((e=e.cornerRadius)&&(a=(e=function(t){if(F(t)){var e=t.length;if(!e)return t;e=1===e?[t[0],t[0],0,0]:2===e?[t[0],t[0],t[1],t[1]]:3===e?t.concat(t[2]):t}else e=[t,t,t,t];return e}(e))[0],s=e[1],l=e[2],u=e[3]),e=_u(L-O)/2,M=bu(e,l),T=bu(e,u),C=bu(e,a),I=bu(e,s),v=h=wu(M,T),_=c=wu(C,I),Su<h||Su<c)&&(x=L*yu(n),w=L*gu(n),b=O*yu(R),S=O*gu(R),m<du)&&(e=function(t,e,n,i,r,o,a,s){var l=(s=s-o)*(n=n-t)-(a=a-r)*(i=i-e);if(!(l*l<Su))return[t+(l=(a*(e-o)-s*(t-r))/l)*n,e+l*i]}(p,d,b,S,x,w,f,g))&&(M=p-e[0],T=d-e[1],C=x-e[0],I=w-e[1],m=1/gu(mu((M*C+T*I)/(xu(M*M+T*T)*xu(C*C+I*I)))/2),M=xu(e[0]*e[0]+e[1]*e[1]),v=bu(h,(L-M)/(1+m)),_=bu(c,(O-M)/(m-1))),y?Su<v?(k=bu(l,v),D=bu(u,v),A=Mu(b,S,p,d,L,k,o),P=Mu(x,w,f,g,L,D,o),t.moveTo(i+A.cx+A.x0,r+A.cy+A.y0),v<h&&k===D?t.arc(i+A.cx,r+A.cy,v,vu(A.y0,A.x0),vu(P.y0,P.x0),!o):(0<k&&t.arc(i+A.cx,r+A.cy,k,vu(A.y0,A.x0),vu(A.y1,A.x1),!o),t.arc(i,r,L,vu(A.cy+A.y1,A.cx+A.x1),vu(P.cy+P.y1,P.cx+P.x1),!o),0<D&&t.arc(i+P.cx,r+P.cy,D,vu(P.y1,P.x1),vu(P.y0,P.x0),!o))):(t.moveTo(i+p,r+d),t.arc(i,r,L,R,n,!o)):t.moveTo(i+p,r+d),Su<O&&y?Su<_?(k=bu(a,_),A=Mu(f,g,x,w,O,-(D=bu(s,_)),o),P=Mu(p,d,b,S,O,-k,o),t.lineTo(i+A.cx+A.x0,r+A.cy+A.y0),_<c&&k===D?t.arc(i+A.cx,r+A.cy,_,vu(A.y0,A.x0),vu(P.y0,P.x0),!o):(0<D&&t.arc(i+A.cx,r+A.cy,D,vu(A.y0,A.x0),vu(A.y1,A.x1),!o),t.arc(i,r,O,vu(A.cy+A.y1,A.cx+A.x1),vu(P.cy+P.y1,P.cx+P.x1),o),0<k&&t.arc(i+P.cx,r+P.cy,k,vu(P.y1,P.x1),vu(P.y0,P.x0),!o))):(t.lineTo(i+f,r+g),t.arc(i,r,O,n,R,o)):t.lineTo(i+f,r+g)):t.moveTo(i,r),t.closePath()))}var Cu,Iu=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},ku=(u(Du,Cu=j),Du.prototype.getDefaultShape=function(){return new Iu},Du.prototype.buildPath=function(t,e){Tu(t,e)},Du.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},Du);function Du(t){return Cu.call(this,t)||this}ku.prototype.type="sector";var Au,Pu=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},Lu=(u(Ou,Au=j),Ou.prototype.getDefaultShape=function(){return new Pu},Ou.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},Ou);function Ou(t){return Au.call(this,t)||this}function Ru(t,e,n){var i=e.smooth,r=e.points;if(r&&2<=r.length){if(i)for(var o=function(t,e,n,i){var r,o,a=[],s=[],l=[],u=[];if(i){for(var h=[1/0,1/0],c=[-1/0,-1/0],p=0,d=t.length;p<d;p++)ie(h,h,t[p]),re(c,c,t[p]);ie(h,h,i[0]),re(c,c,i[1])}for(p=0,d=t.length;p<d;p++){var f=t[p];if(n)r=t[p?p-1:d-1],o=t[(p+1)%d];else{if(0===p||p===d-1){a.push(Ut(t[p]));continue}r=t[p-1],o=t[p+1]}Yt(s,o,r),jt(s,s,e);var g=$t(f,r),y=$t(f,o),m=g+y,m=(0!==m&&(g/=m,y/=m),jt(l,s,-g),jt(u,s,y),Xt([],f,l)),g=Xt([],f,u);i&&(re(m,m,h),ie(m,m,c),re(g,g,h),ie(g,g,c)),a.push(m),a.push(g)}return n&&a.push(a.shift()),a}(r,i,n,e.smoothConstraint),a=(t.moveTo(r[0][0],r[0][1]),r.length),s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}else{t.moveTo(r[0][0],r[0][1]);for(var s=1,c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}Lu.prototype.type="ring";var Eu,Bu=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},Nu=(u(zu,Eu=j),zu.prototype.getDefaultShape=function(){return new Bu},zu.prototype.buildPath=function(t,e){Ru(t,e,!0)},zu);function zu(t){return Eu.call(this,t)||this}Nu.prototype.type="polygon";var Fu,Vu=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},Hu=(u(Wu,Fu=j),Wu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Wu.prototype.getDefaultShape=function(){return new Vu},Wu.prototype.buildPath=function(t,e){Ru(t,e,!1)},Wu);function Wu(t){return Fu.call(this,t)||this}Hu.prototype.type="polyline";var Gu,Uu={},Xu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},Yu=(u(qu,Gu=j),qu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},qu.prototype.getDefaultShape=function(){return new Xu},qu.prototype.buildPath=function(t,e){o=(this.subPixelOptimize?(n=(o=Ss(Uu,e,this.style)).x1,i=o.y1,r=o.x2,o):(n=e.x1,i=e.y1,r=e.x2,e)).y2;var n,i,r,o,e=e.percent;0!==e&&(t.moveTo(n,i),e<1&&(r=n*(1-e)+r*e,o=i*(1-e)+o*e),t.lineTo(r,o))},qu.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},qu);function qu(t){return Gu.call(this,t)||this}Yu.prototype.type="line";var Zu=[],ju=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function Ku(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?Fn:zn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?Fn:zn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?Xn:Un)(t.x1,t.cpx1,t.x2,e),(n?Xn:Un)(t.y1,t.cpy1,t.y2,e)]}u(Ju,$u=j),Ju.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Ju.prototype.getDefaultShape=function(){return new ju},Ju.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,e=e.percent;0!==e&&(t.moveTo(n,i),null==l||null==u?(e<1&&(qn(n,a,r,e,Zu),a=Zu[1],r=Zu[2],qn(i,s,o,e,Zu),s=Zu[1],o=Zu[2]),t.quadraticCurveTo(a,s,r,o)):(e<1&&(Wn(n,a,l,r,e,Zu),a=Zu[1],l=Zu[2],r=Zu[3],Wn(i,s,u,o,e,Zu),s=Zu[1],u=Zu[2],o=Zu[3]),t.bezierCurveTo(a,s,l,u,r,o)))},Ju.prototype.pointAt=function(t){return Ku(this.shape,t,!1)},Ju.prototype.tangentAt=function(t){t=Ku(this.shape,t,!0);return Kt(t,t)};var $u,Qu=Ju;function Ju(t){return $u.call(this,t)||this}Qu.prototype.type="bezier-curve";var th,eh=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},nh=(u(ih,th=j),ih.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},ih.prototype.getDefaultShape=function(){return new eh},ih.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,e=e.clockwise,s=Math.cos(o),l=Math.sin(o);t.moveTo(s*r+n,l*r+i),t.arc(n,i,r,o,a,!e)},ih);function ih(t){return th.call(this,t)||this}nh.prototype.type="arc";u(ah,rh=j),ah.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},ah.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},ah.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},ah.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},ah.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),j.prototype.getBoundingRect.call(this)};var rh,oh=ah;function ah(){var t=null!==rh&&rh.apply(this,arguments)||this;return t.type="compound",t}lh.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})};var sh=lh;function lh(t){this.colorStops=t||[]}u(ch,uh=sh);var uh,hh=ch;function ch(t,e,n,i,r,o){r=uh.call(this,r)||this;return r.x=null==t?0:t,r.y=null==e?0:e,r.x2=null==n?1:n,r.y2=null==i?0:i,r.type="linear",r.global=o||!1,r}u(dh,ph=sh);var ph,sh=dh;function dh(t,e,n,i,r){i=ph.call(this,i)||this;return i.x=null==t?.5:t,i.y=null==e?.5:e,i.r=null==n?.5:n,i.type="radial",i.global=r||!1,i}var fh=[0,0],gh=[0,0],yh=new M,mh=new M,vh=(_h.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width,t=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,t),n[3].set(r,t),e)for(var s=0;s<4;s++)n[s].transform(e);M.sub(i[0],n[1],n[0]),M.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(s=0;s<2;s++)this._origin[s]=i[s].dot(n[0])},_h.prototype.intersect=function(t,e){var n=!0,i=!e;return yh.set(1/0,1/0),mh.set(0,0),!this._intersectCheckOneSide(this,t,yh,mh,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,yh,mh,i,-1)&&(n=!1,i)||i||M.copy(e,n?yh:mh),n},_h.prototype._intersectCheckOneSide=function(t,e,n,i,r,o){for(var a=!0,s=0;s<2;s++){var l=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,fh),this._getProjMinMaxOnAxis(s,e._corners,gh),fh[1]<gh[0]||gh[1]<fh[0]){if(a=!1,r)return a;var u=Math.abs(gh[0]-fh[1]),h=Math.abs(fh[0]-gh[1]);Math.min(u,h)>i.len()&&(u<h?M.scale(i,l,-u*o):M.scale(i,l,h*o))}else n&&(u=Math.abs(gh[0]-fh[1]),h=Math.abs(fh[0]-gh[1]),Math.min(u,h)<n.len())&&(u<h?M.scale(n,l,u*o):M.scale(n,l,-h*o))}return a},_h.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++)var u=e[l].dot(i)+r[t],a=Math.min(u,a),s=Math.max(u,s);n[0]=a,n[1]=s},_h);function _h(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new M;for(n=0;n<2;n++)this._axes[n]=new M;t&&this.fromBoundingRect(t,e)}var xh,wh=[],n=(u(bh,xh=n),bh.prototype.traverse=function(t,e){t.call(e,this)},bh.prototype.useStyle=function(){this.style={}},bh.prototype.getCursor=function(){return this._cursor},bh.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},bh.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},bh.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},bh.prototype.addDisplayable=function(t,e){(e?this._temporaryDisplayables:this._displayables).push(t),this.markRedraw()},bh.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},bh.prototype.getDisplayables=function(){return this._displayables},bh.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},bh.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},bh.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;for(var e,t=0;t<this._temporaryDisplayables.length;t++)(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null},bh.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new X(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(wh)),t.union(i)}this._rect=t}return this._rect},bh.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},bh);function bh(){var t=null!==xh&&xh.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}var Sh=Lo();function Mh(t,e,n,i,r,o,a){var s,l,u,h,c,p,d=!1,f=(k(r)?(a=o,o=r,r=null):L(r)&&(o=r.cb,a=r.during,d=r.isFrom,l=r.removeOpt,r=r.dataIndex),"leave"===t),g=(f||e.stopAnimation("leave"),p=t,s=r,l=f?l||{}:null,i=(g=i)&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null,g&&g.ecModel&&(u=(u=g.ecModel.getUpdatePayload())&&u.animation),p="update"===p,g&&g.isAnimationEnabled()?(c=h=r=void 0,c=l?(r=E(l.duration,200),h=E(l.easing,"cubicOut"),0):(r=g.getShallow(p?"animationDurationUpdate":"animationDuration"),h=g.getShallow(p?"animationEasingUpdate":"animationEasing"),g.getShallow(p?"animationDelayUpdate":"animationDelay")),k(c=u&&(null!=u.duration&&(r=u.duration),null!=u.easing&&(h=u.easing),null!=u.delay)?u.delay:c)&&(c=c(s,i)),{duration:(r=k(r)?r(s):r)||0,delay:c,easing:h}):null);g&&0<g.duration?(p={duration:g.duration,delay:g.delay||0,easing:g.easing,done:o,force:!!o||!!a,setToFinal:!f,scope:t,during:a},d?e.animateFrom(n,p):e.animateTo(n,p)):(e.stopAnimation(),d||e.attr(n),a&&a(1),o&&o())}function Th(t,e,n,i,r,o){Mh("update",t,e,n,i,r,o)}function Ch(t,e,n,i,r,o){Mh("enter",t,e,n,i,r,o)}function Ih(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++)if("leave"===t.animators[e].scope)return!0;return!1}function kh(t,e,n,i,r,o){Ih(t)||Mh("leave",t,e,n,i,r,o)}function Dh(t,e,n,i){t.removeTextContent(),t.removeTextGuideLine(),kh(t,{style:{opacity:0}},e,n,i)}function Ah(t,e,n){function i(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse(function(t){t.isGroup||Dh(t,e,n,i)}):Dh(t,e,n,i)}var Ph=Math.max,Lh=Math.min,Oh={};function Rh(t){return j.extend(t)}var Eh=function(t,e){var n,i=ru(t,e);function r(t){t=n.call(this,t)||this;return t.applyTransform=i.applyTransform,t.buildPath=i.buildPath,t}return u(r,n=eu),r};function Bh(t,e){return Eh(t,e)}function Nh(t,e){Oh[t]=e}function zh(t){if(Oh.hasOwnProperty(t))return Oh[t]}function Fh(t,e,n,i){t=new eu(ru(t,e));return n&&("center"===i&&(n=Hh(n,t.getBoundingRect())),Gh(t,n)),t}function Vh(t,e,n){var i=new xs({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){"center"===n&&(t={width:t.width,height:t.height},i.setStyle(Hh(e,t)))}});return i}function Hh(t,e){var e=e.width/e.height,n=t.height*e,e=n<=t.width?t.height:(n=t.width)/e;return{x:t.x+t.width/2-n/2,y:t.y+t.height/2-e/2,width:n,height:e}}function Wh(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}return(e=new j(e)).createPathProxy(),e.buildPath=function(t){var e;iu(t)&&(t.appendPath(n),e=t.getContext())&&t.rebuildPath(e,1)},e}function Gh(t,e){t.applyTransform&&(e=t.getBoundingRect().calculateTransform(e),t.applyTransform(e))}function Uh(t,e){return Ss(t,t,{lineWidth:e}),t}var Xh=Ts;function Yh(t,e){for(var n=Re([]);t&&t!==e;)Be(n,t.getLocalTransform(),n),t=t.parent;return n}function qh(t,e,n){return e&&!st(e)&&(e=vr.getLocalTransform(e)),ne([],t,e=n?Ve([],e):e)}function Zh(t){return!t.isGroup}function jh(t,e,i){var r,n;function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};return null!=t.shape&&(e.shape=O({},t.shape)),e}t&&e&&(n={},t.traverse(function(t){Zh(t)&&t.anid&&(n[t.anid]=t)}),r=n,e.traverse(function(t){var e,n;Zh(t)&&t.anid&&(e=r[t.anid])&&(n=o(t),t.attr(o(e)),Th(t,n,i,D(t).dataIndex))}))}function Kh(t,n){return z(t,function(t){var e=t[0],e=Ph(e,n.x),t=(e=Lh(e,n.x+n.width),t[1]),t=Ph(t,n.y);return[e,Lh(t,n.y+n.height)]})}function $h(t,e){var n=Ph(t.x,e.x),i=Lh(t.x+t.width,e.x+e.width),r=Ph(t.y,e.y),t=Lh(t.y+t.height,e.y+e.height);if(n<=i&&r<=t)return{x:n,y:r,width:i-n,height:t-r}}function Qh(t,e,n){var e=O({rectHover:!0},e),i=e.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),N(i,n),new xs(e)):Fh(t.replace("path://",""),e,n,"center")}function Jh(t,e,n,i,r,o,a,s){var l,n=n-t,i=i-e,a=a-r,s=s-o,u=a*i-n*s;return!((l=u)<=1e-6&&-1e-6<=l||(r=((l=t-r)*i-n*(t=e-o))/u)<0||1<r||(i=(l*s-a*t)/u)<0||1<i)}function tc(t){var e=t.itemTooltipOption,n=t.componentModel,i=t.itemName,e=V(e)?{formatter:e}:e,r=n.mainType,n=n.componentIndex,o={componentType:r,name:i,$vars:["name"]},a=(o[r+"Index"]=n,t.formatterParamsExtra),t=(a&&R(ht(a),function(t){Ft(o,t)||(o[t]=a[t],o.$vars.push(t))}),D(t.el));t.componentMainType=r,t.componentIndex=n,t.tooltipConfig={name:i,option:N({content:i,encodeHTMLContent:!0,formatterParams:o},e)}}function ec(t,e){var n;(n=t.isGroup?e(t):n)||t.traverse(e)}function nc(t,e){if(t)if(F(t))for(var n=0;n<t.length;n++)ec(t[n],e);else ec(t,e)}Nh("circle",su),Nh("ellipse",cu),Nh("sector",ku),Nh("ring",Lu),Nh("polygon",Nu),Nh("polyline",Hu),Nh("rect",Ds),Nh("line",Yu),Nh("bezierCurve",Qu),Nh("arc",nh);var ic=Object.freeze({__proto__:null,Arc:nh,BezierCurve:Qu,BoundingRect:X,Circle:su,CompoundPath:oh,Ellipse:cu,Group:Wr,Image:xs,IncrementalDisplayable:n,Line:Yu,LinearGradient:hh,OrientedBoundingRect:vh,Path:j,Point:M,Polygon:Nu,Polyline:Hu,RadialGradient:sh,Rect:Ds,Ring:Lu,Sector:ku,Text:Rs,applyTransform:qh,clipPointsByRect:Kh,clipRectByRect:$h,createIcon:Qh,extendPath:Bh,extendShape:Rh,getShapeClass:zh,getTransform:Yh,groupTransition:jh,initProps:Ch,isElementRemoved:Ih,lineLineIntersect:Jh,linePolygonIntersect:function(t,e,n,i,r){for(var o=0,a=r[r.length-1];o<r.length;o++){var s=r[o];if(Jh(t,e,n,i,s[0],s[1],a[0],a[1]))return!0;a=s}},makeImage:Vh,makePath:Fh,mergePath:Wh,registerShape:Nh,removeElement:kh,removeElementWithFadeOut:Ah,resizePath:Gh,setTooltipConfig:tc,subPixelOptimize:Xh,subPixelOptimizeLine:Uh,subPixelOptimizeRect:function(t){return Ms(t.shape,t.shape,t.style),t},transformDirection:function(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),i=qh(["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0],e,n);return Math.abs(i[0])>Math.abs(i[1])?0<i[0]?"right":"left":0<i[1]?"bottom":"top"},traverseElements:nc,updateProps:Th}),rc={};function oc(t,e){for(var n=0;n<Js.length;n++){var i=Js[n],r=e[i],i=t.ensureState(i);i.style=i.style||{},i.style.text=r}var o=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(o,!0)}function ac(t,e,n){for(var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal,l={normal:i=null==(i=r?r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null):i)?k(t.defaultText)?t.defaultText(o,t,n):t.defaultText:i},u=0;u<Js.length;u++){var h=Js[u],c=e[h];l[h]=E(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function sc(t,e,n,i,r){var o,a={},s=a,l=t,u=n,h=i,c=r;u=u||rc;var p,t=l.ecModel,d=t&&t.option.textStyle,f=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||rc).rich;if(n){e=e||{};for(var i=ht(n),r=0;r<i.length;r++){var o=i[r];e[o]=1}}t=t.parentModel}return e}(l);if(f)for(var g in p={},f)f.hasOwnProperty(g)&&(o=l.getModel(["rich",g]),pc(p[g]={},o,d,u,h,c,!1,!0));return p&&(s.rich=p),(t=l.get("overflow"))&&(s.overflow=t),null!=(t=l.get("minMargin"))&&(s.margin=t),pc(s,l,d,u,h,c,!0,!1),e&&O(a,e),a}function lc(t,e,n){e=e||{};var i={},r=t.getShallow("rotate"),o=E(t.getShallow("distance"),n?null:5),a=t.getShallow("offset"),n=t.getShallow("position")||(n?null:"inside");return null!=(n="outside"===n?e.defaultOutsidePosition||"top":n)&&(i.position=n),null!=a&&(i.offset=a),null!=r&&(r*=Math.PI/180,i.rotation=r),null!=o&&(i.distance=o),i.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",i}var uc=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],hc=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],cc=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function pc(t,e,n,i,r,o,a,s){n=!r&&n||rc;var l=i&&i.inheritColor,u=e.getShallow("color"),h=e.getShallow("textBorderColor"),c=E(e.getShallow("opacity"),n.opacity),u=("inherit"!==u&&"auto"!==u||(u=l||null),"inherit"!==h&&"auto"!==h||(h=l||null),o||(u=u||n.color,h=h||n.textBorderColor),null!=u&&(t.fill=u),null!=h&&(t.stroke=h),E(e.getShallow("textBorderWidth"),n.textBorderWidth)),h=(null!=u&&(t.lineWidth=u),E(e.getShallow("textBorderType"),n.textBorderType)),u=(null!=h&&(t.lineDash=h),E(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset));null!=u&&(t.lineDashOffset=u),null!=(c=r||null!=c||s?c:i&&i.defaultOpacity)&&(t.opacity=c),r||o||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var p=0;p<uc.length;p++){var d=uc[p];null!=(f=E(e.getShallow(d),n[d]))&&(t[d]=f)}for(var p=0;p<hc.length;p++){d=hc[p];null!=(f=e.getShallow(d))&&(t[d]=f)}if(null==t.verticalAlign&&null!=(h=e.getShallow("baseline"))&&(t.verticalAlign=h),!a||!i.disableBox){for(p=0;p<cc.length;p++){var f,d=cc[p];null!=(f=e.getShallow(d))&&(t[d]=f)}u=e.getShallow("borderType");null!=u&&(t.borderDash=u),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!l||(t.backgroundColor=l),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!l||(t.borderColor=l)}}var dc=Lo();function fc(n,i,r,t,o){var a,s,l,u=dc(n);u.valueAnimation&&u.prevValue!==u.value&&(a=u.defaultInterpolatedText,s=E(u.interpolatedValue,u.prevValue),l=u.value,n.percent=0,(null==u.prevValue?Ch:Th)(n,{percent:1},t,i,null,function(t){var e=function(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if(ft(i))return no(p=vo(n||0,i,r),o?Math.max(io(n||0),io(i)):e);if(V(i))return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c,p,d=t.getDimensionInfo(h);d&&"ordinal"===d.type?a[h]=(r<1&&s?s:l)[h]:(p=vo(d=s&&s[h]?s[h]:0,c=l[h],r),a[h]=no(p,o?Math.max(io(d),io(c)):e))}return a}(r,u.precision,s,l,t),t=(u.interpolatedValue=1===t?null:e,ac({labelDataIndex:i,labelFetcher:o,defaultText:a?a(e):e+""},u.statesModels,e));oc(n,t)}))}var gc=["textStyle","color"],yc=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],mc=new Rs,Xh=(vc.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(gc):null)},vc.prototype.getFont=function(){return t={fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},e=(e=this.ecModel)&&e.getModel("textStyle"),It([t.fontStyle||e&&e.getShallow("fontStyle")||"",t.fontWeight||e&&e.getShallow("fontWeight")||"",(t.fontSize||e&&e.getShallow("fontSize")||12)+"px",t.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "));var t,e},vc.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<yc.length;n++)e[yc[n]]=this.getShallow(yc[n]);return mc.useStyle(e),mc.update(),mc.getBoundingRect()},vc);function vc(){}var _c=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],xc=jo(_c),wc=(bc.prototype.getLineStyle=function(t){return xc(this,t)},bc);function bc(){}var Sc=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],Mc=jo(Sc),Tc=(Cc.prototype.getItemStyle=function(t,e){return Mc(this,t,e)},Cc);function Cc(){}Dc.prototype.init=function(t,e,n){},Dc.prototype.mergeOption=function(t,e){d(this.option,t,!0)},Dc.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},Dc.prototype.getShallow=function(t,e){var n=this.option,n=null==n?n:n[t];return null!=n||e||(e=this.parentModel)&&(n=e.getShallow(t)),n},Dc.prototype.getModel=function(t,e){var n=null!=t,t=n?this.parsePath(t):null;return new Dc(n?this._doGet(t):this.option,e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(t)),this.ecModel)},Dc.prototype.isEmpty=function(){return null==this.option},Dc.prototype.restoreData=function(){},Dc.prototype.clone=function(){return new this.constructor(y(this.option))},Dc.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},Dc.prototype.resolveParentPath=function(t){return t},Dc.prototype.isAnimationEnabled=function(){if(!b.node&&this.option)return null!=this.option.animation?!!this.option.animation:this.parentModel?this.parentModel.isAnimationEnabled():void 0},Dc.prototype._doGet=function(t,e){var n=this.option;if(t){for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==typeof n?n[t[i]]:null));i++);null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel))}return n};var Ic,kc=Dc;function Dc(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}Go(kc),Uc=kc,Ic=["__\0is_clz",Xo++].join("_"),Uc.prototype[Ic]=!0,Uc.isInstance=function(t){return!(!t||!t[Ic])},at(kc,wc),at(kc,Tc),at(kc,$o),at(kc,Xh);var Ac=Math.round(10*Math.random());function Pc(t){return[t||"",Ac++].join("_")}var Lc="ZH",Oc="EN",Rc=Oc,Ec={},Bc={},Nc=b.domSupported&&-1<(document.documentElement.lang||navigator.language||navigator.browserLanguage||Rc).toUpperCase().indexOf(Lc)?Lc:Rc;function zc(t,e){t=t.toUpperCase(),Bc[t]=new kc(e),Ec[t]=e}zc(Oc,{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),zc(Lc,{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var Fc=1e3,Vc=60*Fc,Hc=60*Vc,Wc=24*Hc,Xo=365*Wc,Gc={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},Uc="{yyyy}-{MM}-{dd}",Xc={year:"{yyyy}",month:"{yyyy}-{MM}",day:Uc,hour:Uc+" "+Gc.hour,minute:Uc+" "+Gc.minute,second:Uc+" "+Gc.second,millisecond:Gc.none},Yc=["year","month","day","hour","minute","second","millisecond"],qc=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Zc(t,e){return"0000".substr(0,e-(t+="").length)+t}function jc(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function Kc(t,e,n,i){var t=uo(t),r=t[Jc(n)](),o=t[tp(n)]()+1,a=Math.floor((o-1)/3)+1,s=t[ep(n)](),l=t["get"+(n?"UTC":"")+"Day"](),u=t[np(n)](),h=(u-1)%12+1,c=t[ip(n)](),p=t[rp(n)](),t=t[op(n)](),n=12<=u?"pm":"am",d=n.toUpperCase(),i=(i instanceof kc?i:Bc[i||Nc]||Bc[Rc]).getModel("time"),f=i.get("month"),g=i.get("monthAbbr"),y=i.get("dayOfWeek"),i=i.get("dayOfWeekAbbr");return(e||"").replace(/{a}/g,n+"").replace(/{A}/g,d+"").replace(/{yyyy}/g,r+"").replace(/{yy}/g,Zc(r%100+"",2)).replace(/{Q}/g,a+"").replace(/{MMMM}/g,f[o-1]).replace(/{MMM}/g,g[o-1]).replace(/{MM}/g,Zc(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Zc(s,2)).replace(/{d}/g,s+"").replace(/{eeee}/g,y[l]).replace(/{ee}/g,i[l]).replace(/{e}/g,l+"").replace(/{HH}/g,Zc(u,2)).replace(/{H}/g,u+"").replace(/{hh}/g,Zc(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,Zc(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,Zc(p,2)).replace(/{s}/g,p+"").replace(/{SSS}/g,Zc(t,3)).replace(/{S}/g,t+"")}function $c(t,e){var t=uo(t),n=t[tp(e)]()+1,i=t[ep(e)](),r=t[np(e)](),o=t[ip(e)](),a=t[rp(e)](),t=0===t[op(e)](),e=t&&0===a,a=e&&0===o,o=a&&0===r,r=o&&1===i;return r&&1===n?"year":r?"month":o?"day":a?"hour":e?"minute":t?"second":"millisecond"}function Qc(t,e,n){var i=ft(t)?uo(t):t;switch(e=e||$c(t,n)){case"year":return i[Jc(n)]();case"half-year":return 6<=i[tp(n)]()?1:0;case"quarter":return Math.floor((i[tp(n)]()+1)/4);case"month":return i[tp(n)]();case"day":return i[ep(n)]();case"half-day":return i[np(n)]()/24;case"hour":return i[np(n)]();case"minute":return i[ip(n)]();case"second":return i[rp(n)]();case"millisecond":return i[op(n)]()}}function Jc(t){return t?"getUTCFullYear":"getFullYear"}function tp(t){return t?"getUTCMonth":"getMonth"}function ep(t){return t?"getUTCDate":"getDate"}function np(t){return t?"getUTCHours":"getHours"}function ip(t){return t?"getUTCMinutes":"getMinutes"}function rp(t){return t?"getUTCSeconds":"getSeconds"}function op(t){return t?"getUTCMilliseconds":"getMilliseconds"}function ap(t){return t?"setUTCMonth":"setMonth"}function sp(t){return t?"setUTCDate":"setDate"}function lp(t){return t?"setUTCHours":"setHours"}function up(t){return t?"setUTCMinutes":"setMinutes"}function hp(t){return t?"setUTCSeconds":"setSeconds"}function cp(t){return t?"setUTCMilliseconds":"setMilliseconds"}function pp(t){var e;return go(t)?(e=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(1<e.length?"."+e[1]:""):V(t)?t:"-"}function dp(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),t=e?t&&t.charAt(0).toUpperCase()+t.slice(1):t}var fp=Tt;function gp(t,e,n){function i(t){return t&&It(t)?t:"-"}function r(t){return null!=t&&!isNaN(t)&&isFinite(t)}var o="time"===e,a=t instanceof Date;if(o||a){var o=o?uo(t):t;if(!isNaN(+o))return Kc(o,"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}",n);if(a)return"-"}return"ordinal"===e?dt(t)?i(t):ft(t)&&r(t)?t+"":"-":r(o=fo(t))?pp(o):dt(t)?i(t):"boolean"==typeof t?t+"":"-"}function yp(t,e){return"{"+t+(null==e?"":e)+"}"}var mp=["a","b","c","d","e","f","g"];function vp(t,e,n){var i=(e=F(e)?e:[e]).length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=mp[o];t=t.replace(yp(a),yp(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(yp(mp[l],s),n?xe(u):u)}return t}function _p(t,e){var t=V(t)?{color:t,extraCssText:e}:t||{},n=t.color,i=t.type,r=(e=t.extraCssText,t.renderMode||"html");return n?"html"===r?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+xe(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+xe(n)+";"+(e||"")+'"></span>':{renderMode:r,content:"{"+(t.markerId||"markerX")+"|}  ",style:"subItem"===i?{width:4,height:4,borderRadius:2,backgroundColor:n}:{width:10,height:10,borderRadius:5,backgroundColor:n}}:""}function xp(t,e){return e=e||"transparent",V(t)?t:L(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}function wp(t,e){var n;"_blank"===e||"blank"===e?((n=window.open()).opener=null,n.location.href=t):window.open(t,e)}var bp=R,Sp=["left","right","top","bottom","width","height"],Mp=[["width","left","right"],["height","top","bottom"]];function Tp(a,s,l,u,h){var c=0,p=0,d=(null==u&&(u=1/0),null==h&&(h=1/0),0);s.eachChild(function(t,e){var n,i,r,o=t.getBoundingRect(),e=s.childAt(e+1),e=e&&e.getBoundingRect();d="horizontal"===a?(i=o.width+(e?-e.x+o.x:0),u<(n=c+i)||t.newline?(c=0,n=i,p+=d+l,o.height):Math.max(d,o.height)):(i=o.height+(e?-e.y+o.y:0),h<(r=p+i)||t.newline?(c+=d+l,p=0,r=i,o.width):Math.max(d,o.width)),t.newline||(t.x=c,t.y=p,t.markRedraw(),"horizontal"===a?c=n+l:p=r+l)})}function Cp(t,e,n){n=fp(n||0);var i=e.width,r=e.height,o=eo(t.left,i),a=eo(t.top,r),e=eo(t.right,i),s=eo(t.bottom,r),l=eo(t.width,i),u=eo(t.height,r),h=n[2]+n[0],c=n[1]+n[3],p=t.aspect;switch(isNaN(l)&&(l=i-e-c-o),isNaN(u)&&(u=r-s-h-a),null!=p&&(isNaN(l)&&isNaN(u)&&(i/r<p?l=.8*i:u=.8*r),isNaN(l)&&(l=p*u),isNaN(u))&&(u=l/p),isNaN(o)&&(o=i-e-l-c),isNaN(a)&&(a=r-s-u-h),t.left||t.right){case"center":o=i/2-l/2-n[3];break;case"right":o=i-l-c}switch(t.top||t.bottom){case"middle":case"center":a=r/2-u/2-n[0];break;case"bottom":a=r-u-h}o=o||0,a=a||0,isNaN(l)&&(l=i-c-o-(e||0)),isNaN(u)&&(u=r-h-a-(s||0));p=new X(o+n[3],a+n[0],l,u);return p.margin=n,p}function Ip(t){t=t.layoutMode||t.constructor.layoutMode;return L(t)?t:t?{type:t}:null}function kp(l,u,t){var h=t&&t.ignoreSize,t=(F(h)||(h=[h,h]),n(Mp[0],0)),e=n(Mp[1],1);function n(t,e){var n={},i=0,r={},o=0;if(bp(t,function(t){r[t]=l[t]}),bp(t,function(t){c(u,t)&&(n[t]=r[t]=u[t]),p(n,t)&&i++,p(r,t)&&o++}),h[e])p(u,t[1])?r[t[2]]=null:p(u,t[2])&&(r[t[1]]=null);else if(2!==o&&i){if(!(2<=i))for(var a=0;a<t.length;a++){var s=t[a];if(!c(n,s)&&c(l,s)){n[s]=l[s];break}}return n}return r}function c(t,e){return t.hasOwnProperty(e)}function p(t,e){return null!=t[e]&&"auto"!==t[e]}function i(t,e,n){bp(t,function(t){e[t]=n[t]})}i(Mp[0],l,t),i(Mp[1],l,e)}function Dp(t){return e={},(n=t)&&e&&bp(Sp,function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e;var e,n}pt(Tp,"vertical"),pt(Tp,"horizontal");var Ap,Pp,Lp,Op,Rp=Lo(),g=(u(Ep,Ap=kc),Ep.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},Ep.prototype.mergeDefaultAndTheme=function(t,e){var n=Ip(this),i=n?Dp(t):{};d(t,e.getTheme().get(this.mainType)),d(t,this.getDefaultOption()),n&&kp(t,i,n)},Ep.prototype.mergeOption=function(t,e){d(this.option,t,!0);var n=Ip(this);n&&kp(this.option,t,n)},Ep.prototype.optionUpdated=function(t,e){},Ep.prototype.getDefaultOption=function(){var t=this.constructor;if(!(e=t)||!e[Ho])return t.defaultOption;var e=Rp(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;0<=a;a--)o=d(o,n[a],!0);e.defaultOption=o}return e.defaultOption},Ep.prototype.getReferringComponents=function(t,e){var n=t+"Id";return No(this.ecModel,t,{index:this.get(t+"Index",!0),id:this.get(n,!0)},e)},Ep.prototype.getBoxLayoutParams=function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}},Ep.prototype.getZLevelKey=function(){return""},Ep.prototype.setZLevel=function(t){this.option.zlevel=t},Ep.protoInitialize=((wc=Ep.prototype).type="component",wc.id="",wc.name="",wc.mainType="",wc.subType="",void(wc.componentIndex=0)),Ep);function Ep(t,e,n){t=Ap.call(this,t,e,n)||this;return t.uid=Pc("ec_cpt_model"),t}function Bp(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}Uo(g,kc),Zo(g),Lp={},(Pp=g).registerSubTypeDefaulter=function(t,e){t=Wo(t);Lp[t.main]=e},Pp.determineSubType=function(t,e){var n,i=e.type;return i||(n=Wo(t).main,Pp.hasSubTypes(t)&&Lp[n]&&(i=Lp[n](e))),i},Op=function(t){var e=[];R(g.getClassesByMainType(t),function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])}),e=z(e,function(t){return Wo(t).main}),"dataset"!==t&&I(e,"dataset")<=0&&e.unshift("dataset");return e},g.topologicalTravel=function(t,e,n,i){if(t.length){a={},s=[],R(o=e,function(n){var e,i,r=Bp(a,n),t=r.originalDeps=Op(n),t=(e=o,i=[],R(t,function(t){0<=I(e,t)&&i.push(t)}),i);r.entryCount=t.length,0===r.entryCount&&s.push(n),R(t,function(t){I(r.predecessor,t)<0&&r.predecessor.push(t);var e=Bp(a,t);I(e.successor,t)<0&&e.successor.push(n)})});var o,a,s,e={graph:a,noEntryList:s},r=e.graph,l=e.noEntryList,u={};for(R(t,function(t){u[t]=!0});l.length;){var h=l.pop(),c=r[h],p=!!u[h];p&&(n.call(i,h,c.originalDeps.slice()),delete u[h]),R(c.successor,p?f:d)}R(u,function(){throw new Error("")})}function d(t){r[t].entryCount--,0===r[t].entryCount&&l.push(t)}function f(t){u[t]=!0,d(t)}};var Tc="",$o=("undefined"!=typeof navigator&&(Tc=navigator.platform||""),"rgba(0, 0, 0, 0.2)"),Np={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:$o,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:$o,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:$o,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:$o,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:$o,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:$o,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Tc.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},zp=B(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),Fp="original",Vp="arrayRows",Hp="objectRows",Wp="keyedColumns",Gp="typedArray",Up="unknown",Xp="column",Yp="row",qp={Must:1,Might:2,Not:3},Zp=Lo();function jp(n,t,e){var r,o,a,i,s,l={},u=Kp(t);return u&&n&&(r=[],o=[],t=t.ecModel,t=Zp(t).datasetMap,u=u.uid+"_"+e.seriesLayoutBy,R(n=n.slice(),function(t,e){t=L(t)?t:n[e]={name:t};"ordinal"===t.type&&null==a&&(a=e,i=c(t)),l[t.name]=[]}),s=t.get(u)||t.set(u,{categoryWayDim:i,valueWayDim:0}),R(n,function(t,e){var n,i=t.name,t=c(t);null==a?(n=s.valueWayDim,h(l[i],n,t),h(o,n,t),s.valueWayDim+=t):a===e?(h(l[i],0,t),h(r,0,t)):(n=s.categoryWayDim,h(l[i],n,t),h(o,n,t),s.categoryWayDim+=t)}),r.length&&(l.itemName=r),o.length)&&(l.seriesName=o),l;function h(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function c(t){t=t.dimsDef;return t?t.length:1}}function Kp(t){if(!t.get("data",!0))return No(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},Bo).models[0]}function $p(t,e){var n,i,r,o=t.data,a=t.sourceFormat,s=t.seriesLayoutBy,l=t.dimensionsDefine,u=t.startIndex,h=e;if(!yt(o)){if(l&&(L(l=l[h])?(i=l.name,r=l.type):V(l)&&(i=l)),null!=r)return"ordinal"===r?qp.Must:qp.Not;if(a===Vp){var c=o;if(s===Yp){for(var p=c[h],d=0;d<(p||[]).length&&d<5;d++)if(null!=(n=_(p[u+d])))return n}else for(d=0;d<c.length&&d<5;d++){var f=c[u+d];if(f&&null!=(n=_(f[h])))return n}}else if(a===Hp){var g=o;if(!i)return qp.Not;for(d=0;d<g.length&&d<5;d++)if((m=g[d])&&null!=(n=_(m[i])))return n}else if(a===Wp){l=o;if(!i)return qp.Not;if(!(p=l[i])||yt(p))return qp.Not;for(d=0;d<p.length&&d<5;d++)if(null!=(n=_(p[d])))return n}else if(a===Fp)for(var y=o,d=0;d<y.length&&d<5;d++){var m,v=So(m=y[d]);if(!F(v))return qp.Not;if(null!=(n=_(v[h])))return n}}return qp.Not;function _(t){var e=V(t);return null!=t&&Number.isFinite(Number(t))&&""!==t?e?qp.Might:qp.Not:e&&"-"!==t?qp.Must:void 0}}var Qp=B();var Jp,td,ed,nd=Lo(),id=(Lo(),rd.prototype.getColorFromPalette=function(t,e,n){var i=xo(this.get("color",!0)),r=this.get("colorLayer",!0),o=this,a=nd;return a=a(e=e||o),o=a.paletteIdx||0,(e=a.paletteNameMap=a.paletteNameMap||{}).hasOwnProperty(t)?e[t]:(r=(r=n==null||!r?i:od(r,n))||i)&&r.length?(n=r[o],t&&(e[t]=n),a.paletteIdx=(o+1)%r.length,n):void 0},rd.prototype.clearColorPalette=function(){var t,e;(e=nd)(t=this).paletteIdx=0,e(t).paletteNameMap={}},rd);function rd(){}function od(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}var ad,sd="\0_ec_inner",ld=(u(l,ad=kc),l.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new kc(i),this._locale=new kc(r),this._optionManager=o},l.prototype.setOption=function(t,e,n){e=cd(e);this._optionManager.setOption(t,n,e),this._resetOption(null,e)},l.prototype.resetOption=function(t,e){return this._resetOption(t,cd(e))},l.prototype._resetOption=function(t,e){var n,i=!1,r=this._optionManager;return t&&"recreate"!==t||(n=r.mountOption("recreate"===t),this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(n,e)):ed(this,n),i=!0),"timeline"!==t&&"media"!==t||this.restoreData(),t&&"recreate"!==t&&"timeline"!==t||(n=r.getTimelineOption(this))&&(i=!0,this._mergeOption(n,e)),t&&"recreate"!==t&&"media"!==t||(n=r.getMediaOption(this)).length&&R(n,function(t){i=!0,this._mergeOption(t,e)},this),i},l.prototype.mergeOption=function(t){this._mergeOption(t,null)},l.prototype._mergeOption=function(i,t){var r=this.option,h=this._componentsMap,c=this._componentsCount,n=[],o=B(),p=t&&t.replaceMergeMainTypeMap;Zp(this).datasetMap=B(),R(i,function(t,e){null!=t&&(g.hasClass(e)?e&&(n.push(e),o.set(e,!0)):r[e]=null==r[e]?y(t):d(r[e],t,!0))}),p&&p.each(function(t,e){g.hasClass(e)&&!o.get(e)&&(n.push(e),o.set(e,!0))}),g.topologicalTravel(n,g.getAllClassMainTypes(),function(o){var a,t=function(t,e,n){return(e=(e=Qp.get(e))&&e(t))?n.concat(e):n}(this,o,xo(i[o])),e=h.get(o),n=e?p&&p.get(o)?"replaceMerge":"normalMerge":"replaceAll",e=Mo(e,t,n),s=(Ao(e,o,g),r[o]=null,h.set(o,null),c.set(o,0),[]),l=[],u=0;R(e,function(t,e){var n=t.existing,i=t.newOption;if(i){var r=g.getClass(o,t.keyInfo.subType,!("series"===o));if(!r)return;if("tooltip"===o){if(a)return;a=!0}n&&n.constructor===r?(n.name=t.keyInfo.name,n.mergeOption(i,this),n.optionUpdated(i,!1)):(e=O({componentIndex:e},t.keyInfo),O(n=new r(i,this,this,e),e),t.brandNew&&(n.__requireNewView=!0),n.init(i,this,this),n.optionUpdated(null,!0))}else n&&(n.mergeOption({},this),n.optionUpdated({},!1));n?(s.push(n.option),l.push(n),u++):(s.push(void 0),l.push(void 0))},this),r[o]=s,h.set(o,l),c.set(o,u),"series"===o&&Jp(this)},this),this._seriesIndices||Jp(this)},l.prototype.getOption=function(){var a=y(this.option);return R(a,function(t,e){if(g.hasClass(e)){for(var n=xo(t),i=n.length,r=!1,o=i-1;0<=o;o--)n[o]&&!Do(n[o])?r=!0:(n[o]=null,r||i--);n.length=i,a[e]=n}}),delete a[sd],a},l.prototype.getTheme=function(){return this._theme},l.prototype.getLocaleModel=function(){return this._locale},l.prototype.setUpdatePayload=function(t){this._payload=t},l.prototype.getUpdatePayload=function(){return this._payload},l.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){t=n[e||0];if(t)return t;if(null==e)for(var i=0;i<n.length;i++)if(n[i])return n[i]}},l.prototype.queryComponents=function(t){var e,n,i,r,o,a=t.mainType;return a&&(e=t.index,n=t.id,i=t.name,r=this._componentsMap.get(a))&&r.length?(null!=e?(o=[],R(xo(e),function(t){r[t]&&o.push(r[t])})):o=null!=n?ud("id",n,r):null!=i?ud("name",i,r):ut(r,function(t){return!!t}),hd(o,t)):[]},l.prototype.findComponents=function(t){var e,n=t.query,i=t.mainType,r=(r=i+"Index",o=i+"Id",e=i+"Name",!(n=n)||null==n[r]&&null==n[o]&&null==n[e]?null:{mainType:i,index:n[r],id:n[o],name:n[e]}),o=r?this.queryComponents(r):ut(this._componentsMap.get(i),function(t){return!!t});return n=hd(o,t),t.filter?ut(n,t.filter):n},l.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(k(t)){var r=e,o=t;i.each(function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}})}else for(var a=V(t)?i.get(t):L(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},l.prototype.getSeriesByName=function(t){var e=Io(t,null);return ut(this._componentsMap.get("series"),function(t){return!!t&&null!=e&&t.name===e})},l.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},l.prototype.getSeriesByType=function(e){return ut(this._componentsMap.get("series"),function(t){return!!t&&t.subType===e})},l.prototype.getSeries=function(){return ut(this._componentsMap.get("series"),function(t){return!!t})},l.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},l.prototype.eachSeries=function(n,i){td(this),R(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)},this)},l.prototype.eachRawSeries=function(e,n){R(this._componentsMap.get("series"),function(t){t&&e.call(n,t,t.componentIndex)})},l.prototype.eachSeriesByType=function(n,i,r){td(this),R(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];e.subType===n&&i.call(r,e,t)},this)},l.prototype.eachRawSeriesByType=function(t,e,n){return R(this.getSeriesByType(t),e,n)},l.prototype.isSeriesFiltered=function(t){return td(this),null==this._seriesIndicesMap.get(t.componentIndex)},l.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},l.prototype.filterSeries=function(n,i){td(this);var r=[];R(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)&&r.push(t)},this),this._seriesIndices=r,this._seriesIndicesMap=B(r)},l.prototype.restoreData=function(n){Jp(this);var t=this._componentsMap,i=[];t.each(function(t,e){g.hasClass(e)&&i.push(e)}),g.topologicalTravel(i,g.getAllClassMainTypes(),function(e){R(t.get(e),function(t){!t||"series"===e&&function(t,e){{var n,i;if(e)return n=e.seriesIndex,i=e.seriesId,e=e.seriesName,null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=e&&t.name!==e}}(t,n)||t.restoreData()})})},l.internalField=(Jp=function(t){var e=t._seriesIndices=[];R(t._componentsMap.get("series"),function(t){t&&e.push(t.componentIndex)}),t._seriesIndicesMap=B(e)},td=function(t){},void(ed=function(t,e){t.option={},t.option[sd]=1,t._componentsMap=B({series:[]}),t._componentsCount=B();var n,i,r=e.aria;L(r)&&null==r.enabled&&(r.enabled=!0),n=e,r=t._theme.option,i=n.color&&!n.colorLayer,R(r,function(t,e){"colorLayer"===e&&i||g.hasClass(e)||("object"==typeof t?n[e]=n[e]?d(n[e],t,!1):y(t):null==n[e]&&(n[e]=t))}),d(e,Np,!1),t._mergeOption(e,null)})),l);function l(){return null!==ad&&ad.apply(this,arguments)||this}function ud(e,t,n){var i,r;return F(t)?(i=B(),R(t,function(t){null!=t&&null!=Io(t,null)&&i.set(t,!0)}),ut(n,function(t){return t&&i.get(t[e])})):(r=Io(t,null),ut(n,function(t){return t&&null!=r&&t[e]===r}))}function hd(t,e){return e.hasOwnProperty("subType")?ut(t,function(t){return t&&t.subType===e.subType}):t}function cd(t){var e=B();return t&&R(xo(t.replaceMerge),function(t){e.set(t,!0)}),{replaceMergeMainTypeMap:e}}at(ld,id);function pd(e){R(dd,function(t){this[t]=ct(e[t],e)},this)}var dd=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],fd={},gd=(yd.prototype.create=function(n,i){var r=[];R(fd,function(t,e){t=t.create(n,i);r=r.concat(t||[])}),this._coordinateSystems=r},yd.prototype.update=function(e,n){R(this._coordinateSystems,function(t){t.update&&t.update(e,n)})},yd.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},yd.register=function(t,e){fd[t]=e},yd.get=function(t){return fd[t]},yd);function yd(){this._coordinateSystems=[]}var md=/^(min|max)?(.+)$/,vd=(_d.prototype.setOption=function(t,e,n){t&&(R(xo(t.series),function(t){t&&t.data&&yt(t.data)&&Dt(t.data)}),R(xo(t.dataset),function(t){t&&t.source&&yt(t.source)&&Dt(t.source)})),t=y(t);var i=this._optionBackup,t=function(t,n,i){var e,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t);h&&F(u)&&R(u,function(t){t&&t.option&&(t.query?o.push(t):e=e||t)});function p(e){R(n,function(t){t(e,i)})}return p(r),R(l,p),R(o,function(t){return p(t.option)}),{baseOption:r,timelineOptions:l||[],mediaDefault:e,mediaList:o}}(t,e,!i);this._newBaseOption=t.baseOption,i?(t.timelineOptions.length&&(i.timelineOptions=t.timelineOptions),t.mediaList.length&&(i.mediaList=t.mediaList),t.mediaDefault&&(i.mediaDefault=t.mediaDefault)):this._optionBackup=t},_d.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],y(t?e.baseOption:this._newBaseOption)},_d.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;return e=n.length&&(t=t.getComponent("timeline"))?y(n[t.getCurrentIndex()]):e},_d.prototype.getMediaOption=function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,o=[],a=[];if(i.length||r){for(var s,l,u=0,h=i.length;u<h;u++)!function(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return R(t,function(t,e){var n,e=e.match(md);e&&e[1]&&e[2]&&(n=e[1],e=e[2].toLowerCase(),e=i[e],t=t,("min"===(n=n)?t<=e:"max"===n?e<=t:e===t)||(r=!1))}),r}(i[u].query,e,n)||o.push(u);(o=!o.length&&r?[-1]:o).length&&(s=o,l=this._currentMediaIndices,s.join(",")!==l.join(","))&&(a=z(o,function(t){return y((-1===t?r:i[t]).option)})),this._currentMediaIndices=o}return a},_d);function _d(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}var xd=R,wd=L,bd=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Sd(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=bd.length;n<i;n++){var r=bd[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?d(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?d(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function Md(t,e,n){var i,r;t&&t[e]&&(t[e].normal||t[e].emphasis)&&(i=t[e].normal,r=t[e].emphasis,i&&(n?(t[e].normal=t[e].emphasis=null,N(t[e],i)):t[e]=i),r)&&(t.emphasis=t.emphasis||{},(t.emphasis[e]=r).focus&&(t.emphasis.focus=r.focus),r.blurScope)&&(t.emphasis.blurScope=r.blurScope)}function Td(t){Md(t,"itemStyle"),Md(t,"lineStyle"),Md(t,"areaStyle"),Md(t,"label"),Md(t,"labelLine"),Md(t,"upperLabel"),Md(t,"edgeLabel")}function Cd(t,e){var n=wd(t)&&t[e],i=wd(n)&&n.textStyle;if(i)for(var r=0,o=bo.length;r<o;r++){var a=bo[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}function Id(t){t&&(Td(t),Cd(t,"label"),t.emphasis)&&Cd(t.emphasis,"label")}function kd(t){return F(t)?t:t?[t]:[]}function Dd(t){return(F(t)?t[0]:t)||{}}function Ad(e,t){xd(kd(e.series),function(t){if(wd(t))if(wd(t)){Sd(t),Td(t),Cd(t,"label"),Cd(t,"upperLabel"),Cd(t,"edgeLabel"),t.emphasis&&(Cd(t.emphasis,"label"),Cd(t.emphasis,"upperLabel"),Cd(t.emphasis,"edgeLabel"));var e=t.markPoint,n=(e&&(Sd(e),Id(e)),t.markLine),i=(n&&(Sd(n),Id(n)),t.markArea),r=(i&&Id(i),t.data);if("graph"===t.type){var r=r||t.nodes,o=t.links||t.edges;if(o&&!yt(o))for(var a=0;a<o.length;a++)Id(o[a]);R(t.categories,function(t){Td(t)})}if(r&&!yt(r))for(a=0;a<r.length;a++)Id(r[a]);if((e=t.markPoint)&&e.data)for(var s=e.data,a=0;a<s.length;a++)Id(s[a]);if((n=t.markLine)&&n.data)for(var l=n.data,a=0;a<l.length;a++)F(l[a])?(Id(l[a][0]),Id(l[a][1])):Id(l[a]);"gauge"===t.type?(Cd(t,"axisLabel"),Cd(t,"title"),Cd(t,"detail")):"treemap"===t.type?(Md(t.breadcrumb,"itemStyle"),R(t.levels,function(t){Td(t)})):"tree"===t.type&&Td(t.leaves)}});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),xd(n,function(t){xd(kd(e[t]),function(t){t&&(Cd(t,"axisLabel"),Cd(t.axisPointer,"label"))})}),xd(kd(e.parallel),function(t){t=t&&t.parallelAxisDefault;Cd(t,"axisLabel"),Cd(t&&t.axisPointer,"label")}),xd(kd(e.calendar),function(t){Md(t,"itemStyle"),Cd(t,"dayLabel"),Cd(t,"monthLabel"),Cd(t,"yearLabel")}),xd(kd(e.radar),function(t){Cd(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)}),xd(kd(e.geo),function(t){wd(t)&&(Id(t),xd(kd(t.regions),function(t){Id(t)}))}),xd(kd(e.timeline),function(t){Id(t),Md(t,"label"),Md(t,"itemStyle"),Md(t,"controlStyle",!0);t=t.data;F(t)&&R(t,function(t){L(t)&&(Md(t,"label"),Md(t,"itemStyle"))})}),xd(kd(e.toolbox),function(t){Md(t,"iconStyle"),xd(t.feature,function(t){Md(t,"iconStyle")})}),Cd(Dd(e.axisPointer),"label"),Cd(Dd(e.tooltip).axisPointer,"label")}function Pd(e){e&&R(Ld,function(t){t[0]in e&&!(t[1]in e)&&(e[t[1]]=e[t[0]])})}var Ld=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Od=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Rd=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Ed(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<Rd.length;n++){var i=Rd[n][1],r=Rd[n][0];null!=e[i]&&(e[r]=e[i])}}function Bd(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function Nd(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function zd(e,t){Ad(e,t),e.series=xo(e.series),R(e.series,function(t){if(L(t)){var e,n=t.type;if("line"===n)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===n||"gauge"===n){if(null!=t.clockWise&&(t.clockwise=t.clockWise),Bd(t.label),(e=t.data)&&!yt(e))for(var i=0;i<e.length;i++)Bd(e[i]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset)}else if("gauge"===n){var r=function(t,e){for(var n=e.split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t,"pointer.color");if(null!=r){var o=t;var a="itemStyle.color";var s=void 0;for(var l,u=a.split(","),h=o,c=0;c<u.length-1;c++)null==h[l=u[c]]&&(h[l]={}),h=h[l];!s&&null!=h[u[c]]||(h[u[c]]=r)}}else if("bar"===n){if(Ed(t),Ed(t.backgroundStyle),Ed(t.emphasis),(e=t.data)&&!yt(e))for(i=0;i<e.length;i++)"object"==typeof e[i]&&(Ed(e[i]),Ed(e[i]&&e[i].emphasis))}else"sunburst"===n?((a=t.highlightPolicy)&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=a)),Nd(t),function t(e,n){if(e)for(var i=0;i<e.length;i++)n(e[i]),e[i]&&t(e[i].children,n)}(t.data,Nd)):"graph"===n||"sankey"===n?(o=t)&&null!=o.focusNodeAdjacency&&(o.emphasis=o.emphasis||{},null==o.emphasis.focus)&&(o.emphasis.focus="adjacency"):"map"===n&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation)&&N(t,t.mapLocation);null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis)&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation),Pd(t)}}),e.dataRange&&(e.visualMap=e.dataRange),R(Od,function(t){t=e[t];t&&R(t=F(t)?t:[t],function(t){Pd(t)})})}function Fd(_){R(_,function(p,d){var f=[],g=[NaN,NaN],t=[p.stackResultDimension,p.stackedOverDimension],y=p.data,m=p.isStackedByIndex,v=p.seriesModel.get("stackStrategy")||"samesign";y.modify(t,function(t,e,n){var i,r,o=y.get(p.stackedDimension,n);if(isNaN(o))return g;m?r=y.getRawIndex(n):i=y.get(p.stackedByDimension,n);for(var a,s,l,u=NaN,h=d-1;0<=h;h--){var c=_[h];if(0<=(r=m?r:c.data.rawIndexOf(c.stackedByDimension,i))){c=c.data.getByRawIndex(c.stackResultDimension,r);if("all"===v||"positive"===v&&0<c||"negative"===v&&c<0||"samesign"===v&&0<=o&&0<c||"samesign"===v&&o<=0&&c<0){a=o,s=c,l=void 0,l=Math.max(io(a),io(s)),a+=s,o=Jr<l?a:no(a,l),u=c;break}}}return f[0]=o,f[1]=u,f})})}var Vd,Hd,Wd=function(t){this.data=t.data||(t.sourceFormat===Wp?{}:[]),this.sourceFormat=t.sourceFormat||Up,this.seriesLayoutBy=t.seriesLayoutBy||Xp,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&$p(this,n)===qp.Must&&(i.type="ordinal")}};function Gd(t){return t instanceof Wd}function Ud(t,e,n){n=n||Yd(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a;if(!t)return{dimensionsDefine:qd(r),startIndex:a,dimensionsDetectedCount:o};{var s;e===Vp?(s=t,"auto"===i||null==i?Zd(function(t){null!=t&&"-"!==t&&(V(t)?null==a&&(a=1):a=0)},n,s,10):a=ft(i)?i:i?1:0,r||1!==a||(r=[],Zd(function(t,e){r[e]=null!=t?t+"":""},n,s,1/0)),o=r?r.length:n===Yp?s.length:s[0]?s[0].length:null):e===Hp?r=r||function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e)return ht(e)}(t):e===Wp?r||(r=[],R(t,function(t,e){r.push(e)})):e===Fp&&(i=So(t[0]),o=F(i)&&i.length||1)}return{startIndex:a,dimensionsDefine:qd(r),dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new Wd({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:y(e)})}function Xd(t){return new Wd({data:t,sourceFormat:yt(t)?Gp:Fp})}function Yd(t){var e=Up;if(yt(t))e=Gp;else if(F(t)){0===t.length&&(e=Vp);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(F(r)||yt(r)){e=Vp;break}if(L(r)){e=Hp;break}}}}else if(L(t))for(var o in t)if(Ft(t,o)&&st(t[o])){e=Wp;break}return e}function qd(t){var i;if(t)return i=B(),z(t,function(t,e){var n,t={name:(t=L(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};return null!=t.name&&(t.name+="",null==t.displayName&&(t.displayName=t.name),(n=i.get(t.name))?t.name+="-"+n.count++:i.set(t.name,{count:1})),t})}function Zd(t,e,n,i){if(e===Yp)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else for(var o=n[0]||[],r=0;r<o.length&&r<i;r++)t(o[r],r)}function jd(t){t=t.sourceFormat;return t===Hp||t===Wp}tf.prototype.getSource=function(){return this._source},tf.prototype.count=function(){return 0},tf.prototype.getItem=function(t,e){},tf.prototype.appendData=function(t){},tf.prototype.clean=function(){},tf.protoInitialize=((Xh=tf.prototype).pure=!1,void(Xh.persistent=!0)),tf.internalField=(Hd=function(t,e,n){var i,r=n.sourceFormat,o=n.seriesLayoutBy,a=n.startIndex,n=n.dimensionsDefine;O(t,Vd[pf(r,o)]),r===Gp?(t.getItem=Kd,t.count=Qd,t.fillStorage=$d):(i=of(r,o),t.getItem=ct(i,null,e,a,n),i=lf(r,o),t.count=ct(i,null,e,a,n))},Kd=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},$d=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var d=r[p*o+a];(c[t+p]=d)<l&&(l=d),u<d&&(u=d)}s[0]=l,s[1]=u}},Qd=function(){return this._data?this._data.length/this._dimSize:0},(Xh={})[Vp+"_"+Xp]={pure:!0,appendData:ef},Xh[Vp+"_"+Yp]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},Xh[Hp]={pure:!0,appendData:ef},Xh[Wp]={pure:!0,appendData:function(t){var r=this._data;R(t,function(t,e){for(var n=r[e]||(r[e]=[]),i=0;i<(t||[]).length;i++)n.push(t[i])})}},Xh[Fp]={appendData:ef},Xh[Gp]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},void(Vd=Xh));var Kd,$d,Qd,Jd=tf;function tf(t,e){var t=Gd(t)?t:Xd(t),n=(this._source=t,this._data=t.data);t.sourceFormat===Gp&&(this._offset=0,this._dimSize=e,this._data=n),Hd(this,n,t)}function ef(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function nf(t,e,n,i){return t[i]}(Uc={})[Vp+"_"+Xp]=function(t,e,n,i){return t[i+e]},Uc[Vp+"_"+Yp]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},Uc[Hp]=nf,Uc[Wp]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=t[n[a].name];o[a]=s?s[i]:null}return o},Uc[Fp]=nf;var rf=Uc;function of(t,e){return rf[pf(t,e)]}function af(t,e,n){return t.length}(wc={})[Vp+"_"+Xp]=function(t,e,n){return Math.max(0,t.length-e)},wc[Vp+"_"+Yp]=function(t,e,n){t=t[0];return t?Math.max(0,t.length-e):0},wc[Hp]=af,wc[Wp]=function(t,e,n){t=t[n[0].name];return t?t.length:0},wc[Fp]=af;var sf=wc;function lf(t,e){return sf[pf(t,e)]}function uf(t,e,n){return t[e]}($o={})[Vp]=uf,$o[Hp]=function(t,e,n){return t[n]},$o[Wp]=uf,$o[Fp]=function(t,e,n){t=So(t);return t instanceof Array?t[e]:t},$o[Gp]=uf;var hf=$o;function cf(t){return hf[t]}function pf(t,e){return t===Vp?t+"_"+e:t}function df(t,e,n){if(t){var i,r,e=t.getRawDataItem(e);if(null!=e)return i=(r=t.getStore()).getSource().sourceFormat,null!=n?(t=t.getDimensionIndex(n),n=r.getDimensionProperty(t),cf(i)(e,t,n)):(r=e,i===Fp?So(e):r)}}var ff=/\{@(.+?)\}/g,Tc=(gf.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),t=s&&s[n.getItemVisual(t,"drawType")||"fill"],s=s&&s.stroke,l=this.mainType,u="series"===l,n=n.userOutput&&n.userOutput.get();return{componentType:l,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:u?this.subType:null,seriesIndex:this.seriesIndex,seriesId:u?this.id:null,seriesName:u?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:t,borderColor:s,dimensionNames:n?n.fullDimensions:null,encode:n?n.encode:null,$vars:["seriesName","name","value"]}},gf.prototype.getFormattedLabel=function(i,t,e,n,r,o){t=t||"normal";var a=this.getData(e),e=this.getDataParams(i,e);return o&&(e.value=o.interpolatedValue),null!=n&&F(e.value)&&(e.value=e.value[n]),k(r=r||a.getItemModel(i).get("normal"===t?["label","formatter"]:[t,"label","formatter"]))?(e.status=t,e.dimensionIndex=n,r(e)):V(r)?vp(r,e).replace(ff,function(t,e){var n=e.length,n=("["===e.charAt(0)&&"]"===e.charAt(n-1)&&(e=+e.slice(1,n-1)),df(a,i,e));return null!=(n=o&&F(o.interpolatedValue)&&0<=(e=a.getDimensionIndex(e))?o.interpolatedValue[e]:n)?n+"":""}):void 0},gf.prototype.getRawValue=function(t,e){return df(this.getData(e),t)},gf.prototype.formatTooltip=function(t,e,n){},gf);function gf(){}function yf(t){var e,n;return L(t)?t.type&&(n=t):e=t,{text:e,frag:n}}function mf(t){return new vf(t)}_f.prototype.perform=function(t){var e,n,i=this._upstream,r=t&&t.skip,o=(this._dirty&&i&&((o=this.context).data=o.outputData=i.context.outputData),this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!r&&(e=this._plan(this.context)),u(this._modBy)),a=this._modDataCount||0,s=u(t&&t.modBy),l=t&&t.modDataCount||0;function u(t){return t=1<=t?t:1}o===s&&a===l||(e="reset"),!this._dirty&&"reset"!==e||(this._dirty=!1,n=this._doReset(r)),this._modBy=s,this._modDataCount=l;o=t&&t.step;if(this._dueEnd=i?i._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var h=this._dueIndex,c=Math.min(null!=o?this._dueIndex+o:1/0,this._dueEnd);if(!r&&(n||h<c)){var p=this._progress;if(F(p))for(var d=0;d<p.length;d++)this._doProgress(p[d],h,c,s,l);else this._doProgress(p,h,c,s,l)}this._dueIndex=c;a=null!=this._settedOutputEnd?this._settedOutputEnd:c;this._outputDueEnd=a}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},_f.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},_f.prototype._doProgress=function(t,e,n,i,r){Cf.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:Cf.next},this.context)},_f.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),F(e))&&!e.length&&(e=null),this._progress=e,this._modBy=this._modDataCount=null;var e,n,t=this._downstream;return t&&t.dirty(),n},_f.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},_f.prototype.pipe=function(t){this._downstream===t&&!this._dirty||((this._downstream=t)._upstream=this,t.dirty())},_f.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},_f.prototype.getUpstream=function(){return this._upstream},_f.prototype.getDownstream=function(){return this._downstream},_f.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var vf=_f;function _f(t){this._reset=(t=t||{}).reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}var xf,wf,bf,Sf,Mf,Tf,Cf=Tf={reset:function(t,e,n,i){wf=t,xf=e,bf=n,Sf=i,Mf=Math.ceil(Sf/bf),Tf.next=1<bf&&0<Sf?kf:If}};function If(){return wf<xf?wf++:null}function kf(){var t=wf%Mf*bf+Math.ceil(wf/Mf),t=xf<=wf?null:t<Sf?t:wf;return wf++,t}function Df(t,e){e=e&&e.type;return"ordinal"===e?t:null==(t="time"!==e||ft(t)||null==t||"-"===t?t:+uo(t))||""===t?NaN:Number(t)}var Af=B({number:function(t){return parseFloat(t)},time:function(t){return+uo(t)},trim:function(t){return V(t)?It(t):t}});function Pf(t){return Af.get(t)}var Lf={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return e<t},gte:function(t,e){return e<=t}},Of=(Rf.prototype.evaluate=function(t){return ft(t)?this._opFn(t,this._rvalFloat):this._opFn(fo(t),this._rvalFloat)},Rf);function Rf(t,e){ft(e)||f(""),this._opFn=Lf[t],this._rvalFloat=fo(e)}Bf.prototype.evaluate=function(t,e){var n=ft(t)?t:fo(t),i=ft(e)?e:fo(e),r=isNaN(n),o=isNaN(i);return r&&(n=this._incomparable),o&&(i=this._incomparable),r&&o&&(r=V(t),o=V(e),r&&(n=o?t:0),o)&&(i=r?e:0),n<i?this._resultLT:i<n?-this._resultLT:0};var Ef=Bf;function Bf(t,e){t="desc"===t;this._resultLT=t?1:-1,this._incomparable="min"===(e=null==e?t?"min":"max":e)?-1/0:1/0}zf.prototype.evaluate=function(t){var e,n=t===this._rval;return n||(e=typeof t)===this._rvalTypeof||"number"!=e&&"number"!==this._rvalTypeof||(n=fo(t)===this._rvalFloat),this._isEQ?n:!n};var Nf=zf;function zf(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=fo(e)}Vf.prototype.getRawData=function(){throw new Error("not supported")},Vf.prototype.getRawDataItem=function(t){throw new Error("not supported")},Vf.prototype.cloneRawData=function(){},Vf.prototype.getDimensionInfo=function(t){},Vf.prototype.cloneAllDimensionInfo=function(){},Vf.prototype.count=function(){},Vf.prototype.retrieveValue=function(t,e){},Vf.prototype.retrieveValueFromItem=function(t,e){},Vf.prototype.convertValue=Df;var Ff=Vf;function Vf(){}function Hf(t){return qf(t.sourceFormat)||f(""),t.data}function Wf(t){var e=t.sourceFormat,n=t.data;if(qf(e)||f(""),e===Vp){for(var i=[],r=0,o=n.length;r<o;r++)i.push(n[r].slice());return i}if(e===Hp){for(i=[],r=0,o=n.length;r<o;r++)i.push(O({},n[r]));return i}}function Gf(t,e,n){if(null!=n)return ft(n)||!isNaN(n)&&!Ft(e,n)?t[n]:Ft(e,n)?e[n]:void 0}function Uf(t){return y(t)}var Xf=B();function Yf(t,e){var n=xo(t),t=n.length;t||f("");for(var i=0,r=t;i<r;i++)e=function(t,i){i.length||f("");L(t)||f("");var e=t.type,d=Xf.get(e);d||f("");e=z(i,function(t){var e=t,t=d,n=new Ff,i=e.data,r=n.sourceFormat=e.sourceFormat,o=e.startIndex,a=(e.seriesLayoutBy!==Xp&&f(""),[]),s={};if(h=e.dimensionsDefine)R(h,function(t,e){var n=t.name,e={index:e,name:n,displayName:t.displayName};a.push(e),null!=n&&(Ft(s,n)&&f(""),s[n]=e)});else for(var l=0;l<e.dimensionsDetectedCount;l++)a.push({index:l});var u=of(r,Xp),h=(t.__isBuiltIn&&(n.getRawDataItem=function(t){return u(i,o,a,t)},n.getRawData=ct(Hf,null,e)),n.cloneRawData=ct(Wf,null,e),lf(r,Xp)),c=(n.count=ct(h,null,i,o,a),cf(r)),p=(n.retrieveValue=function(t,e){t=u(i,o,a,t);return p(t,e)},n.retrieveValueFromItem=function(t,e){var n;return null!=t&&(n=a[e])?c(t,e,n.name):void 0});return n.getDimensionInfo=ct(Gf,null,a,s),n.cloneAllDimensionInfo=ct(Uf,null,a),n});return z(xo(d.transform({upstream:e[0],upstreamList:e,config:y(t.config)})),function(t,e){L(t)||f(""),t.data||f("");qf(Yd(t.data))||f("");var n=i[0],e=n&&0===e&&!t.dimensions?((e=n.startIndex)&&(t.data=n.data.slice(0,e).concat(t.data)),{seriesLayoutBy:Xp,sourceHeader:e,dimensions:n.metaRawOption.dimensions}):{seriesLayoutBy:Xp,sourceHeader:0,dimensions:t.dimensions};return Ud(t.data,e,null)})}(n[i],e),i!==r-1&&(e.length=Math.max(e.length,1));return e}function qf(t){return t===Vp||t===Hp}var Zf,Xh="undefined",jf=typeof Uint32Array==Xh?Array:Uint32Array,Kf=typeof Uint16Array==Xh?Array:Uint16Array,$f=typeof Int32Array==Xh?Array:Int32Array,Uc=typeof Float64Array==Xh?Array:Float64Array,Qf={float:Uc,int:$f,ordinal:Array,number:Array,time:Uc};function Jf(t){return 65535<t?jf:Kf}function tg(){return[1/0,-1/0]}function eg(t,e,n,i,r){n=Qf[n||"float"];if(r){var o=t[e],a=o&&o.length;if(a!==i){for(var s=new n(i),l=0;l<a;l++)s[l]=o[l];t[e]=s}}else t[e]=new n(i)}h.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=Zf[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[],jd(i),this._dimensions=z(e,function(t){return{type:t.type,property:t.property}}),this._initDataFromProvider(0,t.count())},h.prototype.getProvider=function(){return this._provider},h.prototype.getSource=function(){return this._provider.getSource()},h.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new Qf[e||"float"](this._rawCount),this._rawExtent[r]=tg(),r},h.prototype.collectOrdinalMeta=function(t,e){for(var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length,s=(0===o&&(r[t]=tg()),r[t]),l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);isNaN(u)||(s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1]))}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},h.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},h.prototype.getDimensionProperty=function(t){t=this._dimensions[t];return t&&t.property},h.prototype.appendData=function(t){var e=this._provider,n=this.count(),t=(e.appendData(t),e.count());return e.persistent||(t+=n),n<t&&this._initDataFromProvider(n,t,!0),[n,t]},h.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++)eg(n,l,(d=i[l]).type,s,!0);for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var d=i[p],f=Zf.arrayRows.call(this,t[c]||u,d.property,c,p),g=(n[p][h]=f,o[p]);f<g[0]&&(g[0]=f),f>g[1]&&(g[1]=f)}return{start:a,end:this._rawCount=this._count=s}},h.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=z(o,function(t){return t.property}),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=tg()),eg(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++)for(var c=i.getItem(p,c),d=0;d<a;d++){var f=r[d],g=this._dimValueGetter(c,l[d],p,d),f=(f[p]=g,s[d]);g<f[0]&&(f[0]=g),g>f[1]&&(f[1]=g)}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},h.prototype.count=function(){return this._count},h.prototype.get=function(t,e){return 0<=e&&e<this._count&&(t=this._chunks[t])?t[this.getRawIndex(e)]:NaN},h.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;for(var r=0,o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},h.prototype.getByRawIndex=function(t,e){return 0<=e&&e<this._rawCount&&(t=this._chunks[t])?t[e]:NaN},h.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},h.prototype.getMedian=function(t){var e=[],t=(this.each([t],function(t){isNaN(t)||e.push(t)}),e.sort(function(t,e){return t-e})),n=this.count();return 0===n?0:n%2==1?t[(n-1)/2]:(t[n/2]+t[n/2-1])/2},h.prototype.indexOfRawIndex=function(t){if(!(t>=this._rawCount||t<0)){if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=1+o;else{if(!(e[o]>t))return o;r=o-1}}}return-1},h.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks[t],r=[];if(i){null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,l=0,u=this.count();l<u;l++){var h=e-i[this.getRawIndex(l)],c=Math.abs(h);c<=n&&((c<o||c===o&&0<=h&&a<0)&&(o=c,a=h,s=0),h===a)&&(r[s++]=l)}r.length=s}return r},h.prototype.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array)for(var i=new e(n),r=0;r<n;r++)i[r]=t[r];else i=new e(t.buffer,0,n)}else{i=new(e=Jf(this._rawCount))(this.count());for(r=0;r<i.length;r++)i[r]=r}return i},h.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(Jf(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a)c=e(u[l][p],h);else{for(var d=0;d<a;d++)o[d]=u[t[d]][p];o[d]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},h.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=ht(t),r=i.length;if(!r)return this;var o=e.count(),a=new(Jf(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks,l=!1;if(!e._indices){var p=0;if(1===r){for(var d=c[i[0]],f=0;f<n;f++)(u<=(v=d[f])&&v<=h||isNaN(v))&&(a[s++]=p),p++;l=!0}else if(2===r){for(var d=c[i[0]],g=c[i[1]],y=t[i[1]][0],m=t[i[1]][1],f=0;f<n;f++){var v=d[f],_=g[f];(u<=v&&v<=h||isNaN(v))&&(y<=_&&_<=m||isNaN(_))&&(a[s++]=p),p++}l=!0}}if(!l)if(1===r)for(f=0;f<o;f++){var x=e.getRawIndex(f);(u<=(v=c[i[0]][x])&&v<=h||isNaN(v))&&(a[s++]=x)}else for(f=0;f<o;f++){for(var w=!0,x=e.getRawIndex(f),b=0;b<r;b++){var S=i[b];((v=c[S][x])<t[S][0]||v>t[S][1])&&(w=!1)}w&&(a[s++]=e.getRawIndex(f))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},h.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},h.prototype.modify=function(t,e){this._updateDims(this,t,e)},h.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],l=t._rawExtent,u=0;u<e.length;u++)l[e[u]]=tg();for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),p=0;p<o;p++)s[p]=i[e[p]][c];s[o]=h;var d=n&&n.apply(null,s);if(null!=d){"object"!=typeof d&&(r[0]=d,d=r);for(u=0;u<d.length;u++){var f=e[u],g=d[u],y=l[f],f=i[f];f&&(f[c]=g),g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}}},h.prototype.lttbDownSample=function(t,e){var n,i=this.clone([t],!0),r=i._chunks[t],o=this.count(),a=0,s=Math.floor(1/e),l=this.getRawIndex(0),u=new(Jf(this._rawCount))(Math.min(2*(Math.ceil(o/s)+2),o));u[a++]=l;for(var h=1;h<o-1;h+=s){for(var c=Math.min(h+s,o-1),p=Math.min(h+2*s,o),d=(p+c)/2,f=0,g=c;g<p;g++){var y=r[M=this.getRawIndex(g)];isNaN(y)||(f+=y)}f/=p-c;for(var c=h,m=Math.min(h+s,o),v=h-1,_=r[l],x=-1,w=c,b=-1,S=0,g=c;g<m;g++){var M,y=r[M=this.getRawIndex(g)];isNaN(y)?(S++,b<0&&(b=M)):x<(n=Math.abs((v-d)*(y-_)-(v-g)*(f-_)))&&(x=n,w=M)}0<S&&S<m-c&&(u[a++]=Math.min(b,w),w=Math.max(b,w)),l=u[a++]=w}return u[a++]=this.getRawIndex(o-1),i._count=a,i._indices=u,i.getRawIndex=this._getRawIdx,i},h.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=tg(),c=new(Jf(this._rawCount))(Math.ceil(u/s)),p=0,d=0;d<u;d+=s){u-d<s&&(a.length=s=u-d);for(var f=0;f<s;f++){var g=this.getRawIndex(d+f);a[f]=l[g]}var y=n(a),m=this.getRawIndex(Math.min(d+i(a,y)||0,u-1));(l[m]=y)<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[p++]=m}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},h.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},h.prototype.getDataExtent=function(t){var e=this._chunks[t],n=tg();if(!e)return n;var i=this.count();if(!this._indices)return this._rawExtent[t].slice();if(r=this._extent[t])return r.slice();for(var r,o=(r=n)[0],a=r[1],s=0;s<i;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),a<l&&(a=l)}return this._extent[t]=r=[o,a]},h.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},h.prototype.clone=function(t,e){var n,i,r=new h,o=this._chunks,a=t&&lt(t,function(t,e){return t[e]=!0,t},{});if(a)for(var s=0;s<o.length;s++)r._chunks[s]=a[s]?(n=o[s],i=void 0,(i=n.constructor)===Array?n.slice():new i(n)):o[s];else r._chunks=o;return this._copyCommonProps(r),e||(r._indices=this._cloneIndices()),r._updateGetRawIdx(),r},h.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=y(this._extent),t._rawExtent=y(this._rawExtent)},h.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array)for(var n=this._indices.length,e=new t(n),i=0;i<n;i++)e[i]=this._indices[i];else e=new t(this._indices);return e}return null},h.prototype._getRawIdxIdentity=function(t){return t},h.prototype._getRawIdx=function(t){return t<this._count&&0<=t?this._indices[t]:-1},h.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},h.internalField=void(Zf={arrayRows:ig,objectRows:function(t,e,n,i){return Df(t[e],this._dimensions[i])},keyedColumns:ig,original:function(t,e,n,i){t=t&&(null==t.value?t:t.value);return Df(t instanceof Array?t[i]:t,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}});var ng=h;function h(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=B()}function ig(t,e,n,i){return Df(t[i],this._dimensions[i])}og.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},og.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,9e10<this._versionSignBase&&(this._versionSignBase=0)},og.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},og.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},og.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n,i,r,o,a,s=this._sourceHost,l=this._getUpstreamSourceManagers(),u=!!l.length;sg(s)?(i=s,r=t=o=void 0,e=u?((e=l[0]).prepareSource(),o=(r=e.getSource()).data,t=r.sourceFormat,[e._getVersionSign()]):(t=yt(o=i.get("data",!0))?Gp:Fp,[]),i=this._getSourceMetaRawOption()||{},r=r&&r.metaRawOption||{},a=E(i.seriesLayoutBy,r.seriesLayoutBy)||null,n=E(i.sourceHeader,r.sourceHeader),i=E(i.dimensions,r.dimensions),r=a!==r.seriesLayoutBy||!!n!=!!r.sourceHeader||i?[Ud(o,{seriesLayoutBy:a,sourceHeader:n,dimensions:i},t)]:[]):(o=s,e=u?(r=(a=this._applyTransform(l)).sourceList,a.upstreamSignList):(r=[Ud(o.get("source",!0),this._getSourceMetaRawOption(),null)],[])),this._setLocalSource(r,e)},og.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0),o=(null!=r&&1!==t.length&&lg(""),[]),a=[];return R(t,function(t){t.prepareSource();var e=t.getSource(r||0);null==r||e||lg(""),o.push(e),a.push(t._getVersionSign())}),i?e=Yf(i,o,n.componentIndex):null!=r&&(e=[new Wd({data:(t=o[0]).data,sourceFormat:t.sourceFormat,seriesLayoutBy:t.seriesLayoutBy,dimensionsDefine:y(t.dimensionsDefine),startIndex:t.startIndex,dimensionsDetectedCount:t.dimensionsDetectedCount})]),{sourceList:e,upstreamSignList:a}},og.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},og.prototype.getSource=function(t){var e=this._sourceList[t=t||0];return e||(e=this._getUpstreamSourceManagers())[0]&&e[0].getSource(t)},og.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},og.prototype._innerGetDataStore=function(t,e,n){var i,r=this._storeList,o=r[0],r=(o=o||(r[0]={}))[n];return r||(i=this._getUpstreamSourceManagers()[0],sg(this._sourceHost)&&i?r=i._innerGetDataStore(t,e,n):(r=new ng).initData(new Jd(e,t.length),t),o[n]=r),r},og.prototype._getUpstreamSourceManagers=function(){var t,e=this._sourceHost;return sg(e)?(t=Kp(e))?[t.getSourceManager()]:[]:z((t=e).get("transform",!0)||t.get("fromTransformResult",!0)?No(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},Bo).models:[],function(t){return t.getSourceManager()})},og.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;return sg(i)?(t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)):this._getUpstreamSourceManagers().length||(t=(i=i).get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)),{seriesLayoutBy:t,sourceHeader:e,dimensions:n}};var rg=og;function og(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}function ag(t){t.option.transform&&Dt(t.option.transform)}function sg(t){return"series"===t.mainType}function lg(t){throw new Error(t)}var ug="line-height:1";function hg(t,e){var n=t.color||"#6e7079",i=t.fontSize||12,r=t.fontWeight||"400",o=t.color||"#464646",a=t.fontSize||14,t=t.fontWeight||"900";return"html"===e?{nameStyle:"font-size:"+xe(i+"")+"px;color:"+xe(n)+";font-weight:"+xe(r+""),valueStyle:"font-size:"+xe(a+"")+"px;color:"+xe(o)+";font-weight:"+xe(t+"")}:{nameStyle:{fontSize:i,fill:n,fontWeight:r},valueStyle:{fontSize:a,fill:o,fontWeight:t}}}var cg=[0,10,20,30],pg=["","\n","\n\n","\n\n\n"];function dg(t,e){return e.type=t,e}function fg(t){return"section"===t.type}function gg(t){return fg(t)?yg:mg}function yg(i,r,t,o){var n,e=r.noHeader,a=(l=function n(t){var i,e,r;return fg(t)?(i=0,e=t.blocks.length,r=1<e||0<e&&!t.noHeader,R(t.blocks,function(t){var e=n(t);i<=e&&(i=e+ +(r&&(!e||fg(t)&&!t.noHeader)))}),i):0}(r),{html:cg[l],richText:pg[l]}),s=[],l=r.blocks||[],u=(Ct(!l||F(l)),l=l||[],i.orderMode),h=(r.sortBlocks&&u&&(l=l.slice(),Ft(h={valueAsc:"asc",valueDesc:"desc"},u)?(n=new Ef(h[u],null),l.sort(function(t,e){return n.evaluate(t.sortParam,e.sortParam)})):"seriesDesc"===u&&l.reverse()),R(l,function(t,e){var n=r.valueFormatter,n=gg(t)(n?O(O({},i),{valueFormatter:n}):i,t,0<e?a.html:0,o);null!=n&&s.push(n)}),"richText"===i.renderMode?s.join(a.richText):_g(s.join(""),e?t:a.html));return e?h:(u=gp(r.header,"ordinal",i.useUTC),l=hg(o,i.renderMode).nameStyle,"richText"===i.renderMode?xg(i,u,l)+a.richText+h:_g('<div style="'+l+";"+ug+';">'+xe(u)+"</div>"+h,t))}function mg(t,e,n,i){var r,o,a,s,l=t.renderMode,u=e.noName,h=e.noValue,c=!e.markerType,p=e.name,d=t.useUTC,f=e.valueFormatter||t.valueFormatter||function(t){return z(t=F(t)?t:[t],function(t,e){return gp(t,F(o)?o[e]:o,d)})};if(!u||!h)return r=c?"":t.markupStyleCreator.makeTooltipMarker(e.markerType,e.markerColor||"#333",l),p=u?"":gp(p,"ordinal",d),o=e.valueType,f=h?[]:f(e.value,e.dataIndex),e=!c||!u,a=!c&&u,i=hg(i,l),s=i.nameStyle,i=i.valueStyle,"richText"===l?(c?"":r)+(u?"":xg(t,p,s))+(h?"":function(t,e,n,i,r){r=[r],i=i?10:20;return n&&r.push({padding:[0,0,0,i],align:"right"}),t.markupStyleCreator.wrapRichTextStyle(F(e)?e.join("  "):e,r)}(t,f,e,a,i)):_g((c?"":r)+(u?"":'<span style="'+s+";"+(!c?"margin-left:2px":"")+'">'+xe(p)+"</span>")+(h?"":function(t,e,n,i){n=n?"10px":"20px",e=e?"float:right;margin-left:"+n:"";return t=F(t)?t:[t],'<span style="'+e+";"+i+'">'+z(t,xe).join("&nbsp;&nbsp;")+"</span>"}(f,e,a,i)),n)}function vg(t,e,n,i,r,o){if(t)return gg(t)({useUTC:r,renderMode:n,orderMode:i,markupStyleCreator:e,valueFormatter:t.valueFormatter},t,0,o)}function _g(t,e){return'<div style="'+("margin: "+e+"px 0 0")+";"+ug+';">'+t+'<div style="clear:both"></div></div>'}function xg(t,e,n){return t.markupStyleCreator.wrapRichTextStyle(e,n)}function wg(t,e){t=t.get("padding");return null!=t?t:"richText"===e?[8,10]:10}Sg.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},Sg.prototype.makeTooltipMarker=function(t,e,n){var i="richText"===n?this._generateStyleName():null,e=_p({color:e,type:t,renderMode:n,markerId:i});return V(e)?e:(this.richTextStyles[i]=e.style,e.content)},Sg.prototype.wrapRichTextStyle=function(t,e){var n={},e=(F(e)?R(e,function(t){return O(n,t)}):O(n,e),this._generateStyleName());return this.richTextStyles[e]=n,"{"+e+"|"+t+"}"};var bg=Sg;function Sg(){this.richTextStyles={},this._nextStyleNameId=yo()}function Mg(t){var e,n,i,r,o,a,s,l,u,h,c,p=t.series,d=t.dataIndex,t=t.multipleSeries,f=p.getData(),g=f.mapDimensionsAll("defaultedTooltip"),y=g.length,m=p.getRawValue(d),v=F(m),_=(_=d,xp((w=p).getData().getItemVisual(_,"style")[w.visualDrawType]));function x(t,e){e=s.getDimensionInfo(e);e&&!1!==e.otherDims.tooltip&&(l?c.push(dg("nameValue",{markerType:"subItem",markerColor:a,name:e.displayName,value:t,valueType:e.type})):(u.push(t),h.push(e.type)))}1<y||v&&!y?(w=m,r=d,o=g,a=_,s=p.getData(),l=lt(w,function(t,e,n){n=s.getDimensionInfo(n);return t||n&&!1!==n.tooltip&&null!=n.displayName},!1),u=[],h=[],c=[],o.length?R(o,function(t){x(df(s,r,t),t)}):R(w,x),e=(o={inlineValues:u,inlineValueTypes:h,blocks:c}).inlineValueTypes,n=o.blocks,i=(o=o.inlineValues)[0]):y?(w=f.getDimensionInfo(g[0]),i=o=df(f,d,g[0]),e=w.type):i=o=v?m[0]:m;var y=ko(p),g=y&&p.name||"",w=f.getName(d),v=t?g:w;return dg("section",{header:g,noHeader:t||!y,sortParam:i,blocks:[dg("nameValue",{markerType:"item",markerColor:_,name:v,noName:!It(v),value:o,valueType:e,dataIndex:d})].concat(n||[])})}var Tg=Lo();function Cg(t,e){return t.getName(e)||t.getId(e)}u(c,Ig=g),c.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=mf({count:Ag,reset:Pg}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n);(Tg(this).sourceManager=new rg(this)).prepareSource();t=this.getInitialData(t,n);Og(t,this),this.dataTask.context.data=t,Tg(this).dataBeforeProcessed=t,Dg(this),this._initSelectedMapFromData(t)},c.prototype.mergeDefaultAndTheme=function(t,e){var n=Ip(this),i=n?Dp(t):{},r=this.subType;g.hasClass(r),d(t,e.getTheme().get(this.subType)),d(t,this.getDefaultOption()),wo(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&kp(t,i,n)},c.prototype.mergeOption=function(t,e){t=d(this.option,t,!0),this.fillDataTextStyle(t.data);var n=Ip(this),n=(n&&kp(this.option,t,n),Tg(this).sourceManager),n=(n.dirty(),n.prepareSource(),this.getInitialData(t,e));Og(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,Tg(this).dataBeforeProcessed=n,Dg(this),this._initSelectedMapFromData(n)},c.prototype.fillDataTextStyle=function(t){if(t&&!yt(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&wo(t[n],"label",e)},c.prototype.getInitialData=function(t,e){},c.prototype.appendData=function(t){this.getRawData().appendData(t.data)},c.prototype.getData=function(t){var e=Eg(this);return e?(e=e.context.data,null!=t&&e.getLinkedData?e.getLinkedData(t):e):Tg(this).data},c.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},c.prototype.setData=function(t){var e,n=Eg(this);n&&((e=n.context).outputData=t,n!==this.dataTask)&&(e.data=t),Tg(this).data=t},c.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return B(t)},c.prototype.getSourceManager=function(){return Tg(this).sourceManager},c.prototype.getSource=function(){return this.getSourceManager().getSource()},c.prototype.getRawData=function(){return Tg(this).dataBeforeProcessed},c.prototype.getColorBy=function(){return this.get("colorBy")||"series"},c.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},c.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},c.prototype.formatTooltip=function(t,e,n){return Mg({series:this,dataIndex:t,multipleSeries:e})},c.prototype.isAnimationEnabled=function(){var t=this.ecModel;return!!(!b.node||t&&t.ssr)&&!!(t=(t=this.getShallow("animation"))&&this.getData().count()>this.getShallow("animationThreshold")?!1:t)},c.prototype.restoreData=function(){this.dataTask.dirty()},c.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel;return id.prototype.getColorFromPalette.call(this,t,e,n)||i.getColorFromPalette(t,e,n)},c.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},c.prototype.getProgressive=function(){return this.get("progressive")},c.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},c.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},c.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)this.option.selectedMap={},this._selectedDataIndicesMap={};else for(var o=0;o<t.length;o++){var a=Cg(r,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},c.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},c.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=ht(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];0<=r&&n.push(r)}return n},c.prototype.isSelected=function(t,e){var n=this.option.selectedMap;return!!n&&(e=this.getData(e),"all"===n||n[Cg(e,t)])&&!e.getItemModel(t).get(["select","disabled"])},c.prototype.isUniversalTransitionEnabled=function(){var t;return!!this.__universalTransitionEnabled||!!(t=this.option.universalTransition)&&(!0===t||t&&t.enabled)},c.prototype._innerSelect=function(t,e){var n=this.option,i=n.selectedMode,r=e.length;if(i&&r)if("series"===i)n.selectedMap="all";else if("multiple"===i){L(n.selectedMap)||(n.selectedMap={});for(var o=n.selectedMap,a=0;a<r;a++){var s,l=e[a];o[s=Cg(t,l)]=!0,this._selectedDataIndicesMap[s]=t.getRawIndex(l)}}else"single"!==i&&!0!==i||(s=Cg(t,i=e[r-1]),n.selectedMap=((n={})[s]=!0,n),this._selectedDataIndicesMap=((n={})[s]=t.getRawIndex(i),n))},c.prototype._initSelectedMapFromData=function(n){var i;this.option.selectedMap||(i=[],n.hasItemOption&&n.each(function(t){var e=n.getRawDataItem(t);e&&e.selected&&i.push(t)}),0<i.length&&this._innerSelect(n,i))},c.registerClass=function(t){return g.registerClass(t)},c.protoInitialize=((wc=c.prototype).type="series.__base__",wc.seriesIndex=0,wc.ignoreStyleOnData=!1,wc.hasSymbolVisual=!1,wc.defaultSymbol="circle",wc.visualStyleAccessPath="itemStyle",void(wc.visualDrawType="fill"));var Ig,kg=c;function c(){var t=null!==Ig&&Ig.apply(this,arguments)||this;return t._selectedDataIndicesMap={},t}function Dg(t){var e,n,i=t.name;ko(t)||(t.name=(t=(e=(t=t).getRawData()).mapDimensionsAll("seriesName"),n=[],R(t,function(t){t=e.getDimensionInfo(t);t.displayName&&n.push(t.displayName)}),n.join(" ")||i))}function Ag(t){return t.model.getRawData().count()}function Pg(t){t=t.model;return t.setData(t.getRawData().cloneShallow()),Lg}function Lg(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function Og(e,n){R(Bt(e.CHANGABLE_METHODS,e.DOWNSAMPLE_METHODS),function(t){e.wrapMethod(t,pt(Rg,n))})}function Rg(t,e){t=Eg(t);return t&&t.setOutputEnd((e||this).count()),e}function Eg(t){var e,n=(t.ecModel||{}).scheduler,n=n&&n.getPipeline(t.uid);if(n)return(n=n.currentTask)&&(e=n.agentStubMap)?e.get(t.uid):n}at(kg,Tc),at(kg,id),Uo(kg,g);Ng.prototype.init=function(t,e){},Ng.prototype.render=function(t,e,n,i){},Ng.prototype.dispose=function(t,e){},Ng.prototype.updateView=function(t,e,n,i){},Ng.prototype.updateLayout=function(t,e,n,i){},Ng.prototype.updateVisual=function(t,e,n,i){},Ng.prototype.toggleBlurSeries=function(t,e,n){},Ng.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)};var Bg=Ng;function Ng(){this.group=new Wr,this.uid=Pc("viewComponent")}function zg(){var o=Lo();return function(t){var e=o(t),t=t.pipelineContext,n=!!e.large,i=!!e.progressiveRender,r=e.large=!(!t||!t.large),e=e.progressiveRender=!(!t||!t.progressiveRender);return!(n==r&&i==e)&&"reset"}}Go(Bg),Zo(Bg);var Fg=Lo(),Vg=zg(),Hg=(Wg.prototype.init=function(t,e){},Wg.prototype.render=function(t,e,n,i){},Wg.prototype.highlight=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&Ug(t,i,"emphasis")},Wg.prototype.downplay=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&Ug(t,i,"normal")},Wg.prototype.remove=function(t,e){this.group.removeAll()},Wg.prototype.dispose=function(t,e){},Wg.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},Wg.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},Wg.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},Wg.prototype.eachRendered=function(t){nc(this.group,t)},Wg.markUpdateMethod=function(t,e){Fg(t).updateMethod=e},Wg.protoInitialize=void(Wg.prototype.type="chart"),Wg);function Wg(){this.group=new Wr,this.uid=Pc("viewChart"),this.renderTask=mf({plan:Xg,reset:Yg}),this.renderTask.context={view:this}}function Gg(t,e,n){t&&Nl(t)&&("emphasis"===e?wl:bl)(t,n)}function Ug(e,t,n){var i,r=Po(e,t),o=t&&null!=t.highlightKey?(t=t.highlightKey,i=null==(i=qs[t])&&Ys<=32?qs[t]=Ys++:i):null;null!=r?R(xo(r),function(t){Gg(e.getItemGraphicEl(t),n,o)}):e.eachItemGraphicEl(function(t){Gg(t,n,o)})}function Xg(t){return Vg(t.model)}function Yg(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,t=t.view,a=r&&Fg(r).updateMethod,o=o?"incrementalPrepareRender":a&&t[a]?a:"render";return"render"!==o&&t[o](e,n,i,r),qg[o]}Go(Hg),Zo(Hg);var qg={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},Zg="\0__throttleOriginMethod",jg="\0__throttleRate",Kg="\0__throttleType";function $g(t,r,o){var a,s,l,u,h,c=0,p=0,d=null;function f(){p=(new Date).getTime(),d=null,t.apply(l,u||[])}r=r||0;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];a=(new Date).getTime(),l=this,u=t;var n=h||r,i=h||o;h=null,s=a-(i?c:p)-n,clearTimeout(d),i?d=setTimeout(f,n):0<=s?f():d=setTimeout(f,-s),c=a}return e.clear=function(){d&&(clearTimeout(d),d=null)},e.debounceNextCall=function(t){h=t},e}function Qg(t,e,n,i){var r=t[e];if(r){var o=r[Zg]||r,a=r[Kg];if(r[jg]!==n||a!==i){if(null==n||!i)return t[e]=o;(r=t[e]=$g(o,n,"debounce"===i))[Zg]=o,r[Kg]=i,r[jg]=n}}}function Jg(t,e){var n=t[e];n&&n[Zg]&&(n.clear&&n.clear(),t[e]=n[Zg])}var ty=Lo(),ey={itemStyle:jo(Sc,!0),lineStyle:jo(_c,!0)},ny={lineStyle:"stroke",itemStyle:"fill"};function iy(t,e){t=t.visualStyleMapper||ey[e];return t||(console.warn("Unknown style type '"+e+"'."),ey.itemStyle)}function ry(t,e){t=t.visualDrawType||ny[e];return t||(console.warn("Unknown style type '"+e+"'."),"fill")}var $o={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),n=r.visualStyleAccessPath||"itemStyle",i=r.getModel(n),o=iy(r,n)(i),i=i.getShallow("decal"),a=(i&&(e.setVisual("decal",i),i.dirty=!0),ry(r,n)),i=o[a],s=k(i)?i:null,n="auto"===o.fill||"auto"===o.stroke;if(o[a]&&!s&&!n||(i=r.getColorFromPalette(r.name,null,t.getSeriesCount()),o[a]||(o[a]=i,e.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||k(o.fill)?i:o.fill,o.stroke="auto"===o.stroke||k(o.stroke)?i:o.stroke),e.setVisual("style",o),e.setVisual("drawType",a),!t.isSeriesFiltered(r)&&s)return e.setVisual("colorFromPalette",!1),{dataEach:function(t,e){var n=r.getDataParams(e),i=O({},o);i[a]=s(n),t.setItemVisual(e,"style",i)}}}},oy=new kc,Xh={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var i,r,o;if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t))return e=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=iy(t,i),o=e.getVisual("drawType"),{dataEach:e.hasItemOption?function(t,e){var n=t.getRawDataItem(e);n&&n[i]&&(oy.option=n[i],n=r(oy),O(t.ensureUniqueItemVisual(e,"style"),n),oy.option.decal&&(t.setItemVisual(e,"decal",oy.option.decal),oy.option.decal.dirty=!0),o in n)&&t.setItemVisual(e,"colorFromPalette",!1)}:null}}},Uc={performRawSeries:!0,overallReset:function(e){var i=B();e.eachSeries(function(t){var e,n=t.getColorBy();t.isColorBySeries()||(n=t.type+"-"+n,(e=i.get(n))||i.set(n,e={}),ty(t).scope=e)}),e.eachSeries(function(i){var r,o,a,s,t,l;i.isColorBySeries()||e.isSeriesFiltered(i)||(r=i.getRawData(),o={},a=i.getData(),s=ty(i).scope,t=i.visualStyleAccessPath||"itemStyle",l=ry(i,t),a.each(function(t){var e=a.getRawIndex(t);o[e]=t}),r.each(function(t){var e,n=o[t];a.getItemVisual(n,"colorFromPalette")&&(n=a.ensureUniqueItemVisual(n,"style"),t=r.getName(t)||t+"",e=r.count(),n[l]=i.getColorFromPalette(t,s,e))}))})}},ay=Math.PI;ly.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){t=t.overallTask;t&&t.dirty()})},ly.prototype.getPerformArgs=function(t,e){var n,i;if(t.__pipeline)return i=(n=this._pipelineMap.get(t.__pipeline.id)).context,{step:e=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,modBy:null!=(t=i&&i.modDataCount)?Math.ceil(t/e):null,modDataCount:t}},ly.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},ly.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),e=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,r=t.get("large")&&i>=t.get("largeThreshold"),i="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:e,modDataCount:i,large:r}},ly.prototype.restorePipelines=function(t){var i=this,r=i._pipelineMap=B();t.eachSeries(function(t){var e=t.getProgressive(),n=t.uid;r.set(n,{id:n,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:e&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(e||700),count:0}),i._pipe(t,t.dataTask)})},ly.prototype.prepareStageTasks=function(){var n=this._stageTaskMap,i=this.api.getModel(),r=this.api;R(this._allHandlers,function(t){var e=n.get(t.uid)||n.set(t.uid,{});Ct(!(t.reset&&t.overallReset),""),t.reset&&this._createSeriesStageTask(t,e,i,r),t.overallReset&&this._createOverallStageTask(t,e,i,r)},this)},ly.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},ly.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},ly.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},ly.prototype._performStageTasks=function(t,s,l,u){u=u||{};var h=!1,c=this;function p(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}R(t,function(i,t){var e,n,r,o,a;u.visualType&&u.visualType!==i.visualType||(e=(n=c._stageTaskMap.get(i.uid)).seriesTaskMap,(n=n.overallTask)?((o=n.agentStubMap).each(function(t){p(u,t)&&(t.dirty(),r=!0)}),r&&n.dirty(),c.updatePayload(n,l),a=c.getPerformArgs(n,u.block),o.each(function(t){t.perform(a)}),n.perform(a)&&(h=!0)):e&&e.each(function(t,e){p(u,t)&&t.dirty();var n=c.getPerformArgs(t,u.block);n.skip=!i.performRawSeries&&s.isSeriesFiltered(t.context.model),c.updatePayload(t,l),t.perform(n)&&(h=!0)}))}),this.unfinished=h||this.unfinished},ly.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e=t.dataTask.perform()||e}),this.unfinished=e||this.unfinished},ly.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}}while(e=e.getUpstream())})},ly.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},ly.prototype._createSeriesStageTask=function(n,t,i,r){var o=this,a=t.seriesTaskMap,s=t.seriesTaskMap=B(),t=n.seriesType,e=n.getTargetSeries;function l(t){var e=t.uid,e=s.set(e,a&&a.get(e)||mf({plan:dy,reset:fy,count:my}));e.context={model:t,ecModel:i,api:r,useClearVisual:n.isVisual&&!n.isLayout,plan:n.plan,reset:n.reset,scheduler:o},o._pipe(t,e)}n.createOnAllSeries?i.eachRawSeries(l):t?i.eachRawSeriesByType(t,l):e&&e(i,r).each(l)},ly.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||mf({reset:uy}),a=(o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r},o.agentStubMap),s=o.agentStubMap=B(),e=t.seriesType,l=t.getTargetSeries,u=!0,h=!1;function c(t){var e=t.uid,e=s.set(e,a&&a.get(e)||(h=!0,mf({reset:hy,onDirty:py})));e.context={model:t,overallProgress:u},e.agent=o,e.__block=u,r._pipe(t,e)}Ct(!t.createOnAllSeries,""),e?n.eachRawSeriesByType(e,c):l?l(n,i).each(c):(u=!1,R(n.getSeries(),c)),h&&o.dirty()},ly.prototype._pipe=function(t,e){t=t.uid,t=this._pipelineMap.get(t);t.head||(t.head=e),t.tail&&t.tail.pipe(e),(t.tail=e).__idxInPipeline=t.count++,e.__pipeline=t},ly.wrapStageHandler=function(t,e){return(t=k(t)?{overallReset:t,seriesType:function(t){vy=null;try{t(_y,xy)}catch(t){}return vy}(t)}:t).uid=Pc("stageHandler"),e&&(t.visualType=e),t};var sy=ly;function ly(t,e,n,i){this._stageTaskMap=B(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}function uy(t){t.overallReset(t.ecModel,t.api,t.payload)}function hy(t){return t.overallProgress&&cy}function cy(){this.agent.dirty(),this.getDownstream().dirty()}function py(){this.agent&&this.agent.dirty()}function dy(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function fy(t){t.useClearVisual&&t.data.clearAllVisual();t=t.resetDefines=xo(t.reset(t.model,t.ecModel,t.api,t.payload));return 1<t.length?z(t,function(t,e){return yy(e)}):gy}var gy=yy(0);function yy(o){return function(t,e){var n=e.data,i=e.resetDefines[o];if(i&&i.dataEach)for(var r=t.start;r<t.end;r++)i.dataEach(n,r);else i&&i.progress&&i.progress(t,n)}}function my(t){return t.data.count()}var vy,_y={},xy={};function wy(t,e){for(var n in e.prototype)t[n]=Vt}wy(_y,ld),wy(xy,pd),_y.eachSeriesByType=_y.eachRawSeriesByType=function(t){vy=t},_y.eachComponent=function(t){"series"===t.mainType&&t.subType&&(vy=t.subType)};function by(){return{axisLine:{lineStyle:{color:Sy}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}}var wc=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],Tc={color:wc,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],wc]},Sy="#B9B8CE",Sc="#100C2A",_c=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],wc={darkMode:!0,color:_c,backgroundColor:Sc,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Sy}},textStyle:{color:Sy},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Sy}},dataZoom:{borderColor:"#71708A",textStyle:{color:Sy},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Sy}},timeline:{lineStyle:{color:Sy},label:{color:Sy},controlStyle:{color:Sy,borderColor:Sy}},calendar:{itemStyle:{color:Sc},dayLabel:{color:Sy},monthLabel:{color:Sy},yearLabel:{color:Sy}},timeAxis:by(),logAxis:by(),valueAxis:by(),categoryAxis:by(),line:{symbol:"circle"},graph:{color:_c},gauge:{title:{color:Sy},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Sy},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}},My=(wc.categoryAxis.splitLine.show=!1,Ty.prototype.normalizeQuery=function(t){var e,a,s,l={},u={},h={};return V(t)?(e=Wo(t),l.mainType=e.main||null,l.subType=e.sub||null):(a=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1},R(t,function(t,e){for(var n=!1,i=0;i<a.length;i++){var r=a[i],o=e.lastIndexOf(r);0<o&&o===e.length-r.length&&"data"!==(o=e.slice(0,o))&&(l.mainType=o,l[r.toLowerCase()]=t,n=!0)}s.hasOwnProperty(e)&&(u[e]=t,n=!0),n||(h[e]=t)})),{cptQuery:l,dataQuery:u,otherQuery:h}},Ty.prototype.filter=function(t,e){var n,i,r,o,a,s=this.eventInfo;return!s||(n=s.targetEl,i=s.packedEvent,r=s.model,s=s.view,!r)||!s||(o=e.cptQuery,a=e.dataQuery,l(o,r,"mainType")&&l(o,r,"subType")&&l(o,r,"index","componentIndex")&&l(o,r,"name")&&l(o,r,"id")&&l(a,i,"name")&&l(a,i,"dataIndex")&&l(a,i,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,i)));function l(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},Ty.prototype.afterTrigger=function(){this.eventInfo=null},Ty);function Ty(){}var Cy=["symbol","symbolSize","symbolRotate","symbolOffset"],Iy=Cy.concat(["symbolKeepAspect"]),Sc={createOnAllSeries:!0,performRawSeries:!0,reset:function(a,t){var e=a.getData();if(a.legendIcon&&e.setVisual("legendIcon",a.legendIcon),a.hasSymbolVisual){for(var s,n={},l={},i=!1,r=0;r<Cy.length;r++){var o=Cy[r],u=a.get(o);k(u)?(i=!0,l[o]=u):n[o]=u}if(n.symbol=n.symbol||a.defaultSymbol,e.setVisual(O({legendIcon:a.legendIcon||n.symbol,symbolKeepAspect:a.get("symbolKeepAspect")},n)),!t.isSeriesFiltered(a))return s=ht(l),{dataEach:i?function(t,e){for(var n=a.getRawValue(e),i=a.getDataParams(e),r=0;r<s.length;r++){var o=s[r];t.setItemVisual(e,o,l[o](n,i))}}:null}}}},_c={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),i=0;i<Iy.length;i++){var r=Iy[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}:null}}};function ky(t,e,s,n,l){var u=t+e;s.isSilent(u)||n.eachComponent({mainType:"series",subType:"pie"},function(t){for(var e,n,i=t.seriesIndex,r=t.option.selectedMap,o=l.selected,a=0;a<o.length;a++)o[a].seriesIndex===i&&(n=Po(e=t.getData(),l.fromActionPayload),s.trigger(u,{type:u,seriesId:t.id,name:F(n)?e.getName(n[0]):e.getName(n),selected:V(r)?r:O({},r)}))})}function Dy(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var Ay=Math.round(9*Math.random()),Py="function"==typeof Object.defineProperty,Ly=(Oy.prototype.get=function(t){return this._guard(t)[this._id]},Oy.prototype.set=function(t,e){t=this._guard(t);return Py?Object.defineProperty(t,this._id,{value:e,enumerable:!1,configurable:!0}):t[this._id]=e,this},Oy.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},Oy.prototype.has=function(t){return!!this._guard(t)[this._id]},Oy.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},Oy);function Oy(){this._id="__ec_inner_"+Ay++}var Ry=j.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i+e),t.lineTo(n-r,i+e),t.closePath()}}),Ey=j.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i),t.lineTo(n,i+e),t.lineTo(n-r,i),t.closePath()}}),By=j.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,e=Math.max(r,e.height),r=r/2,o=r*r/(e-r),e=i-e+r+o,a=Math.asin(o/r),s=Math.cos(a)*r,l=Math.sin(a),u=Math.cos(a),h=.6*r,c=.7*r;t.moveTo(n-s,e+o),t.arc(n,e,r,Math.PI-a,2*Math.PI+a),t.bezierCurveTo(n+s-l*h,e+o+u*h,n,i-c,n,i),t.bezierCurveTo(n,i-c,n-s+l*h,e+o+u*h,n-s,e+o),t.closePath()}}),Ny=j.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,e=e.y,i=i/3*2;t.moveTo(r,e),t.lineTo(r+i,e+n),t.lineTo(r,e+n/4*3),t.lineTo(r-i,e+n),t.lineTo(r,e),t.closePath()}}),zy={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){n=Math.min(n,i);r.x=t,r.y=e,r.width=n,r.height=n},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},Fy={},Vy=(R({line:Yu,rect:Ds,roundRect:Ds,square:Ds,circle:su,diamond:Ey,pin:By,arrow:Ny,triangle:Ry},function(t,e){Fy[e]=new t}),j.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var t=Dr(t,e,n),i=this.shape;return i&&"pin"===i.symbolType&&"inside"===e.position&&(t.y=n.y+.4*n.height),t},buildPath:function(t,e,n){var i,r=e.symbolType;"none"!==r&&(i=(i=Fy[r])||Fy[r="rect"],zy[r](e.x,e.y,e.width,e.height,i.shape),i.buildPath(t,i.shape,n))}}));function Hy(t,e){var n;"image"!==this.type&&(n=this.style,this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw())}function Wy(t,e,n,i,r,o,a){var s=0===t.indexOf("empty");return(a=0===(t=s?t.substr(5,1).toLowerCase()+t.substr(6):t).indexOf("image://")?Vh(t.slice(8),new X(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?Fh(t.slice(7),{},new X(e,n,i,r),a?"center":"cover"):new Vy({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=s,a.setColor=Hy,o&&a.setColor(o),a}function Gy(t){return isFinite(t)}function Uy(t,e,n){for(var i,r,o,a,s,l,u,h,c,p="radial"===e.type?(i=t,r=e,a=(o=n).width,s=o.height,l=Math.min(a,s),u=null==r.x?.5:r.x,h=null==r.y?.5:r.y,c=null==r.r?.5:r.r,r.global||(u=u*a+o.x,h=h*s+o.y,c*=l),u=Gy(u)?u:.5,h=Gy(h)?h:.5,c=0<=c&&Gy(c)?c:.5,i.createRadialGradient(u,h,0,u,h,c)):(r=t,a=n,o=null==(s=e).x?0:s.x,l=null==s.x2?1:s.x2,i=null==s.y?0:s.y,u=null==s.y2?0:s.y2,s.global||(o=o*a.width+a.x,l=l*a.width+a.x,i=i*a.height+a.y,u=u*a.height+a.y),o=Gy(o)?o:0,l=Gy(l)?l:1,i=Gy(i)?i:0,u=Gy(u)?u:0,r.createLinearGradient(o,i,l,u)),d=e.colorStops,f=0;f<d.length;f++)p.addColorStop(d[f].offset,d[f].color);return p}function Xy(t){return parseInt(t,10)}function Yy(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],e=["paddingRight","paddingBottom"][e];return null!=n[i]&&"auto"!==n[i]?parseFloat(n[i]):(n=document.defaultView.getComputedStyle(t),(t[r]||Xy(n[i])||Xy(t.style[i]))-(Xy(n[o])||0)-(Xy(n[e])||0)|0)}function qy(t){var e,n=t.style,i=n.lineDash&&0<n.lineWidth&&(r=n.lineDash,i=n.lineWidth,r&&"solid"!==r&&0<i?"dashed"===r?[4*i,2*i]:"dotted"===r?[i]:ft(r)?[r]:F(r)?r:null:null),r=n.lineDashOffset;return i&&(e=n.strokeNoScale&&t.getLineScale?t.getLineScale():1)&&1!==e&&(i=z(i,function(t){return t/e}),r/=e),[i,r]}var Zy=new ja(!0);function jy(t){var e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))}function Ky(t){return"string"==typeof t&&"none"!==t}function $y(t){t=t.fill;return null!=t&&"none"!==t}function Qy(t,e){var n;null!=e.fillOpacity&&1!==e.fillOpacity?(n=t.globalAlpha,t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n):t.fill()}function Jy(t,e){var n;null!=e.strokeOpacity&&1!==e.strokeOpacity?(n=t.globalAlpha,t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n):t.stroke()}function tm(t,e,n){var n=ta(e.image,e.__image,n);if(na(n))return t=t.createPattern(n,e.repeat||"repeat"),"function"==typeof DOMMatrix&&t&&t.setTransform&&((n=new DOMMatrix).translateSelf(e.x||0,e.y||0),n.rotateSelf(0,0,(e.rotation||0)*Ht),n.scaleSelf(e.scaleX||1,e.scaleY||1),t.setTransform(n)),t}var em=["shadowBlur","shadowOffsetX","shadowOffsetY"],nm=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function im(t,e,n,i,r){var o,a=!1;if(!i&&e===(n=n||{}))return!1;!i&&e.opacity===n.opacity||(hm(t,r),a=!0,o=Math.max(Math.min(e.opacity,1),0),t.globalAlpha=isNaN(o)?ya.opacity:o),!i&&e.blend===n.blend||(a||(hm(t,r),a=!0),t.globalCompositeOperation=e.blend||ya.blend);for(var s=0;s<em.length;s++){var l=em[s];!i&&e[l]===n[l]||(a||(hm(t,r),a=!0),t[l]=t.dpr*(e[l]||0))}return!i&&e.shadowColor===n.shadowColor||(a||(hm(t,r),a=!0),t.shadowColor=e.shadowColor||ya.shadowColor),a}function rm(t,e,n,i,r){var o=cm(e,r.inHover),a=i?null:n&&cm(n,r.inHover)||{};if(o!==a){var s=im(t,o,a,i,r);(i||o.fill!==a.fill)&&(s||(hm(t,r),s=!0),Ky(o.fill))&&(t.fillStyle=o.fill),(i||o.stroke!==a.stroke)&&(s||(hm(t,r),s=!0),Ky(o.stroke))&&(t.strokeStyle=o.stroke),!i&&o.opacity===a.opacity||(s||(hm(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()&&(n=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1),t.lineWidth!==n)&&(s||(hm(t,r),s=!0),t.lineWidth=n);for(var l=0;l<nm.length;l++){var u=nm[l],h=u[0];!i&&o[h]===a[h]||(s||(hm(t,r),s=!0),t[h]=o[h]||u[1])}}}function om(t,e){var e=e.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)}var am=1,sm=2,lm=3,um=4;function hm(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function cm(t,e){return e&&t.__hoverStyle||t.style}function pm(t,e){dm(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function dm(t,e,n,B){var i=e.transform;if(e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1)){var r=e.__clipPaths,o=n.prevElClipPaths,a=!1,s=!1;if(!o||function(t,e){if(t!==e&&(t||e)){if(!t||!e||t.length!==e.length)return 1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return 1}}(r,o)){if(o&&o.length&&(hm(t,n),t.restore(),s=a=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),r&&r.length){hm(t,n),t.save();for(var N=r,l=t,o=n,z=!1,F=0;F<N.length;F++){var V=N[F],z=z||V.isZeroArea();om(l,V),l.beginPath(),V.buildPath(l,V.shape),l.clip()}o.allClipped=z,a=!0}n.prevElClipPaths=r}if(n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var u,h,c,p,d,f,g,y,m,v,_,x,w,H,b,S,M,T,C,I,k,D,A,o=n.prevEl,P=(o||(s=a=!0),e instanceof j&&e.autoBatch&&(r=e.style,P=$y(r),u=jy(r),!(r.lineDash||!(+P^+u)||P&&"string"!=typeof r.fill||u&&"string"!=typeof r.stroke||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1))),a=(a||(u=i,r=o.transform,u&&r?u[0]!==r[0]||u[1]!==r[1]||u[2]!==r[2]||u[3]!==r[3]||u[4]!==r[4]||u[5]!==r[5]:u||r)?(hm(t,n),om(t,e)):P||hm(t,n),cm(e,n.inHover));if(e instanceof j)n.lastDrawType!==am&&(s=!0,n.lastDrawType=am),rm(t,e,o,s,n),P&&(n.batchFill||n.batchStroke)||t.beginPath(),i=t,r=e,R=P,b=jy(p=a),S=$y(p),M=p.strokePercent,T=M<1,C=!r.path,r.silent&&!T||!C||r.createPathProxy(),I=r.path||Zy,k=r.__dirty,R||(d=p.fill,A=p.stroke,f=S&&!!d.colorStops,g=b&&!!A.colorStops,y=S&&!!d.image,m=b&&!!A.image,D=w=x=_=v=void 0,(f||g)&&(D=r.getBoundingRect()),f&&(v=k?Uy(i,d,D):r.__canvasFillGradient,r.__canvasFillGradient=v),g&&(_=k?Uy(i,A,D):r.__canvasStrokeGradient,r.__canvasStrokeGradient=_),y&&(x=k||!r.__canvasFillPattern?tm(i,d,r):r.__canvasFillPattern,r.__canvasFillPattern=x),m&&(w=k||!r.__canvasStrokePattern?tm(i,A,r):r.__canvasStrokePattern,r.__canvasStrokePattern=x),f?i.fillStyle=v:y&&(x?i.fillStyle=x:S=!1),g?i.strokeStyle=_:m&&(w?i.strokeStyle=w:b=!1)),D=r.getGlobalScale(),I.setScale(D[0],D[1],r.segmentIgnoreThreshold),i.setLineDash&&p.lineDash&&(H=(d=qy(r))[0],O=d[1]),A=!0,(C||k&xn)&&(I.setDPR(i.dpr),T?I.setContext(null):(I.setContext(i),A=!1),I.reset(),r.buildPath(I,r.shape,R),I.toStatic(),r.pathUpdated()),A&&I.rebuildPath(i,T?M:1),H&&(i.setLineDash(H),i.lineDashOffset=O),R||(p.strokeFirst?(b&&Jy(i,p),S&&Qy(i,p)):(S&&Qy(i,p),b&&Jy(i,p))),H&&i.setLineDash([]),P&&(n.batchFill=a.fill||"",n.batchStroke=a.stroke||"");else if(e instanceof gs)n.lastDrawType!==lm&&(s=!0,n.lastDrawType=lm),rm(t,e,o,s,n),f=t,v=e,null!=(x=(y=a).text)&&(x+=""),x&&(f.font=y.font||K,f.textAlign=y.textAlign,f.textBaseline=y.textBaseline,_=g=void 0,f.setLineDash&&y.lineDash&&(g=(v=qy(v))[0],_=v[1]),g&&(f.setLineDash(g),f.lineDashOffset=_),y.strokeFirst?(jy(y)&&f.strokeText(x,y.x,y.y),$y(y)&&f.fillText(x,y.x,y.y)):($y(y)&&f.fillText(x,y.x,y.y),jy(y)&&f.strokeText(x,y.x,y.y)),g)&&f.setLineDash([]);else if(e instanceof xs)n.lastDrawType!==sm&&(s=!0,n.lastDrawType=sm),m=o,w=s,im(t,cm(e,(D=n).inHover),m&&cm(m,D.inHover),w,D),d=t,C=a,(r=(k=e).__image=ta(C.image,k.__image,k,k.onload))&&na(r)&&(A=C.x||0,I=C.y||0,T=k.getWidth(),k=k.getHeight(),M=r.width/r.height,null==T&&null!=k?T=k*M:null==k&&null!=T?k=T/M:null==T&&null==k&&(T=r.width,k=r.height),C.sWidth&&C.sHeight?(h=C.sx||0,c=C.sy||0,d.drawImage(r,h,c,C.sWidth,C.sHeight,A,I,T,k)):C.sx&&C.sy?(h=C.sx,c=C.sy,d.drawImage(r,h,c,T-h,k-c,A,I,T,k)):d.drawImage(r,A,I,T,k));else if(e.getTemporalDisplayables){n.lastDrawType!==um&&(s=!0,n.lastDrawType=um);var L,W,G=t,O=e,R=n,U=O.getDisplayables(),X=O.getTemporalDisplayables(),Y=(G.save(),{prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:R.viewWidth,viewHeight:R.viewHeight,inHover:R.inHover});for(L=O.getCursor(),W=U.length;L<W;L++)(E=U[L]).beforeBrush&&E.beforeBrush(),E.innerBeforeBrush(),dm(G,E,Y,L===W-1),E.innerAfterBrush(),E.afterBrush&&E.afterBrush(),Y.prevEl=E;for(var E,q=0,Z=X.length;q<Z;q++)(E=X[q]).beforeBrush&&E.beforeBrush(),E.innerBeforeBrush(),dm(G,E,Y,q===Z-1),E.innerAfterBrush(),E.afterBrush&&E.afterBrush(),Y.prevEl=E;O.clearTemporalDisplayables(),O.notClear=!0,G.restore()}P&&B&&hm(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),(n.prevEl=e).__dirty=0,e.__isRendered=!0}}else e.__dirty&=~_n,e.__isRendered=!1}var fm=new Ly,gm=new ni(100),ym=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function mm(t,e){if("none"===t)return null;var a=e.getDevicePixelRatio(),s=e.getZr(),l="svg"===s.painter.type,e=(t.dirty&&fm.delete(t),fm.get(t));if(e)return e;for(var n,u=N(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512}),e=("none"===u.backgroundColor&&(u.backgroundColor=null),{repeat:"repeat"}),i=e,r=[a],o=!0,h=0;h<ym.length;++h){var c=u[ym[h]];if(null!=c&&!F(c)&&!V(c)&&!ft(c)&&"boolean"!=typeof c){o=!1;break}r.push(c)}o&&(n=r.join(",")+(l?"-svg":""),v=gm.get(n))&&(l?i.svgElement=v:i.image=v);var p,d=function t(e){if(!e||0===e.length)return[[0,0]];if(ft(e))return[[o=Math.ceil(e),o]];var n=!0;for(var i=0;i<e.length;++i)if(!ft(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i){var o;ft(e[i])?(o=Math.ceil(e[i]),r.push([o,o])):(o=z(e[i],function(t){return Math.ceil(t)})).length%2==1?r.push(o.concat(o)):r.push(o)}return r}(u.dashArrayX),f=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if(ft(t))return[e=Math.ceil(t),e];var e=z(t,function(t){return Math.ceil(t)});return t.length%2?e.concat(e):e}(u.dashArrayY),g=function t(e){if(!e||0===e.length)return[["rect"]];if(V(e))return[[e]];var n=!0;for(var i=0;i<e.length;++i)if(!V(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i)V(e[i])?r.push([e[i]]):r.push(e[i]);return r}(u.symbol),y=function(t){return z(t,vm)}(d),m=vm(f),v=!l&&H.createCanvas(),_=l&&{tag:"g",attrs:{},key:"dcl",children:[]},x=function(){for(var t=1,e=0,n=y.length;e<n;++e)t=mo(t,y[e]);for(var i=1,e=0,n=g.length;e<n;++e)i=mo(i,g[e].length);t*=i;var r=m*y.length*g.length;return{width:Math.max(1,Math.min(t,u.maxTileWidth)),height:Math.max(1,Math.min(r,u.maxTileHeight))}}();v&&(v.width=x.width*a,v.height=x.height*a,p=v.getContext("2d")),p&&(p.clearRect(0,0,v.width,v.height),u.backgroundColor)&&(p.fillStyle=u.backgroundColor,p.fillRect(0,0,v.width,v.height));for(var w=0,b=0;b<f.length;++b)w+=f[b];if(!(w<=0))for(var S=-m,M=0,T=0,C=0;S<x.height;){if(M%2==0){for(var I=T/2%g.length,k=0,D=0,A=0;k<2*x.width;){for(var P,L,O,R,E,B=0,b=0;b<d[C].length;++b)B+=d[C][b];if(B<=0)break;D%2==0&&(L=.5*(1-u.symbolSize),P=k+d[C][D]*L,L=S+f[M]*L,O=d[C][D]*u.symbolSize,R=f[M]*u.symbolSize,E=A/2%g[I].length,function(t,e,n,i,r){var o=l?1:a,r=Wy(r,t*o,e*o,n*o,i*o,u.color,u.symbolKeepAspect);l?(t=s.painter.renderOneToVNode(r))&&_.children.push(t):pm(p,r)}(P,L,O,R,g[I][E])),k+=d[C][D],++A,++D===d[C].length&&(D=0)}++C===d.length&&(C=0)}S+=f[M],++T,++M===f.length&&(M=0)}return o&&gm.put(n,v||_),i.image=v,i.svgElement=_,i.svgWidth=x.width,i.svgHeight=x.height,e.rotation=u.rotation,e.scaleX=e.scaleY=l?1:1/a,fm.set(t,e),t.dirty=!1,e}function vm(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}var _m=new ue,xm={};var Ey={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:4500,ARIA:6e3,DECAL:7e3}},wm="__flagInMainProcess",bm="__pendingUpdate",Sm="__needsUpdateStatus",Mm=/^[a-zA-Z0-9_]+$/,Tm="__connectUpdateStatus";function Cm(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(!this.isDisposed())return km(this,n,t);this.id}}function Im(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return km(this,n,t)}}function km(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),ue.prototype[e].apply(t,n)}u(Pm,Dm=ue);var Dm,Am=Pm;function Pm(){return null!==Dm&&Dm.apply(this,arguments)||this}var Lm,Om,Rm,Em,Bm,Nm,zm,Fm,Vm,Hm,Wm,Gm,Um,Xm,Ym,qm,Zm,jm,Km,By=Am.prototype,$m=(By.on=Im("on"),By.off=Im("off"),u(p,Km=ue),p.prototype._onframe=function(){if(!this._disposed){jm(this);var t=this._scheduler;if(this[bm]){var e=this[bm].silent;this[wm]=!0;try{Lm(this),Em.update.call(this,null,this[bm].updateParams)}catch(t){throw this[wm]=!1,this[bm]=null,t}this._zr.flush(),this[wm]=!1,this[bm]=null,Fm.call(this,e),Vm.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date}while(t.performSeriesTasks(i),t.performDataProcessorTasks(i),Nm(this,i),t.performVisualTasks(i),Xm(this,this._model,r,"remain",{}),0<(n-=+new Date-o)&&t.unfinished);t.unfinished||this._zr.flush()}}},p.prototype.getDom=function(){return this._dom},p.prototype.getId=function(){return this.id},p.prototype.getZr=function(){return this._zr},p.prototype.isSSR=function(){return this._ssr},p.prototype.setOption=function(t,e,n){if(!this[wm])if(this._disposed)this.id;else{L(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this[wm]=!0,this._model&&!e||(e=new vd(this._api),a=this._theme,(s=this._model=new ld).scheduler=this._scheduler,s.ssr=this._ssr,s.init(null,null,null,a,this._locale,e)),this._model.setOption(t,{replaceMerge:r},s0);var i,r,o,a,s={seriesTransition:o,optionChanged:!0};if(n)this[bm]={silent:i,updateParams:s},this[wm]=!1,this.getZr().wakeUp();else{try{Lm(this),Em.update.call(this,null,s)}catch(t){throw this[bm]=null,this[wm]=!1,t}this._ssr||this._zr.flush(),this[bm]=null,this[wm]=!1,Fm.call(this,i),Vm.call(this,i)}}},p.prototype.setTheme=function(){},p.prototype.getModel=function(){return this._model},p.prototype.getOption=function(){return this._model&&this._model.getOption()},p.prototype.getWidth=function(){return this._zr.getWidth()},p.prototype.getHeight=function(){return this._zr.getHeight()},p.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||b.hasGlobalWindow&&window.devicePixelRatio||1},p.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},p.prototype.renderToCanvas=function(t){return this._zr.painter.getRenderedCanvas({backgroundColor:(t=t||{}).backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},p.prototype.renderToSVGString=function(t){return this._zr.painter.renderToString({useViewBox:(t=t||{}).useViewBox})},p.prototype.getSvgDataURL=function(){var t;if(b.svgSupported)return R((t=this._zr).storage.getDisplayList(),function(t){t.stopAnimation(null,!0)}),t.painter.toDataURL()},p.prototype.getDataURL=function(t){var e,n,i,r;if(!this._disposed)return r=(t=t||{}).excludeComponents,e=this._model,n=[],i=this,R(r,function(t){e.eachComponent({mainType:t},function(t){t=i._componentsMap[t.__viewId];t.group.ignore||(n.push(t),t.group.ignore=!0)})}),r="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png")),R(n,function(t){t.group.ignore=!1}),r;this.id},p.prototype.getConnectedDataURL=function(i){var r,o,a,s,l,u,h,c,p,e,t,n,d,f,g;if(!this._disposed)return r="svg"===i.type,o=this.group,a=Math.min,s=Math.max,p0[o]?(u=l=1/0,c=h=-1/0,p=[],e=i&&i.pixelRatio||this.getDevicePixelRatio(),R(c0,function(t,e){var n;t.group===o&&(n=r?t.getZr().painter.getSvgDom().innerHTML:t.renderToCanvas(y(i)),t=t.getDom().getBoundingClientRect(),l=a(t.left,l),u=a(t.top,u),h=s(t.right,h),c=s(t.bottom,c),p.push({dom:n,left:t.left,top:t.top}))}),t=(h*=e)-(l*=e),n=(c*=e)-(u*=e),d=H.createCanvas(),(f=jr(d,{renderer:r?"svg":"canvas"})).resize({width:t,height:n}),r?(g="",R(p,function(t){var e=t.left-l,n=t.top-u;g+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),f.painter.getSvgRoot().innerHTML=g,i.connectedBackgroundColor&&f.painter.setBackgroundColor(i.connectedBackgroundColor),f.refreshImmediately(),f.painter.toDataURL()):(i.connectedBackgroundColor&&f.add(new Ds({shape:{x:0,y:0,width:t,height:n},style:{fill:i.connectedBackgroundColor}})),R(p,function(t){t=new xs({style:{x:t.left*e-l,y:t.top*e-u,image:t.dom}});f.add(t)}),f.refreshImmediately(),d.toDataURL("image/"+(i&&i.type||"png")))):this.getDataURL(i);this.id},p.prototype.convertToPixel=function(t,e){return Bm(this,"convertToPixel",t,e)},p.prototype.convertFromPixel=function(t,e){return Bm(this,"convertFromPixel",t,e)},p.prototype.containPixel=function(t,i){var r;if(!this._disposed)return R(Ro(this._model,t),function(t,n){0<=n.indexOf("Models")&&R(t,function(t){var e=t.coordinateSystem;e&&e.containPoint?r=r||!!e.containPoint(i):"seriesModels"===n&&(e=this._chartsMap[t.__viewId])&&e.containPoint&&(r=r||e.containPoint(i,t))},this)},this),!!r;this.id},p.prototype.getVisual=function(t,e){var t=Ro(this._model,t,{defaultMainType:"series"}),n=t.seriesModel.getData(),t=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?n.indexOfRawIndex(t.dataIndex):null;if(null!=t){var i=n,r=t,o=e;switch(o){case"color":return i.getItemVisual(r,"style")[i.getVisual("drawType")];case"opacity":return i.getItemVisual(r,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return i.getItemVisual(r,o)}}else{var a=n,s=e;switch(s){case"color":return a.getVisual("style")[a.getVisual("drawType")];case"opacity":return a.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return a.getVisual(s)}}},p.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},p.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},p.prototype._initEvents=function(){var t,n,i,s=this;R(i0,function(a){function t(t){var n,e,i,r=s.getModel(),o=t.target;"globalout"===a?n={}:o&&Dy(o,function(t){var e,t=D(t);return t&&null!=t.dataIndex?(e=t.dataModel||r.getSeriesByIndex(t.seriesIndex),n=e&&e.getDataParams(t.dataIndex,t.dataType,o)||{},1):t.eventData&&(n=O({},t.eventData),1)},!0),n&&(e=n.componentType,i=n.componentIndex,"markLine"!==e&&"markPoint"!==e&&"markArea"!==e||(e="series",i=n.seriesIndex),i=(e=e&&null!=i&&r.getComponent(e,i))&&s["series"===e.mainType?"_chartsMap":"_componentsMap"][e.__viewId],n.event=t,n.type=a,s._$eventProcessor.eventInfo={targetEl:o,packedEvent:n,model:e,view:i},s.trigger(a,n))}t.zrEventfulCallAtLast=!0,s._zr.on(a,t,s)}),R(o0,function(t,e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),R(["selectchanged"],function(e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),t=this._messageCenter,i=(n=this)._api,t.on("selectchanged",function(t){var e=i.getModel();t.isFromClick?(ky("map","selectchanged",n,e,t),ky("pie","selectchanged",n,e,t)):"select"===t.fromAction?(ky("map","selected",n,e,t),ky("pie","selected",n,e,t)):"unselect"===t.fromAction&&(ky("map","unselected",n,e,t),ky("pie","unselected",n,e,t))})},p.prototype.isDisposed=function(){return this._disposed},p.prototype.clear=function(){this._disposed?this.id:this.setOption({series:[]},!0)},p.prototype.dispose=function(){var t,e,n;this._disposed?this.id:(this._disposed=!0,this.getDom()&&zo(this.getDom(),g0,""),e=(t=this)._api,n=t._model,R(t._componentsViews,function(t){t.dispose(n,e)}),R(t._chartsViews,function(t){t.dispose(n,e)}),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete c0[t.id])},p.prototype.resize=function(t){if(!this[wm])if(this._disposed)this.id;else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var e=e.resetOption("media"),n=t&&t.silent;this[bm]&&(null==n&&(n=this[bm].silent),e=!0,this[bm]=null),this[wm]=!0;try{e&&Lm(this),Em.update.call(this,{type:"resize",animation:O({duration:0},t&&t.animation)})}catch(t){throw this[wm]=!1,t}this[wm]=!1,Fm.call(this,n),Vm.call(this,n)}}},p.prototype.showLoading=function(t,e){this._disposed?this.id:(L(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),h0[t]&&(t=h0[t](this._api,e),e=this._zr,this._loadingFX=t,e.add(t)))},p.prototype.hideLoading=function(){this._disposed?this.id:(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},p.prototype.makeActionFromEvent=function(t){var e=O({},t);return e.type=o0[t.type],e},p.prototype.dispatchAction=function(t,e){var n;this._disposed?this.id:(L(e)||(e={silent:!!e}),r0[t.type]&&this._model&&(this[wm]?this._pendingActions.push(t):(n=e.silent,zm.call(this,t,n),(t=e.flush)?this._zr.flush():!1!==t&&b.browser.weChat&&this._throttledZrFlush(),Fm.call(this,n),Vm.call(this,n))))},p.prototype.updateLabelLayout=function(){_m.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},p.prototype.appendData=function(t){var e;this._disposed?this.id:(e=t.seriesIndex,this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp())},p.internalField=(Lm=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),Om(t,!0),Om(t,!1),e.plan()},Om=function(t,r){for(var o=t._model,a=t._scheduler,s=r?t._componentsViews:t._chartsViews,l=r?t._componentsMap:t._chartsMap,u=t._zr,h=t._api,e=0;e<s.length;e++)s[e].__alive=!1;function n(t){var e,n=t.__requireNewView,i=(t.__requireNewView=!1,"_ec_"+t.id+"_"+t.type),n=!n&&l[i];n||(e=Wo(t.type),(n=new(r?Bg.getClass(e.main,e.sub):Hg.getClass(e.sub))).init(o,h),l[i]=n,s.push(n),u.add(n.group)),t.__viewId=n.__id=i,n.__alive=!0,n.__model=t,n.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},r||a.prepareView(n,t,o,h)}r?o.eachComponent(function(t,e){"series"!==t&&n(e)}):o.eachSeries(n);for(e=0;e<s.length;){var i=s[e];i.__alive?e++:(r||i.renderTask.dispose(),u.remove(i.group),i.dispose(o,h),s.splice(e,1),l[i.__id]===i&&delete l[i.__id],i.__id=i.group.__ecComponentInfo=null)}},Rm=function(c,e,p,n,t){var i,d,r=c._model;function o(t){t&&t.__alive&&t[e]&&t[e](t.__model,r,c._api,p)}r.setUpdatePayload(p),n?((i={})[n+"Id"]=p[n+"Id"],i[n+"Index"]=p[n+"Index"],i[n+"Name"]=p[n+"Name"],i={mainType:n,query:i},t&&(i.subType=t),null!=(t=p.excludeSeriesId)&&(d=B(),R(xo(t),function(t){t=Io(t,null);null!=t&&d.set(t,!0)})),r&&r.eachComponent(i,function(t){var e,n,i=d&&null!=d.get(t.id);if(!i)if(Fl(p))if(t instanceof kg){if(p.type===el&&!p.notBlur&&!t.get(["emphasis","disabled"])){var i=t,r=p,o=c._api,a=i.seriesIndex,s=i.getData(r.dataType);if(s){var r=(F(r=Po(s,r))?r[0]:r)||0,l=s.getItemGraphicEl(r);if(!l)for(var u=s.count(),h=0;!l&&h<u;)l=s.getItemGraphicEl(h++);l?kl(a,(r=D(l)).focus,r.blurScope,o):(r=i.get(["emphasis","focus"]),i=i.get(["emphasis","blurScope"]),null!=r&&kl(a,r,i,o))}}}else{a=Al(t.mainType,t.componentIndex,p.name,c._api),r=a.focusSelf,i=a.dispatchers;p.type===el&&r&&!p.notBlur&&Dl(t.mainType,t.componentIndex,c._api),i&&R(i,function(t){(p.type===el?wl:bl)(t)})}else zl(p)&&t instanceof kg&&(o=t,i=p,c._api,zl(i)&&(e=i.dataType,F(n=Po(o.getData(e),i))||(n=[n]),o[i.type===ol?"toggleSelect":i.type===il?"select":"unselect"](n,e)),Pl(t),Zm(c))},c),r&&r.eachComponent(i,function(t){d&&null!=d.get(t.id)||o(c["series"===n?"_chartsMap":"_componentsMap"][t.__viewId])},c)):R([].concat(c._componentsViews).concat(c._chartsViews),o)},Em={prepareAndUpdate:function(t){Lm(this),Em.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(t,e){var n=this._model,i=this._api,r=this._zr,o=this._coordSysMgr,a=this._scheduler;n&&(n.setUpdatePayload(t),a.restoreData(n,t),a.performSeriesTasks(n),o.create(n,i),a.performDataProcessorTasks(n,t),Nm(this,n),o.update(n,i),Qm(n),a.performVisualTasks(n,t),Gm(this,n,i,t,e),o=n.get("backgroundColor")||"transparent",a=n.get("darkMode"),r.setBackgroundColor(o),null!=a&&"auto"!==a&&r.setDarkMode(a),_m.trigger("afterupdate",n,i))},updateTransform:function(n){var i,r,o=this,a=this._model,s=this._api;a&&(a.setUpdatePayload(n),i=[],a.eachComponent(function(t,e){"series"!==t&&(t=o.getViewOfComponentModel(e))&&t.__alive&&(!t.updateTransform||(e=t.updateTransform(e,a,s,n))&&e.update)&&i.push(t)}),r=B(),a.eachSeries(function(t){var e=o._chartsMap[t.__viewId];(!e.updateTransform||(e=e.updateTransform(t,a,s,n))&&e.update)&&r.set(t.uid,1)}),Qm(a),this._scheduler.performVisualTasks(a,n,{setDirty:!0,dirtyMap:r}),Xm(this,a,s,n,{},r),_m.trigger("afterupdate",a,s))},updateView:function(t){var e=this._model;e&&(e.setUpdatePayload(t),Hg.markUpdateMethod(t,"updateView"),Qm(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),Gm(this,e,this._api,t,{}),_m.trigger("afterupdate",e,this._api))},updateVisual:function(n){var i=this,r=this._model;r&&(r.setUpdatePayload(n),r.eachSeries(function(t){t.getData().clearAllVisual()}),Hg.markUpdateMethod(n,"updateVisual"),Qm(r),this._scheduler.performVisualTasks(r,n,{visualType:"visual",setDirty:!0}),r.eachComponent(function(t,e){"series"!==t&&(t=i.getViewOfComponentModel(e))&&t.__alive&&t.updateVisual(e,r,i._api,n)}),r.eachSeries(function(t){i._chartsMap[t.__viewId].updateVisual(t,r,i._api,n)}),_m.trigger("afterupdate",r,this._api))},updateLayout:function(t){Em.update.call(this,t)}},Bm=function(t,e,n,i){if(t._disposed)t.id;else for(var r=t._model,o=t._coordSysMgr.getCoordinateSystems(),a=Ro(r,n),s=0;s<o.length;s++){var l=o[s];if(l[e]&&null!=(l=l[e](r,a,i)))return l}},Nm=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})},zm=function(i,t){var r,o,a=this,e=this.getModel(),n=i.type,s=i.escapeConnect,l=r0[n],u=l.actionInfo,h=(u.update||"update").split(":"),c=h.pop(),p=null!=h[0]&&Wo(h[0]),h=(this[wm]=!0,[i]),d=!1,f=(i.batch&&(d=!0,h=z(i.batch,function(t){return(t=N(O({},t),i)).batch=null,t})),[]),g=zl(i),y=Fl(i);if(y&&Il(this._api),R(h,function(t){var e,n;(r=(r=l.action(t,a._model,a._api))||O({},t)).type=u.event||r.type,f.push(r),y?(e=(n=Eo(i)).queryOptionMap,n=n.mainTypeSpecified?e.keys()[0]:"series",Rm(a,c,t,n),Zm(a)):g?(Rm(a,c,t,"series"),Zm(a)):p&&Rm(a,c,t,p.main,p.sub)}),"none"!==c&&!y&&!g&&!p)try{this[bm]?(Lm(this),Em.update.call(this,i),this[bm]=null):Em[c].call(this,i)}catch(t){throw this[wm]=!1,t}r=d?{type:u.event||n,escapeConnect:s,batch:f}:f[0],this[wm]=!1,t||((h=this._messageCenter).trigger(r.type,r),g&&(d={type:"selectchanged",escapeConnect:s,selected:(o=[],e.eachSeries(function(n){R(n.getAllData(),function(t){t.data;var t=t.type,e=n.getSelectedDataIndices();0<e.length&&(e={dataIndex:e,seriesIndex:n.seriesIndex},null!=t&&(e.dataType=t),o.push(e))})}),o),isFromClick:i.isFromClick||!1,fromAction:i.type,fromActionPayload:i},h.trigger(d.type,d)))},Fm=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();zm.call(this,n,t)}},Vm=function(t){t||this.trigger("updated")},Hm=function(e,n){e.on("rendered",function(t){n.trigger("rendered",t),!e.animation.isFinished()||n[bm]||n._scheduler.unfinished||n._pendingActions.length||n.trigger("finished")})},Wm=function(t,a){t.on("mouseover",function(t){var e,n,i,r,o=Dy(t.target,Nl);o&&(o=o,e=t,t=a._api,n=D(o),i=(r=Al(n.componentMainType,n.componentIndex,n.componentHighDownName,t)).dispatchers,r=r.focusSelf,i?(r&&Dl(n.componentMainType,n.componentIndex,t),R(i,function(t){return _l(t,e)})):(kl(n.seriesIndex,n.focus,n.blurScope,t),"self"===n.focus&&Dl(n.componentMainType,n.componentIndex,t),_l(o,e)),Zm(a))}).on("mouseout",function(t){var e,n,i=Dy(t.target,Nl);i&&(i=i,e=t,Il(t=a._api),(n=Al((n=D(i)).componentMainType,n.componentIndex,n.componentHighDownName,t).dispatchers)?R(n,function(t){return xl(t,e)}):xl(i,e),Zm(a))}).on("click",function(t){var e,t=Dy(t.target,function(t){return null!=D(t).dataIndex},!0);t&&(e=t.selected?"unselect":"select",t=D(t),a._api.dispatchAction({type:e,dataType:t.dataType,dataIndexInside:t.dataIndex,seriesIndex:t.seriesIndex,isFromClick:!0}))})},Gm=function(t,e,n,i,r){var o,a,s,l,u,h,c;u=[],c=!(h=[]),(o=e).eachComponent(function(t,e){var n=e.get("zlevel")||0,i=e.get("z")||0,r=e.getZLevelKey();c=c||!!r,("series"===t?h:u).push({zlevel:n,z:i,idx:e.componentIndex,type:t,key:r})}),c&&(vn(a=u.concat(h),function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel}),R(a,function(t){var e=o.getComponent(t.type,t.idx),n=t.zlevel,t=t.key;null!=s&&(n=Math.max(s,n)),t?(n===s&&t!==l&&n++,l=t):l&&(n===s&&n++,l=""),s=n,e.setZLevel(n)})),Um(t,e,n,i,r),R(t._chartsViews,function(t){t.__alive=!1}),Xm(t,e,n,i,r),R(t._chartsViews,function(t){t.__alive||t.remove(e,n)})},Um=function(t,n,i,r,e,o){R(o||t._componentsViews,function(t){var e=t.__model;e0(0,t),t.render(e,n,i,r),t0(e,t),n0(e,t)})},Xm=function(r,t,e,o,n,a){var i,s,l,u,h=r._scheduler,c=(n=O(n||{},{updatedSeries:t.getSeries()}),_m.trigger("series:beforeupdate",t,e,n),!1);t.eachSeries(function(t){var e,n=r._chartsMap[t.__viewId],i=(n.__alive=!0,n.renderTask);h.updatePayload(i,o),e0(0,n),a&&a.get(t.uid)&&i.dirty(),i.perform(h.getPerformArgs(i))&&(c=!0),n.group.silent=!!t.get("silent"),i=n,e=t.get("blendMode")||null,i.eachRendered(function(t){t.isGroup||(t.style.blend=e)}),Pl(t)}),h.unfinished=c||h.unfinished,_m.trigger("series:layoutlabels",t,e,n),_m.trigger("series:transition",t,e,n),t.eachSeries(function(t){var e=r._chartsMap[t.__viewId];t0(t,e),n0(t,e)}),s=t,l=(i=r)._zr.storage,u=0,l.traverse(function(t){t.isGroup||u++}),u>s.get("hoverLayerThreshold")&&!b.node&&!b.worker&&s.eachSeries(function(t){t.preventUsingHoverLayer||(t=i._chartsMap[t.__viewId]).__alive&&t.eachRendered(function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)})}),_m.trigger("series:afterupdate",t,e,n)},Zm=function(t){t[Sm]=!0,t.getZr().wakeUp()},jm=function(t){t[Sm]&&(t.getZr().storage.traverse(function(t){Ih(t)||Jm(t)}),t[Sm]=!1)},Ym=function(n){return u(t,e=pd),t.prototype.getCoordinateSystems=function(){return n._coordSysMgr.getCoordinateSystems()},t.prototype.getComponentByElement=function(t){for(;t;){var e=t.__ecComponentInfo;if(null!=e)return n._model.getComponent(e.mainType,e.index);t=t.parent}},t.prototype.enterEmphasis=function(t,e){wl(t,e),Zm(n)},t.prototype.leaveEmphasis=function(t,e){bl(t,e),Zm(n)},t.prototype.enterBlur=function(t){gl(t,hl),Zm(n)},t.prototype.leaveBlur=function(t){Sl(t),Zm(n)},t.prototype.enterSelect=function(t){Ml(t),Zm(n)},t.prototype.leaveSelect=function(t){Tl(t),Zm(n)},t.prototype.getModel=function(){return n.getModel()},t.prototype.getViewOfComponentModel=function(t){return n.getViewOfComponentModel(t)},t.prototype.getViewOfSeriesModel=function(t){return n.getViewOfSeriesModel(t)},new t(n);function t(){return null!==e&&e.apply(this,arguments)||this}var e},void(qm=function(i){function r(t,e){for(var n=0;n<t.length;n++)t[n][Tm]=e}R(o0,function(t,e){i._messageCenter.on(e,function(t){var e,n;!p0[i.group]||0===i[Tm]||t&&t.escapeConnect||(e=i.makeActionFromEvent(t),n=[],R(c0,function(t){t!==i&&t.group===i.group&&n.push(t)}),r(n,0),R(n,function(t){1!==t[Tm]&&t.dispatchAction(e)}),r(n,2))})})})),p);function p(t,e,n){var i=Km.call(this,new My)||this,t=(i._chartsViews=[],i._chartsMap={},i._componentsViews=[],i._componentsMap={},i._pendingActions=[],n=n||{},V(e)&&(e=u0[e]),i._dom=t,n.ssr&&$r(function(t){var e,t=D(t),n=t.dataIndex;if(null!=n)return(e=B()).set("series_index",t.seriesIndex),e.set("data_index",n),t.ssrType&&e.set("ssr_type",t.ssrType),e}),i._zr=jr(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:E(n.useDirtyRect,!1),useCoarsePointer:E(n.useCoarsePointer,"auto"),pointerSize:n.pointerSize})),n=(i._ssr=n.ssr,i._throttledZrFlush=$g(ct(t.flush,t),17),(e=y(e))&&zd(e,!0),i._theme=e,i._locale=V(e=n.locale||Nc)?(n=Ec[e.toUpperCase()]||{},e===Lc||e===Oc?y(n):d(y(n),y(Ec[Rc]),!1)):d(y(e),y(Ec[Rc]),!1),i._coordSysMgr=new gd,i._api=Ym(i));function r(t,e){return t.__prio-e.__prio}return vn(l0,r),vn(a0,r),i._scheduler=new sy(i,n,a0,l0),i._messageCenter=new Am,i._initEvents(),i.resize=ct(i.resize,i),t.animation.on("frame",i._onframe,i),Hm(t,i),Wm(t,i),Dt(i),i}function Qm(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function Jm(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),t.hoverState===Qs&&t.states.emphasis?e.push("emphasis"):t.hoverState===$s&&t.states.blur&&e.push("blur"),t.useStates(e)}function t0(t,e){var n,i;t.preventAutoZ||(n=t.get("z")||0,i=t.get("zlevel")||0,e.eachRendered(function(t){return function t(e,n,i,r){var o=e.getTextContent();var a=e.getTextGuideLine();var s=e.isGroup;if(s)for(var l=e.childrenRef(),u=0;u<l.length;u++)r=Math.max(t(l[u],n,i,r),r);else e.z=n,e.zlevel=i,r=Math.max(e.z2,r);o&&(o.z=n,o.zlevel=i,isFinite(r))&&(o.z2=r+2);a&&(s=e.textGuideLineConfig,a.z=n,a.zlevel=i,isFinite(r))&&(a.z2=r+(s&&s.showAbove?1:-1));return r}(t,n,i,-1/0),!0}))}function e0(t,e){e.eachRendered(function(t){var e,n;Ih(t)||(e=t.getTextContent(),n=t.getTextGuideLine(),t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null))})}function n0(t,e){var n=t.getModel("stateAnimation"),r=t.isAnimationEnabled(),t=n.get("duration"),o=0<t?{duration:t,delay:n.get("delay"),easing:n.get("easing")}:null;e.eachRendered(function(t){var e,n,i;t.states&&t.states.emphasis&&(Ih(t)||(t instanceof j&&((i=Zs(n=t)).normalFill=n.style.fill,i.normalStroke=n.style.stroke,n=n.states.select||{},i.selectFill=n.style&&n.style.fill||null,i.selectStroke=n.style&&n.style.stroke||null),t.__dirty&&(i=t.prevStates)&&t.useStates(i),r&&(t.stateTransition=o,n=t.getTextContent(),e=t.getTextGuideLine(),n&&(n.stateTransition=o),e)&&(e.stateTransition=o),t.__dirty&&Jm(t)))})}var Ny=$m.prototype,i0=(Ny.on=Cm("on"),Ny.off=Cm("off"),Ny.one=function(i,r,t){var o=this;this.on.call(this,i,function t(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];r&&r.apply&&r.apply(this,e),o.off(i,t)},t)},["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"]);var r0={},o0={},a0=[],s0=[],l0=[],u0={},h0={},c0={},p0={},d0=+new Date,f0=+new Date,g0="_echarts_instance_";function y0(t){p0[t]=!1}Ry=y0;function m0(t){return c0[e=g0,(t=t).getAttribute?t.getAttribute(e):t[e]];var e}function v0(t,e){u0[t]=e}function _0(t){I(s0,t)<0&&s0.push(t)}function x0(t,e){D0(a0,t,e,2e3)}function w0(t){S0("afterinit",t)}function b0(t){S0("afterupdate",t)}function S0(t,e){_m.on(t,e)}function M0(t,e,n){k(e)&&(n=e,e="");var i=L(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,o0[e]||(Ct(Mm.test(i)&&Mm.test(e)),r0[i]||(r0[i]={action:n,actionInfo:t}),o0[e]=i)}function T0(t,e){gd.register(t,e)}function C0(t,e){D0(l0,t,e,1e3,"layout")}function I0(t,e){D0(l0,t,e,3e3,"visual")}var k0=[];function D0(t,e,n,i,r){(k(e)||L(e))&&(n=e,e=i),0<=I(k0,n)||(k0.push(n),(i=sy.wrapStageHandler(n,r)).__prio=e,i.__raw=n,t.push(i))}function A0(t,e){h0[t]=e}function P0(t,e,n){var i=xm.registerMap;i&&i(t,e,n)}function L0(t){var e=(t=y(t)).type,n=(e||f(""),e.split(":")),i=(2!==n.length&&f(""),!1);"echarts"===n[0]&&(e=n[1],i=!0),t.__isBuiltIn=i,Xf.set(e,t)}I0(2e3,$o),I0(4500,Xh),I0(4500,Uc),I0(2e3,Sc),I0(4500,_c),I0(7e3,function(e,i){e.eachRawSeries(function(t){var n;!e.isSeriesFiltered(t)&&((n=t.getData()).hasItemVisual()&&n.each(function(t){var e=n.getItemVisual(t,"decal");e&&(n.ensureUniqueItemVisual(t,"style").decal=mm(e,i))}),t=n.getVisual("decal"))&&(n.getVisual("style").decal=mm(t,i))})}),_0(zd),x0(900,function(t){var i=B();t.eachSeries(function(t){var e,n=t.get("stack");n&&(n=i.get(n)||i.set(n,[]),(t={stackResultDimension:(e=t.getData()).getCalculationInfo("stackResultDimension"),stackedOverDimension:e.getCalculationInfo("stackedOverDimension"),stackedDimension:e.getCalculationInfo("stackedDimension"),stackedByDimension:e.getCalculationInfo("stackedByDimension"),isStackedByIndex:e.getCalculationInfo("isStackedByIndex"),data:e,seriesModel:t}).stackedDimension)&&(t.isStackedByIndex||t.stackedByDimension)&&(n.length&&e.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(t))}),i.each(Fd)}),A0("default",function(i,r){N(r=r||{},{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var o,t=new Wr,a=new Ds({style:{fill:r.maskColor},zlevel:r.zlevel,z:1e4}),s=(t.add(a),new Rs({style:{text:r.text,fill:r.textColor,fontSize:r.fontSize,fontWeight:r.fontWeight,fontStyle:r.fontStyle,fontFamily:r.fontFamily},zlevel:r.zlevel,z:10001})),l=new Ds({style:{fill:"none"},textContent:s,textConfig:{position:"right",distance:10},zlevel:r.zlevel,z:10001});return t.add(l),r.showSpinner&&((o=new nh({shape:{startAngle:-ay/2,endAngle:-ay/2+.1,r:r.spinnerRadius},style:{stroke:r.color,lineCap:"round",lineWidth:r.lineWidth},zlevel:r.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*ay/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*ay/2}).delay(300).start("circularInOut"),t.add(o)),t.resize=function(){var t=s.getBoundingRect().width,e=r.showSpinner?r.spinnerRadius:0,t=(i.getWidth()-2*e-(r.showSpinner&&t?10:0)-t)/2-(r.showSpinner&&t?0:5+t/2)+(r.showSpinner?0:t/2)+(t?0:e),n=i.getHeight()/2;r.showSpinner&&o.setShape({cx:t,cy:n}),l.setShape({x:t-e,y:n-e,width:2*e,height:2*e}),a.setShape({x:0,y:0,width:i.getWidth(),height:i.getHeight()})},t.resize(),t}),M0({type:el,event:el,update:el},Vt),M0({type:nl,event:nl,update:nl},Vt),M0({type:il,event:il,update:il},Vt),M0({type:rl,event:rl,update:rl},Vt),M0({type:ol,event:ol,update:ol},Vt),v0("light",Tc),v0("dark",wc);function O0(t){return null==t?0:t.length||1}function R0(t){return t}B0.prototype.add=function(t){return this._add=t,this},B0.prototype.update=function(t){return this._update=t,this},B0.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},B0.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},B0.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},B0.prototype.remove=function(t){return this._remove=t,this},B0.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},B0.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a,s=i[o],l=n[s],u=O0(l);1<u?(a=l.shift(),1===l.length&&(n[s]=l[0]),this._update&&this._update(a,o)):1===u?(n[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},B0.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=O0(l),c=O0(u);if(1<h&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&1<c)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(1<h&&1<c)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(1<h)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},B0.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=O0(r);if(1<o)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},B0.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a,s,l="_ec_"+this[i](t[o],o);r||(n[o]=l),e&&(0===(s=O0(a=e[l]))?(e[l]=o,r&&n.push(l)):1===s?e[l]=[a,o]:a.push(o))}};var E0=B0;function B0(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||R0,this._newKeyGetter=i||R0,this.context=r,this._diffModeMultiple="multiple"===o}z0.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},z0.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames};var N0=z0;function z0(t,e){this._encode=t,this._schema=e}function F0(o,t){var e={},a=e.encode={},s=B(),l=[],u=[],h={},i=(R(o.dimensions,function(t){var e,n,i=o.getDimensionInfo(t),r=i.coordDim;r&&(e=i.coordDimIndex,V0(a,r)[e]=t,i.isExtraCoord||(s.set(r,1),"ordinal"!==(n=i.type)&&"time"!==n&&(l[0]=t),V0(h,r)[e]=o.getDimensionIndex(i.name)),i.defaultTooltip)&&u.push(t),zp.each(function(t,e){var n=V0(a,e),e=i.otherDims[e];null!=e&&!1!==e&&(n[e]=i.name)})}),[]),r={},n=(s.each(function(t,e){var n=a[e];r[e]=n[0],i=i.concat(n)}),e.dataDimsOnCoord=i,e.dataDimIndicesOnCoord=z(i,function(t){return o.getDimensionInfo(t).storeDimIndex}),e.encodeFirstDimNotExtra=r,a.label),n=(n&&n.length&&(l=n.slice()),a.tooltip);return n&&n.length?u=n.slice():u.length||(u=l.slice()),a.defaultedLabel=l,a.defaultedTooltip=u,e.userOutput=new N0(h,t),e}function V0(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}var H0=function(t){this.otherDims={},null!=t&&O(this,t)},W0=Lo(),G0={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},U0=(X0.prototype.isDimensionOmitted=function(){return this._dimOmitted},X0.prototype._updateDimOmitted=function(t){(this._dimOmitted=t)&&!this._dimNameMap&&(this._dimNameMap=Z0(this.source))},X0.prototype.getSourceDimensionIndex=function(t){return E(this._dimNameMap.get(t),-1)},X0.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},X0.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=jd(this.source),n=!(30<t),i="",r=[],o=0,a=0;o<t;o++){var s,l=void 0,u=void 0,h=void 0,c=this.dimensions[a];c&&c.storeDimIndex===o?(l=e?c.name:null,u=c.type,h=c.ordinalMeta,a++):(s=this.getSourceDimension(o))&&(l=e?s.name:null,u=s.type),r.push({property:l,type:u,ordinalMeta:h}),!e||null==l||c&&c.isCalculationCoord||(i+=n?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),i=i+"$"+(G0[u]||"f"),h&&(i+=h.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},X0.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];r&&r.storeDimIndex===e?(r.isCalculationCoord||(i=r.name),n++):(r=this.getSourceDimension(e))&&(i=r.name),t.push(i)}return t},X0.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},X0);function X0(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}function Y0(t){return t instanceof U0}function q0(t){for(var e=B(),n=0;n<(t||[]).length;n++){var i=t[n],i=L(i)?i.name:i;null!=i&&null==e.get(i)&&e.set(i,n)}return e}function Z0(t){var e=W0(t);return e.dimNameMap||(e.dimNameMap=q0(t.dimensionsDefine))}var j0,K0,$0,Q0,J0,tv,ev,nv=L,iv=z,rv="undefined"==typeof Int32Array?Array:Int32Array,ov=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],av=["_approximateExtent"],sv=(m.prototype.getDimension=function(t){var e;return null==(e=this._recognizeDimIndex(t))?t:(e=t,this._dimOmitted?null!=(t=this._dimIdxToName.get(e))?t:(t=this._schema.getSourceDimension(e))?t.name:void 0:this.dimensions[e])},m.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);return null!=e?e:null==t?-1:(e=this._getDimInfo(t))?e.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},m.prototype._recognizeDimIndex=function(t){if(ft(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},m.prototype._getStoreDimIndex=function(t){return this.getDimensionIndex(t)},m.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},m.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},m.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},m.prototype.mapDimension=function(t,e){var n=this._dimSummary;return null==e?n.encodeFirstDimNotExtra[t]:(n=n.encode[t])?n[e]:null},m.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},m.prototype.getStore=function(){return this._store},m.prototype.initData=function(t,e,n){var i,r,o=this;(i=t instanceof ng?t:i)||(r=this.dimensions,t=Gd(t)||st(t)?new Jd(t,r.length):t,i=new ng,r=iv(r,function(t){return{type:o._dimInfos[t].type,property:t}}),i.initData(t,r,n)),this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=F0(this,this._schema),this.userOutput=this._dimSummary.userOutput},m.prototype.appendData=function(t){t=this._store.appendData(t);this._doInit(t[0],t[1])},m.prototype.appendValues=function(t,e){var t=this._store.appendValues(t,e.length),n=t.start,i=t.end,r=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var o=n;o<i;o++)this._nameList[o]=e[o-n],r&&ev(this,o)},m.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},m.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==Gp&&!t.fillStorage},m.prototype._doInit=function(t,e){if(!(e<=t)){var n=this._store.getProvider(),i=(this._updateOrdinalMeta(),this._nameList),r=this._idList;if(n.getSource().sourceFormat===Fp&&!n.pure)for(var o=[],a=t;a<e;a++){var s,l=n.getItem(a,o);this.hasItemOption||!L(s=l)||s instanceof Array||(this.hasItemOption=!0),l&&(s=l.name,null==i[a]&&null!=s&&(i[a]=Io(s,null)),l=l.id,null==r[a])&&null!=l&&(r[a]=Io(l,null))}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)ev(this,a);j0(this)}},m.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},m.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},m.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},m.prototype.setCalculationInfo=function(t,e){nv(t)?O(this._calculationInfo,t):this._calculationInfo[t]=e},m.prototype.getName=function(t){var t=this.getRawIndex(t),e=this._nameList[t];return e=null==(e=null==e&&null!=this._nameDimIdx?$0(this,this._nameDimIdx,t):e)?"":e},m.prototype._getCategory=function(t,e){e=this._store.get(t,e),t=this._store.getOrdinalMeta(t);return t?t.categories[e]:e},m.prototype.getId=function(t){return K0(this,this.getRawIndex(t))},m.prototype.count=function(){return this._store.count()},m.prototype.get=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.get(t.storeDimIndex,e)},m.prototype.getByRawIndex=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.getByRawIndex(t.storeDimIndex,e)},m.prototype.getIndices=function(){return this._store.getIndices()},m.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},m.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},m.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},m.prototype.getValues=function(t,e){var n=this,i=this._store;return F(t)?i.getValues(iv(t,function(t){return n._getStoreDimIndex(t)}),e):i.getValues(t)},m.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},m.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},m.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},m.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},m.prototype.rawIndexOf=function(t,e){t=(t&&this._invertedIndicesMap[t])[e];return null==t||isNaN(t)?-1:t},m.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},m.prototype.each=function(t,e,n){k(t)&&(n=e,e=t,t=[]);n=n||this,t=iv(Q0(t),this._getStoreDimIndex,this);this._store.each(t,n?ct(e,n):e)},m.prototype.filterSelf=function(t,e,n){k(t)&&(n=e,e=t,t=[]);n=n||this,t=iv(Q0(t),this._getStoreDimIndex,this);return this._store=this._store.filter(t,n?ct(e,n):e),this},m.prototype.selectRange=function(n){var i=this,r={};return R(ht(n),function(t){var e=i._getStoreDimIndex(t);r[e]=n[t]}),this._store=this._store.selectRange(r),this},m.prototype.mapArray=function(t,e,n){k(t)&&(n=e,e=t,t=[]);var i=[];return this.each(t,function(){i.push(e&&e.apply(this,arguments))},n=n||this),i},m.prototype.map=function(t,e,n,i){n=n||i||this,i=iv(Q0(t),this._getStoreDimIndex,this),t=tv(this);return t._store=this._store.map(i,n?ct(e,n):e),t},m.prototype.modify=function(t,e,n,i){n=n||i||this,i=iv(Q0(t),this._getStoreDimIndex,this);this._store.modify(i,n?ct(e,n):e)},m.prototype.downSample=function(t,e,n,i){var r=tv(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},m.prototype.lttbDownSample=function(t,e){var n=tv(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},m.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},m.prototype.getItemModel=function(t){var e=this.hostModel,t=this.getRawDataItem(t);return new kc(t,e,e&&e.ecModel)},m.prototype.diff=function(e){var n=this;return new E0(e?e.getStore().getIndices():[],this.getStore().getIndices(),function(t){return K0(e,t)},function(t){return K0(n,t)})},m.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},m.prototype.setVisual=function(t,e){this._visual=this._visual||{},nv(t)?O(this._visual,t):this._visual[t]=e},m.prototype.getItemVisual=function(t,e){t=this._itemVisuals[t],t=t&&t[e];return null==t?this.getVisual(e):t},m.prototype.hasItemVisual=function(){return 0<this._itemVisuals.length},m.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t],n=(i=i||(n[t]={}))[e];return null==n&&(F(n=this.getVisual(e))?n=n.slice():nv(n)&&(n=O({},n)),i[e]=n),n},m.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,nv(e)?O(i,e):i[e]=n},m.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},m.prototype.setLayout=function(t,e){nv(t)?O(this._layout,t):this._layout[t]=e},m.prototype.getLayout=function(t){return this._layout[t]},m.prototype.getItemLayout=function(t){return this._itemLayouts[t]},m.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?O(this._itemLayouts[t]||{},e):e},m.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},m.prototype.setItemGraphicEl=function(t,e){var n,i,r,o,a=this.hostModel&&this.hostModel.seriesIndex;n=a,i=this.dataType,r=t,(a=e)&&((o=D(a)).dataIndex=r,o.dataType=i,o.seriesIndex=n,o.ssrType="chart","group"===a.type)&&a.traverse(function(t){t=D(t);t.seriesIndex=n,t.dataIndex=r,t.dataType=i,t.ssrType="chart"}),this._graphicEls[t]=e},m.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},m.prototype.eachItemGraphicEl=function(n,i){R(this._graphicEls,function(t,e){t&&n&&n.call(i,t,e)})},m.prototype.cloneShallow=function(t){return t=t||new m(this._schema||iv(this.dimensions,this._getDimInfo,this),this.hostModel),J0(t,this),t._store=this._store,t},m.prototype.wrapMethod=function(t,e){var n=this[t];k(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(Mt(arguments)))})},m.internalField=(j0=function(a){var s=a._invertedIndicesMap;R(s,function(t,e){var n=a._dimInfos[e],i=n.ordinalMeta,r=a._store;if(i){t=s[e]=new rv(i.categories.length);for(var o=0;o<t.length;o++)t[o]=-1;for(o=0;o<r.count();o++)t[r.get(n.storeDimIndex,o)]=o}})},$0=function(t,e,n){return Io(t._getCategory(e,n),null)},K0=function(t,e){var n=t._idList[e];return n=null==(n=null==n&&null!=t._idDimIdx?$0(t,t._idDimIdx,e):n)?"e\0\0"+e:n},Q0=function(t){return t=F(t)?t:null!=t?[t]:[]},tv=function(t){var e=new m(t._schema||iv(t.dimensions,t._getDimInfo,t),t.hostModel);return J0(e,t),e},J0=function(e,n){R(ov.concat(n.__wrappedMethods||[]),function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e.__wrappedMethods=n.__wrappedMethods,R(av,function(t){e[t]=y(n[t])}),e._calculationInfo=O({},n._calculationInfo)},void(ev=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];null==a&&null!=r&&(n[e]=a=$0(t,r,e)),null==s&&null!=o&&(i[e]=s=$0(t,o,e)),null==s&&null!=a&&(s=a,1<(r=(n=t._nameRepeatCount)[a]=(n[a]||0)+1)&&(s+="__ec__"+r),i[e]=s)})),m);function m(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"];for(var n,i,r=!(this.DOWNSAMPLE_METHODS=["downSample","lttbDownSample"]),o=(Y0(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(r=!0,n=t),n=n||["x","y"],{}),a=[],s={},l=!1,u={},h=0;h<n.length;h++){var c=n[h],c=V(c)?new H0({name:c}):c instanceof H0?c:new H0(c),p=c.name,d=(c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0),c.otherDims=c.otherDims||{});a.push(p),null!=u[p]&&(l=!0),(o[p]=c).createInvertedIndices&&(s[p]=[]),0===d.itemName&&(this._nameDimIdx=h),0===d.itemId&&(this._idDimIdx=h),r&&(c.storeDimIndex=h)}this.dimensions=a,this._dimInfos=o,this._initGetDimensionInfo(l),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted&&(i=this._dimIdxToName=B(),R(a,function(t){i.set(o[t].storeDimIndex,t)}))}function lv(t,e){Gd(t)||(t=Xd(t));for(var n,i,r=(e=e||{}).coordDimensions||[],o=e.dimensionsDefine||t.dimensionsDefine||[],a=B(),s=[],l=(u=t,n=r,p=e.dimensionsCount,i=Math.max(u.dimensionsDetectedCount||1,n.length,o.length,p||0),R(n,function(t){L(t)&&(t=t.dimsDef)&&(i=Math.max(i,t.length))}),i),u=e.canOmitUnusedDimensions&&30<l,h=o===t.dimensionsDefine,c=h?Z0(t):q0(o),p=e.encodeDefine,d=B(p=!p&&e.encodeDefaulter?e.encodeDefaulter(t,l):p),f=new $f(l),g=0;g<f.length;g++)f[g]=-1;function y(t){var e,n,i,r=f[t];return r<0?(e=L(e=o[t])?e:{name:e},n=new H0,null!=(i=e.name)&&null!=c.get(i)&&(n.name=n.displayName=i),null!=e.type&&(n.type=e.type),null!=e.displayName&&(n.displayName=e.displayName),f[t]=s.length,n.storeDimIndex=t,s.push(n),n):s[r]}if(!u)for(g=0;g<l;g++)y(g);d.each(function(t,n){var i,t=xo(t).slice();1===t.length&&!V(t[0])&&t[0]<0?d.set(n,!1):(i=d.set(n,[]),R(t,function(t,e){t=V(t)?c.get(t):t;null!=t&&t<l&&v(y(i[e]=t),n,e)}))});var m=0;function v(t,e,n){null!=zp.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,a.set(e,!0))}R(r,function(t){V(t)?(o=t,r={}):(o=(r=t).name,t=r.ordinalMeta,r.ordinalMeta=null,(r=O({},r)).ordinalMeta=t,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null);var n,i,r,o,e=d.get(o);if(!1!==e){if(!(e=xo(e)).length)for(var a=0;a<(n&&n.length||1);a++){for(;m<l&&null!=y(m).coordDim;)m++;m<l&&e.push(m++)}R(e,function(t,e){t=y(t);h&&null!=r.type&&(t.type=r.type),v(N(t,r),o,e),null==t.name&&n&&(L(e=n[e])||(e={name:e}),t.name=t.displayName=e.name,t.defaultTooltip=e.defaultTooltip),i&&N(t.otherDims,i)})}});var _=e.generateCoord,x=null!=(w=e.generateCoordCount),w=_?w||1:0,b=_||"value";function S(t){null==t.name&&(t.name=t.coordDim)}if(u)R(s,function(t){S(t)}),s.sort(function(t,e){return t.storeDimIndex-e.storeDimIndex});else for(var M=0;M<l;M++){var T=y(M);null==T.coordDim&&(T.coordDim=function(t,e,n){if(n||e.hasKey(t)){for(var i=0;e.hasKey(t+i);)i++;t+=i}return e.set(t,!0),t}(b,a,x),T.coordDimIndex=0,(!_||w<=0)&&(T.isExtraCoord=!0),w--),S(T),null!=T.type||$p(t,M)!==qp.Must&&(!T.isExtraCoord||null==T.otherDims.itemName&&null==T.otherDims.seriesName)||(T.type="ordinal")}for(var C=s,I=B(),k=0;k<C.length;k++){var D=C[k],A=D.name,P=I.get(A)||0;0<P&&(D.name=A+(P-1)),P++,I.set(A,P)}return new U0({source:t,dimensions:s,fullDimensionCount:l,dimensionOmitted:u})}var uv=function(t){this.coordSysDims=[],this.axisMap=B(),this.categoryAxisMap=B(),this.coordSysName=t};var hv={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",Bo).models[0],t=t.getReferringComponents("yAxis",Bo).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",t),cv(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),cv(t)&&(i.set("y",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){t=t.getReferringComponents("singleAxis",Bo).models[0];e.coordSysDims=["single"],n.set("single",t),cv(t)&&(i.set("single",t),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var t=t.getReferringComponents("polar",Bo).models[0],r=t.findAxisModel("radiusAxis"),t=t.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",r),n.set("angle",t),cv(r)&&(i.set("radius",r),e.firstCategoryDimIndex=0),cv(t)&&(i.set("angle",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,i,r,o){var a=t.ecModel,t=a.getComponent("parallel",t.get("parallelIndex")),s=i.coordSysDims=t.dimensions.slice();R(t.parallelAxisIndex,function(t,e){var t=a.getComponent("parallelAxis",t),n=s[e];r.set(n,t),cv(t)&&(o.set(n,t),null==i.firstCategoryDimIndex)&&(i.firstCategoryDimIndex=e)})}};function cv(t){return"category"===t.get("type")}function pv(t,e,n){var i,r,o,a,s,l,u,h,c,p=(n=n||{}).byIndex,d=n.stackedCoordDimension,f=(Y0(e.schema)?(r=e.schema,i=r.dimensions,o=e.store):i=e,!(!t||!t.get("stack")));return R(i,function(t,e){V(t)&&(i[e]=t={name:t}),f&&!t.isExtraCoord&&(p||a||!t.ordinalMeta||(a=t),s||"ordinal"===t.type||"time"===t.type||d&&d!==t.coordDim||(s=t))}),!s||p||a||(p=!0),s&&(l="__\0ecstackresult_"+t.id,u="__\0ecstackedover_"+t.id,a&&(a.createInvertedIndices=!0),h=s.coordDim,n=s.type,c=0,R(i,function(t){t.coordDim===h&&c++}),e={name:l,coordDim:h,coordDimIndex:c,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},t={name:u,coordDim:u,coordDimIndex:c+1,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1},r?(o&&(e.storeDimIndex=o.ensureCalculationDimension(u,n),t.storeDimIndex=o.ensureCalculationDimension(l,n)),r.appendCalculationDimension(e),r.appendCalculationDimension(t)):(i.push(e),i.push(t))),{stackedDimension:s&&s.name,stackedByDimension:a&&a.name,isStackedByIndex:p,stackedOverDimension:u,stackResultDimension:l}}function dv(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function fv(t,e){return dv(t,e)?t.getCalculationInfo("stackResultDimension"):e}function gv(t,e,n){n=n||{};var i,r,o,a,s,l,u=e.getSourceManager(),h=!1,t=(t?(h=!0,i=Xd(t)):h=(i=u.getSource()).sourceFormat===Fp,function(t){var e=t.get("coordinateSystem"),n=new uv(e);if(e=hv[e])return e(t,n,n.axisMap,n.categoryAxisMap),n}(e)),c=(r=t,c=(c=e).get("coordinateSystem"),c=gd.get(c),p=(p=r&&r.coordSysDims?z(r.coordSysDims,function(t){var e={name:t},t=r.axisMap.get(t);return t&&(t=t.get("type"),e.type="category"===(t=t)?"ordinal":"time"===t?"time":"float"),e}):p)||c&&(c.getDimensionsInfo?c.getDimensionsInfo():c.dimensions.slice())||["x","y"]),p=n.useEncodeDefaulter,p=k(p)?p:p?pt(jp,c,e):null,c={coordDimensions:c,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:p,canOmitUnusedDimensions:!h},p=lv(i,c),d=(c=p.dimensions,o=n.createInvertedIndices,(a=t)&&R(c,function(t,e){var n=t.coordDim,n=a.categoryAxisMap.get(n);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta(),o)&&(t.createInvertedIndices=!0),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(c[s].otherDims.itemName=0),s),n=h?null:u.getSharedDataStore(p),t=pv(e,{schema:p,store:n}),c=new sv(p,e),p=(c.setCalculationInfo(t),null==d||(u=i).sourceFormat!==Fp||F(So(function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(u.data||[])))?null:function(t,e,n,i){return i===d?n:this.defaultDimValueGetter(t,e,n,i)});return c.hasItemOption=!1,c.initData(h?i:n,null,p),c}mv.prototype.getSetting=function(t){return this._setting[t]},mv.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},mv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},mv.prototype.getExtent=function(){return this._extent.slice()},mv.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},mv.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},mv.prototype.isBlank=function(){return this._isBlank},mv.prototype.setBlank=function(t){this._isBlank=t};var yv=mv;function mv(t){this._setting=t||{},this._extent=[1/0,-1/0]}Zo(yv);var vv=0,_v=(xv.createByAxisModel=function(t){var t=t.option,e=t.data,e=e&&z(e,wv);return new xv({categories:e,needCollect:!e,deduplication:!1!==t.dedplication})},xv.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},xv.prototype.parseAndCollect=function(t){var e,n,i=this._needCollect;return V(t)||i?(i&&!this._deduplication?(n=this.categories.length,this.categories[n]=t):null==(n=(e=this._getOrCreateMap()).get(t))&&(i?(n=this.categories.length,this.categories[n]=t,e.set(t,n)):n=NaN),n):t},xv.prototype._getOrCreateMap=function(){return this._map||(this._map=B(this.categories))},xv);function xv(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++vv}function wv(t){return L(t)&&null!=t.value?t.value:t+""}function bv(t){return"interval"===t.type||"log"===t.type}function Sv(t,e,n,i){var r={},o=t[1]-t[0],o=r.interval=po(o/e,!0),e=(null!=n&&o<n&&(o=r.interval=n),null!=i&&i<o&&(o=r.interval=i),r.intervalPrecision=Tv(o)),n=r.niceTickExtent=[no(Math.ceil(t[0]/o)*o,e),no(Math.floor(t[1]/o)*o,e)];return i=n,o=t,isFinite(i[0])||(i[0]=o[0]),isFinite(i[1])||(i[1]=o[1]),Cv(i,0,o),Cv(i,1,o),i[0]>i[1]&&(i[0]=i[1]),r}function Mv(t){var e=Math.pow(10,co(t)),t=t/e;return t?2===t?t=3:3===t?t=5:t*=2:t=1,no(t*e)}function Tv(t){return io(t)+2}function Cv(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function Iv(t,e){return t>=e[0]&&t<=e[1]}function kv(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function Dv(t,e){return t*(e[1]-e[0])+e[0]}u(Lv,Av=yv),Lv.prototype.parse=function(t){return null==t?NaN:V(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},Lv.prototype.contain=function(t){return Iv(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},Lv.prototype.normalize=function(t){return kv(t=this._getTickNumber(this.parse(t)),this._extent)},Lv.prototype.scale=function(t){return t=Math.round(Dv(t,this._extent)),this.getRawOrdinalNumber(t)},Lv.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},Lv.prototype.getMinorTicks=function(t){},Lv.prototype.setSortInfo=function(t){if(null==t)this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;else{for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];i[n[r]=s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}},Lv.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&0<=t&&t<e.length?e[t]:t},Lv.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&0<=t&&t<e.length?e[t]:t},Lv.prototype.getLabel=function(t){if(!this.isBlank())return t=this.getRawOrdinalNumber(t.value),null==(t=this._ordinalMeta.categories[t])?"":t+""},Lv.prototype.count=function(){return this._extent[1]-this._extent[0]+1},Lv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Lv.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},Lv.prototype.getOrdinalMeta=function(){return this._ordinalMeta},Lv.prototype.calcNiceTicks=function(){},Lv.prototype.calcNiceExtent=function(){},Lv.type="ordinal";var Av,Pv=Lv;function Lv(t){var t=Av.call(this,t)||this,e=(t.type="ordinal",t.getSetting("ordinalMeta"));return F(e=e||new _v({}))&&(e=new _v({categories:z(e,function(t){return L(t)?t.value:t})})),t._ordinalMeta=e,t._extent=t.getSetting("extent")||[0,e.categories.length-1],t}yv.registerClass(Pv);var Ov,Rv=no,Ev=(u(Bv,Ov=yv),Bv.prototype.parse=function(t){return t},Bv.prototype.contain=function(t){return Iv(t,this._extent)},Bv.prototype.normalize=function(t){return kv(t,this._extent)},Bv.prototype.scale=function(t){return Dv(t,this._extent)},Bv.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},Bv.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},Bv.prototype.getInterval=function(){return this._interval},Bv.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Tv(t)},Bv.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(e){n[0]<i[0]&&o.push(t?{value:Rv(i[0]-e,r)}:{value:n[0]});for(var a=i[0];a<=i[1]&&(o.push({value:a}),(a=Rv(a+e,r))!==o[o.length-1].value);)if(1e4<o.length)return[];var s=o.length?o[o.length-1].value:i[1];n[1]>s&&o.push(t?{value:Rv(s+e,r)}:{value:n[1]})}return o},Bv.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o.value-a.value)/t;s<t-1;){var h=Rv(a.value+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},Bv.prototype.getLabel=function(t,e){return null==t?"":(null==(e=e&&e.precision)?e=io(t.value)||0:"auto"===e&&(e=this._intervalPrecision),pp(Rv(t.value,e,!0)))},Bv.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];isFinite(r)&&(r<0&&i.reverse(),r=Sv(i,t,e,n),this._intervalPrecision=r.intervalPrecision,this._interval=r.interval,this._niceExtent=r.niceTickExtent)},Bv.prototype.calcNiceExtent=function(t){var e=this._extent,n=(e[0]===e[1]&&(0!==e[0]?(n=Math.abs(e[0]),t.fixMax||(e[1]+=n/2),e[0]-=n/2):e[1]=1),e[1]-e[0]),n=(isFinite(n)||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval),this._interval);t.fixMin||(e[0]=Rv(Math.floor(e[0]/n)*n)),t.fixMax||(e[1]=Rv(Math.ceil(e[1]/n)*n))},Bv.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},Bv.type="interval",Bv);function Bv(){var t=null!==Ov&&Ov.apply(this,arguments)||this;return t.type="interval",t._interval=0,t._intervalPrecision=2,t}yv.registerClass(Ev);var Nv="undefined"!=typeof Float32Array,zv=Nv?Float32Array:Array;function Fv(t){return F(t)?Nv?new Float32Array(t):t:new zv(t)}var Vv="__ec_stack_";function Hv(t){return t.get("stack")||Vv+t.seriesIndex}function Wv(t){return t.dim+t.index}function Gv(t,e){var n=[];return e.eachSeriesByType(t,function(t){qv(t)&&n.push(t)}),n}function Uv(t){var a,d,l=function(t){var e,l={},n=(R(t,function(t){var e=t.coordinateSystem.getBaseAxis();if("time"===e.type||"value"===e.type)for(var t=t.getData(),n=e.dim+"_"+e.index,i=t.getDimensionIndex(t.mapDimension(e.dim)),r=t.getStore(),o=0,a=r.count();o<a;++o){var s=r.get(i,o);l[n]?l[n].push(s):l[n]=[s]}}),{});for(e in l)if(l.hasOwnProperty(e)){var i=l[e];if(i){i.sort(function(t,e){return t-e});for(var r=null,o=1;o<i.length;++o){var a=i[o]-i[o-1];0<a&&(r=null===r?a:Math.min(r,a))}n[e]=r}}return n}(t),u=[];return R(t,function(t){var e,n=t.coordinateSystem.getBaseAxis(),i=n.getExtent(),r=(e="category"===n.type?n.getBandWidth():"value"===n.type||"time"===n.type?(e=n.dim+"_"+n.index,e=l[e],r=Math.abs(i[1]-i[0]),o=n.scale.getExtent(),o=Math.abs(o[1]-o[0]),e?r/o*e:r):(o=t.getData(),Math.abs(i[1]-i[0])/o.count()),eo(t.get("barWidth"),e)),i=eo(t.get("barMaxWidth"),e),o=eo(t.get("barMinWidth")||(Zv(t)?.5:1),e),a=t.get("barGap"),s=t.get("barCategoryGap");u.push({bandWidth:e,barWidth:r,barMaxWidth:i,barMinWidth:o,barGap:a,barCategoryGap:s,axisKey:Wv(n),stackId:Hv(t)})}),a={},R(u,function(t,e){var n=t.axisKey,i=t.bandWidth,i=a[n]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},r=i.stacks,n=(a[n]=i,t.stackId),o=(r[n]||i.autoWidthCount++,r[n]=r[n]||{width:0,maxWidth:0},t.barWidth),o=(o&&!r[n].width&&(r[n].width=o,o=Math.min(i.remainedWidth,o),i.remainedWidth-=o),t.barMaxWidth),o=(o&&(r[n].maxWidth=o),t.barMinWidth),r=(o&&(r[n].minWidth=o),t.barGap),n=(null!=r&&(i.gap=r),t.barCategoryGap);null!=n&&(i.categoryGap=n)}),d={},R(a,function(t,n){d[n]={};var i,e=t.stacks,r=t.bandWidth,o=t.categoryGap,a=(null==o&&(a=ht(e).length,o=Math.max(35-4*a,15)+"%"),eo(o,r)),s=eo(t.gap,1),l=t.remainedWidth,u=t.autoWidthCount,h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),c=(R(e,function(t){var e,n=t.maxWidth,i=t.minWidth;t.width?(e=t.width,n&&(e=Math.min(e,n)),i&&(e=Math.max(e,i)),t.width=e,l-=e+s*e,u--):(e=h,n&&n<e&&(e=Math.min(n,l)),(e=i&&e<i?i:e)!==h&&(t.width=e,l-=e+s*e,u--))}),h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),0),p=(R(e,function(t,e){t.width||(t.width=h),c+=(i=t).width*(1+s)}),i&&(c-=i.width*s),-c/2);R(e,function(t,e){d[n][e]=d[n][e]||{bandWidth:r,offset:p,width:t.width},p+=t.width*(1+s)})}),d}function Xv(t,e){var t=Gv(t,e),r=Uv(t);R(t,function(t){var e=t.getData(),n=t.coordinateSystem.getBaseAxis(),t=Hv(t),n=r[Wv(n)][t],t=n.offset,i=n.width;e.setLayout({bandWidth:n.bandWidth,offset:t,size:i})})}function Yv(t){return{seriesType:t,plan:zg(),reset:function(t){var e,x,n,w,b,S,i,r,M,T,C,I,k,D,A,P;if(qv(t))return e=t.getData(),i=(x=t.coordinateSystem).getBaseAxis(),n=x.getOtherAxis(i),w=e.getDimensionIndex(e.mapDimension(n.dim)),b=e.getDimensionIndex(e.mapDimension(i.dim)),S=t.get("showBackground",!0),i=e.mapDimension(n.dim),r=e.getCalculationInfo("stackResultDimension"),M=dv(e,i)&&!!e.getCalculationInfo("stackedOnSeries"),T=n.isHorizontal(),C=function(t){var e=t.model.get("startValue");e=e||0;return t.toGlobalCoord(t.dataToCoord("log"!==t.type||0<e?e:1))}(n),I=Zv(t),k=t.get("barMinHeight")||0,D=r&&e.getDimensionIndex(r),A=e.getLayout("size"),P=e.getLayout("offset"),{progress:function(t,e){for(var n,i=t.count,r=I&&Fv(3*i),o=I&&S&&Fv(3*i),a=I&&Fv(i),s=x.master.getRect(),l=T?s.width:s.height,u=e.getStore(),h=0;null!=(n=t.next());){var c,p=u.get(M?D:w,n),d=u.get(b,n),f=C,g=void 0,y=(M&&(g=+p-u.get(w,n)),void 0),m=void 0,v=void 0,_=void 0;T?(c=x.dataToPoint([p,d]),y=f=M?x.dataToPoint([g,d])[0]:f,m=c[1]+P,v=c[0]-f,_=A,Math.abs(v)<k&&(v=(v<0?-1:1)*k)):(c=x.dataToPoint([d,p]),M&&(f=x.dataToPoint([d,g])[1]),y=c[0]+P,m=f,v=A,_=c[1]-f,Math.abs(_)<k&&(_=(_<=0?-1:1)*k)),I?(r[h]=y,r[h+1]=m,r[h+2]=T?v:_,o&&(o[h]=T?s.x:y,o[h+1]=T?m:s.y,o[h+2]=l),a[n]=n):e.setItemLayout(n,{x:y,y:m,width:v,height:_}),h+=3}I&&e.setLayout({largePoints:r,largeDataIndices:a,largeBackgroundPoints:o,valueAxisHorizontal:T})}}}}}function qv(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function Zv(t){return t.pipelineContext&&t.pipelineContext.large}u($v,jv=Ev),$v.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return Kc(t.value,Xc[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(jc(this._minLevelUnit))]||Xc.second,e,this.getSetting("locale"))},$v.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC"),r=this.getSetting("locale"),o=null;if(V(n))o=n;else if(k(n))o=n(t.value,e,{level:t.level});else{var a=O({},Gc);if(0<t.level)for(var s=0;s<Yc.length;++s)a[Yc[s]]="{primary|"+a[Yc[s]]+"}";var l=n?!1===n.inherit?n:N(n,a):a,u=$c(t.value,i);if(l[u])o=l[u];else if(l.inherit){for(s=qc.indexOf(u)-1;0<=s;--s)if(l[u]){o=l[u];break}o=o||a.none}F(o)&&(e=null==t.level?0:0<=t.level?t.level:o.length+t.level,o=o[e=Math.min(e,o.length-1)])}return Kc(new Date(t.value),o,i,r)},$v.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];return t&&(n.push({value:e[0],level:0}),t=this.getSetting("useUTC"),t=function(t,b,S,M){var e=qc,n=0;function i(t,e,n){var i=[],r=!e.length;if(!function(t,e,n,i){function r(t){return Qc(c,t,i)===Qc(p,t,i)}function o(){return r("year")}function a(){return o()&&r("month")}function s(){return a()&&r("day")}function l(){return s()&&r("hour")}function u(){return l()&&r("minute")}function h(){return u()&&r("second")}var c=uo(e),p=uo(n);switch(t){case"year":return o();case"month":return a();case"day":return s();case"hour":return l();case"minute":return u();case"second":return h();case"millisecond":return h()&&r("millisecond")}}(jc(t),M[0],M[1],S)){r&&(e=[{value:function(t,e,n){var i=new Date(t);switch(jc(e)){case"year":case"month":i[ap(n)](0);case"day":i[sp(n)](1);case"hour":i[lp(n)](0);case"minute":i[up(n)](0);case"second":i[hp(n)](0),i[cp(n)](0)}return i.getTime()}(new Date(M[0]),t,S)},{value:M[1]}]);for(var o,a,s=0;s<e.length-1;s++){var l=e[s].value,u=e[s+1].value;if(l!==u){var h=void 0,c=void 0,p=void 0;switch(t){case"year":h=Math.max(1,Math.round(b/Wc/365)),c=Jc(S),p=S?"setUTCFullYear":"setFullYear";break;case"half-year":case"quarter":case"month":a=b,h=6<(a/=30*Wc)?6:3<a?3:2<a?2:1,c=tp(S),p=ap(S);break;case"week":case"half-week":case"day":a=b,h=16<(a/=Wc)?16:7.5<a?7:3.5<a?4:1.5<a?2:1,c=ep(S),p=sp(S),0;break;case"half-day":case"quarter-day":case"hour":o=b,h=12<(o/=Hc)?12:6<o?6:3.5<o?4:2<o?2:1,c=np(S),p=lp(S);break;case"minute":h=Jv(b,!0),c=ip(S),p=up(S);break;case"second":h=Jv(b,!1),c=rp(S),p=hp(S);break;case"millisecond":h=po(b,!0),c=op(S),p=cp(S)}w=x=_=v=m=y=g=f=d=void 0;for(var d=h,f=l,g=u,y=c,m=p,v=i,_=new Date(f),x=f,w=_[y]();x<g&&x<=M[1];)v.push({value:x}),_[m](w+=d),x=_.getTime();v.push({value:x,notAdd:!0}),"year"===t&&1<n.length&&0===s&&n.unshift({value:n[0].value-h})}}for(s=0;s<i.length;s++)n.push(i[s])}}for(var r=[],o=[],a=0,s=0,l=0;l<e.length&&n++<1e4;++l){var u=jc(e[l]);if(function(t){return t===jc(t)}(e[l])){i(e[l],r[r.length-1]||[],o);var h=e[l+1]?jc(e[l+1]):null;if(u!==h){if(o.length){s=a,o.sort(function(t,e){return t.value-e.value});for(var c=[],p=0;p<o.length;++p){var d=o[p].value;0!==p&&o[p-1].value===d||(c.push(o[p]),d>=M[0]&&d<=M[1]&&a++)}u=(M[1]-M[0])/b;if(1.5*u<a&&u/1.5<s)break;if(r.push(c),u<a||t===e[l])break}o=[]}}}for(var f=ut(z(r,function(t){return ut(t,function(t){return t.value>=M[0]&&t.value<=M[1]&&!t.notAdd})}),function(t){return 0<t.length}),g=[],y=f.length-1,l=0;l<f.length;++l)for(var m=f[l],v=0;v<m.length;++v)g.push({value:m[v].value,level:y-l});g.sort(function(t,e){return t.value-e.value});for(var _=[],l=0;l<g.length;++l)0!==l&&g[l].value===g[l-1].value||_.push(g[l]);return _}(this._minLevelUnit,this._approxInterval,t,e),(n=n.concat(t)).push({value:e[1],level:0})),n},$v.prototype.calcNiceExtent=function(t){var e,n=this._extent;n[0]===n[1]&&(n[0]-=Wc,n[1]+=Wc),n[1]===-1/0&&n[0]===1/0&&(e=new Date,n[1]=+new Date(e.getFullYear(),e.getMonth(),e.getDate()),n[0]=n[1]-Wc),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},$v.prototype.calcNiceTicks=function(t,e,n){var i=this._extent,i=i[1]-i[0],i=(this._approxInterval=i/(t=t||10),null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n),Qv.length),t=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=1+r:i=r}return n}(Qv,this._approxInterval,0,i),i-1);this._interval=Qv[t][1],this._minLevelUnit=Qv[Math.max(t-1,0)][0]},$v.prototype.parse=function(t){return ft(t)?t:+uo(t)},$v.prototype.contain=function(t){return Iv(this.parse(t),this._extent)},$v.prototype.normalize=function(t){return kv(this.parse(t),this._extent)},$v.prototype.scale=function(t){return Dv(t,this._extent)},$v.type="time";var jv,Kv=$v;function $v(t){t=jv.call(this,t)||this;return t.type="time",t}var Qv=[["second",Fc],["minute",Vc],["hour",Hc],["quarter-day",6*Hc],["half-day",12*Hc],["day",1.2*Wc],["half-week",3.5*Wc],["week",7*Wc],["month",31*Wc],["quarter",95*Wc],["half-year",Xo/2],["year",Xo]];function Jv(t,e){return 30<(t/=e?Vc:Fc)?30:20<t?20:15<t?15:10<t?10:5<t?5:2<t?2:1}yv.registerClass(Kv);var t_,e_=yv.prototype,n_=Ev.prototype,i_=no,r_=Math.floor,o_=Math.ceil,a_=Math.pow,s_=Math.log,l_=(u(u_,t_=yv),u_.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return z(n_.getTicks.call(this,t),function(t){var t=t.value,e=no(a_(this.base,t)),e=t===n[0]&&this._fixMin?h_(e,i[0]):e;return{value:t===n[1]&&this._fixMax?h_(e,i[1]):e}},this)},u_.prototype.setExtent=function(t,e){var n=s_(this.base);t=s_(Math.max(0,t))/n,e=s_(Math.max(0,e))/n,n_.setExtent.call(this,t,e)},u_.prototype.getExtent=function(){var t=this.base,e=e_.getExtent.call(this);e[0]=a_(t,e[0]),e[1]=a_(t,e[1]);t=this._originalScale.getExtent();return this._fixMin&&(e[0]=h_(e[0],t[0])),this._fixMax&&(e[1]=h_(e[1],t[1])),e},u_.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=s_(t[0])/s_(e),t[1]=s_(t[1])/s_(e),e_.unionExtent.call(this,t)},u_.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},u_.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n==1/0||n<=0)){var i=ho(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&0<Math.abs(i);)i*=10;t=[no(o_(e[0]/i)*i),no(r_(e[1]/i)*i)];this._interval=i,this._niceExtent=t}},u_.prototype.calcNiceExtent=function(t){n_.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},u_.prototype.parse=function(t){return t},u_.prototype.contain=function(t){return Iv(t=s_(t)/s_(this.base),this._extent)},u_.prototype.normalize=function(t){return kv(t=s_(t)/s_(this.base),this._extent)},u_.prototype.scale=function(t){return t=Dv(t,this._extent),a_(this.base,t)},u_.type="log",u_);function u_(){var t=null!==t_&&t_.apply(this,arguments)||this;return t.type="log",t.base=10,t._originalScale=new Ev,t._interval=0,t}Ly=l_.prototype;function h_(t,e){return i_(t,io(e))}Ly.getMinorTicks=n_.getMinorTicks,Ly.getLabel=n_.getLabel,yv.registerClass(l_);p_.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type,r=(this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero(),e.get("min",!0)),r=(null==r&&(r=e.get("startValue",!0)),this._modelMinRaw=r),r=(k(r)?this._modelMinNum=g_(t,r({min:n[0],max:n[1]})):"dataMin"!==r&&(this._modelMinNum=g_(t,r)),this._modelMaxRaw=e.get("max",!0));k(r)?this._modelMaxNum=g_(t,r({min:n[0],max:n[1]})):"dataMax"!==r&&(this._modelMaxNum=g_(t,r)),i?this._axisDataLen=e.getCategories().length:"boolean"==typeof(t=F(n=e.get("boundaryGap"))?n:[n||0,n||0])[0]||"boolean"==typeof t[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[kr(t[0],1),kr(t[1],1)]},p_.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s,e=(null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),null!=a&&isFinite(a)||(a=NaN),null!=s&&isFinite(s)||(s=NaN),wt(a)||wt(s)||t&&!i),n=(this._needCrossZero&&(a=0<a&&0<s&&!l?0:a)<0&&s<0&&!u&&(s=0),this._determinedMin),r=this._determinedMax;return null!=n&&(a=n,l=!0),null!=r&&(s=r,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:e}},p_.prototype.modifyDataMinMax=function(t,e){this[f_[t]]=e},p_.prototype.setDeterminedMinMax=function(t,e){this[d_[t]]=e},p_.prototype.freeze=function(){this.frozen=!0};var c_=p_;function p_(t,e,n){this._prepareParams(t,e,n)}var d_={min:"_determinedMin",max:"_determinedMax"},f_={min:"_dataMin",max:"_dataMax"};function g_(t,e){return null==e?null:wt(e)?NaN:t.parse(e)}function y_(t,e){var n,i,r,o,a,s=t.type,l=(l=e,u=(h=t).getExtent(),(c=h.rawExtentInfo)||(c=new c_(h,l,u),h.rawExtentInfo=c),c.calculate()),u=(t.setBlank(l.isBlank),l.min),h=l.max,c=e.ecModel;return c&&"time"===s&&(t=Gv("bar",c),n=!1,R(t,function(t){n=n||t.getBaseAxis()===e.axis}),n)&&(s=Uv(t),c=u,t=h,s=s,a=(a=(i=e).axis.getExtent())[1]-a[0],void 0!==(s=function(t,e,n){if(t&&e)return null!=(t=t[Wv(e)])&&null!=n?t[Hv(n)]:t}(s,i.axis))&&(r=1/0,R(s,function(t){r=Math.min(t.offset,r)}),o=-1/0,R(s,function(t){o=Math.max(t.offset+t.width,o)}),r=Math.abs(r),o=Math.abs(o),t+=o/(i=r+o)*(a=(s=t-c)/(1-(r+o)/a)-s),c-=r/i*a),u=(s={min:c,max:t}).min,h=s.max),{extent:[u,h],fixMin:l.minFixed,fixMax:l.maxFixed}}function m_(t,e){var n=y_(t,e),i=n.extent,r=e.get("splitNumber"),o=(t instanceof l_&&(t.base=e.get("logBase")),t.type),a=e.get("interval"),o="interval"===o||"time"===o;t.setExtent(i[0],i[1]),t.calcNiceExtent({splitNumber:r,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:o?e.get("minInterval"):null,maxInterval:o?e.get("maxInterval"):null}),null!=a&&t.setInterval&&t.setInterval(a)}function v_(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Pv({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new Kv({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(yv.getClass(e)||Ev)}}function __(n){var i,e,r,t=n.getLabelModel().get("formatter"),o="category"===n.type?n.scale.getExtent()[0]:null;return"time"===n.scale.type?(r=t,function(t,e){return n.scale.getFormattedLabel(t,e,r)}):V(t)?(e=t,function(t){t=n.scale.getLabel(t);return e.replace("{value}",null!=t?t:"")}):k(t)?(i=t,function(t,e){return null!=o&&(e=t.value-o),i(x_(n,t),e,null!=t.level?{level:t.level}:null)}):function(t){return n.scale.getLabel(t)}}function x_(t,e){return"category"===t.type?t.scale.getLabel(e):e.value}function w_(t){var e=t.model,n=t.scale;if(e.get(["axisLabel","show"])&&!n.isBlank()){var i,r,o=n.getExtent(),a=n instanceof Pv?n.count():(i=n.getTicks()).length,s=t.getLabelModel(),l=__(t),u=1;40<a&&(u=Math.ceil(a/40));for(var h,c,p,d=0;d<a;d+=u){var f=l(i?i[d]:{value:o[0]+d},d),f=s.getTextRect(f),g=(f=f,h=s.get("rotate")||0,c=p=g=c=void 0,h=h*Math.PI/180,c=f.width,g=f.height,p=c*Math.abs(Math.cos(h))+Math.abs(g*Math.sin(h)),c=c*Math.abs(Math.sin(h))+Math.abs(g*Math.cos(h)),new X(f.x,f.y,p,c));r?r.union(g):r=g}return r}}function b_(t){t=t.get("interval");return null==t?"auto":t}function S_(t){return"category"===t.type&&0===b_(t.getLabelModel())}T_.prototype.getNeedCrossZero=function(){return!this.option.scale},T_.prototype.getCoordSysModel=function(){};var M_=T_;function T_(){}var By=Object.freeze({__proto__:null,createDimensions:function(t,e){return lv(t,e).dimensions},createList:function(t){return gv(null,t)},createScale:function(t,e){var n=e;return(e=v_(n=e instanceof kc?n:new kc(e))).setExtent(t[0],t[1]),m_(e,n),e},createSymbol:Wy,createTextStyle:function(t,e){return sc(t,null,null,"normal"!==(e=e||{}).state)},dataStack:{isDimensionStacked:dv,enableDataStack:pv,getStackedDimension:fv},enableHoverEmphasis:Ll,getECData:D,getLayoutRect:Cp,mixinAxisModelCommonMethods:function(t){at(t,M_)}}),C_=[],I_={registerPreprocessor:_0,registerProcessor:x0,registerPostInit:w0,registerPostUpdate:b0,registerUpdateLifecycle:S0,registerAction:M0,registerCoordinateSystem:T0,registerLayout:C0,registerVisual:I0,registerTransform:L0,registerLoading:A0,registerMap:P0,registerImpl:function(t,e){xm[t]=e},PRIORITY:Ey,ComponentModel:g,ComponentView:Bg,SeriesModel:kg,ChartView:Hg,registerComponentModel:function(t){g.registerClass(t)},registerComponentView:function(t){Bg.registerClass(t)},registerSeriesModel:function(t){kg.registerClass(t)},registerChartView:function(t){Hg.registerClass(t)},registerSubTypeDefaulter:function(t,e){g.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){Kr(t,e)}};function k_(t){F(t)?R(t,function(t){k_(t)}):0<=I(C_,t)||(C_.push(t),(t=k(t)?{install:t}:t).install(I_))}var D_=1e-8;function A_(t,e){return Math.abs(t-e)<D_}function P_(t,e,n){var i=0,r=t[0];if(r){for(var o=1;o<t.length;o++){var a=t[o];i+=ts(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return A_(r[0],s[0])&&A_(r[1],s[1])||(i+=ts(r[0],r[1],s[0],s[1],e,n)),0!==i}}var L_=[];function O_(t,e){for(var n=0;n<t.length;n++)ne(t[n],t[n],e)}function R_(t,e,n,i){for(var r=0;r<t.length;r++){var o=t[r];(o=i?i.project(o):o)&&isFinite(o[0])&&isFinite(o[1])&&(ie(e,e,o),re(n,n,o))}}E_.prototype.setCenter=function(t){this._center=t},E_.prototype.getCenter=function(){return this._center||(this._center=this.calcCenter())};Ny=E_;function E_(t){this.name=t}var B_,N_,z_=function(t,e){this.type="polygon",this.exterior=t,this.interiors=e},F_=function(t){this.type="linestring",this.points=t},V_=(u(H_,B_=Ny),H_.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,i=0;i<e.length;i++){var r=e[i],o=r.exterior,o=o&&o.length;n<o&&(t=r,n=o)}if(t){for(var a=t.exterior,s=0,l=0,u=0,h=a.length,c=a[h-1][0],p=a[h-1][1],d=0;d<h;d++){var f=a[d][0],g=a[d][1],y=c*g-f*p;s+=y,l+=(c+f)*y,u+=(p+g)*y,c=f,p=g}return s?[l/s/3,u/s/3,s]:[a[0][0]||0,a[0][1]||0]}var m=this.getBoundingRect();return[m.x+m.width/2,m.y+m.height/2]},H_.prototype.getBoundingRect=function(e){var n,i,t=this._rect;return t&&!e||(n=[1/0,1/0],i=[-1/0,-1/0],R(this.geometries,function(t){"polygon"===t.type?R_(t.exterior,n,i,e):R(t.points,function(t){R_(t,n,i,e)})}),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),t=new X(n[0],n[1],i[0]-n[0],i[1]-n[1]),e)||(this._rect=t),t},H_.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(e.contain(t[0],t[1]))t:for(var i=0,r=n.length;i<r;i++){var o=n[i];if("polygon"===o.type){var a=o.exterior,s=o.interiors;if(P_(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(P_(s[l],t[0],t[1]))continue t;return!0}}}return!1},H_.prototype.transformTo=function(t,e,n,i){for(var r=this.getBoundingRect(),o=r.width/r.height,o=(n?i=i||n/o:n=o*i,new X(t,e,n,i)),a=r.calculateTransform(o),s=this.geometries,l=0;l<s.length;l++){var u=s[l];"polygon"===u.type?(O_(u.exterior,a),R(u.interiors,function(t){O_(t,a)})):R(u.points,function(t){O_(t,a)})}(r=this._rect).copy(o),this._center=[r.x+r.width/2,r.y+r.height/2]},H_.prototype.cloneShallow=function(t){t=new H_(t=null==t?this.name:t,this.geometries,this._center);return t._rect=this._rect,t.transformTo=null,t},H_);function H_(t,e,n){t=B_.call(this,t)||this;return t.type="geoJSON",t.geometries=e,t._center=n&&[n[0],n[1]],t}function W_(t,e){t=N_.call(this,t)||this;return t.type="geoSVG",t._elOnlyForCalculate=e,t}function G_(t,e,n){for(var i=0;i<t.length;i++)t[i]=U_(t[i],e[i],n)}function U_(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=(s=t.charCodeAt(a)-64)>>1^-(1&s),l=(l=t.charCodeAt(a+1)-64)>>1^-(1&l);i.push([(r=s+=r)/n,(o=l+=o)/n])}return i}function X_(t,o){var e,n,r;return z(ut((t=(e=t).UTF8Encoding?(null==(r=(n=e).UTF8Scale)&&(r=1024),R(n.features,function(t){var e=t.geometry,n=e.encodeOffsets,i=e.coordinates;if(n)switch(e.type){case"LineString":e.coordinates=U_(i,n,r);break;case"Polygon":case"MultiLineString":G_(i,n,r);break;case"MultiPolygon":R(i,function(t,e){return G_(t,n[e],r)})}}),n.UTF8Encoding=!1,n):e).features,function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length}),function(t){var e=t.properties,n=t.geometry,i=[];switch(n.type){case"Polygon":var r=n.coordinates;i.push(new z_(r[0],r.slice(1)));break;case"MultiPolygon":R(n.coordinates,function(t){t[0]&&i.push(new z_(t[0],t.slice(1)))});break;case"LineString":i.push(new F_([n.coordinates]));break;case"MultiLineString":i.push(new F_(n.coordinates))}t=new V_(e[o||"name"],i,e.cp);return t.properties=e,t})}u(W_,N_=Ny),W_.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),e=[e.x+e.width/2,e.y+e.height/2],n=Re(L_),i=t;i&&!i.isGeoSVGGraphicRoot;)Be(n,i.getLocalTransform(),n),i=i.parent;return Ve(n,n),ne(e,e,n),e};var $o=Object.freeze({__proto__:null,MAX_SAFE_INTEGER:9007199254740991,asc:function(t){return t.sort(function(t,e){return t-e}),t},getPercentWithPrecision:function(t,e,n){return t[e]&&function(t,e){var n=lt(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return[];var i=Math.pow(10,e),e=z(t,function(t){return(isNaN(t)?0:t)/n*i*100}),r=100*i,o=z(e,function(t){return Math.floor(t)}),a=lt(o,function(t,e){return t+e},0),s=z(e,function(t,e){return t-o[e]});for(;a<r;){for(var l=Number.NEGATIVE_INFINITY,u=null,h=0,c=s.length;h<c;++h)s[h]>l&&(l=s[h],u=h);++o[u],s[u]=0,++a}return z(o,function(t){return t/i})}(t,n)[e]||0},getPixelPrecision:oo,getPrecision:io,getPrecisionSafe:ro,isNumeric:go,isRadianAroundZero:so,linearMap:to,nice:po,numericToNumber:fo,parseDate:uo,quantile:function(t,e){var e=(t.length-1)*e+1,n=Math.floor(e),i=+t[n-1];return(e=e-n)?i+e*(t[n]-i):i},quantity:ho,quantityExponent:co,reformIntervals:function(t){t.sort(function(t,e){return function t(e,n,i){return e.interval[i]<n.interval[i]||e.interval[i]===n.interval[i]&&(e.close[i]-n.close[i]==(i?-1:1)||!i&&t(e,n,1))}(t,e,0)?-1:1});for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t},remRadian:ao,round:no}),Xh=Object.freeze({__proto__:null,format:Kc,parse:uo}),Uc=Object.freeze({__proto__:null,Arc:nh,BezierCurve:Qu,BoundingRect:X,Circle:su,CompoundPath:oh,Ellipse:cu,Group:Wr,Image:xs,IncrementalDisplayable:n,Line:Yu,LinearGradient:hh,Polygon:Nu,Polyline:Hu,RadialGradient:sh,Rect:Ds,Ring:Lu,Sector:ku,Text:Rs,clipPointsByRect:Kh,clipRectByRect:$h,createIcon:Qh,extendPath:Bh,extendShape:Rh,getShapeClass:zh,getTransform:Yh,initProps:Ch,makeImage:Vh,makePath:Fh,mergePath:Wh,registerShape:Nh,resizePath:Gh,updateProps:Th}),Sc=Object.freeze({__proto__:null,addCommas:pp,capitalFirst:function(t){return t&&t.charAt(0).toUpperCase()+t.substr(1)},encodeHTML:xe,formatTime:function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=(e=uo(e))[(n=n?"getUTC":"get")+"FullYear"](),r=e[n+"Month"]()+1,o=e[n+"Date"](),a=e[n+"Hours"](),s=e[n+"Minutes"](),l=e[n+"Seconds"](),e=e[n+"Milliseconds"]();return t=t.replace("MM",Zc(r,2)).replace("M",r).replace("yyyy",i).replace("yy",Zc(i%100+"",2)).replace("dd",Zc(o,2)).replace("d",o).replace("hh",Zc(a,2)).replace("h",a).replace("mm",Zc(s,2)).replace("m",s).replace("ss",Zc(l,2)).replace("s",l).replace("SSS",Zc(e,3))},formatTpl:vp,getTextRect:function(t,e,n,i,r,o,a,s){return new Rs({style:{text:t,font:e,align:n,verticalAlign:i,padding:r,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()},getTooltipMarker:_p,normalizeCssArray:fp,toCamelCase:dp,truncateText:ra}),_c=Object.freeze({__proto__:null,bind:ct,clone:y,curry:pt,defaults:N,each:R,extend:O,filter:ut,indexOf:I,inherits:ot,isArray:F,isFunction:k,isObject:L,isString:V,map:z,merge:d,reduce:lt}),Y_=Lo();function q_(e,t){t=z(t,function(t){return e.scale.parse(t)});return"time"===e.type&&0<t.length&&(t.sort(),t.unshift(t[0]),t.push(t[t.length-1])),t}function Z_(n){var i,r,o,t,e,a=n.getLabelModel().get("customValues");return a?(i=__(n),{labels:q_(n,a).map(function(t){var e={value:t};return{formattedLabel:i(e),rawLabel:n.scale.getLabel(e),tickValue:t}})}):"category"===n.type?(t=(a=n).getLabelModel(),e=K_(a,t),!t.get("show")||a.scale.isBlank()?{labels:[],labelCategoryInterval:e.labelCategoryInterval}:e):(t=(r=n).scale.getTicks(),o=__(r),{labels:z(t,function(t,e){return{level:t.level,formattedLabel:o(t,e),rawLabel:r.scale.getLabel(t),tickValue:t.value}})})}function j_(t,e){var n,i,r,o,a,s=t.getTickModel().get("customValues");return s?{ticks:q_(t,s)}:"category"===t.type?(s=e,r=$_(e=t,"ticks"),o=b_(s),(a=Q_(r,o))||(s.get("show")&&!e.scale.isBlank()||(n=[]),n=k(o)?e1(e,o,!0):"auto"===o?(a=K_(e,e.getLabelModel()),i=a.labelCategoryInterval,z(a.labels,function(t){return t.tickValue})):t1(e,i=o,!0),J_(r,o,{ticks:n,tickCategoryInterval:i}))):{ticks:z(t.scale.getTicks(),function(t){return t.value})}}function K_(t,e){var n,i=$_(t,"labels"),e=b_(e),r=Q_(i,e);return r||J_(i,e,{labels:k(e)?e1(t,e):t1(t,n="auto"===e?null!=(i=Y_(r=t).autoInterval)?i:Y_(r).autoInterval=r.calculateCategoryInterval():e),labelCategoryInterval:n})}function $_(t,e){return Y_(t)[e]||(Y_(t)[e]=[])}function Q_(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function J_(t,e,n){return t.push({key:e,value:n}),n}function t1(t,e,n){for(var i=__(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),e=o[0],u=r.count(),u=(0!==e&&1<l&&2<u/l&&(e=Math.round(Math.ceil(e/l)*l)),S_(t)),t=a.get("showMinLabel")||u,a=a.get("showMaxLabel")||u,h=(t&&e!==o[0]&&c(o[0]),e);h<=o[1];h+=l)c(h);function c(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t})}return a&&h-l!==o[1]&&c(o[1]),s}function e1(t,i,r){var o=t.scale,a=__(t),s=[];return R(o.getTicks(),function(t){var e=o.getLabel(t),n=t.value;i(t.value,e)&&s.push(r?n:{formattedLabel:a(t),rawLabel:e,tickValue:n})}),s}var n1=[0,1],Tc=(i1.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),e=Math.max(e[0],e[1]);return n<=t&&t<=e},i1.prototype.containData=function(t){return this.scale.contain(t)},i1.prototype.getExtent=function(){return this._extent.slice()},i1.prototype.getPixelPrecision=function(t){return oo(t||this.scale.getExtent(),this._extent)},i1.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},i1.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&r1(n=n.slice(),i.count()),to(t,n1,n,e)},i1.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale,i=(this.onBand&&"ordinal"===i.type&&r1(n=n.slice(),i.count()),to(t,n,n1,e));return this.scale.scale(i)},i1.prototype.pointToData=function(t,e){},i1.prototype.getTicksCoords=function(t){var e,n,i,r,o,a,s,l=(t=t||{}).tickModel||this.getTickModel(),u=z(j_(this,l).ticks,function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}},this),l=l.get("alignWithLabel");function h(t,e){return t=no(t),e=no(e),a?e<t:t<e}return e=this,n=u,l=l,t=t.clamp,s=n.length,e.onBand&&!l&&s&&(l=e.getExtent(),1===s?(n[0].coord=l[0],i=n[1]={coord:l[1]}):(o=n[s-1].tickValue-n[0].tickValue,r=(n[s-1].coord-n[0].coord)/o,R(n,function(t){t.coord-=r/2}),e=1+(o=e.scale.getExtent())[1]-n[s-1].tickValue,i={coord:n[s-1].coord+r*e},n.push(i)),a=l[0]>l[1],h(n[0].coord,l[0])&&(t?n[0].coord=l[0]:n.shift()),t&&h(l[0],n[0].coord)&&n.unshift({coord:l[0]}),h(l[1],i.coord)&&(t?i.coord=l[1]:n.pop()),t)&&h(i.coord,l[1])&&n.push({coord:l[1]}),u},i1.prototype.getMinorTicksCoords=function(){var t;return"ordinal"===this.scale.type?[]:(t=this.model.getModel("minorTick").get("splitNumber"),z(this.scale.getMinorTicks(t=0<t&&t<100?t:5),function(t){return z(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this))},i1.prototype.getViewLabels=function(){return Z_(this).labels},i1.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},i1.prototype.getTickModel=function(){return this.model.getModel("axisTick")},i1.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),e=e[1]-e[0]+(this.onBand?1:0),t=(0===e&&(e=1),Math.abs(t[1]-t[0]));return Math.abs(t)/e},i1.prototype.calculateCategoryInterval=function(){r=(n=d=this).getLabelModel();var t={axisRotate:n.getRotate?n.getRotate():n.isHorizontal&&!n.isHorizontal()?90:0,labelRotate:r.get("rotate")||0,font:r.getFont()},e=__(d),n=(t.axisRotate-t.labelRotate)/180*Math.PI,i=(r=d.scale).getExtent(),r=r.count();if(i[1]-i[0]<1)return 0;for(var o=1,a=(40<r&&(o=Math.max(1,Math.floor(r/40))),i[0]),s=d.dataToCoord(a+1)-d.dataToCoord(a),l=Math.abs(s*Math.cos(n)),s=Math.abs(s*Math.sin(n)),u=0,h=0;a<=i[1];a+=o)var c=1.3*(p=Mr(e({value:a}),t.font,"center","top")).width,p=1.3*p.height,u=Math.max(u,c,7),h=Math.max(h,p,7);var n=u/l,l=h/s,s=(isNaN(n)&&(n=1/0),isNaN(l)&&(l=1/0),Math.max(0,Math.floor(Math.min(n,l)))),n=Y_(d.model),l=d.getExtent(),d=n.lastAutoInterval,f=n.lastTickCount;return null!=d&&null!=f&&Math.abs(d-s)<=1&&Math.abs(f-r)<=1&&s<d&&n.axisExtent0===l[0]&&n.axisExtent1===l[1]?s=d:(n.lastTickCount=r,n.lastAutoInterval=s,n.axisExtent0=l[0],n.axisExtent1=l[1]),s},i1);function i1(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}function r1(t,e){e=(t[1]-t[0])/e/2;t[0]+=e,t[1]-=e}var o1=2*Math.PI,a1=ja.CMD,s1=["top","right","bottom","left"];function l1(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,n=n-t,i=i-e,h=Math.sqrt(n*n+i*i),l=(l*(n/=h)+u*(i/=h))/h,u=(s&&(l=Math.min(Math.max(l,0),1)),a[0]=t+(l*=h)*n),s=a[1]=e+l*i;return Math.sqrt((u-r)*(u-r)+(s-o)*(s-o))}function u1(t,e,n,i,r,o,a){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i);n=t+n,i=e+i,t=a[0]=Math.min(Math.max(r,t),n),n=a[1]=Math.min(Math.max(o,e),i);return Math.sqrt((t-r)*(t-r)+(n-o)*(n-o))}var h1=[];function c1(t,e,n){for(var i,r,o,a,s,l,u,h,c,p=0,d=0,f=0,g=0,y=1/0,m=e.data,v=t.x,_=t.y,x=0;x<m.length;){var w=m[x++],b=(1===x&&(f=p=m[x],g=d=m[x+1]),y);switch(w){case a1.M:p=f=m[x++],d=g=m[x++];break;case a1.L:b=l1(p,d,m[x],m[x+1],v,_,h1,!0),p=m[x++],d=m[x++];break;case a1.C:b=Gn(p,d,m[x++],m[x++],m[x++],m[x++],m[x],m[x+1],v,_,h1),p=m[x++],d=m[x++];break;case a1.Q:b=Zn(p,d,m[x++],m[x++],m[x],m[x+1],v,_,h1),p=m[x++],d=m[x++];break;case a1.A:var S=m[x++],M=m[x++],T=m[x++],C=m[x++],I=m[x++],k=m[x++],D=(x+=1,!!(1-m[x++])),A=Math.cos(I)*T+S,P=Math.sin(I)*C+M;x<=1&&(f=A,g=P),P=(A=I)+k,D=D,a=(v-S)*(o=C)/T+S,s=_,l=h1,c=h=u=void 0,a-=i=S,s-=r=M,u=Math.sqrt(a*a+s*s),h=(a/=u)*o+i,c=(s/=u)*o+r,b=Math.abs(A-P)%o1<1e-4||((P=D?(D=A,A=Qa(P),Qa(D)):(A=Qa(A),Qa(P)))<A&&(P+=o1),(D=Math.atan2(s,a))<0&&(D+=o1),A<=D&&D<=P)||A<=D+o1&&D+o1<=P?(l[0]=h,l[1]=c,u-o):(c=((D=o*Math.cos(A)+i)-a)*(D-a)+((h=o*Math.sin(A)+r)-s)*(h-s))<(i=((u=o*Math.cos(P)+i)-a)*(u-a)+((A=o*Math.sin(P)+r)-s)*(A-s))?(l[0]=D,l[1]=h,Math.sqrt(c)):(l[0]=u,l[1]=A,Math.sqrt(i)),p=Math.cos(I+k)*T+S,d=Math.sin(I+k)*C+M;break;case a1.R:b=u1(f=p=m[x++],g=d=m[x++],m[x++],m[x++],v,_,h1);break;case a1.Z:b=l1(p,d,f,g,v,_,h1,!0),p=f,d=g}b<y&&(y=b,n.set(h1[0],h1[1]))}return y}var p1=new M,d1=new M,f1=new M,g1=new M,y1=new M;function m1(t,e){if(t){var n=t.getTextGuideLine(),i=t.getTextContent();if(i&&n){var r=t.textGuideLineConfig||{},o=[[0,0],[0,0],[0,0]],a=r.candidates||s1,s=i.getBoundingRect().clone(),l=(s.applyTransform(i.getComputedTransform()),1/0),u=r.anchor,h=t.getComputedTransform(),c=h&&Ve([],h),p=e.get("length2")||0;u&&f1.copy(u);for(var d,f,g=0;g<a.length;g++){var y=a[g],m=(S=b=w=x=_=v=m=void 0,y),v=0,_=s,x=p1,w=g1,b=_.width,S=_.height;switch(m){case"top":x.set(_.x+b/2,_.y-v),w.set(0,-1);break;case"bottom":x.set(_.x+b/2,_.y+S+v),w.set(0,1);break;case"left":x.set(_.x-v,_.y+S/2),w.set(-1,0);break;case"right":x.set(_.x+b+v,_.y+S/2),w.set(1,0)}M.scaleAndAdd(d1,p1,g1,p),d1.transform(c);y=t.getBoundingRect(),y=u?u.distance(d1):t instanceof j?c1(d1,t.path,f1):(m=f1,d=u1((d=y).x,y.y,y.width,y.height,d1.x,d1.y,h1),m.set(h1[0],h1[1]),d);y<l&&(l=y,d1.transform(h),f1.transform(h),f1.toArray(o[0]),d1.toArray(o[1]),p1.toArray(o[2]))}i=o,(r=e.get("minTurnAngle"))<=180&&0<r&&(r=r/180*Math.PI,p1.fromArray(i[0]),d1.fromArray(i[1]),f1.fromArray(i[2]),M.sub(g1,p1,d1),M.sub(y1,f1,d1),e=g1.len(),f=y1.len(),e<.001||f<.001||(g1.scale(1/e),y1.scale(1/f),e=g1.dot(y1),Math.cos(r)<e&&(f=l1(d1.x,d1.y,f1.x,f1.y,p1.x,p1.y,v1,!1),_1.fromArray(v1),_1.scaleAndAdd(y1,f/Math.tan(Math.PI-r)),e=f1.x!==d1.x?(_1.x-d1.x)/(f1.x-d1.x):(_1.y-d1.y)/(f1.y-d1.y),isNaN(e)||(e<0?M.copy(_1,d1):1<e&&M.copy(_1,f1),_1.toArray(i[1]))))),n.setShape({points:o})}}}var v1=[],_1=new M;function x1(t,e,n,i){var r="normal"===n,n=r?t:t.ensureState(n),e=(n.ignore=e,i.get("smooth")),e=(e&&!0===e&&(e=.3),n.shape=n.shape||{},0<e&&(n.shape.smooth=e),i.getModel("lineStyle").getLineStyle());r?t.useStyle(e):n.style=e}function w1(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),0<n&&3<=i.length){var e=Qt(i[0],i[1]),r=Qt(i[1],i[2]);e&&r?(n=Math.min(e,r)*n,e=ee([],i[1],i[0],n/e),n=ee([],i[1],i[2],n/r),r=ee([],e,n,.5),t.bezierCurveTo(e[0],e[1],e[0],e[1],r[0],r[1]),t.bezierCurveTo(n[0],n[1],n[0],n[1],i[2][0],i[2][1])):(t.lineTo(i[1][0],i[1][1]),t.lineTo(i[2][0],i[2][1]))}else for(var o=1;o<i.length;o++)t.lineTo(i[o][0],i[o][1])}function b1(t){for(var e=[],n=0;n<t.length;n++){var i,r,o,a,s,l,u=t[n];u.defaultAttr.ignore||(r=(i=u.label).getComputedTransform(),o=i.getBoundingRect(),a=!r||r[1]<1e-5&&r[2]<1e-5,l=i.style.margin||0,(s=o.clone()).applyTransform(r),s.x-=l/2,s.y-=l/2,s.width+=l,s.height+=l,l=a?new vh(o,r):null,e.push({label:i,labelLine:u.labelLine,rect:s,localRect:o,obb:l,priority:u.priority,defaultAttr:u.defaultAttr,layoutOption:u.computedLayoutOption,axisAligned:a,transform:r}))}return e}function S1(s,l,u,t,e,n){var h=s.length;if(!(h<2)){s.sort(function(t,e){return t.rect[l]-e.rect[l]});for(var i=0,o=!1,r=0,a=0;a<h;a++){var c,p=s[a],d=p.rect;(c=d[l]-i)<0&&(d[l]-=c,p.label[l]-=c,o=!0),r+=Math.max(-c,0),i=d[l]+d[u]}0<r&&n&&x(-r/h,0,h);var f,g,y=s[0],m=s[h-1];return v(),f<0&&w(-f,.8),g<0&&w(g,.8),v(),_(f,g,1),_(g,f,-1),v(),f<0&&b(-f),g<0&&b(g),o}function v(){f=y.rect[l]-t,g=e-m.rect[l]-m.rect[u]}function _(t,e,n){t<0&&(0<(e=Math.min(e,-t))?(x(e*n,0,h),(e=e+t)<0&&w(-e*n,1)):w(-t*n,1))}function x(t,e,n){0!==t&&(o=!0);for(var i=e;i<n;i++){var r=s[i];r.rect[l]+=t,r.label[l]+=t}}function w(t,e){for(var n=[],i=0,r=1;r<h;r++){var o=s[r-1].rect,o=Math.max(s[r].rect[l]-o[l]-o[u],0);n.push(o),i+=o}if(i){var a=Math.min(Math.abs(t)/i,e);if(0<t)for(r=0;r<h-1;r++)x(n[r]*a,0,r+1);else for(r=h-1;0<r;r--)x(-(n[r-1]*a),r,h)}}function b(t){for(var e=t<0?-1:1,n=(t=Math.abs(t),Math.ceil(t/(h-1))),i=0;i<h-1;i++)if(0<e?x(n,0,i+1):x(-n,h-i-1,h),(t-=n)<=0)return}}function M1(t){var e=[],n=(t.sort(function(t,e){return e.priority-t.priority}),new X(0,0,0,0));function i(t){var e;t.ignore||null==(e=t.ensureState("emphasis")).ignore&&(e.ignore=!1),t.ignore=!0}for(var r=0;r<t.length;r++){for(var o=t[r],a=o.axisAligned,s=o.localRect,l=o.transform,u=o.label,h=o.labelLine,c=(n.copy(o.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05,o.obb),p=!1,d=0;d<e.length;d++){var f=e[d];if(n.intersect(f.rect)){if(a&&f.axisAligned){p=!0;break}if(f.obb||(f.obb=new vh(f.localRect,f.transform)),(c=c||new vh(s,l)).intersect(f.obb)){p=!0;break}}}p?(i(u),h&&i(h)):(u.attr("ignore",o.defaultAttr.ignore),h&&h.attr("ignore",o.defaultAttr.labelGuideIgnore),e.push(o))}}function T1(t,e){var n=t.label,e=e&&e.getTextGuideLine();return{dataIndex:t.dataIndex,dataType:t.dataType,seriesIndex:t.seriesModel.seriesIndex,text:t.label.style.text,rect:t.hostRect,labelRect:t.rect,align:n.style.align,verticalAlign:n.style.verticalAlign,labelLinePoints:function(t){if(t){for(var e=[],n=0;n<t.length;n++)e.push(t[n].slice());return e}}(e&&e.shape.points)}}var C1=["align","verticalAlign","width","height","fontSize"],I1=new vr,k1=Lo(),D1=Lo();function A1(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null!=e[r]&&(t[r]=e[r])}}var P1=["x","y","rotation"],L1=(O1.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},O1.prototype._addLabel=function(t,e,n,i,r){var o,a=i.style,s=i.__hostTarget.textConfig||{},l=i.getComputedTransform(),u=i.getBoundingRect().plain(),l=(X.applyTransform(u,u,l),l?I1.setLocalTransform(l):(I1.x=I1.y=I1.rotation=I1.originX=I1.originY=0,I1.scaleX=I1.scaleY=1),I1.rotation=Qa(I1.rotation),i.__hostTarget),h=(l&&(o=l.getBoundingRect().plain(),h=l.getComputedTransform(),X.applyTransform(o,o,h)),o&&l.getTextGuideLine());this._labelList.push({label:i,labelLine:h,seriesModel:n,dataIndex:t,dataType:e,layoutOption:r,computedLayoutOption:null,rect:u,hostRect:o,priority:o?o.width*o.height:0,defaultAttr:{ignore:i.ignore,labelGuideIgnore:h&&h.ignore,x:I1.x,y:I1.y,scaleX:I1.scaleX,scaleY:I1.scaleY,rotation:I1.rotation,style:{x:a.x,y:a.y,align:a.align,verticalAlign:a.verticalAlign,width:a.width,height:a.height,fontSize:a.fontSize},cursor:i.cursor,attachedPos:s.position,attachedRot:s.rotation}})},O1.prototype.addLabelsOfSeries=function(t){var n=this,i=(this._chartViewList.push(t),t.__model),r=i.get("labelLayout");(k(r)||ht(r).length)&&t.group.traverse(function(t){if(t.ignore)return!0;var e=t.getTextContent(),t=D(t);e&&!e.disableLabelLayout&&n._addLabel(t.dataIndex,t.dataType,i,e,r)})},O1.prototype.updateLayoutConfig=function(t){var e=t.getWidth(),n=t.getHeight();for(var i=0;i<this._labelList.length;i++){var r=this._labelList[i],o=r.label,a=o.__hostTarget,s=r.defaultAttr,l=void 0,l=k(r.layoutOption)?r.layoutOption(T1(r,a)):r.layoutOption,u=(r.computedLayoutOption=l=l||{},Math.PI/180),h=(a&&a.setTextConfig({local:!1,position:null!=l.x||null!=l.y?null:s.attachedPos,rotation:null!=l.rotate?l.rotate*u:s.attachedRot,offset:[l.dx||0,l.dy||0]}),!1);null!=l.x?(o.x=eo(l.x,e),o.setStyle("x",0),h=!0):(o.x=s.x,o.setStyle("x",s.style.x)),null!=l.y?(o.y=eo(l.y,n),o.setStyle("y",0),h=!0):(o.y=s.y,o.setStyle("y",s.style.y)),l.labelLinePoints&&(c=a.getTextGuideLine())&&(c.setShape({points:l.labelLinePoints}),h=!1),k1(o).needsUpdateLabelLine=h,o.rotation=null!=l.rotate?l.rotate*u:s.rotation,o.scaleX=s.scaleX,o.scaleY=s.scaleY;for(var c,p=0;p<C1.length;p++){var d=C1[p];o.setStyle(d,(null!=l[d]?l:s.style)[d])}l.draggable?(o.draggable=!0,o.cursor="move",a&&(c=r.seriesModel,null!=r.dataIndex&&(c=r.seriesModel.getData(r.dataType).getItemModel(r.dataIndex)),o.on("drag",function(t,e){return function(){m1(t,e)}}(a,c.getModel("labelLine"))))):(o.off("drag"),o.cursor=s.cursor)}},O1.prototype.layout=function(t){var e,n,i=t.getWidth(),t=t.getHeight(),r=b1(this._labelList),o=ut(r,function(t){return"shiftX"===t.layoutOption.moveOverlap}),a=ut(r,function(t){return"shiftY"===t.layoutOption.moveOverlap});S1(o,"x","width",0,i,e),S1(a,"y","height",0,t,n),M1(ut(r,function(t){return t.layoutOption.hideOverlap}))},O1.prototype.processLabelsOverall=function(){var a=this;R(this._chartViewList,function(t){var i=t.__model,r=t.ignoreLabelLineUpdate,o=i.isAnimationEnabled();t.group.traverse(function(t){if(t.ignore&&!t.forceLabelAnimation)return!0;var e=!r,n=t.getTextContent();(e=!e&&n?k1(n).needsUpdateLabelLine:e)&&a._updateLabelLine(t,i),o&&a._animateLabels(t,i)})})},O1.prototype._updateLabelLine=function(t,e){var n=t.getTextContent(),i=D(t),r=i.dataIndex;if(n&&null!=r){var n=e.getData(i.dataType),e=n.getItemModel(r),i={},r=n.getItemVisual(r,"style"),r=(r&&(n=n.getVisual("drawType"),i.stroke=r[n]),e.getModel("labelLine")),o=t,a=function(t,e){for(var n={normal:t.getModel(e=e||"labelLine")},i=0;i<Js.length;i++){var r=Js[i];n[r]=t.getModel([r,e])}return n}(e),n=i,s=o.getTextGuideLine(),l=o.getTextContent();if(l){for(var e=a.normal,u=e.get("show"),h=l.ignore,c=0;c<tl.length;c++){var p,d=tl[c],f=a[d],g="normal"===d;f&&(p=f.get("show"),(g?h:E(l.states[d]&&l.states[d].ignore,h))||!E(p,u)?((p=g?s:s&&s.states[d])&&(p.ignore=!0),s&&x1(s,!0,d,f)):(s||(s=new Hu,o.setTextGuideLine(s),g||!h&&u||x1(s,!0,"normal",a.normal),o.stateProxy&&(s.stateProxy=o.stateProxy)),x1(s,!1,d,f)))}s&&(N(s.style,n),s.style.fill=null,n=e.get("showAbove"),(o.textGuideLineConfig=o.textGuideLineConfig||{}).showAbove=n||!1,s.buildPath=w1)}else s&&o.removeTextGuideLine();m1(t,r)}},O1.prototype._animateLabels=function(t,e){var n,i,r,o,a,s=t.getTextContent(),l=t.getTextGuideLine();!s||!t.forceLabelAnimation&&(s.ignore||s.invisible||t.disableLabelAnimation||Ih(t))||(o=(r=k1(s)).oldLayout,n=(i=D(t)).dataIndex,a={x:s.x,y:s.y,rotation:s.rotation},i=e.getData(i.dataType),o?(s.attr(o),(t=t.prevStates)&&(0<=I(t,"select")&&s.attr(r.oldLayoutSelect),0<=I(t,"emphasis"))&&s.attr(r.oldLayoutEmphasis),Th(s,a,e,n)):(s.attr(a),dc(s).valueAnimation||(t=E(s.style.opacity,1),s.style.opacity=0,Ch(s,{style:{opacity:t}},e,n))),r.oldLayout=a,s.states.select&&(A1(t=r.oldLayoutSelect={},a,P1),A1(t,s.states.select,P1)),s.states.emphasis&&(A1(t=r.oldLayoutEmphasis={},a,P1),A1(t,s.states.emphasis,P1)),fc(s,n,i,e,e)),!l||l.ignore||l.invisible||(o=(r=D1(l)).oldLayout,a={points:l.shape.points},o?(l.attr({shape:o}),Th(l,{shape:a},e)):(l.setShape(a),l.style.strokePercent=0,Ch(l,{style:{strokePercent:1}},e)),r.oldLayout=a)},O1);function O1(){this._labelList=[],this._chartViewList=[]}var R1=Lo();function E1(t){t.registerUpdateLifecycle("series:beforeupdate",function(t,e,n){(R1(e).labelManager||(R1(e).labelManager=new L1)).clearLabels()}),t.registerUpdateLifecycle("series:layoutlabels",function(t,e,n){var i=R1(e).labelManager;n.updatedSeries.forEach(function(t){i.addLabelsOfSeries(e.getViewOfSeriesModel(t))}),i.updateLayoutConfig(e),i.layout(e),i.processLabelsOverall()})}function B1(t,e,n){var i=H.createCanvas(),r=e.getWidth(),e=e.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=r+"px",o.height=e+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=e*n,i}k_(E1);u(F1,N1=ue),F1.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},F1.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},F1.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},F1.prototype.setUnpainted=function(){this.__firstTimePaint=!0},F1.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=B1("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},F1.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var l=[],u=this.maxRepaintRectCount,h=!1,c=new X(0,0,0,0);function r(t){if(t.isFinite()&&!t.isZero())if(0===l.length)(e=new X(0,0,0,0)).copy(t),l.push(e);else{for(var e,n=!1,i=1/0,r=0,o=0;o<l.length;++o){var a=l[o];if(a.intersect(t)){var s=new X(0,0,0,0);s.copy(a),s.union(t),l[o]=s,n=!0;break}h&&(c.copy(t),c.union(a),s=t.width*t.height,a=a.width*a.height,(a=c.width*c.height-s-a)<i)&&(i=a,r=o)}h&&(l[r].union(t),n=!0),n||((e=new X(0,0,0,0)).copy(t),l.push(e)),h=h||l.length>=u}}for(var o,a=this.__startIndex;a<this.__endIndex;++a)(s=t[a])&&(d=s.shouldBePainted(n,i,!0,!0),(p=s.__isRendered&&(s.__dirty&_n||!d)?s.getPrevPaintRect():null)&&r(p),o=d&&(s.__dirty&_n||!s.__isRendered)?s.getPaintRect():null)&&r(o);for(a=this.__prevStartIndex;a<this.__prevEndIndex;++a){var s,p,d=(s=e[a])&&s.shouldBePainted(n,i,!0,!0);!s||d&&s.__zr||!s.__isRendered||(p=s.getPrevPaintRect())&&r(p)}do{for(var f=!1,a=0;a<l.length;)if(l[a].isZero())l.splice(a,1);else{for(var g=a+1;g<l.length;)l[a].intersect(l[g])?(f=!0,l[a].union(l[g]),l.splice(g,1)):g++;a++}}while(f);return this._paintRects=l},F1.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},F1.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n)&&this.ctxBack.scale(n,n)},F1.prototype.clear=function(t,o,e){var n=this.dom,a=this.ctx,i=n.width,r=n.height,s=(o=o||this.clearColor,this.motionBlur&&!t),l=this.lastFrameAlpha,u=this.dpr,h=this,c=(s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,i/u,r/u)),this.domBack);function p(t,e,n,i){var r;a.clearRect(t,e,n,i),o&&"transparent"!==o&&(r=void 0,vt(o)?(r=(o.global||o.__width===n&&o.__height===i)&&o.__canvasGradient||Uy(a,o,{x:0,y:0,width:n,height:i}),o.__canvasGradient=r,o.__width=n,o.__height=i):_t(o)&&(o.scaleX=o.scaleX||u,o.scaleY=o.scaleY||u,r=tm(a,o,{dirty:function(){h.setUnpainted(),h.painter.refresh()}})),a.save(),a.fillStyle=r||o,a.fillRect(t,e,n,i),a.restore()),s&&(a.save(),a.globalAlpha=l,a.drawImage(c,t,e,n,i),a.restore())}!e||s?p(0,0,i,r):e.length&&R(e,function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)})};var N1,z1=F1;function F1(t,e,n){var i,r=N1.call(this)||this,t=(r.motionBlur=!1,r.lastFrameAlpha=.7,r.dpr=1,r.virtual=!1,r.config={},r.incremental=!1,r.zlevel=0,r.maxRepaintRectCount=5,r.__dirty=!0,r.__firstTimePaint=!0,r.__used=!1,r.__drawIndex=0,r.__startIndex=0,r.__endIndex=0,r.__prevStartIndex=null,r.__prevEndIndex=null,n=n||ur,"string"==typeof t?i=B1(t,e,n):L(t)&&(t=(i=t).id),r.id=t,(r.dom=i).style);return t&&(zt(i),i.onselectstart=function(){return!1},t.padding="0",t.margin="0",t.borderWidth="0"),r.painter=e,r.dpr=n,r}var V1=314159;v.prototype.getType=function(){return"canvas"},v.prototype.isSingleCanvas=function(){return this._singleCanvas},v.prototype.getViewportRoot=function(){return this._domRoot},v.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},v.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o,a=i[r],a=this._layers[a];!a.__builtin__&&a.refresh&&(o=0===r?this._backgroundColor:null,a.refresh(o))}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},v.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},v.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n=n||(this._hoverlayer=this.getLayer(1e5)),i||(i=n.ctx).save(),dm(i,a,r,o===e-1))}i&&i.restore()}},v.prototype.getHoverLayer=function(){return this.getLayer(1e5)},v.prototype.paintOne=function(t,e){pm(t,e)},v.prototype._paintList=function(t,e,n,i){var r,o,a;this._redrawId===i&&(n=n||!1,this._updateLayerStatus(t),r=(o=this._doPaintList(t,e,n)).finished,o=o.needsRefreshHover,this._needsManuallyCompositing&&this._compositeManually(),o&&this._paintHoverList(t),r?this.eachLayer(function(t){t.afterBrush&&t.afterBrush()}):(a=this,Cn(function(){a._paintList(t,e,n,i)})))},v.prototype._compositeManually=function(){var e=this.getLayer(V1).ctx,n=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,n,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,n,i)})},v.prototype._doPaintList=function(d,f,g){for(var y=this,m=[],v=this._opts.useDirtyRect,t=0;t<this._zlevelList.length;t++){var e=this._zlevelList[t],e=this._layers[e];e.__builtin__&&e!==this._hoverlayer&&(e.__dirty||g)&&m.push(e)}for(var _=!0,x=!1,n=function(t){function e(t){var e={inHover:!1,allClipped:!1,prevEl:null,viewWidth:y._width,viewHeight:y._height};for(i=s;i<r.__endIndex;i++){var n=d[i];if(n.__inHover&&(x=!0),y._doPaintEl(n,r,v,t,e,i===r.__endIndex-1),l)if(15<Date.now()-u)break}e.prevElClipPaths&&o.restore()}var n,i,r=m[t],o=r.ctx,a=v&&r.createRepaintRects(d,f,w._width,w._height),s=g?r.__startIndex:r.__drawIndex,l=!g&&r.incremental&&Date.now,u=l&&Date.now(),t=r.zlevel===w._zlevelList[0]?w._backgroundColor:null;r.__startIndex!==r.__endIndex&&(s!==r.__startIndex||(n=d[s]).incremental&&n.notClear&&!g)||r.clear(!1,t,a),-1===s&&(console.error("For some unknown reason. drawIndex is -1"),s=r.__startIndex);if(a)if(0===a.length)i=r.__endIndex;else for(var h=w.dpr,c=0;c<a.length;++c){var p=a[c];o.save(),o.beginPath(),o.rect(p.x*h,p.y*h,p.width*h,p.height*h),o.clip(),e(p),o.restore()}else o.save(),e(),o.restore();r.__drawIndex=i,r.__drawIndex<r.__endIndex&&(_=!1)},w=this,i=0;i<m.length;i++)n(i);return b.wxa&&R(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),{finished:_,needsRefreshHover:x}},v.prototype._doPaintEl=function(t,e,n,i,r,o){e=e.ctx;n?(n=t.getPaintRect(),(!i||n&&n.intersect(i))&&(dm(e,t,r,o),t.setPrevPaintRect(n))):dm(e,t,r,o)},v.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=V1);var n=this._layers[t];return n||((n=new z1("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?d(n,this._layerConfig[t],!0):this._layerConfig[t-.01]&&d(n,this._layerConfig[t-.01],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},v.prototype.insertLayer=function(t,e){var n,i=this._layers,r=this._zlevelList,o=r.length,a=this._domRoot,s=null,l=-1;if(!i[t]&&(n=e)&&(n.__builtin__||"function"==typeof n.resize&&"function"==typeof n.refresh)){if(0<o&&t>r[0]){for(l=0;l<o-1&&!(r[l]<t&&r[l+1]>t);l++);s=i[r[l]]}r.splice(l+1,0,t),(i[t]=e).virtual||(s?(n=s.dom).nextSibling?a.insertBefore(e.dom,n.nextSibling):a.appendChild(e.dom):a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom)),e.painter||(e.painter=this)}},v.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},v.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},v.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},v.prototype.getLayers=function(){return this._layers},v.prototype._updateLayerStatus=function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++)if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}for(var i,r=null,o=0,a=0;a<t.length;a++){var s,l=(s=t[a]).zlevel,u=void 0;i!==l&&(i=l,o=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,o=1):u=this.getLayer(l+(0<o?.01:0),this._needsManuallyCompositing),u.__builtin__||it("ZLevel "+l+" has been used by unkown layer "+u.id),u!==r&&(u.__used=!0,u.__startIndex!==a&&(u.__dirty=!0),u.__startIndex=a,u.incremental?u.__drawIndex=-1:u.__drawIndex=a,e(a),r=u),s.__dirty&_n&&!s.__inHover&&(u.__dirty=!0,u.incremental)&&u.__drawIndex<0&&(u.__drawIndex=a)}e(a),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},v.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},v.prototype._clearLayer=function(t){t.clear()},v.prototype.setBackgroundColor=function(t){this._backgroundColor=t,R(this._layers,function(t){t.setUnpainted()})},v.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?d(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];r!==t&&r!==t+.01||d(this._layers[r],n[t],!0)}}},v.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(I(n,t),1))},v.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot,i=(n.style.display="none",this._opts),r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=Yy(r,0,i),e=Yy(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(V1).resize(t,e)}return this},v.prototype.clearLayer=function(t){t=this._layers[t];t&&t.clear()},v.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},v.prototype.getRenderedCanvas=function(t){if(this._singleCanvas&&!this._compositeManually)return this._layers[V1].dom;var e=new z1("image",this,(t=t||{}).pixelRatio||this.dpr),n=(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),e.ctx);if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer(function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];dm(n,u,o,s===l-1)}return e.dom},v.prototype.getWidth=function(){return this._width},v.prototype.getHeight=function(){return this._height};var H1=v;function v(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=O({},n||{}),this.dpr=n.devicePixelRatio||ur,this._singleCanvas=r;(this.root=t).style&&(zt(t),t.innerHTML=""),this.storage=e;var o,a,e=this._zlevelList,s=(this._prevDisplayList=[],this._layers);r?(o=(r=t).width,a=r.height,null!=n.width&&(o=n.width),null!=n.height&&(a=n.height),this.dpr=n.devicePixelRatio||1,r.width=o*this.dpr,r.height=a*this.dpr,this._width=o,this._height=a,(o=new z1(r,this,this.dpr)).__builtin__=!0,o.initContext(),(s[V1]=o).zlevel=V1,e.push(V1),this._domRoot=t):(this._width=Yy(t,0,n),this._height=Yy(t,1,n),o=this._domRoot=(a=this._width,r=this._height,(s=document.createElement("div")).style.cssText=["position:relative","width:"+a+"px","height:"+r+"px","padding:0","margin:0","border-width:0"].join(";")+";",s),t.appendChild(o))}u(U1,W1=g),U1.prototype.init=function(t,e,n){W1.prototype.init.call(this,t,e,n),this._sourceManager=new rg(this),ag(this)},U1.prototype.mergeOption=function(t,e){W1.prototype.mergeOption.call(this,t,e),ag(this)},U1.prototype.optionUpdated=function(){this._sourceManager.dirty()},U1.prototype.getSourceManager=function(){return this._sourceManager},U1.type="dataset",U1.defaultOption={seriesLayoutBy:Xp};var W1,G1=U1;function U1(){var t=null!==W1&&W1.apply(this,arguments)||this;return t.type="dataset",t}u(q1,X1=Bg),q1.type="dataset";var X1,Y1=q1;function q1(){var t=null!==X1&&X1.apply(this,arguments)||this;return t.type="dataset",t}function Z1(t){t.registerComponentModel(G1),t.registerComponentView(Y1)}k_([function(t){t.registerPainter("canvas",H1)},Z1]),k_(E1);var j1={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},minmax:function(t){for(var e=-1/0,n=-1/0,i=0;i<t.length;i++){var r=t[i],o=Math.abs(r);e<o&&(e=o,n=r)}return isFinite(n)?n:NaN},nearest:function(t){return t[0]}},K1=function(t){return Math.round(t.length/2)};u(Q1,$1=kg),Q1.prototype.getInitialData=function(t,e){return gv(null,this,{useEncodeDefaulter:!0})},Q1.prototype.getMarkerPosition=function(t,p,e){var d,f,n=this.coordinateSystem;return n&&n.clampData?(d=n.clampData(t),f=n.dataToPoint(d),e?R(n.getAxes(),function(t,e){if("category"===t.type&&null!=p){var n=t.getTicksCoords(),i=t.getTickModel().get("alignWithLabel"),r=d[e],o="x1"===p[e]||"y1"===p[e];if(o&&!i&&(r+=1),!(n.length<2))if(2===n.length)f[e]=t.toGlobalCoord(t.getExtent()[o?1:0]);else{for(var a=void 0,s=void 0,l=1,u=0;u<n.length;u++){var h=n[u].coord,c=u===n.length-1?n[u-1].tickValue+l:n[u].tickValue;if(c===r){s=h;break}if(c<r)a=h;else if(null!=a&&r<c){s=(h+a)/2;break}1===u&&(l=c-n[0].tickValue)}null==s&&(s=(a?n[n.length-1]:n[0]).coord),f[e]=t.toGlobalCoord(s)}}}):(e=(t=this.getData()).getLayout("offset"),t=t.getLayout("size"),n=n.getBaseAxis().isHorizontal()?0:1,f[n]+=e+t/2),f):[NaN,NaN]},Q1.type="series.__base_bar__",Q1.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"};var $1,wc=Q1;function Q1(){var t=null!==$1&&$1.apply(this,arguments)||this;return t.type=Q1.type,t}kg.registerClass(wc);u(ex,J1=wc),ex.prototype.getInitialData=function(){return gv(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},ex.prototype.getProgressive=function(){return!!this.get("large")&&this.get("progressive")},ex.prototype.getProgressiveThreshold=function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return t=t<e?e:t},ex.prototype.brushSelector=function(t,e,n){return n.rect(e.getItemLayout(t))},ex.type="series.bar",ex.dependencies=["grid","polar"],ex.defaultOption=(Xo={clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1},d(d({},wc.defaultOption,!0),Xo,!0));var J1,tx=ex;function ex(){var t=null!==J1&&J1.apply(this,arguments)||this;return t.type=ex.type,t}function nx(t,e,n,i,r){var o,a,s,l,u,h,c,p,d;return t?"polar"===t.type?(l=e,u=n,h=(s=t).getArea(),c=no(h.r0,1),p=no(h.r,1),d=new ku({shape:{cx:no(s.cx,1),cy:no(s.cy,1),r0:c,r:p,startAngle:h.startAngle,endAngle:h.endAngle,clockwise:h.clockwise}}),l&&("angle"===s.getBaseAxis().dim?d.shape.endAngle=h.startAngle:d.shape.r=c,Ch(d,{shape:{endAngle:h.endAngle,r:p}},u)),d):"cartesian2d"===t.type?(l=e,s=n,c=i,o=r,u=(p=(h=t).getArea()).x,d=p.y,e=p.width,p=p.height,n=s.get(["lineStyle","width"])||2,u-=n/2,d-=n/2,e+=n,p+=n,e=Math.ceil(e),u!==Math.floor(u)&&(u=Math.floor(u),e++),a=new Ds({shape:{x:u,y:d,width:e,height:p}}),l&&(l=(n=h.getBaseAxis()).isHorizontal(),h=n.inverse,l?(h&&(a.shape.x+=e),a.shape.width=0):(h||(a.shape.y+=p),a.shape.height=0),n=k(o)?function(t){o(t,a)}:null,Ch(a,{shape:{width:e,height:p,x:u,y:d}},s,null,c,n)),a):null:null}var ix,rx=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},ox=(u(ax,ix=j),ax.prototype.getDefaultShape=function(){return new rx},ax.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=.5*(o-r),s=r+a,l=e.startAngle,u=e.endAngle,e=e.clockwise,h=2*Math.PI,c=e?u-l<h:l-u<h,h=(c||(l=u-(e?h:-h)),Math.cos(l)),p=Math.sin(l),d=Math.cos(u),f=Math.sin(u);c?(t.moveTo(h*r+n,p*r+i),t.arc(h*s+n,p*s+i,a,-Math.PI+l,l,!e)):t.moveTo(h*o+n,p*o+i),t.arc(n,i,o,l,u,!e),t.arc(d*s+n,f*s+i,a,u-2*Math.PI,u-Math.PI,!e),0!==r&&t.arc(n,i,r,u,l,e)},ax);function ax(t){t=ix.call(this,t)||this;return t.type="sausage",t}function sx(t,e){return t.type===e}function lx(t,e,n){return e*Math.sin(t)*(n?-1:1)}function ux(t,e,n){return e*Math.cos(t)*(n?1:-1)}var hx=Math.max,cx=Math.min;u(fx,px=Hg),fx.prototype.render=function(t,e,n,i){this._model=t,this._removeOnRenderedListener(n),this._updateDrawMode(t);var r=t.get("coordinateSystem");"cartesian2d"!==r&&"polar"!==r||(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(t,e,n):this._renderNormal(t,e,n,i))},fx.prototype.incrementalPrepareRender=function(t){this._clear(),this._updateDrawMode(t),this._updateLargeClip(t)},fx.prototype.incrementalRender=function(t,e){this._progressiveEls=[],this._incrementalRenderLarge(t,e)},fx.prototype.eachRendered=function(t){nc(this._progressiveEls||this.group,t)},fx.prototype._updateDrawMode=function(t){t=t.pipelineContext.large;null!=this._isLargeDraw&&t===this._isLargeDraw||(this._isLargeDraw=t,this._clear())},fx.prototype._renderNormal=function(a,t,e,n){var s,i,r,l=this.group,u=a.getData(),h=this._data,c=a.coordinateSystem,p=c.getBaseAxis(),d=("cartesian2d"===c.type?s=p.isHorizontal():"polar"===c.type&&(s="angle"===p.dim),a.isAnimationEnabled()?a:null),f=function(t,e){var t=t.get("realtimeSort",!0),n=e.getBaseAxis();if(t&&"category"===n.type&&"cartesian2d"===e.type)return{baseAxis:n,otherAxis:e.getOtherAxis(n)}}(a,c),g=(f&&this._enableRealtimeSort(f,u,e),a.get("clip",!0)||f),y=(e=u,r=(i=c).getArea&&i.getArea(),!sx(i,"cartesian2d")||"category"===(i=i.getBaseAxis()).type&&i.onBand||(e=e.getLayout("bandWidth"),i.isHorizontal()?(r.x-=e,r.width+=2*e):(r.y-=e,r.height+=2*e)),r),m=(l.removeClipPath(),a.get("roundCap",!0)),v=a.get("showBackground",!0),_=a.getModel("backgroundStyle"),x=_.get("borderRadius")||0,w=[],b=this._backgroundEls,S=n&&n.isInitSort,M=n&&"changeAxisOrder"===n.type;function T(t){var e=Sx[c.type](u,t),n=(n=s,new("polar"===c.type?ku:Ds)({shape:Lx(n,e,c),silent:!0,z2:0}));return n.useStyle(_.getItemStyle()),"cartesian2d"===c.type?n.setShape("r",x):n.setShape("cornerRadius",x),w[t]=n}u.diff(h).add(function(t){var e,n,i=u.getItemModel(t),r=Sx[c.type](u,t,i);v&&T(t),u.hasValue(t)&&bx[c.type](r)&&(e=!1,g&&(e=gx[c.type](y,r)),n=yx[c.type](a,u,t,r,s,d,p.model,!1,m),f&&(n.forceLabelAnimation=!0),Tx(n,u,t,i,r,a,s,"polar"===c.type),S?n.attr({shape:r}):f?mx(f,d,n,r,t,s,!1,!1):Ch(n,{shape:r},a,t),u.setItemGraphicEl(t,n),l.add(n),n.ignore=e)}).update(function(t,e){var n,i=u.getItemModel(t),r=Sx[c.type](u,t,i),o=(v&&(o=void 0,0===b.length?o=T(e):((o=b[e]).useStyle(_.getItemStyle()),"cartesian2d"===c.type?o.setShape("r",x):o.setShape("cornerRadius",x),w[t]=o),n=Sx[c.type](u,t),Th(o,{shape:Lx(s,n,c)},d,t)),h.getItemGraphicEl(e));u.hasValue(t)&&bx[c.type](r)?(n=!1,g&&(n=gx[c.type](y,r))&&l.remove(o),o?Sh(e=o).oldStyle=e.style:o=yx[c.type](a,u,t,r,s,d,p.model,!!o,m),f&&(o.forceLabelAnimation=!0),M?(e=o.getTextContent())&&null!=(e=dc(e)).prevValue&&(e.prevValue=e.value):Tx(o,u,t,i,r,a,s,"polar"===c.type),S?o.attr({shape:r}):f?mx(f,d,o,r,t,s,!0,M):Th(o,{shape:r},a,t,null),u.setItemGraphicEl(t,o),o.ignore=n,l.add(o)):l.remove(o)}).remove(function(t){var e=h.getItemGraphicEl(t);e&&Ah(e,a,t)}).execute();var o=this._backgroundGroup||(this._backgroundGroup=new Wr);o.removeAll();for(var C=0;C<w.length;++C)o.add(w[C]);l.add(o),this._backgroundEls=w,this._data=u},fx.prototype._renderLarge=function(t,e,n){this._clear(),Ax(t,this.group),this._updateLargeClip(t)},fx.prototype._incrementalRenderLarge=function(t,e){this._removeBackground(),Ax(e,this.group,this._progressiveEls,!0)},fx.prototype._updateLargeClip=function(t){var t=t.get("clip",!0)&&nx(t.coordinateSystem,!1,t),e=this.group;t?e.setClipPath(t):e.removeClipPath()},fx.prototype._enableRealtimeSort=function(t,e,n){var i,r,o=this;e.count()&&(i=t.baseAxis,this._isFirstFrame?(this._dispatchInitSort(e,t,n),this._isFirstFrame=!1):(r=function(t){t=e.getItemGraphicEl(t),t=t&&t.shape;return t&&Math.abs(i.isHorizontal()?t.height:t.width)||0},this._onRendered=function(){o._updateSortWithinSameData(e,r,i,n)},n.getZr().on("rendered",this._onRendered)))},fx.prototype._dataSort=function(t,e,i){var r=[];return t.each(t.mapDimension(e.dim),function(t,e){var n=i(e);r.push({dataIndex:e,mappedValue:null==n?NaN:n,ordinalNumber:t})}),r.sort(function(t,e){return e.mappedValue-t.mappedValue}),{ordinalNumbers:z(r,function(t){return t.ordinalNumber})}},fx.prototype._isOrderChangedWithinSameData=function(t,e,n){for(var i=n.scale,r=t.mapDimension(n.dim),o=Number.MAX_VALUE,a=0,s=i.getOrdinalMeta().categories.length;a<s;++a){var l=t.rawIndexOf(r,i.getRawOrdinalNumber(a)),l=l<0?Number.MIN_VALUE:e(t.indexOfRawIndex(l));if(o<l)return!0;o=l}return!1},fx.prototype._isOrderDifferentInView=function(t,e){for(var n=e.scale,e=n.getExtent(),i=Math.max(0,e[0]),r=Math.min(e[1],n.getOrdinalMeta().categories.length-1);i<=r;++i)if(t.ordinalNumbers[i]!==n.getRawOrdinalNumber(i))return!0},fx.prototype._updateSortWithinSameData=function(t,e,n,i){this._isOrderChangedWithinSameData(t,e,n)&&(t=this._dataSort(t,n,e),this._isOrderDifferentInView(t,n))&&(this._removeOnRenderedListener(i),i.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",axisId:n.index,sortInfo:t}))},fx.prototype._dispatchInitSort=function(e,n,t){var i=n.baseAxis,r=this._dataSort(e,i,function(t){return e.get(e.mapDimension(n.otherAxis.dim),t)});t.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",isInitSort:!0,axisId:i.index,sortInfo:r})},fx.prototype.remove=function(t,e){this._clear(this._model),this._removeOnRenderedListener(e)},fx.prototype.dispose=function(t,e){this._removeOnRenderedListener(e)},fx.prototype._removeOnRenderedListener=function(t){this._onRendered&&(t.getZr().off("rendered",this._onRendered),this._onRendered=null)},fx.prototype._clear=function(e){var t=this.group,n=this._data;e&&e.isAnimationEnabled()&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl(function(t){Ah(t,e,D(t).dataIndex)})):t.removeAll(),this._data=null,this._isFirstFrame=!0},fx.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},fx.type="bar";var px,dx=fx;function fx(){var t=px.call(this)||this;return t.type=fx.type,t._isFirstFrame=!0,t}var gx={cartesian2d:function(t,e){var n=e.width<0?-1:1,i=e.height<0?-1:1,r=(n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height),t.x+t.width),o=t.y+t.height,a=hx(e.x,t.x),s=cx(e.x+e.width,r),t=hx(e.y,t.y),l=cx(e.y+e.height,o),u=s<a,h=l<t;return e.x=u&&r<a?s:a,e.y=h&&o<t?l:t,e.width=u?0:s-a,e.height=h?0:l-t,n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height),u||h},polar:function(t,e){var n,i=e.r0<=e.r?1:-1,r=(i<0&&(n=e.r,e.r=e.r0,e.r0=n),cx(e.r,t.r)),t=hx(e.r0,t.r0),r=(e.r=r)-(e.r0=t)<0;return i<0&&(n=e.r,e.r=e.r0,e.r0=n),r}},yx={cartesian2d:function(t,e,n,i,r,o,a,s,l){i=new Ds({shape:O({},i),z2:1});return i.__dataIndex=n,i.name="item",o&&(i.shape[r?"height":"width"]=0),i},polar:function(t,e,n,i,r,o,a,s,l){var w,b,l=!r&&l?ox:ku,u=new l({shape:i,z2:1}),h=(u.name="item",Mx(r));return u.calculateTextPosition=(w=h,b=({isRoundCap:l===ox}||{}).isRoundCap,function(t,e,n){var i=e.position;if(!i||i instanceof Array)return Dr(t,e,n);var i=w(i),r=null!=e.distance?e.distance:5,o=this.shape,a=o.cx,s=o.cy,l=o.r,u=o.r0,h=(l+u)/2,c=o.startAngle,p=o.endAngle,d=(c+p)/2,f=b?Math.abs(l-u)/2:0,g=Math.cos,y=Math.sin,m=a+l*g(c),v=s+l*y(c),_="left",x="top";switch(i){case"startArc":m=a+(u-r)*g(d),v=s+(u-r)*y(d),_="center",x="top";break;case"insideStartArc":m=a+(u+r)*g(d),v=s+(u+r)*y(d),_="center",x="bottom";break;case"startAngle":m=a+h*g(c)+lx(c,r+f,!1),v=s+h*y(c)+ux(c,r+f,!1),_="right",x="middle";break;case"insideStartAngle":m=a+h*g(c)+lx(c,f-r,!1),v=s+h*y(c)+ux(c,f-r,!1),_="left",x="middle";break;case"middle":m=a+h*g(d),v=s+h*y(d),_="center",x="middle";break;case"endArc":m=a+(l+r)*g(d),v=s+(l+r)*y(d),_="center",x="bottom";break;case"insideEndArc":m=a+(l-r)*g(d),v=s+(l-r)*y(d),_="center",x="top";break;case"endAngle":m=a+h*g(p)+lx(p,r+f,!0),v=s+h*y(p)+ux(p,r+f,!0),_="left",x="middle";break;case"insideEndAngle":m=a+h*g(p)+lx(p,f-r,!0),v=s+h*y(p)+ux(p,f-r,!0),_="right",x="middle";break;default:return Dr(t,e,n)}return(t=t||{}).x=m,t.y=v,t.align=_,t.verticalAlign=x,t}),o&&(h={},u.shape[l=r?"r":"endAngle"]=r?i.r0:i.startAngle,h[l]=i[l],(s?Th:Ch)(u,{shape:h},o)),u}};function mx(t,e,n,i,r,o,a,s){var l,o=o?(l={x:i.x,width:i.width},{y:i.y,height:i.height}):(l={y:i.y,height:i.height},{x:i.x,width:i.width});s||(a?Th:Ch)(n,{shape:o},e,r,null),(a?Th:Ch)(n,{shape:l},e?t.baseAxis.model:null,r)}function vx(t,e){for(var n=0;n<e.length;n++)if(!isFinite(t[e[n]]))return 1}var _x=["x","y","width","height"],xx=["cx","cy","r","startAngle","endAngle"],bx={cartesian2d:function(t){return!vx(t,_x)},polar:function(t){return!vx(t,xx)}},Sx={cartesian2d:function(t,e,n){var t=t.getItemLayout(e),i=n&&(e=t,i=(n=n).get(["itemStyle","borderColor"]))&&"none"!==i?(i=n.get(["itemStyle","borderWidth"])||0,n=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),e=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height),Math.min(i,n,e)):0,n=0<t.width?1:-1,e=0<t.height?1:-1;return{x:t.x+n*i/2,y:t.y+e*i/2,width:t.width-n*i,height:t.height-e*i}},polar:function(t,e,n){t=t.getItemLayout(e);return{cx:t.cx,cy:t.cy,r0:t.r0,r:t.r,startAngle:t.startAngle,endAngle:t.endAngle,clockwise:t.clockwise}}};function Mx(t){return e=t?"Arc":"Angle",function(t){switch(t){case"start":case"insideStart":case"end":case"insideEnd":return t+e;default:return t}};var e}function Tx(t,s,e,n,i,r,o,a){var l=s.getItemVisual(e,"style"),u=(a?r.get("roundCap")||(O(u=t.shape,function(t,e,n){if(null==(t=t.get("borderRadius")))return n?{cornerRadius:0}:null;F(t)||(t=[t,t,t,t]);var i=Math.abs(e.r||0-e.r0||0);return{cornerRadius:z(t,function(t){return kr(t,i)})}}(n.getModel("itemStyle"),u,!0)),t.setShape(u)):(u=n.get(["itemStyle","borderRadius"])||0,t.setShape("r",u)),t.useStyle(l),n.getShallow("cursor")),u=(u&&t.attr("cursor",u),a?o?i.r>=i.r0?"endArc":"startArc":i.endAngle>=i.startAngle?"endAngle":"startAngle":o?0<=i.height?"bottom":"top":0<=i.width?"right":"left"),h=function(t,e){for(var n={normal:t.getModel(e=e||"label")},i=0;i<Js.length;i++){var r=Js[i];n[r]=t.getModel([r,e])}return n}(n),c=t,p=h,d={labelFetcher:r,labelDataIndex:e,defaultText:function(t,e){var n,i=t.mapDimensionsAll("defaultedLabel"),r=i.length;if(1===r)return null!=(n=df(t,e,i[0]))?n+"":null;if(r){for(var o=[],a=0;a<i.length;a++)o.push(df(t,e,i[a]));return o.join(" ")}}(r.getData(),e),inheritColor:l.fill,defaultOpacity:l.opacity,defaultOutsidePosition:u},f=void 0;d=d||rc;for(var g=c instanceof Rs,y=!1,m=0;m<tl.length;m++)if((b=p[tl[m]])&&b.getShallow("show")){y=!0;break}var v=g?c:c.getTextContent();if(y){g||(v||(v=new Rs,c.setTextContent(v)),c.stateProxy&&(v.stateProxy=c.stateProxy));var _=ac(d,p),l=p.normal,x=!!l.getShallow("show"),w=sc(l,f&&f.normal,d,!1,!g);w.text=_.normal,g||c.setTextConfig(lc(l,d,!1));for(m=0;m<Js.length;m++){var b,S,M,T=Js[m];(b=p[T])&&(S=v.ensureState(T),(M=!!E(b.getShallow("show"),x))!=x&&(S.ignore=!M),S.style=sc(b,f&&f[T],d,!0,!g),S.style.text=_[T],g||(c.ensureState(T).textConfig=lc(b,d,!0)))}v.silent=!!l.getShallow("silent"),null!=v.style.x&&(w.x=v.style.x),null!=v.style.y&&(w.y=v.style.y),v.ignore=!x,v.useStyle(w),v.dirty(),d.enableTextSetter&&(dc(v).setLabelText=function(t){t=ac(d,p,t);oc(v,t)})}else v&&(v.ignore=!0);c.dirty();var l=t.getTextContent(),h=(a&&l&&(w=n.get(["label","position"]),t.textConfig.inside="middle"===w||null,function(t,e,n,i){if(ft(i))t.setTextConfig({rotation:i});else if(F(e))t.setTextConfig({rotation:0});else{var r,i=t.shape,o=i.clockwise?i.startAngle:i.endAngle,a=i.clockwise?i.endAngle:i.startAngle,s=(o+a)/2,i=n(e);switch(i){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":r=s;break;case"startAngle":case"insideStartAngle":r=o;break;case"endAngle":case"insideEndAngle":r=a;break;default:return t.setTextConfig({rotation:0})}n=1.5*Math.PI-r;"middle"===i&&n>Math.PI/2&&n<1.5*Math.PI&&(n-=Math.PI),t.setTextConfig({rotation:n})}}(t,"outside"===w?u:w,Mx(o),n.get(["label","rotate"]))),a=l,u=h,o=r.getRawValue(e),l=function(t){var e=s,n=t,i=e.mapDimensionsAll("defaultedLabel");if(!F(n))return n+"";for(var r=[],o=0;o<i.length;o++){var a=e.getDimensionIndex(i[o]);0<=a&&r.push(n[a])}return r.join(" ")},a&&((a=dc(a)).prevValue=a.value,a.value=o,o=u.normal,a.valueAnimation=o.get("valueAnimation"),a.valueAnimation)&&(a.precision=o.get("precision"),a.defaultInterpolatedText=l,a.statesModels=u),n.getModel(["emphasis"])),C=(Ol(t,h.get("focus"),h.get("blurScope"),h.get("disabled")),t),I=n,k=void 0,D=void 0;k=k||"itemStyle";for(var A=0;A<Rl.length;A++){var P=Rl[A],L=I.getModel([P,k]);C.ensureState(P).style=D?D(L):L[El[k]]()}null!=(r=i).startAngle&&null!=r.endAngle&&r.startAngle===r.endAngle&&(t.style.fill="none",t.style.stroke="none",R(t.states,function(t){t.style&&(t.style.fill=t.style.stroke="none")}))}var Cx,Ix=function(){},kx=(u(Dx,Cx=j),Dx.prototype.getDefaultShape=function(){return new Ix},Dx.prototype.buildPath=function(t,e){for(var n=e.points,i=this.baseDimIdx,r=1-this.baseDimIdx,o=[],a=[],s=this.barWidth,l=0;l<n.length;l+=3)a[i]=s,a[r]=n[l+2],o[i]=n[l+i],o[r]=n[l+r],t.rect(o[0],o[1],a[0],a[1])},Dx);function Dx(t){t=Cx.call(this,t)||this;return t.type="largeBar",t}function Ax(t,e,n,i){var r=t.getData(),o=r.getLayout("valueAxisHorizontal")?1:0,a=r.getLayout("largeDataIndices"),s=r.getLayout("size"),l=t.getModel("backgroundStyle"),u=r.getLayout("largeBackgroundPoints"),l=(u&&((u=new kx({shape:{points:u},incremental:!!i,silent:!0,z2:0})).baseDimIdx=o,u.largeDataIndices=a,u.barWidth=s,u.useStyle(l.getItemStyle()),e.add(u),n)&&n.push(u),new kx({shape:{points:r.getLayout("largePoints")},incremental:!!i,ignoreCoarsePointer:!0,z2:1}));l.baseDimIdx=o,l.largeDataIndices=a,l.barWidth=s,e.add(l),l.useStyle(r.getVisual("style")),D(l).seriesIndex=t.seriesIndex,t.get("silent")||(l.on("mousedown",Px),l.on("mousemove",Px)),n&&n.push(l)}var Px=$g(function(t){t=function(t,e,n){for(var i=t.baseDimIdx,r=1-i,o=t.shape.points,a=t.largeDataIndices,s=[],l=[],u=t.barWidth,h=0,c=o.length/3;h<c;h++){var p=3*h;if(l[i]=u,l[r]=o[2+p],s[i]=o[p+i],s[r]=o[p+r],l[r]<0&&(s[r]+=l[r],l[r]=-l[r]),s[0]<=e&&e<=s[0]+l[0]&&s[1]<=n&&n<=s[1]+l[1])return a[h]}return-1}(this,t.offsetX,t.offsetY);D(this).dataIndex=0<=t?t:null},30,!1);function Lx(t,e,n){var i,r;return sx(n,"cartesian2d")?(i=e,r=n.getArea(),{x:(t?i:r).x,y:(t?r:i).y,width:(t?i:r).width,height:(t?r:i).height}):{cx:(r=n.getArea()).cx,cy:r.cy,r0:(t?r:e).r0,r:(t?r:e).r,startAngle:t?e.startAngle:0,endAngle:t?e.endAngle:2*Math.PI}}k_(function(t){t.registerChartView(dx),t.registerSeriesModel(tx),t.registerLayout(t.PRIORITY.VISUAL.LAYOUT,pt(Xv,"bar")),t.registerLayout(t.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,Yv("bar")),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,{seriesType:"bar",reset:function(t,e,n){var i,r=t.getData(),o=t.get("sampling"),a=t.coordinateSystem,s=r.count();10<s&&"cartesian2d"===a.type&&o&&(i=a.getBaseAxis(),a=a.getOtherAxis(i),i=i.getExtent(),n=n.getDevicePixelRatio(),i=Math.abs(i[1]-i[0])*(n||1),n=Math.round(s/i),isFinite(n))&&1<n&&("lttb"===o&&t.setData(r.lttbDownSample(r.mapDimension(a.dim),1/n)),s=void 0,V(o)?s=j1[o]:k(o)&&(s=o),s)&&t.setData(r.downSample(r.mapDimension(a.dim),1/n,s,K1))}}),t.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},function(e,t){var n=e.componentType||"series";t.eachComponent({mainType:n,query:e},function(t){e.sortInfo&&t.axis.setCategorySortInfo(e.sortInfo)})})});u(Ex,Ox=g),Ex.type="grid",Ex.dependencies=["xAxis","yAxis"],Ex.layoutMode="box",Ex.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"};var Ox,Rx=Ex;function Ex(){return null!==Ox&&Ox.apply(this,arguments)||this}u(zx,Bx=g),zx.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",Bo).models[0]},zx.type="cartesian2dAxis";var Bx,Nx=zx;function zx(){return null!==Bx&&Bx.apply(this,arguments)||this}at(Nx,M_);var Ly={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},Ny=d({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Ly),Qu=d({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},Ly),Fx={category:Ny,value:Qu,time:d({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},Qu),log:N({logBase:10},Qu)},Vx={value:1,category:1,time:1,log:1};function Hx(o,a,s,l){R(Vx,function(t,r){var e,n=d(d({},Fx[r],!0),l,!0),n=(u(i,e=s),i.prototype.mergeDefaultAndTheme=function(t,e){var n=Ip(this),i=n?Dp(t):{};d(t,e.getTheme().get(r+"Axis")),d(t,this.getDefaultOption()),t.type=Wx(t),n&&kp(t,i,n)},i.prototype.optionUpdated=function(){"category"===this.option.type&&(this.__ordinalMeta=_v.createByAxisModel(this))},i.prototype.getCategories=function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},i.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},i.type=a+"Axis."+r,i.defaultOption=n,i);function i(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=a+"Axis."+r,t}o.registerComponentModel(n)}),o.registerSubTypeDefaulter(a+"Axis",Wx)}function Wx(t){return t.type||(t.data?"category":"value")}function Gx(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}Gx.prototype.getAxis=function(t){return this._axes[t]},Gx.prototype.getAxes=function(){return z(this._dimList,function(t){return this._axes[t]},this)},Gx.prototype.getAxesByScale=function(e){return e=e.toLowerCase(),ut(this.getAxes(),function(t){return t.scale.type===e})},Gx.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)};var Ux=["x","y"];function Xx(t){return"interval"===t.type||"time"===t.type}u(Zx,Yx=Gx),Zx.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t,e,n,i,r=this.getAxis("x").scale,o=this.getAxis("y").scale;Xx(r)&&Xx(o)&&(r=r.getExtent(),o=o.getExtent(),i=this.dataToPoint([r[0],o[0]]),e=this.dataToPoint([r[1],o[1]]),t=r[1]-r[0],n=o[1]-o[0],t)&&n&&(t=(e[0]-i[0])/t,e=(e[1]-i[1])/n,n=i[0]-r[0]*t,r=i[1]-o[0]*e,i=this._transform=[t,0,0,e,n,r],this._invTransform=Ve([],i))},Zx.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},Zx.prototype.containPoint=function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},Zx.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},Zx.prototype.containZone=function(t,e){var t=this.dataToPoint(t),e=this.dataToPoint(e),n=this.getArea(),e=new X(t[0],t[1],e[0]-t[0],e[1]-t[1]);return n.intersect(e)},Zx.prototype.dataToPoint=function(t,e,n){n=n||[];var i,r=t[0],o=t[1];return this._transform&&null!=r&&isFinite(r)&&null!=o&&isFinite(o)?ne(n,t,this._transform):(t=this.getAxis("x"),i=this.getAxis("y"),n[0]=t.toGlobalCoord(t.dataToCoord(r,e)),n[1]=i.toGlobalCoord(i.dataToCoord(o,e)),n)},Zx.prototype.clampData=function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),n=n.parse(t[0]),i=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),n),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),i),Math.max(o[0],o[1])),e},Zx.prototype.pointToData=function(t,e){var n,i,r=[];return this._invTransform?ne(r,t,this._invTransform):(n=this.getAxis("x"),i=this.getAxis("y"),r[0]=n.coordToData(n.toLocalCoord(t[0]),e),r[1]=i.coordToData(i.toLocalCoord(t[1]),e),r)},Zx.prototype.getOtherAxis=function(t){return this.getAxis("x"===t.dim?"y":"x")},Zx.prototype.getArea=function(t){t=t||0;var e=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),i=Math.min(e[0],e[1])-t,r=Math.min(n[0],n[1])-t,e=Math.max(e[0],e[1])-i+t,n=Math.max(n[0],n[1])-r+t;return new X(i,r,e,n)};var Yx,qx=Zx;function Zx(){var t=null!==Yx&&Yx.apply(this,arguments)||this;return t.type="cartesian2d",t.dimensions=Ux,t}u($x,jx=Tc),$x.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},$x.prototype.getGlobalExtent=function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},$x.prototype.pointToData=function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},$x.prototype.setCategorySortInfo=function(t){if("category"!==this.type)return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)};var jx,Kx=$x;function $x(t,e,n,i,r){t=jx.call(this,t,e,n)||this;return t.index=0,t.type=i||"value",t.position=r||"bottom",t}function Qx(t,e,n){n=n||{};var t=t.coordinateSystem,i=e.axis,r={},o=i.getAxesOnZeroOf()[0],a=i.position,s=o?"onZero":a,i=i.dim,t=t.getRect(),t=[t.x,t.x+t.width,t.y,t.y+t.height],l={left:0,right:1,top:0,bottom:1,onZero:2},u=e.get("offset")||0,u="x"===i?[t[2]-u,t[3]+u]:[t[0]-u,t[1]+u],h=(o&&(h=o.toGlobalCoord(o.dataToCoord(0)),u[l.onZero]=Math.max(Math.min(h,u[1]),u[0])),r.position=["y"===i?u[l[s]]:t[0],"x"===i?u[l[s]]:t[3]],r.rotation=Math.PI/2*("x"===i?0:1),r.labelDirection=r.tickDirection=r.nameDirection={top:-1,bottom:1,left:-1,right:1}[a],r.labelOffset=o?u[l[a]]-u[l.onZero]:0,e.get(["axisTick","inside"])&&(r.tickDirection=-r.tickDirection),bt(n.labelInside,e.get(["axisLabel","inside"]))&&(r.labelDirection=-r.labelDirection),e.get(["axisLabel","rotate"]));return r.labelRotate="top"===s?-h:h,r.z2=1,r}function Jx(t){return"cartesian2d"===t.get("coordinateSystem")}function tw(i){var r={xAxisModel:null,yAxisModel:null};return R(r,function(t,e){var n=e.replace(/Model$/,""),n=i.getReferringComponents(n,Bo).models[0];r[e]=n}),r}var ew=Math.log;iw.prototype.getRect=function(){return this._rect},iw.prototype.update=function(t,e){var n=this._axesMap;function i(t){var d,e=ht(t),n=e.length;if(n){for(var i=[],r=n-1;0<=r;r--){var o=t[+e[r]],a=o.model,s=o.scale;bv(s)&&a.get("alignTicks")&&null==a.get("interval")?i.push(o):(m_(s,a),bv(s)&&(d=o))}i.length&&(d||m_((d=i.pop()).scale,d.model),R(i,function(t){var e=t.scale,t=t.model,n=d.scale,i=Ev.prototype,r=i.getTicks.call(n),o=i.getTicks.call(n,!0),a=r.length-1,n=i.getInterval.call(n),s=(t=y_(e,t)).extent,l=t.fixMin,t=t.fixMax,u=("log"===e.type&&(u=ew(e.base),s=[ew(s[0])/u,ew(s[1])/u]),e.setExtent(s[0],s[1]),e.calcNiceExtent({splitNumber:a,fixMin:l,fixMax:t}),i.getExtent.call(e)),h=(l&&(s[0]=u[0]),t&&(s[1]=u[1]),i.getInterval.call(e)),c=s[0],p=s[1];if(l&&t)h=(p-c)/a;else if(l)for(p=s[0]+h*a;p<s[1]&&isFinite(p)&&isFinite(s[1]);)h=Mv(h),p=s[0]+h*a;else if(t)for(c=s[1]-h*a;c>s[0]&&isFinite(c)&&isFinite(s[0]);)h=Mv(h),c=s[1]-h*a;else{u=(h=a<e.getTicks().length-1?Mv(h):h)*a;(c=no((p=Math.ceil(s[1]/h)*h)-u))<0&&0<=s[0]?(c=0,p=no(u)):0<p&&s[1]<=0&&(p=0,c=-no(u))}l=(r[0].value-o[0].value)/n,t=(r[a].value-o[a].value)/n,i.setExtent.call(e,c+h*l,p+h*t),i.setInterval.call(e,h),(l||t)&&i.setNiceExtent.call(e,c+h,p-h)}))}}this._updateScale(t,this.model),i(n.x),i(n.y);var r={};R(n.x,function(t){ow(n,"y",t,r)}),R(n.y,function(t){ow(n,"x",t,r)}),this.resize(this.model,e)},iw.prototype.resize=function(t,e,n){var i=t.getBoxLayoutParams(),n=!n&&t.get("containLabel"),a=Cp(i,{width:e.getWidth(),height:e.getHeight()}),r=(this._rect=a,this._axesList);function o(){R(r,function(t){var e,n,i=t.isHorizontal(),r=i?[0,a.width]:[0,a.height],o=t.inverse?1:0;t.setExtent(r[o],r[1-o]),r=t,e=i?a.x:a.y,o=r.getExtent(),n=o[0]+o[1],r.toGlobalCoord="x"===r.dim?function(t){return t+e}:function(t){return n-t+e},r.toLocalCoord="x"===r.dim?function(t){return t-e}:function(t){return n-t+e}})}o(),n&&(R(r,function(t){var e,n,i;t.model.get(["axisLabel","inside"])||(e=w_(t))&&(n=t.isHorizontal()?"height":"width",i=t.model.get(["axisLabel","margin"]),a[n]-=e[n]+i,"top"===t.position?a.y+=e.height+i:"left"===t.position&&(a.x+=e.width+i))}),o()),R(this._coordsList,function(t){t.calcAffineTransform()})},iw.prototype.getAxis=function(t,e){t=this._axesMap[t];if(null!=t)return t[e||0]},iw.prototype.getAxes=function(){return this._axesList.slice()},iw.prototype.getCartesian=function(t,e){if(null!=t&&null!=e)return this._coordsMap["x"+t+"y"+e];L(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,i=this._coordsList;n<i.length;n++)if(i[n].getAxis("x").index===t||i[n].getAxis("y").index===e)return i[n]},iw.prototype.getCartesians=function(){return this._coordsList.slice()},iw.prototype.convertToPixel=function(t,e,n){e=this._findConvertTarget(e);return e.cartesian?e.cartesian.dataToPoint(n):e.axis?e.axis.toGlobalCoord(e.axis.dataToCoord(n)):null},iw.prototype.convertFromPixel=function(t,e,n){e=this._findConvertTarget(e);return e.cartesian?e.cartesian.pointToData(n):e.axis?e.axis.coordToData(e.axis.toLocalCoord(n)):null},iw.prototype._findConvertTarget=function(t){var e,n,i=t.seriesModel,r=t.xAxisModel||i&&i.getReferringComponents("xAxis",Bo).models[0],o=t.yAxisModel||i&&i.getReferringComponents("yAxis",Bo).models[0],t=t.gridModel,a=this._coordsList;return i?I(a,e=i.coordinateSystem)<0&&(e=null):r&&o?e=this.getCartesian(r.componentIndex,o.componentIndex):r?n=this.getAxis("x",r.componentIndex):o?n=this.getAxis("y",o.componentIndex):t&&t.coordinateSystem===this&&(e=this._coordsList[0]),{cartesian:e,axis:n}},iw.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},iw.prototype._initCartesian=function(o,t,e){var a=this,s=this,l={left:!1,right:!1,top:!1,bottom:!1},u={x:{},y:{}},h={x:0,y:0};function n(r){return function(t,e){var n,i;rw(t,o)&&(n=t.get("position"),"x"===r?"top"!==n&&"bottom"!==n&&(n=l.bottom?"top":"bottom"):"left"!==n&&"right"!==n&&(n=l.left?"right":"left"),l[n]=!0,i="category"===(n=new Kx(r,v_(t),[0,0],t.get("type"),n)).type,n.onBand=i&&t.get("boundaryGap"),n.inverse=t.get("inverse"),(t.axis=n).model=t,n.grid=s,n.index=e,s._axesList.push(n),u[r][e]=n,h[r]++)}}t.eachComponent("xAxis",n("x"),this),t.eachComponent("yAxis",n("y"),this),h.x&&h.y?R((this._axesMap=u).x,function(i,r){R(u.y,function(t,e){var e="x"+r+"y"+e,n=new qx(e);n.master=a,n.model=o,a._coordsMap[e]=n,a._coordsList.push(n),n.addAxis(i),n.addAxis(t)})}):(this._axesMap={},this._axesList=[])},iw.prototype._updateScale=function(t,i){function r(e,n){var i,t,r;R((i=e,t=n.dim,r={},R(i.mapDimensionsAll(t),function(t){r[fv(i,t)]=!0}),ht(r)),function(t){n.scale.unionExtentFromData(e,t)})}R(this._axesList,function(t){var e;t.scale.setExtent(1/0,-1/0),"category"===t.type&&(e=t.model.get("categorySortInfo"),t.scale.setSortInfo(e))}),t.eachSeries(function(t){var e,n;Jx(t)&&(n=(e=tw(t)).xAxisModel,e=e.yAxisModel,rw(n,i))&&rw(e,i)&&(n=this.getCartesian(n.componentIndex,e.componentIndex),e=t.getData(),t=n.getAxis("x"),n=n.getAxis("y"),r(e,t),r(e,n))},this)},iw.prototype.getTooltipAxes=function(n){var i=[],r=[];return R(this.getCartesians(),function(t){var e=null!=n&&"auto"!==n?t.getAxis(n):t.getBaseAxis(),t=t.getOtherAxis(e);I(i,e)<0&&i.push(e),I(r,t)<0&&r.push(t)}),{baseAxes:i,otherAxes:r}},iw.create=function(i,r){var o=[];return i.eachComponent("grid",function(t,e){var n=new iw(t,i,r);n.name="grid_"+e,n.resize(t,r,!0),t.coordinateSystem=n,o.push(n)}),i.eachSeries(function(t){var e,n,i;Jx(t)&&(e=(n=tw(t)).xAxisModel,n=n.yAxisModel,i=e.getCoordSysModel().coordinateSystem,t.coordinateSystem=i.getCartesian(e.componentIndex,n.componentIndex))}),o},iw.dimensions=Ux;var nw=iw;function iw(t,e,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=Ux,this._initCartesian(t,e,n),this.model=t}function rw(t,e){return t.getCoordSysModel()===e}function ow(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],t=n.model,e=t.get(["axisLine","onZero"]),n=t.get(["axisLine","onZeroAxisIndex"]);if(e){if(null!=n)aw(o[n])&&(r=o[n]);else for(var a in o)if(o.hasOwnProperty(a)&&aw(o[a])&&!i[s(o[a])]){r=o[a];break}r&&(i[s(r)]=!0)}function s(t){return t.dim+"_"+t.index}}function aw(t){return t&&"category"!==t.type&&"time"!==t.type&&(e=(t=(t=t).scale.getExtent())[0],t=t[1],!(0<e&&0<t||e<0&&t<0));var e}var sw=Math.PI,lw=(uw.prototype.hasBuilder=function(t){return!!hw[t]},uw.prototype.add=function(t){hw[t](this.opt,this.axisModel,this.group,this._transformGroup)},uw.prototype.getGroup=function(){return this.group},uw.innerTextLayout=function(t,e,n){var i,e=ao(e-t),t=so(e)?(i=0<n?"top":"bottom","center"):so(e-sw)?(i=0<n?"bottom":"top","center"):(i="middle",0<e&&e<sw?0<n?"right":"left":0<n?"left":"right");return{rotation:e,textAlign:t,textVerticalAlign:i}},uw.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},uw.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},uw);function uw(t,e){this.group=new Wr,this.opt=e,this.axisModel=t,N(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});t=new Wr({x:e.position[0],y:e.position[1],rotation:e.rotation});t.updateTransform(),this._transformGroup=t}var hw={axisLine:function(i,t,r,e){var o,a,s,l,u,h,c,n=t.get(["axisLine","show"]);(n="auto"===n&&i.handleAutoShown?i.handleAutoShown("axisLine"):n)&&(n=t.axis.getExtent(),e=e.transform,o=[n[0],0],a=[n[1],0],s=a[0]<o[0],e&&(ne(o,o,e),ne(a,a,e)),l=O({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),Uh((n=new Yu({shape:{x1:o[0],y1:o[1],x2:a[0],y2:a[1]},style:l,strokeContainThreshold:i.strokeContainThreshold||5,silent:!0,z2:1})).shape,n.style.lineWidth),n.anid="line",r.add(n),null!=(u=t.get(["axisLine","symbol"])))&&(e=t.get(["axisLine","symbolSize"]),V(u)&&(u=[u,u]),(V(e)||ft(e))&&(e=[e,e]),n=function(t,e){if(null!=t)return[eo((t=F(t)?t:[t,t])[0],e[0])||0,eo(E(t[1],t[0]),e[1])||0]}(t.get(["axisLine","symbolOffset"])||0,e),h=e[0],c=e[1],R([{rotate:i.rotation+Math.PI/2,offset:n[0],r:0},{rotate:i.rotation-Math.PI/2,offset:n[1],r:Math.sqrt((o[0]-a[0])*(o[0]-a[0])+(o[1]-a[1])*(o[1]-a[1]))}],function(t,e){var n;"none"!==u[e]&&null!=u[e]&&(e=Wy(u[e],-h/2,-c/2,h,c,l.stroke,!0),n=t.r+t.offset,e.attr({rotation:t.rotate,x:(t=s?a:o)[0]+n*Math.cos(i.rotation),y:t[1]-n*Math.sin(i.rotation),silent:!0,z2:11}),r.add(e))}))},axisTickLabel:function(t,e,n,i){var r,o,a,s,l,u=function(t,e,n,i){var r=n.axis,o=n.getModel("axisTick"),a=o.get("show");"auto"===a&&i.handleAutoShown&&(a=i.handleAutoShown("axisTick"));if(a&&!r.scale.isBlank()){for(var a=o.getModel("lineStyle"),i=i.tickDirection*o.get("length"),s=fw(r.getTicksCoords(),e.transform,i,N(a.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),l=0;l<s.length;l++)t.add(s[l]);return s}}(n,i,e,t),h=function(f,g,y,m){var v,_,x,w,b,S,M,T,C=y.axis,t=bt(m.axisLabelShow,y.get(["axisLabel","show"]));if(t&&!C.scale.isBlank())return v=y.getModel("axisLabel"),_=v.get("margin"),x=C.getViewLabels(),t=(bt(m.labelRotate,v.get("rotate"))||0)*sw/180,w=lw.innerTextLayout(m.rotation,t,m.labelDirection),b=y.getCategories&&y.getCategories(!0),S=[],M=lw.isLabelSilent(y),T=y.get("triggerEvent"),R(x,function(t,e){var n="ordinal"===C.scale.type?C.scale.getRawOrdinalNumber(t.tickValue):t.tickValue,i=t.formattedLabel,r=t.rawLabel,o=v,a=(o=b&&b[n]&&L(a=b[n])&&a.textStyle?new kc(a.textStyle,v,y.ecModel):o).getTextColor()||y.get(["axisLine","lineStyle","color"]),s=C.dataToCoord(n),l=o.getShallow("align",!0)||w.textAlign,u=E(o.getShallow("alignMinLabel",!0),l),h=E(o.getShallow("alignMaxLabel",!0),l),c=o.getShallow("verticalAlign",!0)||o.getShallow("baseline",!0)||w.textVerticalAlign,p=E(o.getShallow("verticalAlignMinLabel",!0),c),d=E(o.getShallow("verticalAlignMaxLabel",!0),c),s=new Rs({x:s,y:m.labelOffset+m.labelDirection*_,rotation:w.rotation,silent:M,z2:10+(t.level||0),style:sc(o,{text:i,align:0===e?u:e===x.length-1?h:l,verticalAlign:0===e?p:e===x.length-1?d:c,fill:k(a)?a("category"===C.type?r:"value"===C.type?n+"":n,e):a})});s.anid="label_"+n,T&&((t=lw.makeAxisEventDataBase(y)).targetType="axisLabel",t.value=r,t.tickIndex=e,"category"===C.type&&(t.dataIndex=n),D(s).eventData=t),g.add(s),s.updateTransform(),S.push(s),f.add(s),s.decomposeTransform()}),S}(n,i,e,t),c=(o=h,u=u,S_((r=e).axis)||(d=r.get(["axisLabel","showMinLabel"]),r=r.get(["axisLabel","showMaxLabel"]),u=u||[],y=(o=o||[])[0],f=o[1],a=o[o.length-1],o=o[o.length-2],s=u[0],g=u[1],l=u[u.length-1],u=u[u.length-2],!1===d?(cw(y),cw(s)):pw(y,f)&&(d?(cw(f),cw(g)):(cw(y),cw(s))),!1===r?(cw(a),cw(l)):pw(o,a)&&(r?(cw(o),cw(u)):(cw(a),cw(l)))),n),p=i,d=e,f=t.tickDirection,g=d.axis,y=d.getModel("minorTick");if(y.get("show")&&!g.scale.isBlank()){var m=g.getMinorTicksCoords();if(m.length)for(var g=y.getModel("lineStyle"),v=f*y.get("length"),_=N(g.getLineStyle(),N(d.getModel("axisTick").getLineStyle(),{stroke:d.get(["axisLine","lineStyle","color"])})),x=0;x<m.length;x++)for(var w=fw(m[x],p.transform,v,_,"minorticks_"+x),b=0;b<w.length;b++)c.add(w[b])}e.get(["axisLabel","hideOverlap"])&&M1(b1(z(h,function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}})))},axisName:function(t,e,n,i){var r,o,a,s,l,u,h,c,p=bt(t.axisName,e.get("name"));p&&(c=e.get("nameLocation"),l=t.nameDirection,r=e.getModel("nameTextStyle"),u=e.get("nameGap")||0,o=(h=e.axis.getExtent())[0]>h[1]?-1:1,o=["start"===c?h[0]-o*u:"end"===c?h[1]+o*u:(h[0]+h[1])/2,dw(c)?t.labelOffset+l*u:0],null!=(u=e.get("nameRotate"))&&(u=u*sw/180),dw(c)?a=lw.innerTextLayout(t.rotation,null!=u?u:t.rotation,l):(a=function(t,e,n,i){var r,n=ao(n-t),t=i[0]>i[1],i="start"===e&&!t||"start"!==e&&t;e=so(n-sw/2)?(r=i?"bottom":"top","center"):so(n-1.5*sw)?(r=i?"top":"bottom","center"):(r="middle",n<1.5*sw&&sw/2<n?i?"left":"right":i?"right":"left");return{rotation:n,textAlign:e,textVerticalAlign:r}}(t.rotation,c,u||0,h),null!=(s=t.axisNameAvailableWidth)&&(s=Math.abs(s/Math.sin(a.rotation)),isFinite(s)||(s=null))),l=r.getFont(),u=(c=e.get("nameTruncate",!0)||{}).ellipsis,h=bt(t.nameTruncateMaxWidth,c.maxWidth,s),tc({el:t=new Rs({x:o[0],y:o[1],rotation:a.rotation,silent:lw.isLabelSilent(e),style:sc(r,{text:p,font:l,overflow:"truncate",width:h,ellipsis:u,fill:r.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:r.get("align")||a.textAlign,verticalAlign:r.get("verticalAlign")||a.textVerticalAlign}),z2:1}),componentModel:e,itemName:p}),t.__fullText=p,t.anid="name",e.get("triggerEvent")&&((c=lw.makeAxisEventDataBase(e)).targetType="axisName",c.name=p,D(t).eventData=c),i.add(t),t.updateTransform(),n.add(t),t.decomposeTransform())}};function cw(t){t&&(t.ignore=!0)}function pw(t,e){var n,i=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(i&&r)return ze(n=Re([]),n,-t.rotation),i.applyTransform(Be([],n,t.getLocalTransform())),r.applyTransform(Be([],n,e.getLocalTransform())),i.intersect(r)}function dw(t){return"middle"===t||"center"===t}function fw(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord,u=(a[0]=u,s[a[1]=0]=u,s[1]=n,e&&(ne(a,a,e),ne(s,s,e)),new Yu({shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0}));Uh(u.shape,u.style.lineWidth),u.anid=r+"_"+t[l].tickValue,o.push(u)}return o}function gw(t,e){var h,c,r,p,d,f,o,n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return h=n,e=e,r=(c=t).getComponent("tooltip"),p=c.getComponent("axisPointer"),d=p.get("link",!0)||[],f=[],R(e.getCoordinateSystems(),function(s){var l,u,t,e,n;function i(t,e,n){var i,r,o=n.model.getModel("axisPointer",p),a=o.get("show");a&&("auto"!==a||t||vw(o))&&(null==e&&(e=o.get("triggerTooltip")),a=(o=t?function(t,e,n,i,r,o){var a=e.getModel("axisPointer"),s={};R(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){s[t]=y(a.get(t))}),s.snap="category"!==t.type&&!!o,"cross"===a.get("type")&&(s.type="line");e=s.label||(s.label={});null==e.show&&(e.show=!1),"cross"===r&&(r=a.get(["label","show"]),e.show=null==r||r,o||(r=s.lineStyle=a.get("crossStyle"))&&N(e,r.textStyle));return t.model.getModel("axisPointer",new kc(s,n,i))}(n,u,p,c,t,e):o).get("snap"),t=o.get("triggerEmphasis"),i=_w(n.model),r=e||a||"category"===n.type,e=h.axesInfo[i]={key:i,axis:n,coordSys:s,axisPointerModel:o,triggerTooltip:e,triggerEmphasis:t,involveSeries:r,snap:a,useHandle:vw(o),seriesModels:[],linkGroup:null},l[i]=e,h.seriesInvolved=h.seriesInvolved||r,null!=(t=function(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(yw(o[i+"AxisId"],n.id)||yw(o[i+"AxisIndex"],n.componentIndex)||yw(o[i+"AxisName"],n.name))return r}}(d,n)))&&((a=f[t]||(f[t]={axesInfo:{}})).axesInfo[i]=e,a.mapper=d[t].mapper,e.linkGroup=a)}s.axisPointerEnabled&&(t=_w(s.model),l=h.coordSysAxesInfo[t]={},u=(h.coordSysMap[t]=s).model.getModel("tooltip",r),R(s.getAxes(),pt(i,!1,null)),s.getTooltipAxes)&&r&&u.get("show")&&(t="axis"===u.get("trigger"),e="cross"===u.get(["axisPointer","type"]),n=s.getTooltipAxes(u.get(["axisPointer","axis"])),(t||e)&&R(n.baseAxes,pt(i,!e||"cross",t)),e)&&R(n.otherAxes,pt(i,"cross",!1))}),n.seriesInvolved&&(o=n,t.eachSeries(function(n){var i=n.coordinateSystem,t=n.get(["tooltip","trigger"],!0),e=n.get(["tooltip","show"],!0);i&&"none"!==t&&!1!==t&&"item"!==t&&!1!==e&&!1!==n.get(["axisPointer","show"],!0)&&R(o.coordSysAxesInfo[_w(i.model)],function(t){var e=t.axis;i.getAxis(e.dim)===e&&(t.seriesModels.push(n),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=n.getData().count())})})),n}function yw(t,e){return"all"===t||F(t)&&0<=I(t,e)||t===e}function mw(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[_w(t)]}function vw(t){return!!t.get(["handle","show"])}function _w(t){return t.type+"||"+t.id}var xw,ww={},bw=(u(Sw,xw=Bg),Sw.prototype.render=function(t,e,n,i){var r,o,a,s,l,u;this.axisPointerClass&&(r=mw(r=t))&&(l=r.axisPointerModel,o=r.axis.scale,a=l.option,u=l.get("status"),null!=(s=l.get("value"))&&(s=o.parse(s)),l=vw(l),null==u&&(a.status=l?"show":"hide"),(u=o.getExtent().slice())[0]>u[1]&&u.reverse(),(s=null==s||s>u[1]?u[1]:s)<u[0]&&(s=u[0]),a.value=s,l)&&(a.status=r.axis.scale.isBlank()?"hide":"show"),xw.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(t,n,!0)},Sw.prototype.updateAxisPointer=function(t,e,n,i){this._doUpdateAxisPointerClass(t,n,!1)},Sw.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},Sw.prototype.dispose=function(t,e){this._disposeAxisPointer(e),xw.prototype.dispose.apply(this,arguments)},Sw.prototype._doUpdateAxisPointerClass=function(t,e,n){var i,r=Sw.getAxisPointerClass(this.axisPointerClass);r&&((i=(i=mw(i=t))&&i.axisPointerModel)?(this._axisPointer||(this._axisPointer=new r)).render(t,i,e,n):this._disposeAxisPointer(e))},Sw.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},Sw.registerAxisPointerClass=function(t,e){ww[t]=e},Sw.getAxisPointerClass=function(t){return t&&ww[t]},Sw.type="axis",Sw);function Sw(){var t=null!==xw&&xw.apply(this,arguments)||this;return t.type=Sw.type,t}var Mw=Lo();var Tw,Cw=["axisLine","axisTickLabel","axisName"],Iw=["splitArea","splitLine","minorSplitLine"],su=(u(kw,Tw=bw),kw.prototype.render=function(i,t,e,n){this.group.removeAll();var r,o,a=this._axisGroup;this._axisGroup=new Wr,this.group.add(this._axisGroup),i.get("show")&&(o=Qx(r=i.getCoordSysModel(),i),o=new lw(i,O({handleAutoShown:function(t){for(var e=r.coordinateSystem.getCartesians(),n=0;n<e.length;n++)if(bv(e[n].getOtherAxis(i.axis).scale))return!0;return!1}},o)),R(Cw,o.add,o),this._axisGroup.add(o.getGroup()),R(Iw,function(t){i.get([t,"show"])&&Aw[t](this,this._axisGroup,i,r)},this),n&&"changeAxisOrder"===n.type&&n.isInitSort||jh(a,this._axisGroup,i),Tw.prototype.render.call(this,i,t,e,n))},kw.prototype.remove=function(){Mw(this).splitAreaColors=null},kw.type="cartesianAxis",kw);function kw(){var t=null!==Tw&&Tw.apply(this,arguments)||this;return t.type=kw.type,t.axisPointerClass="CartesianAxisPointer",t}var Dw,Aw={splitLine:function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank())for(var n=n.getModel("splitLine"),o=n.getModel("lineStyle"),a=F(a=o.get("color"))?a:[a],s=i.coordinateSystem.getRect(),l=r.isHorizontal(),u=0,h=r.getTicksCoords({tickModel:n}),c=[],p=[],d=o.getLineStyle(),f=0;f<h.length;f++){var g=r.toGlobalCoord(h[f].coord),g=(l?(c[0]=g,c[1]=s.y,p[0]=g,p[1]=s.y+s.height):(c[0]=s.x,c[1]=g,p[0]=s.x+s.width,p[1]=g),u++%a.length),y=h[f].tickValue,y=new Yu({anid:null!=y?"line_"+h[f].tickValue:null,autoBatch:!0,shape:{x1:c[0],y1:c[1],x2:p[0],y2:p[1]},style:N({stroke:a[g]},d),silent:!0});Uh(y.shape,d.lineWidth),e.add(y)}},minorSplitLine:function(t,e,n,i){var r=n.axis,n=n.getModel("minorSplitLine").getModel("lineStyle"),o=i.coordinateSystem.getRect(),a=r.isHorizontal(),s=r.getMinorTicksCoords();if(s.length)for(var l=[],u=[],h=n.getLineStyle(),c=0;c<s.length;c++)for(var p=0;p<s[c].length;p++){var d=r.toGlobalCoord(s[c][p].coord),d=(a?(l[0]=d,l[1]=o.y,u[0]=d,u[1]=o.y+o.height):(l[0]=o.x,l[1]=d,u[0]=o.x+o.width,u[1]=d),new Yu({anid:"minor_line_"+s[c][p].tickValue,autoBatch:!0,shape:{x1:l[0],y1:l[1],x2:u[0],y2:u[1]},style:h,silent:!0}));Uh(d.shape,h.lineWidth),e.add(d)}},splitArea:function(t,e,n,i){var r=e,e=i,o=(i=n).axis;if(!o.scale.isBlank()){var i=i.getModel("splitArea"),n=i.getModel("areaStyle"),a=n.get("color"),s=e.coordinateSystem.getRect(),l=o.getTicksCoords({tickModel:i,clamp:!0});if(l.length){var u=a.length,h=Mw(t).splitAreaColors,c=B(),p=0;if(h)for(var d=0;d<l.length;d++){var f=h.get(l[d].tickValue);if(null!=f){p=(f+(u-1)*d)%u;break}}for(var g=o.toGlobalCoord(l[0].coord),y=n.getAreaStyle(),a=F(a)?a:[a],d=1;d<l.length;d++){var m=o.toGlobalCoord(l[d].coord),v=void 0,_=void 0,x=void 0,w=void 0,g=o.isHorizontal()?(v=g,_=s.y,w=s.height,v+(x=m-v)):(v=s.x,_=g,x=s.width,_+(w=m-_)),m=l[d-1].tickValue;null!=m&&c.set(m,p),r.add(new Ds({anid:null!=m?"area_"+m:null,shape:{x:v,y:_,width:x,height:w},style:N({fill:a[p]},y),autoBatch:!0,silent:!0})),p=(p+1)%u}Mw(t).splitAreaColors=c}}}},Pw=(u(Lw,Dw=su),Lw.type="xAxis",Lw);function Lw(){var t=null!==Dw&&Dw.apply(this,arguments)||this;return t.type=Lw.type,t}u(Ew,Ow=su),Ew.type="yAxis";var Ow,Rw=Ew;function Ew(){var t=null!==Ow&&Ow.apply(this,arguments)||this;return t.type=Pw.type,t}u(zw,Bw=Bg),zw.prototype.render=function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new Ds({shape:t.coordinateSystem.getRect(),style:N({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},zw.type="grid";var Bw,Nw=zw;function zw(){var t=null!==Bw&&Bw.apply(this,arguments)||this;return t.type="grid",t}var Fw={offset:0};k_(function(t){t.registerComponentView(Nw),t.registerComponentModel(Rx),t.registerCoordinateSystem("cartesian2d",nw),Hx(t,"x",Nx,Fw),Hx(t,"y",Nx,Fw),t.registerComponentView(Pw),t.registerComponentView(Rw),t.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})});u(Ww,Vw=g),Ww.type="title",Ww.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}};var Vw,Hw=Ww;function Ww(){var t=null!==Vw&&Vw.apply(this,arguments)||this;return t.type=Ww.type,t.layoutMode={type:"box",ignoreSize:!0},t}u(Xw,Gw=Bg),Xw.prototype.render=function(t,e,n){var i,r,o,a,s,l,u,h,c;this.group.removeAll(),t.get("show")&&(i=this.group,u=t.getModel("textStyle"),r=t.getModel("subtextStyle"),h=t.get("textAlign"),c=E(t.get("textBaseline"),t.get("textVerticalAlign")),s=(u=new Rs({style:sc(u,{text:t.get("text"),fill:u.getTextColor()},{disableBox:!0}),z2:10})).getBoundingRect(),l=t.get("subtext"),r=new Rs({style:sc(r,{text:l,fill:r.getTextColor(),y:s.height+t.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),o=t.get("link"),a=t.get("sublink"),s=t.get("triggerEvent",!0),u.silent=!o&&!s,r.silent=!a&&!s,o&&u.on("click",function(){wp(o,"_"+t.get("target"))}),a&&r.on("click",function(){wp(a,"_"+t.get("subtarget"))}),D(u).eventData=D(r).eventData=s?{componentType:"title",componentIndex:t.componentIndex}:null,i.add(u),l&&i.add(r),s=i.getBoundingRect(),(l=t.getBoxLayoutParams()).width=s.width,l.height=s.height,l=Cp(l,{width:n.getWidth(),height:n.getHeight()},t.get("padding")),h||("right"===(h="middle"===(h=t.get("left")||t.get("right"))?"center":h)?l.x+=l.width:"center"===h&&(l.x+=l.width/2)),c||("bottom"===(c="center"===(c=t.get("top")||t.get("bottom"))?"middle":c)?l.y+=l.height:"middle"===c&&(l.y+=l.height/2),c=c||"top"),i.x=l.x,i.y=l.y,i.markRedraw(),u.setStyle(n={align:h,verticalAlign:c}),r.setStyle(n),s=i.getBoundingRect(),u=l.margin,(h=t.getItemStyle(["color","opacity"])).fill=t.get("backgroundColor"),c=new Ds({shape:{x:s.x-u[3],y:s.y-u[0],width:s.width+u[1]+u[3],height:s.height+u[0]+u[2],r:t.get("borderRadius")},style:h,subPixelOptimize:!0,silent:!0}),i.add(c))},Xw.type="title";var Gw,Uw=Xw;function Xw(){var t=null!==Gw&&Gw.apply(this,arguments)||this;return t.type=Xw.type,t}k_(function(t){t.registerComponentModel(Hw),t.registerComponentView(Uw)});var Yw=Lo(),qw=y,Zw=ct;function jw(){this._dragging=!1,this.animationThreshold=15}function Kw(t,e,n,i){!function n(i,t){{var r;return L(i)&&L(t)?(r=!0,R(t,function(t,e){r=r&&n(i[e],t)}),!!r):i===t}}(Yw(n).lastProp,i)&&(Yw(n).lastProp=i,e?Th(n,i,t):(n.stopAnimation(),n.attr(i)))}function $w(t,e){t[e.get(["label","show"])?"show":"hide"]()}function Qw(t){return{x:t.x||0,y:t.y||0,rotation:t.rotation||0}}function Jw(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)})}function tb(t,e,n,i,r){var o=eb(n.get("value"),e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get(["label","precision"]),formatter:n.get(["label","formatter"])}),n=n.getModel("label"),a=fp(n.get("padding")||0),s=n.getFont(),l=Mr(o,s),u=r.position,h=l.width+a[1]+a[3],l=l.height+a[0]+a[2],c=r.align,c=("right"===c&&(u[0]-=h),"center"===c&&(u[0]-=h/2),r.verticalAlign),i=("bottom"===c&&(u[1]-=l),"middle"===c&&(u[1]-=l/2),r=u,c=h,h=l,i=(l=i).getWidth(),l=l.getHeight(),r[0]=Math.min(r[0]+c,i)-c,r[1]=Math.min(r[1]+h,l)-h,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0),n.get("backgroundColor"));i&&"auto"!==i||(i=e.get(["axisLine","lineStyle","color"])),t.label={x:u[0],y:u[1],style:sc(n,{text:o,font:s,fill:n.getTextColor(),padding:a,backgroundColor:i}),z2:10}}function eb(t,e,n,i,r){t=e.scale.parse(t);var o,a=e.scale.getLabel({value:t},{precision:r.precision}),r=r.formatter;return r&&(o={value:x_(e,{value:t}),axisDimension:e.dim,axisIndex:e.index,seriesData:[]},R(i,function(t){var e=n.getSeriesByIndex(t.seriesIndex),t=t.dataIndexInside,e=e&&e.getDataParams(t);e&&o.seriesData.push(e)}),V(r)?a=r.replace("{value}",a):k(r)&&(a=r(o))),a}function nb(t,e,n){var i=Oe();return ze(i,i,n.rotation),Ne(i,i,n.position),qh([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}jw.prototype.render=function(t,e,n,i){var r,o,a=e.get("value"),s=e.get("status");this._axisModel=t,this._axisPointerModel=e,this._api=n,!i&&this._lastValue===a&&this._lastStatus===s||(this._lastValue=a,this._lastStatus=s,i=this._group,r=this._handle,s&&"hide"!==s?(i&&i.show(),r&&r.show(),this.makeElOption(s={},a,t,e,n),(o=s.graphicKey)!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=o,o=this._moveAnimation=this.determineAnimation(t,e),i?(o=pt(Kw,e,o),this.updatePointerEl(i,s,o),this.updateLabelEl(i,s,o,e)):(i=this._group=new Wr,this.createPointerEl(i,s,t,e),this.createLabelEl(i,s,t,e),n.getZr().add(i)),Jw(i,e,!0),this._renderHandle(a)):(i&&i.hide(),r&&r.hide()))},jw.prototype.remove=function(t){this.clear(t)},jw.prototype.dispose=function(t){this.clear(t)},jw.prototype.determineAnimation=function(t,e){var n,i=e.get("animation"),r=t.axis,o="category"===r.type,e=e.get("snap");return!(!e&&!o)&&("auto"===i||null==i?(n=this.animationThreshold,o&&r.getBandWidth()>n||!!e&&(o=mw(t).seriesDataCount,e=r.getExtent(),Math.abs(e[0]-e[1])/o>n)):!0===i)},jw.prototype.makeElOption=function(t,e,n,i,r){},jw.prototype.createPointerEl=function(t,e,n,i){var r=e.pointer;r&&(r=Yw(t).pointerEl=new ic[r.type](qw(e.pointer)),t.add(r))},jw.prototype.createLabelEl=function(t,e,n,i){e.label&&(e=Yw(t).labelEl=new Rs(qw(e.label)),t.add(e),$w(e,i))},jw.prototype.updatePointerEl=function(t,e,n){t=Yw(t).pointerEl;t&&e.pointer&&(t.setStyle(e.pointer.style),n(t,{shape:e.pointer.shape}))},jw.prototype.updateLabelEl=function(t,e,n,i){t=Yw(t).labelEl;t&&(t.setStyle(e.label.style),n(t,{x:e.label.x,y:e.label.y}),$w(t,i))},jw.prototype._renderHandle=function(t){var e,n,i,r,o,a;!this._dragging&&this.updateHandleTransform&&(e=this._axisPointerModel,n=this._api.getZr(),i=this._handle,r=e.getModel("handle"),a=e.get("status"),r.get("show")&&a&&"hide"!==a?(this._handle||(o=!0,i=this._handle=Qh(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){ke(t.event)},onmousedown:Zw(this._onHandleDragMove,this,0,0),drift:Zw(this._onHandleDragMove,this),ondragend:Zw(this._onHandleDragEnd,this)}),n.add(i)),Jw(i,e,!1),i.setStyle(r.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"])),F(a=r.get("size"))||(a=[a,a]),i.scaleX=a[0]/2,i.scaleY=a[1]/2,Qg(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,o)):(i&&n.remove(i),this._handle=null))},jw.prototype._moveHandleToValue=function(t,e){Kw(this._axisPointerModel,!e&&this._moveAnimation,this._handle,Qw(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},jw.prototype._onHandleDragMove=function(t,e){var n=this._handle;n&&(this._dragging=!0,t=this.updateHandleTransform(Qw(n),[t,e],this._axisModel,this._axisPointerModel),this._payloadInfo=t,n.stopAnimation(),n.attr(Qw(t)),Yw(n).lastProp=null,this._doDispatchAxisPointer())},jw.prototype._doDispatchAxisPointer=function(){var t,e;this._handle&&(t=this._payloadInfo,e=this._axisModel,this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:e.axis.dim,axisIndex:e.componentIndex}]}))},jw.prototype._onHandleDragEnd=function(){var t;this._dragging=!1,this._handle&&(t=this._axisPointerModel.get("value"),this._moveHandleToValue(t),this._api.dispatchAction({type:"hideTip"}))},jw.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var t=t.getZr(),e=this._group,n=this._handle;t&&e&&(this._lastGraphicKey=null,e&&t.remove(e),n&&t.remove(n),this._group=null,this._handle=null,this._payloadInfo=null),Jg(this,"_doDispatchAxisPointer")},jw.prototype.doClear=function(){},jw.prototype.buildLabel=function(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}};u(ob,ib=jw),ob.prototype.makeElOption=function(t,e,n,i,r){var o,a,s=n.axis,l=s.grid,u=i.get("type"),h=ab(l,s).getOtherAxis(s).getGlobalExtent(),c=s.toGlobalCoord(s.dataToCoord(e,!0)),p=(u&&"none"!==u&&(o=(a=i).get("type"),a=a.getModel(o+"Style"),"line"===o?(p=a.getLineStyle()).fill=null:"shadow"===o&&((p=a.getAreaStyle()).stroke=null),o=p,(a=sb[u](s,c,h)).style=o,t.graphicKey=a.type,t.pointer=a),Qx(l.model,n));u=e,s=t,c=p,h=n,o=i,a=r,l=lw.innerTextLayout(c.rotation,0,c.labelDirection),c.labelMargin=o.get(["label","margin"]),tb(s,h,o,a,{position:nb(h.axis,u,c),align:l.textAlign,verticalAlign:l.textVerticalAlign})},ob.prototype.getHandleTransform=function(t,e,n){var i=Qx(e.axis.grid.model,e,{labelInside:!1}),n=(i.labelMargin=n.get(["handle","margin"]),nb(e.axis,t,i));return{x:n[0],y:n[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},ob.prototype.updateHandleTransform=function(t,e,n,i){var n=n.axis,r=n.grid,o=n.getGlobalExtent(!0),r=ab(r,n).getOtherAxis(n).getGlobalExtent(),n="x"===n.dim?0:1,a=[t.x,t.y],e=(a[n]+=e[n],a[n]=Math.min(o[1],a[n]),a[n]=Math.max(o[0],a[n]),(r[1]+r[0])/2),o=[e,e];o[n]=a[n];return{x:a[0],y:a[1],rotation:t.rotation,cursorPoint:o,tooltipOption:[{verticalAlign:"middle"},{align:"center"}][n]}};var ib,rb=ob;function ob(){return null!==ib&&ib.apply(this,arguments)||this}function ab(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var sb={line:function(t,e,n){var i;return i=[e,n[0]],e=[e,n[1]],n=lb(t),{type:"Line",subPixelOptimize:!0,shape:{x1:i[n=n||0],y1:i[1-n],x2:e[n],y2:e[1-n]}}},shadow:function(t,e,n){var i=Math.max(1,t.getBandWidth()),r=n[1]-n[0];return{type:"Rect",shape:(e=[e-i/2,n[0]],n=[i,r],i=lb(t),{x:e[i=i||0],y:e[1-i],width:n[i],height:n[1-i]})}}};function lb(t){return"x"===t.dim?0:1}u(cb,ub=g),cb.type="axisPointer",cb.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}};var ub,hb=cb;function cb(){var t=null!==ub&&ub.apply(this,arguments)||this;return t.type=cb.type,t}var pb=Lo(),db=R;function fb(t,e,n){var i,c,p;function r(t,h){c.on(t,function(e){n=p;var n,i,r={dispatchAction:o,pendings:i={showTip:[],hideTip:[]}};function o(t){var e=i[t.type];e?e.push(t):(t.dispatchAction=o,n.dispatchAction(t))}db(pb(c).records,function(t){t&&h(t,e,r.dispatchAction)});var t,a=r.pendings,s=p,l=a.showTip.length,u=a.hideTip.length;l?t=a.showTip[l-1]:u&&(t=a.hideTip[u-1]),t&&(t.dispatchAction=null,s.dispatchAction(t))})}b.node||(i=e.getZr(),pb(i).records||(pb(i).records={}),p=e,pb(c=i).initialized||(pb(c).initialized=!0,r("click",pt(yb,"click")),r("mousemove",pt(yb,"mousemove")),r("globalout",gb)),(pb(i).records[t]||(pb(i).records[t]={})).handler=n)}function gb(t,e,n){t.handler("leave",null,n)}function yb(t,e,n,i){e.handler(t,n,i)}function mb(t,e){b.node||(e=e.getZr(),(pb(e).records||{})[t]&&(pb(e).records[t]=null))}u(xb,vb=Bg),xb.prototype.render=function(t,e,n){var e=e.getComponent("tooltip"),i=t.get("triggerOn")||e&&e.get("triggerOn")||"mousemove|click";fb("axisPointer",n,function(t,e,n){"none"!==i&&("leave"===t||0<=i.indexOf(t))&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},xb.prototype.remove=function(t,e){mb("axisPointer",e)},xb.prototype.dispose=function(t,e){mb("axisPointer",e)},xb.type="axisPointer";var vb,_b=xb;function xb(){var t=null!==vb&&vb.apply(this,arguments)||this;return t.type=xb.type,t}function wb(t,e){var n,i,r,o,a=[],s=t.seriesIndex;return null==s||!(e=e.getSeriesByIndex(s))||null==(s=Po(n=e.getData(),t))||s<0||F(s)?{point:[]}:(i=n.getItemGraphicEl(s),r=e.coordinateSystem,e.getTooltipPosition?a=e.getTooltipPosition(s)||[]:r&&r.dataToPoint?a=t.isStacked?(e=r.getBaseAxis(),t=r.getOtherAxis(e).dim,e=e.dim,t="x"===t||"radius"===t?1:0,e=n.mapDimension(e),(o=[])[t]=n.get(e,s),o[1-t]=n.get(n.getCalculationInfo("stackResultDimension"),s),r.dataToPoint(o)||[]):r.dataToPoint(n.getValues(z(r.dimensions,function(t){return n.mapDimension(t)}),s))||[]:i&&((e=i.getBoundingRect().clone()).applyTransform(i.transform),a=[e.x+e.width/2,e.y+e.height/2]),{point:a,el:i})}var bb=Lo();function Sb(t,e,n){var o,a,i,s,l,r,u,h,c,p,d,f,g,y,m=t.currTrigger,v=[t.x,t.y],_=t,x=t.dispatchAction||ct(n.dispatchAction,n),w=e.getComponent("axisPointer").coordSysAxesInfo;if(w)return kb(v)&&(v=wb({seriesIndex:_.seriesIndex,dataIndex:_.dataIndex},e).point),o=kb(v),a=_.axesInfo,i=w.axesInfo,s="leave"===m||kb(v),l={},e={list:[],map:{}},u={showPointer:pt(Tb,r={}),showTooltip:pt(Cb,e)},R(w.coordSysMap,function(t,e){var r=o||t.containPoint(v);R(w.coordSysAxesInfo[e],function(t,e){var n=t.axis,i=function(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}(a,t);s||!r||a&&!i||null!=(i=null!=(i=i&&i.value)||o?i:n.pointToData(v))&&Mb(t,i,u,!1,l)})}),h={},R(i,function(n,t){var i=n.linkGroup;i&&!r[t]&&R(i.axesInfo,function(t,e){var e=r[e];t!==n&&e&&(e=e.value,i.mapper&&(e=n.axis.scale.parse(i.mapper(e,Ib(t),Ib(n)))),h[n.key]=e)})}),R(h,function(t,e){Mb(i[e],t,u,!0,l)}),c=r,_=i,p=l.axesInfo=[],R(_,function(t,e){var n=t.axisPointerModel.option,e=c[e];e?(t.useHandle||(n.status="show"),n.value=e.value,n.seriesDataIndices=(e.payloadBatch||[]).slice()):t.useHandle||(n.status="hide"),"show"===n.status&&p.push({axisDim:t.axis.dim,axisIndex:t.axis.model.componentIndex,value:n.value})}),m=e,_=t,e=x,kb(t=v)||!m.list.length?e({type:"hideTip"}):(x=((m.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{},e({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:_.tooltipOption,position:_.position,dataIndexInside:x.dataIndexInside,dataIndex:x.dataIndex,seriesIndex:x.seriesIndex,dataByCoordSys:m.list})),e=i,_=(t=n).getZr(),x="axisPointerLastHighlights",d=bb(_)[x]||{},f=bb(_)[x]={},R(e,function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&t.triggerEmphasis&&R(n.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;f[e]=t})}),g=[],y=[],R(d,function(t,e){f[e]||y.push(t)}),R(f,function(t,e){d[e]||g.push(t)}),y.length&&t.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:y}),g.length&&t.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:g}),l}function Mb(t,e,n,i,r){var o,a,s,l,u,h,c,p,d,f,g=t.axis;!g.scale.isBlank()&&g.containData(e)&&(t.involveSeries?(a=e,s=t.axis,l=s.dim,u=a,h=[],c=Number.MAX_VALUE,p=-1,R(t.seriesModels,function(e,t){var n,i=e.getData().mapDimensionsAll(l);if(e.getAxisTooltipData)var r=e.getAxisTooltipData(i,a,s),o=r.dataIndices,r=r.nestestValue;else{if(!(o=e.getData().indicesOfNearest(i[0],a,"category"===s.type?.5:null)).length)return;r=e.getData().get(i[0],o[0])}null!=r&&isFinite(r)&&(i=a-r,(n=Math.abs(i))<=c)&&((n<c||0<=i&&p<0)&&(c=n,p=i,u=r,h.length=0),R(o,function(t){h.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}),f=(o={payloadBatch:h,snapToValue:u}).snapToValue,(d=o.payloadBatch)[0]&&null==r.seriesIndex&&O(r,d[0]),!i&&t.snap&&g.containData(f)&&null!=f&&(e=f),n.showPointer(t,e,d),n.showTooltip(t,o,f)):n.showPointer(t,e))}function Tb(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function Cb(t,e,n,i){var r,o,n=n.payloadBatch,a=e.axis,s=a.model,l=e.axisPointerModel;e.triggerTooltip&&n.length&&(r=_w(e=e.coordSys.model),(o=t.map[r])||(o=t.map[r]={coordSysId:e.id,coordSysIndex:e.componentIndex,coordSysType:e.type,coordSysMainType:e.mainType,dataByAxis:[]},t.list.push(o)),o.dataByAxis.push({axisDim:a.dim,axisIndex:s.componentIndex,axisType:s.type,axisId:s.id,value:i,valueLabelOpt:{precision:l.get(["label","precision"]),formatter:l.get(["label","formatter"])},seriesDataIndices:n.slice()}))}function Ib(t){var e=t.axis.model,n={},t=n.axisDim=t.axis.dim;return n.axisIndex=n[t+"AxisIndex"]=e.componentIndex,n.axisName=n[t+"AxisName"]=e.name,n.axisId=n[t+"AxisId"]=e.id,n}function kb(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function Db(t){bw.registerAxisPointerClass("CartesianAxisPointer",rb),t.registerComponentModel(hb),t.registerComponentView(_b),t.registerPreprocessor(function(t){var e;t&&(t.axisPointer&&0!==t.axisPointer.length||(t.axisPointer={}),e=t.axisPointer.link)&&!F(e)&&(t.axisPointer.link=[e])}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=gw(t,e)}),t.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},Sb)}u(Lb,Ab=g),Lb.type="tooltip",Lb.dependencies=["axisPointer"],Lb.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}};var Ab,Pb=Lb;function Lb(){var t=null!==Ab&&Ab.apply(this,arguments)||this;return t.type=Lb.type,t}function Ob(t){var e=t.get("confine");return null!=e?e:"richText"===t.get("renderMode")}function Rb(t){if(b.domSupported)for(var e=document.documentElement.style,n=0,i=t.length;n<i;n++)if(t[n]in e)return t[n]}var Eb=Rb(["transform","webkitTransform","OTransform","MozTransform","msTransform"]);function Bb(t,e){if(!t)return e;e=dp(e,!0);var n=t.indexOf(e);return(t=-1===n?e:"-"+t.slice(0,n)+"-"+e).toLowerCase()}var Nb=Bb(Rb(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),"transition"),zb=Bb(Eb,"transform"),Fb="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(b.transform3dSupported?"will-change:transform;":"");function Vb(t,e,n){var i,t=t.toFixed(0)+"px",e=e.toFixed(0)+"px";return b.transformSupported?(i="translate"+((i=b.transform3dSupported)?"3d":"")+"("+t+","+e+(i?",0":"")+")",n?"top:0;left:0;"+zb+":"+i+";":[["top",0],["left",0],[Eb,i]]):n?"top:"+e+";left:"+t+";":[["top",e],["left",t]]}function Hb(i,t,e){var n,r,o=[],a=i.get("transitionDuration"),s=i.get("backgroundColor"),l=i.get("shadowBlur"),u=i.get("shadowColor"),h=i.get("shadowOffsetX"),c=i.get("shadowOffsetY"),p=i.getModel("textStyle"),d=wg(i,"html");return o.push("box-shadow:"+(h+"px "+c+"px "+l+"px "+u)),t&&a&&o.push((u="opacity"+(l=" "+(h=a)/2+"s "+(c="cubic-bezier(0.23,1,0.32,1)"))+",visibility"+l,e||(l=" "+h+"s "+c,u+=b.transformSupported?","+zb+l:",left"+l+",top"+l),Nb+":"+u)),s&&o.push("background-color:"+s),R(["width","color","radius"],function(t){var e="border-"+t,n=dp(e),n=i.get(n);null!=n&&o.push(e+":"+n+("color"===t?"":"px"))}),o.push((r=[],t=(n=p).get("fontSize"),(a=n.getTextColor())&&r.push("color:"+a),r.push("font:"+n.getFont()),t&&r.push("line-height:"+Math.round(3*t/2)+"px"),a=n.get("textShadowColor"),t=n.get("textShadowBlur")||0,e=n.get("textShadowOffsetX")||0,h=n.get("textShadowOffsetY")||0,a&&t&&r.push("text-shadow:"+e+"px "+h+"px "+t+"px "+a),R(["decoration","align"],function(t){var e=n.get(t);e&&r.push("text-"+t+":"+e)}),r.join(";"))),null!=d&&o.push("padding:"+fp(d).join("px ")+"px"),o.join(";")+";"}function Wb(t,e,n,i,r){var o,a,s=e&&e.painter;n?(o=s&&s.getViewportRoot())&&(a=t,n=n,ye(ge,o,i,r,!0))&&ye(a,n,ge[0],ge[1]):(t[0]=i,t[1]=r,(o=s&&s.getViewportRootOffset())&&(t[0]+=o.offsetLeft,t[1]+=o.offsetTop)),t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}Ub.prototype.update=function(t){this._container||(i=this._api.getDom(),n="position",n=(e=(e=i).currentStyle||document.defaultView&&document.defaultView.getComputedStyle(e))?n?e[n]:e:null,"absolute"!==(e=i.style).position&&"absolute"!==n&&(e.position="relative"));var e,n,i=t.get("alwaysShowContent");i&&this._moveIfResized(),this._alwaysShowContent=i,this.el.className=t.get("className")||""},Ub.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var n=this.el,i=n.style,r=this._styleCoord;n.innerHTML?i.cssText=Fb+Hb(t,!this._firstShow,this._longHide)+Vb(r[0],r[1],!0)+"border-color:"+xp(e)+";"+(t.get("extraCssText")||"")+";pointer-events:"+(this._enterable?"auto":"none"):i.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},Ub.prototype.setContent=function(t,e,n,i,r){var o=this.el;if(null==t)o.innerHTML="";else{var a,s,l,u,h,c="";if(V(r)&&"item"===n.get("trigger")&&!Ob(n)&&(n=n,i=i,c=V(r=r)&&"inside"!==r?(a=n.get("backgroundColor"),n=n.get("borderWidth"),i=xp(i),r=r="left"===(r=r)?"right":"right"===r?"left":"top"===r?"bottom":"top",p=Math.max(1.5*Math.round(n),6),s="",l=zb+":",-1<I(["left","right"],r)?(s+="top:50%",l+="translateY(-50%) rotate("+(h="left"==r?-225:-45)+"deg)"):(s+="left:50%",l+="translateX(-50%) rotate("+(h="top"==r?225:45)+"deg)"),h=h*Math.PI/180,h=(u=p+n)*Math.abs(Math.cos(h))+u*Math.abs(Math.sin(h)),i=i+" solid "+n+"px;",'<div style="'+["position:absolute;width:"+p+"px;height:"+p+"px;z-index:-1;",(s+=";"+r+":-"+Math.round(100*((h-Math.SQRT2*n)/2+Math.SQRT2*n-(h-u)/2))/100+"px")+";"+l+";","border-bottom:"+i,"border-right:"+i,"background-color:"+a+";"].join("")+'"></div>'):""),V(t))o.innerHTML=t+c;else if(t){o.innerHTML="",F(t)||(t=[t]);for(var p,d=0;d<t.length;d++)mt(t[d])&&t[d].parentNode!==o&&o.appendChild(t[d]);c&&o.childNodes.length&&((p=document.createElement("div")).innerHTML=c,o.appendChild(p))}}},Ub.prototype.setEnterable=function(t){this._enterable=t},Ub.prototype.getSize=function(){var t=this.el;return[t.offsetWidth,t.offsetHeight]},Ub.prototype.moveTo=function(t,e){var n,i=this._styleCoord;Wb(i,this._zr,this._container,t,e),null!=i[0]&&null!=i[1]&&(n=this.el.style,R(Vb(i[0],i[1]),function(t){n[t[0]]=t[1]}))},Ub.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},Ub.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",b.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},Ub.prototype.hideLater=function(t){!this._show||this._inContent&&this._enterable||this._alwaysShowContent||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ct(this.hide,this),t)):this.hide())},Ub.prototype.isShow=function(){return this._show},Ub.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null};var Gb=Ub;function Ub(t,e){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,b.wxa)return null;var n=document.createElement("div"),i=(n.domBelongToZr=!0,this.el=n,this._zr=t.getZr()),e=e.appendTo,e=e&&(V(e)?document.querySelector(e):mt(e)?e:k(e)&&e(t.getDom())),r=(Wb(this._styleCoord,i,e,t.getWidth()/2,t.getHeight()/2),(e||t.getDom()).appendChild(n),this._api=t,this._container=e,this);n.onmouseenter=function(){r._enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},n.onmousemove=function(t){var e;t=t||window.event,r._enterable||(e=i.handler,Ie(i.painter.getViewportRoot(),t,!0),e.dispatch("mousemove",t))},n.onmouseleave=function(){r._inContent=!1,r._enterable&&r._show&&r.hideLater(r._hideDelay)}}Yb.prototype.update=function(t){t=t.get("alwaysShowContent");t&&this._moveIfResized(),this._alwaysShowContent=t},Yb.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},Yb.prototype.setContent=function(t,e,n,i,r){var o=this,a=(L(t)&&f(""),this.el&&this._zr.remove(this.el),n.getModel("textStyle")),s=(this.el=new Rs({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:i,textShadowColor:a.get("textShadowColor"),fill:n.get(["textStyle","color"]),padding:wg(n,"richText"),verticalAlign:"top",align:"left"},z:n.get("z")}),R(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(t){o.el.style[t]=n.get(t)}),R(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(t){o.el.style[t]=a.get(t)||0}),this._zr.add(this.el),this);this.el.on("mouseover",function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0}),this.el.on("mouseout",function(){s._enterable&&s._show&&s.hideLater(s._hideDelay),s._inContent=!1})},Yb.prototype.setEnterable=function(t){this._enterable=t},Yb.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),t=Zb(t.style);return[e.width+t.left+t.right,e.height+t.top+t.bottom]},Yb.prototype.moveTo=function(t,e){var n,i,r=this.el;r&&(jb(i=this._styleCoord,this._zr,t,e),t=i[0],e=i[1],n=qb((i=r.style).borderWidth||0),i=Zb(i),r.x=t+n+i.left,r.y=e+n+i.top,r.markRedraw())},Yb.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},Yb.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},Yb.prototype.hideLater=function(t){!this._show||this._inContent&&this._enterable||this._alwaysShowContent||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ct(this.hide,this),t)):this.hide())},Yb.prototype.isShow=function(){return this._show},Yb.prototype.dispose=function(){this._zr.remove(this.el)};var Xb=Yb;function Yb(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),jb(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}function qb(t){return Math.max(0,t)}function Zb(t){var e=qb(t.shadowBlur||0),n=qb(t.shadowOffsetX||0),t=qb(t.shadowOffsetY||0);return{left:qb(e-n),right:qb(e+n),top:qb(e-t),bottom:qb(e+t)}}function jb(t,e,n,i){t[0]=n,t[1]=i,t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}var Kb,$b=new Ds({shape:{x:-1,y:-1,width:2,height:2}}),Qb=(u(Jb,Kb=Bg),Jb.prototype.init=function(t,e){var n;!b.node&&e.getDom()&&(t=t.getComponent("tooltip"),n=this._renderMode="auto"===(n=t.get("renderMode"))?b.domSupported?"html":"richText":n||"html",this._tooltipContent="richText"===n?new Xb(e):new Gb(e,{appendTo:t.get("appendToBody",!0)?"body":t.get("appendTo",!0)}))},Jb.prototype.render=function(t,e,n){!b.node&&n.getDom()&&(this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,(e=this._tooltipContent).update(t),e.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow(),"richText"!==this._renderMode&&t.get("transitionDuration")?Qg(this,"_updatePosition",50,"fixRate"):Jg(this,"_updatePosition"))},Jb.prototype._initGlobalListener=function(){var i=this._tooltipModel.get("triggerOn");fb("itemTooltip",this._api,ct(function(t,e,n){"none"!==i&&(0<=i.indexOf(t)?this._tryShow(e,n):"leave"===t&&this._hide(n))},this))},Jb.prototype._keepShow=function(){var t,e=this._tooltipModel,n=this._ecModel,i=this._api,r=e.get("triggerOn");null!=this._lastX&&null!=this._lastY&&"none"!==r&&"click"!==r&&(t=this,clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){i.isDisposed()||t.manuallyShowTip(e,n,i,{x:t._lastX,y:t._lastY,dataByCoordSys:t._lastDataByCoordSys})}))},Jb.prototype.manuallyShowTip=function(t,e,n,i){var r,o,a,s;i.from!==this.uid&&!b.node&&n.getDom()&&(r=eS(i,n),this._ticket="",s=i.dataByCoordSys,(o=function(n,t,e){var i=Eo(n).queryOptionMap,r=i.keys()[0];if(r&&"series"!==r){var o,t=No(t,r,i.get(r),{useDefault:!1,enableAll:!1,enableNone:!1}).models[0];if(t)if(e.getViewOfComponentModel(t).group.traverse(function(t){var e=D(t).tooltipConfig;if(e&&e.name===n.name)return o=t,!0}),o)return{componentMainType:r,componentIndex:t.componentIndex,el:o}}}(i,e,n))?((a=o.el.getBoundingRect().clone()).applyTransform(o.el.transform),this._tryShow({offsetX:a.x+a.width/2,offsetY:a.y+a.height/2,target:o.el,position:i.position,positionDefault:"bottom"},r)):i.tooltip&&null!=i.x&&null!=i.y?((a=$b).x=i.x,a.y=i.y,a.update(),D(a).tooltipConfig={name:null,option:i.tooltip},this._tryShow({offsetX:i.x,offsetY:i.y,target:a},r)):s?this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,dataByCoordSys:s,tooltipOption:i.tooltipOption},r):null!=i.seriesIndex?this._manuallyAxisShowTip(t,e,n,i)||(a=(o=wb(i,e)).point[0],s=o.point[1],null!=a&&null!=s&&this._tryShow({offsetX:a,offsetY:s,target:o.el,position:i.position,positionDefault:"bottom"},r)):null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target},r)))},Jb.prototype.manuallyHideTip=function(t,e,n,i){var r=this._tooltipContent;this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,i.from!==this.uid&&this._hide(eS(i,n))},Jb.prototype._manuallyAxisShowTip=function(t,e,n,i){var r=i.seriesIndex,o=i.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=o&&null!=a){a=e.getSeriesByIndex(r);if(a){e=tS([a.getData().getItemModel(o),a,(a.coordinateSystem||{}).model],this._tooltipModel);if("axis"===e.get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:o,position:i.position}),!0}}},Jb.prototype._tryShow=function(t,e){var n,i,r,o=t.target;this._tooltipModel&&(this._lastX=t.offsetX,this._lastY=t.offsetY,(n=t.dataByCoordSys)&&n.length?this._showAxisTooltip(n,t):o?"legend"!==D(o).ssrType&&(Dy(o,function(t){return null!=D(t).dataIndex?(i=t,1):null!=D(t).tooltipConfig&&(r=t,1)},!(this._lastDataByCoordSys=null)),i?this._showSeriesItemTooltip(t,i,e):r?this._showComponentItemTooltip(t,r,e):this._hide(e)):(this._lastDataByCoordSys=null,this._hide(e)))},Jb.prototype._showOrMove=function(t,e){t=t.get("showDelay");e=ct(e,this),clearTimeout(this._showTimout),0<t?this._showTimout=setTimeout(e,t):e()},Jb.prototype._showAxisTooltip=function(t,e){var u=this._ecModel,h=this._tooltipModel,n=[e.offsetX,e.offsetY],i=tS([e.tooltipOption],h),c=this._renderMode,p=[],d=dg("section",{blocks:[],noHeader:!0}),f=[],g=new bg,r=(R(t,function(t){R(t.dataByAxis,function(r){var o,a,s=u.getComponent(r.axisDim+"Axis",r.axisIndex),l=r.value;s&&null!=l&&(o=eb(l,s.axis,u,r.seriesDataIndices,r.valueLabelOpt),a=dg("section",{header:o,noHeader:!It(o),sortBlocks:!0,blocks:[]}),d.blocks.push(a),R(r.seriesDataIndices,function(t){var e,n=u.getSeriesByIndex(t.seriesIndex),t=t.dataIndexInside,i=n.getDataParams(t);i.dataIndex<0||(i.axisDim=r.axisDim,i.axisIndex=r.axisIndex,i.axisType=r.axisType,i.axisId=r.axisId,i.axisValue=x_(s.axis,{value:l}),i.axisValueLabel=o,i.marker=g.makeTooltipMarker("item",xp(i.color),c),(e=(t=yf(n.formatTooltip(t,!0,null))).frag)&&(n=tS([n],h).get("valueFormatter"),a.blocks.push(n?O({valueFormatter:n},e):e)),t.text&&f.push(t.text),p.push(i))}))})}),d.blocks.reverse(),f.reverse(),e.position),e=i.get("order"),e=vg(d,g,c,e,u.get("useUTC"),i.get("textStyle")),e=(e&&f.unshift(e),"richText"===c?"\n\n":"<br/>"),o=f.join(e);this._showOrMove(i,function(){this._updateContentNotChangedOnAxis(t,p)?this._updatePosition(i,r,n[0],n[1],this._tooltipContent,p):this._showTooltipContent(i,o,p,Math.random()+"",n[0],n[1],r,null,g)})},Jb.prototype._showSeriesItemTooltip=function(t,e,n){var i,r,o,a,s,l=this._ecModel,e=D(e),u=e.seriesIndex,h=l.getSeriesByIndex(u),c=e.dataModel||h,p=e.dataIndex,e=e.dataType,d=c.getData(e),f=this._renderMode,g=t.positionDefault,y=tS([d.getItemModel(p),c,h&&(h.coordinateSystem||{}).model],this._tooltipModel,g?{position:g}:null),h=y.get("trigger");null!=h&&"item"!==h||(i=c.getDataParams(p,e),r=new bg,i.marker=r.makeTooltipMarker("item",xp(i.color),f),g=yf(c.formatTooltip(p,!1,e)),h=y.get("order"),e=y.get("valueFormatter"),o=g.frag,a=o?vg(e?O({valueFormatter:e},o):o,r,f,h,l.get("useUTC"),y.get("textStyle")):g.text,s="item_"+c.name+"_"+p,this._showOrMove(y,function(){this._showTooltipContent(y,a,i,s,t.offsetX,t.offsetY,t.position,t.target,r)}),n({type:"showTip",dataIndexInside:p,dataIndex:d.getRawIndex(p),seriesIndex:u,from:this.uid}))},Jb.prototype._showComponentItemTooltip=function(e,n,t){var i="html"===this._renderMode,r=D(n),o=r.tooltipConfig.option||{},a=o.encodeHTMLContent,a=(V(o)&&(o={content:o,formatter:o},a=!0),a&&i&&o.content&&((o=y(o)).content=xe(o.content)),[o]),i=this._ecModel.getComponent(r.componentMainType,r.componentIndex),r=(i&&a.push(i),a.push({formatter:o.content}),e.positionDefault),s=tS(a,this._tooltipModel,r?{position:r}:null),l=s.get("content"),u=Math.random()+"",h=new bg;this._showOrMove(s,function(){var t=y(s.get("formatterParams")||{});this._showTooltipContent(s,l,t,u,e.offsetX,e.offsetY,e.position,n,h)}),t({type:"showTip",from:this.uid})},Jb.prototype._showTooltipContent=function(n,t,i,e,r,o,a,s,l){var u,h,c,p,d;this._ticket="",n.get("showContent")&&n.get("show")&&((u=this._tooltipContent).setEnterable(n.get("enterable")),h=n.get("formatter"),a=a||n.get("position"),t=t,c=this._getNearestPoint([r,o],i,n.get("trigger"),n.get("borderColor")).color,h&&(t=V(h)?(p=n.ecModel.get("useUTC"),t=h,vp(t=(d=F(i)?i[0]:i)&&d.axisType&&0<=d.axisType.indexOf("time")?Kc(d.axisValue,t,p):t,i,!0)):k(h)?(d=ct(function(t,e){t===this._ticket&&(u.setContent(e,l,n,c,a),this._updatePosition(n,a,r,o,u,i,s))},this),this._ticket=e,h(i,e,d)):h),u.setContent(t,l,n,c,a),u.show(n,c),this._updatePosition(n,a,r,o,u,i,s))},Jb.prototype._getNearestPoint=function(t,e,n,i){return"axis"===n||F(e)?{color:i||("html"===this._renderMode?"#fff":"none")}:F(e)?void 0:{color:i||e.color||e.borderColor}},Jb.prototype._updatePosition=function(t,e,n,i,r,o,a){var s,l=this._api.getWidth(),u=this._api.getHeight(),h=(e=e||t.get("position"),r.getSize()),c=t.get("align"),p=t.get("verticalAlign"),d=a&&a.getBoundingRect().clone();a&&d.applyTransform(a.transform),F(e=k(e)?e([n,i],o,r.el,d,{viewSize:[l,u],contentSize:h.slice()}):e)?(n=eo(e[0],l),i=eo(e[1],u)):L(e)?((o=e).width=h[0],o.height=h[1],n=(o=Cp(o,{width:l,height:u})).x,i=o.y,p=c=null):i=(n=(s=V(e)&&a?function(t,e,n,i){var r=n[0],o=n[1],a=Math.ceil(Math.SQRT2*i)+8,s=0,l=0,u=e.width,h=e.height;switch(t){case"inside":s=e.x+u/2-r/2,l=e.y+h/2-o/2;break;case"top":s=e.x+u/2-r/2,l=e.y-o-a;break;case"bottom":s=e.x+u/2-r/2,l=e.y+h+a;break;case"left":s=e.x-r-a,l=e.y+h/2-o/2;break;case"right":s=e.x+u+a,l=e.y+h/2-o/2}return[s,l]}(e,d,h,t.get("borderWidth")):function(t,e,n,i,r,o,a){var n=n.getSize(),s=n[0],n=n[1];null!=o&&(i<t+s+o+2?t-=s+o:t+=o);null!=a&&(r<e+n+a?e-=n+a:e+=a);return[t,e]}(n,i,r,l,u,c?null:20,p?null:20))[0],s[1]),c&&(n-=nS(c)?h[0]/2:"right"===c?h[0]:0),p&&(i-=nS(p)?h[1]/2:"bottom"===p?h[1]:0),Ob(t)&&(o=n,a=i,e=l,d=u,c=(c=r).getSize(),p=c[0],c=c[1],o=Math.min(o+p,e)-p,a=Math.min(a+c,d)-c,o=Math.max(o,0),a=Math.max(a,0),n=(s=[o,a])[0],i=s[1]),r.moveTo(n,i)},Jb.prototype._updateContentNotChangedOnAxis=function(n,o){var t=this._lastDataByCoordSys,a=this._cbParamsList,s=!!t&&t.length===n.length;return s&&R(t,function(t,e){var t=t.dataByAxis||[],r=(n[e]||{}).dataByAxis||[];(s=s&&t.length===r.length)&&R(t,function(t,e){var e=r[e]||{},n=t.seriesDataIndices||[],i=e.seriesDataIndices||[];(s=s&&t.value===e.value&&t.axisType===e.axisType&&t.axisId===e.axisId&&n.length===i.length)&&R(n,function(t,e){e=i[e];s=s&&t.seriesIndex===e.seriesIndex&&t.dataIndex===e.dataIndex}),a&&R(t.seriesDataIndices,function(t){var t=t.seriesIndex,e=o[t],t=a[t];e&&t&&t.data!==e.data&&(s=!1)})})}),this._lastDataByCoordSys=n,this._cbParamsList=o,!!s},Jb.prototype._hide=function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},Jb.prototype.dispose=function(t,e){!b.node&&e.getDom()&&(Jg(this,"_updatePosition"),this._tooltipContent.dispose(),mb("itemTooltip",e))},Jb.type="tooltip",Jb);function Jb(){var t=null!==Kb&&Kb.apply(this,arguments)||this;return t.type=Jb.type,t}function tS(t,e,n){for(var i=e.ecModel,r=n?(r=new kc(n,i,i),new kc(e.option,r,i)):e,o=t.length-1;0<=o;o--){var a=t[o];(a=a&&(V(a=a instanceof kc?a.get("tooltip",!0):a)?{formatter:a}:a))&&(r=new kc(a,r,i))}return r}function eS(t,e){return t.dispatchAction||ct(e.dispatchAction,e)}function nS(t){return"center"===t||"middle"===t}k_(function(t){k_(Db),t.registerComponentModel(Pb),t.registerComponentView(Qb),t.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},Vt),t.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},Vt)}),k_(Z1);var iS={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},rS=(oS.prototype.evaluate=function(t){var e=typeof t;return V(e)?this._condVal.test(t):!!ft(e)&&this._condVal.test(t+"")},oS);function oS(t){null==(this._condVal=V(t)?new RegExp(t):xt(t)?t:null)&&f("")}sS.prototype.evaluate=function(){return this.value};var aS=sS;function sS(){}uS.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(!t[e].evaluate())return!1;return!0};var lS=uS;function uS(){}cS.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(t[e].evaluate())return!0;return!1};var hS=cS;function cS(){}dS.prototype.evaluate=function(){return!this.child.evaluate()};var pS=dS;function dS(){}gS.prototype.evaluate=function(){for(var t=!!this.valueParser,e=(0,this.getValue)(this.valueGetterParam),n=t?this.valueParser(e):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(t?n:e))return!1;return!0};var fS=gS;function gS(){}function yS(t,e){if(!0===t||!1===t)return(n=new aS).value=t,n;var n;if(vS(t)||f(""),t.and)return mS("and",t,e);if(t.or)return mS("or",t,e);if(t.not)return n=e,vS(o=(o=t).not)||f(""),(l=new pS).child=yS(o,n),l.child||f(""),l;for(var i=t,r=e,o=r.prepareGetValue(i),a=[],s=ht(i),l=i.parser,u=l?Pf(l):null,h=0;h<s.length;h++){var c,p=s[h];"parser"===p||r.valueGetterAttrMap.get(p)||(c=Ft(iS,p)?iS[p]:p,p=i[p],p=u?u(p):p,(c=function(t,e){return"eq"===t||"ne"===t?new Nf("eq"===t,e):Ft(Lf,t)?new Of(t,e):null}(c,p)||"reg"===c&&new rS(p))||f(""),a.push(c))}return a.length||f(""),(l=new fS).valueGetterParam=o,l.valueParser=u,l.getValue=r.getValue,l.subCondList=a,l}function mS(t,e,n){e=e[t],F(e)||f(""),e.length||f(""),t=new("and"===t?lS:hS);return t.children=z(e,function(t){return yS(t,n)}),t.children.length||f(""),t}function vS(t){return L(t)&&!st(t)}xS.prototype.evaluate=function(){return this._cond.evaluate()};var _S=xS;function xS(t,e){this._cond=yS(t,e)}var wS={type:"echarts:filter",transform:function(t){for(var e,n,i=t.upstream,r=(t=t.config,n={valueGetterAttrMap:B({dimension:!0}),prepareGetValue:function(t){var e=t.dimension,t=(Ft(t,"dimension")||f(""),i.getDimensionInfo(e));return t||f(""),{dimIdx:t.index}},getValue:function(t){return i.retrieveValueFromItem(e,t.dimIdx)}},new _S(t,n)),o=[],a=0,s=i.count();a<s;a++)e=i.getRawDataItem(a),r.evaluate()&&o.push(e);return{data:o}}},bS={type:"echarts:sort",transform:function(t){for(var a=t.upstream,t=t.config,t=xo(t),s=(t.length||f(""),[]),t=(R(t,function(t){var e=t.dimension,n=t.order,i=t.parser,t=t.incomparable,e=(null==e&&f(""),"asc"!==n&&"desc"!==n&&f(""),t&&"min"!==t&&"max"!==t&&f(""),"asc"!==n&&"desc"!==n&&f(""),a.getDimensionInfo(e)),r=(e||f(""),i?Pf(i):null);i&&!r&&f(""),s.push({dimIdx:e.index,parser:r,comparator:new Ef(n,t)})}),a.sourceFormat),e=(t!==Vp&&t!==Hp&&f(""),[]),n=0,i=a.count();n<i;n++)e.push(a.getRawDataItem(n));return e.sort(function(t,e){for(var n=0;n<s.length;n++){var i=s[n],r=a.retrieveValueFromItem(t,i.dimIdx),o=a.retrieveValueFromItem(e,i.dimIdx),i=(i.parser&&(r=i.parser(r),o=i.parser(o)),i.comparator.evaluate(r,o));if(0!==i)return i}return 0}),{data:e}}};k_(function(t){t.registerTransform(wS),t.registerTransform(bS)}),t.Axis=Tc,t.ChartView=Hg,t.ComponentModel=g,t.ComponentView=Bg,t.List=sv,t.Model=kc,t.PRIORITY=Ey,t.SeriesModel=kg,t.color=xi,t.connect=function(e){var t;return F(e)&&(t=e,e=null,R(t,function(t){null!=t.group&&(e=t.group)}),e=e||"g_"+f0++,R(t,function(t){t.group=e})),p0[e]=!0,e},t.dataTool={},t.dependencies={zrender:"5.6.0"},t.disConnect=Ry,t.disconnect=y0,t.dispose=function(t){V(t)?t=c0[t]:t instanceof $m||(t=m0(t)),t instanceof $m&&!t.isDisposed()&&t.dispose()},t.env=b,t.extendChartView=function(t){return t=Hg.extend(t),Hg.registerClass(t),t},t.extendComponentModel=function(t){return t=g.extend(t),g.registerClass(t),t},t.extendComponentView=function(t){return t=Bg.extend(t),Bg.registerClass(t),t},t.extendSeriesModel=function(t){return t=kg.extend(t),kg.registerClass(t),t},t.format=Sc,t.getCoordinateSystemDimensions=function(t){if(t=gd.get(t))return t.getDimensionsInfo?t.getDimensionsInfo():t.dimensions.slice()},t.getInstanceByDom=m0,t.getInstanceById=function(t){return c0[t]},t.getMap=function(t){var e=xm.getMap;return e&&e(t)},t.graphic=Uc,t.helper=By,t.init=function(t,e,n){var i=!(n&&n.ssr);if(i){var r=m0(t);if(r)return r}return(r=new $m(t,e,n)).id="ec_"+d0++,c0[r.id]=r,i&&zo(t,g0,r.id),qm(r),_m.trigger("afterinit",r),r},t.innerDrawElementOnCanvas=pm,t.matrix=He,t.number=$o,t.parseGeoJSON=X_,t.parseGeoJson=X_,t.registerAction=M0,t.registerCoordinateSystem=T0,t.registerLayout=C0,t.registerLoading=A0,t.registerLocale=zc,t.registerMap=P0,t.registerPostInit=w0,t.registerPostUpdate=b0,t.registerPreprocessor=_0,t.registerProcessor=x0,t.registerTheme=v0,t.registerTransform=L0,t.registerUpdateLifecycle=S0,t.registerVisual=I0,t.setCanvasCreator=function(t){C({createCanvas:t})},t.setPlatformAPI=C,t.throttle=$g,t.time=Xh,t.use=k_,t.util=_c,t.vector=oe,t.version="5.5.1",t.zrUtil=Wt,t.zrender=Qr});