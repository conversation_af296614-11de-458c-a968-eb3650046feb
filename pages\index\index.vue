<template>
	<view class="ai-query-container">
		<!-- 连接状态指示器 -->
		<view class="connection-status" :class="connectionStatusClass" @click="reconnectMCP">
			{{ connectionStatusText }}
		</view>

		<view class="container">
			<text class="title">AI智能问数</text>

			<view class="search-container">
				<input type="text" class="search-input" placeholder="去年营业额前十的门店同比情况" v-model="searchQuery"
					@keypress="onKeyPress" />
				<button class="search-btn voice-btn" :class="{ 'recording': isRecording }"
					@click="toggleVoiceRecognition" :title="isRecording ? '正在聆听...点击停止' : '语音输入'">
					<text class="voice-icon">🎤</text>
				</button>
				<button class="search-btn" @click="performSearch">
					<text class="search-icon">🔍</text>
				</button>
				<canvas v-if="isRecording && isH5" id="voiceWaveCanvas" class="voice-wave-canvas"></canvas>
			</view>

			<!-- 查询等待提示 -->
			<view class="loading" v-if="isLoading">
				<view class="loading-content">
					<view class="spinner"></view>
					<text class="loading-text">
						{{ loadingMessage }}<text class="loading-dots"></text>
					</text>
					<text class="loading-sub-text">{{ loadingSubMessage }}</text>
				</view>
			</view>

			<!-- 查询结果 -->
			<view class="result-container" v-if="showResult">
				<text class="result-title">📊 查询结果</text>
				<rich-text class="result-content" :nodes="formattedResult"></rich-text>
			</view>

			<view class="section">
				<text class="section-title">热门搜索</text>
				<view class="tags-container">
					<view class="tag" v-for="(tag, index) in hotTags" :key="index" @click="searchTag(tag)">
						{{ tag }}
					</view>
				</view>
			</view>

			<view class="section">
				<text class="section-title">历史搜索</text>
				<view class="history-container">
					<view class="history-item" v-for="(item, index) in searchHistory" :key="index"
						@click="searchTag(item)">
						{{ item }}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// MCP连接相关
				// 本地测试地址
				mcpApiBase: 'http://localhost:8081',
				// 线上地址
				// mcpApiBase: 'https://dada-183-250-154-98.ngrok-free.app',
				mcpConnected: false,
				connectionStatus: 'disconnected', // connected, disconnected, connecting

				// 搜索相关
				searchQuery: '',
				isLoading: false,
				showResult: false,
				queryResult: '',

				// 加载动画相关
				loadingMessage: '正在智能分析您的问题',
				loadingSubMessage: 'AI正在理解查询意图',
				loadingMessages: [{
						main: '正在智能分析您的问题',
						sub: 'AI正在理解查询意图'
					},
					{
						main: '正在连接数据库',
						sub: '建立与SQL Server的安全连接'
					},
					{
						main: '正在查询数据',
						sub: '从StoreRevenue表中检索相关数据'
					},
					{
						main: '正在处理结果',
						sub: '对查询结果进行智能分析和格式化'
					}
				],
				loadingInterval: null,

				// 语音识别相关
				isRecording: false,
				aliyunAppKey: 'DzVzUYbM1vG83nxm',
				aliyunToken: '74902900e45c44a497f830c29522ede0',
				aliyunWs: null,
				aliyunRecorder: null,
				aliyunAudioContext: null,
				aliyunStream: null,
				aliyunAnalyser: null,
				aliyunWaveAnimationId: null,
				silenceStartTime: null,
				silenceThreshold: 0.02,
				silenceTimeout: 1000,

				// 热门搜索标签
				hotTags: [
					'本月平台收入总共多少',
					'本月平台收入明细',
					'上个月平台收入',
					'平台收入排名前5',
					'去年营业额',
					'今年营业额',
					'门店营业额排名'
				],

				// 搜索历史
				searchHistory: [
					'本月平台收入总共多少',
					'列一个本月平台收入的明细给我',
					'上个月的平台收入总共有多少',
					'看看去年的营业额',
					'今年的营业额',
					'门店营业额前十门店营业额',
					'去年营业额前十门店同比情况',
					'门店日期间3天利润趋势'
				],

				isH5: false,
				_waveAnimId: null,
			}
		},

		computed: {
			connectionStatusClass() {
				return `connection-status ${this.connectionStatus}`;
			},

			connectionStatusText() {
				const statusMap = {
					'connected': '🟢 已连接',
					'disconnected': '🔴 未连接',
					'connecting': '🟡 连接中...'
				};
				return statusMap[this.connectionStatus] || '🔴 未连接';
			},

			formattedResult() {
				if (!this.queryResult) return '';

				let formatted = this.queryResult
					.replace(/\n/g, '<br>')
					.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
					.replace(/\*(.*?)\*/g, '<em>$1</em>')
					.replace(/`(.*?)`/g,
						'<code style="background: #f1f3f4; padding: 2px 4px; border-radius: 3px;">$1</code>');

				// 数字高亮
				formatted = formatted.replace(/(\d+\.?\d*)(元|万元|亿元)/g,
					'<span style="color: #4A90E2; font-weight: bold;">$1$2</span>');

				return `<div style="line-height: 1.8;">${formatted}</div>`;
			}
		},

		methods: {
			// MCP连接管理
			async connectToMCP() {
				try {
					this.updateConnectionStatus('connecting');
					console.log('正在连接MCP服务器...');

					const [err, response] = await uni.request({
						url: `${this.mcpApiBase}/mcp/connect`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json'
						}
					});

					if (response && response.data && response.data.success) {
						this.mcpConnected = true;
						this.updateConnectionStatus('connected');
						console.log('MCP服务器连接成功');
					} else {
						throw new Error((response && response.data && response.data.message) || '连接失败');
					}

				} catch (error) {
					console.error('MCP连接错误:', error);
					this.mcpConnected = false;
					this.updateConnectionStatus('disconnected');

					setTimeout(() => {
						uni.showModal({
							title: '连接失败',
							content: `MCP服务器连接失败: ${error.message}\n\n请确保:\n1. MCP服务器正在运行 (端口8080)\n2. MCP客户端正在运行 (端口8081)`,
							showCancel: false
						});
					}, 1000);
				}
			},

			updateConnectionStatus(status) {
				this.connectionStatus = status;
			},

			async reconnectMCP() {
				if (!this.mcpConnected) {
					console.log('尝试重新连接MCP...');
					await this.connectToMCP();
				}
			},

			// 搜索功能
			async performSearch() {
				const query = this.searchQuery.trim();
				if (!query) {
					uni.showToast({
						title: '请输入查询内容',
						icon: 'none'
					});
					return;
				}

				if (!this.mcpConnected) {
					uni.showToast({
						title: 'MCP服务器未连接，请稍后重试',
						icon: 'none'
					});
					return;
				}

				await this.executeQuery(query);
			},

			async searchTag(query) {
				this.searchQuery = query;
				await this.executeQuery(query);
			},

			async executeQuery(query) {
				this.isLoading = true;
				this.showResult = false;
				this.showLoadingWithMessages();

				console.log('开始执行查询:', query);

				try {
					const [err, response] = await uni.request({
						url: `${this.mcpApiBase}/mcp/tools/business-query?query=${encodeURIComponent(query)}`,
						method: 'POST',
						header: {
							'Content-Type': 'application/json',
							'Accept': 'application/json'
						}
					});

					console.log('API响应状态:', response);

					if (response && response.data && response.data.success && response.data.data && response.data.data.content) {
						const content = response.data.data.content[0];
						if (content && content.text) {
							this.queryResult = content.text;
							this.showResult = true;
							this.addToHistory(query);
							console.log('查询成功，结果已显示');
						} else {
							this.queryResult = '查询结果为空，请尝试其他问题';
							this.showResult = true;
						}
					} else {
						const errorMsg = (response && response.data && (response.data.message || response.data.error)) || '查询失败，请稍后重试';
						this.queryResult = `❌ ${errorMsg}`;
						this.showResult = true;
					}

				} catch (error) {
					console.error('查询错误:', error);

					let errorMessage = '';
					if (error.message && error.message.includes('Failed to fetch')) {
						errorMessage = '❌ 无法连接到MCP服务，请检查服务是否正在运行';
					} else if (error.message && error.message.includes('HTTP 404')) {
						errorMessage = '❌ API接口不存在，请检查MCP客户端是否正确启动';
					} else if (error.message && error.message.includes('HTTP 500')) {
						errorMessage = '❌ 服务器内部错误，请检查MCP服务器日志';
					} else {
						errorMessage = `❌ 查询出错: ${error.message}`;
					}

					this.queryResult = `${errorMessage}
					<div style="margin-top: 10px; font-size: 12px; color: #666;">
						<strong>故障排除建议：</strong><br>
						1. 确保MCP服务器正在运行 (端口8080)<br>
						2. 确保MCP客户端正在运行 (端口8081)<br>
						3. 检查网络连接<br>
						4. 刷新页面重新连接
					</div>`;
					this.showResult = true;
				} finally {
					this.hideLoading();
				}
			},

			// 加载动画管理
			showLoadingWithMessages() {
				let currentIndex = 0;
				this.updateLoadingMessage(this.loadingMessages[currentIndex]);

				this.loadingInterval = setInterval(() => {
					currentIndex = (currentIndex + 1) % this.loadingMessages.length;
					this.updateLoadingMessage(this.loadingMessages[currentIndex]);
				}, 1500);
			},

			updateLoadingMessage(message) {
				this.loadingMessage = message.main;
				this.loadingSubMessage = message.sub;
			},

			hideLoading() {
				this.isLoading = false;
				if (this.loadingInterval) {
					clearInterval(this.loadingInterval);
					this.loadingInterval = null;
				}
			},

			// 历史记录管理
			addToHistory(query) {
				// 避免重复
				this.searchHistory = this.searchHistory.filter(item => item !== query);

				// 添加到开头
				this.searchHistory.unshift(query);

				// 限制历史记录数量
				if (this.searchHistory.length > 10) {
					this.searchHistory = this.searchHistory.slice(0, 10);
				}

				// 保存到本地存储
				uni.setStorageSync('searchHistory', JSON.stringify(this.searchHistory));
			},

			loadSearchHistory() {
				try {
					const history = uni.getStorageSync('searchHistory');
					if (history) {
						this.searchHistory = JSON.parse(history);
					}
				} catch (error) {
					console.error('加载搜索历史失败:', error);
				}
			},

			// 键盘事件
			onKeyPress(e) {
				if (e.keyCode === 13) { // Enter键
					this.performSearch();
				}
			},

			// 语音识别功能（简化版，uniapp环境下可能需要特殊处理）
			toggleVoiceRecognition() {
				if (this.isRecording) {
					this.stopVoiceRecognition();
				} else {
					this.startVoiceRecognition();
				}
			},

			startVoiceRecognition() {
				const systemInfo = uni.getSystemInfoSync();
				const platform = systemInfo.platform;
				// #ifdef H5
				if (typeof window !== 'undefined' && (window.SpeechRecognition || window.webkitSpeechRecognition)) {
					this.isRecording = true;
					const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
					this._recognition = new SpeechRecognition();
					this._recognition.lang = 'zh-CN';
					this._recognition.continuous = false;
					this._recognition.interimResults = false;
					this._recognition.onresult = (event) => {
						const text = event.results[0][0].transcript;
						this.searchQuery = text;
						this.stopVoiceRecognition();
						this.performSearch();
					};
					this._recognition.onerror = (event) => {
						this.stopVoiceRecognition();
						uni.showToast({ title: '语音识别失败', icon: 'none' });
					};
					this._recognition.onend = () => {
						this.stopVoiceRecognition();
					};
					this._recognition.start();
					// 启动波形动画
					this.$nextTick(() => { this._startWaveAnim(); });
					return;
				}
				// #endif
				// #ifdef MP-WEIXIN
				if (platform === 'devtools' || platform === 'android' || platform === 'ios') {
					this.isRecording = true;
					const plugin = requirePlugin('WechatSI');
					plugin.getRecordRecognitionManager().start({
						lang: 'zh_CN',
						duration: 60000
					});
					plugin.getRecordRecognitionManager().onRecognize = (res) => {
						if (res.result) {
							this.searchQuery = res.result;
							this.stopVoiceRecognition();
							this.performSearch();
						}
					};
					plugin.getRecordRecognitionManager().onStop = (res) => {
						if (res.result) {
							this.searchQuery = res.result;
							this.performSearch();
						}
						this.stopVoiceRecognition();
					};
					plugin.getRecordRecognitionManager().onError = (res) => {
						this.stopVoiceRecognition();
						uni.showToast({ title: '语音识别失败', icon: 'none' });
					};
					return;
				}
				// #endif
				// 其他平台
				uni.showToast({
					title: '当前平台暂不支持语音识别',
					icon: 'none',
					duration: 2000
				});
			},

			stopVoiceRecognition() {
				this.isRecording = false;
				// #ifdef H5
				if (this._recognition) {
					this._recognition.stop();
					this._recognition = null;
				}
				if (this._waveAnimId) {
					cancelAnimationFrame(this._waveAnimId);
					this._waveAnimId = null;
					const canvas = document.getElementById('voiceWaveCanvas');
					if (canvas) {
						const ctx = canvas.getContext('2d');
						ctx.clearRect(0, 0, canvas.width, canvas.height);
					}
				}
				// #endif
				// #ifdef MP-WEIXIN
				try {
					const plugin = requirePlugin('WechatSI');
					plugin.getRecordRecognitionManager().stop();
				} catch (e) {}
				// #endif
			},

			// H5端波形动画
			_startWaveAnim() {
				const canvas = document.getElementById('voiceWaveCanvas');
				if (!canvas) return;
				const ctx = canvas.getContext('2d');
				const W = canvas.width = 120;
				const H = canvas.height = 40;
				let t = 0;
				const draw = () => {
					ctx.clearRect(0, 0, W, H);
					ctx.save();
					ctx.strokeStyle = '#4A90E2';
					ctx.lineWidth = 3;
					ctx.beginPath();
					for (let x = 0; x <= W; x += 2) {
						const y = H/2 + Math.sin((x/12) + t) * (10 + 8*Math.sin(t/2));
						if (x === 0) ctx.moveTo(x, y);
						else ctx.lineTo(x, y);
					}
					ctx.stroke();
					ctx.restore();
					t += 0.08;
					if (this.isRecording) this._waveAnimId = requestAnimationFrame(draw);
				};
				draw();
			},
		},

		mounted() {
			console.log('AI智能问数页面已加载');
			console.log('MCP API地址:', this.mcpApiBase);
			// 判断平台
			this.isH5 = typeof window !== 'undefined' && !!window.SpeechRecognition;
		},

		onLoad(options) {
			// 自动建立MCP连接
			this.connectToMCP();

			// 加载搜索历史
			this.loadSearchHistory();
		},

		onUnload() {
			// 清理定时器
			if (this.loadingInterval) {
				clearInterval(this.loadingInterval);
			}

			// 停止语音识别
			if (this.isRecording) {
				this.stopVoiceRecognition();
			}
		}
	}
</script>

<style lang="scss" scoped>
	.ai-query-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 20px;
	}

	.connection-status {
		position: fixed;
		top: 60px;
		right: 20px;
		padding: 8px 16px;
		border-radius: 20px;
		font-size: 12px;
		font-weight: 500;
		z-index: 1000;
		transition: all 0.3s ease;

		&.connected {
			background: #d4edda;
			color: #155724;
			border: 1px solid #c3e6cb;
		}

		&.disconnected {
			background: #f8d7da;
			color: #721c24;
			border: 1px solid #f5c6cb;
		}

		&.connecting {
			background: #fff3cd;
			color: #856404;
			border: 1px solid #ffeaa7;
		}
	}

	.container {
		max-width: 800px;
		margin: 0 auto;
		background: rgba(255, 255, 255, 0.95);
		border-radius: 20px;
		padding: 40px;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
		backdrop-filter: blur(10px);
	}

	.title {
		display: block;
		text-align: center;
		font-size: 32px;
		font-weight: 600;
		color: #4A90E2;
		margin-bottom: 40px;
		letter-spacing: 2px;
	}

	.search-container {
		display: flex;
		align-items: center;
		background: #fff;
		border-radius: 50px;
		padding: 8px 16px;
		box-shadow: 0 4px 16px rgba(74, 144, 226, 0.08);
		margin-bottom: 40px;
		position: relative;
		min-height: 56px;
	}

	.search-input {
		flex: 1;
		min-width: 0;
		border: none;
		outline: none;
		font-size: 16px;
		background: transparent;
		padding: 8px 0 8px 0;
		margin-right: 12px;
		box-shadow: none;
		border-radius: 0;
	}

	.search-btn {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		border: none;
		background: #e6edfa;
		color: #4A90E2;
		margin-left: 8px;
		transition: background 0.2s, color 0.2s;
		font-size: 18px;
		cursor: pointer;
		box-shadow: 0 2px 8px rgba(74, 144, 226, 0.08);
	}

	.search-btn:active, .search-btn:hover {
		background: #4A90E2;
		color: #fff;
	}

	.search-btn.voice-btn {
		background: #fff;
		border: 2px solid #4A90E2;
		color: #4A90E2;
	}

	.search-btn.voice-btn.recording {
		background: #4A90E2;
		color: #fff;
	}

	.voice-wave-canvas {
		position: absolute;
		right: 100px;
		top: 50%;
		transform: translateY(-50%);
		z-index: 2;
		background: transparent;
		border-radius: 8px;
		pointer-events: none;
		height: 40px;
		width: 120px;
		display: block;
	}

	.section {
		margin-bottom: 30px;
	}

	.section-title {
		display: block;
		font-size: 16px;
		color: #666;
		margin-bottom: 15px;
		font-weight: 500;
	}

	.tags-container {
		display: flex;
		flex-wrap: wrap;
		gap: 10px;
	}

	.tag {
		padding: 8px 16px;
		background: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 20px;
		font-size: 14px;
		color: #495057;
		cursor: pointer;
		transition: all 0.3s ease;
		white-space: nowrap;

		&:hover {
			background: #4A90E2;
			color: white;
			border-color: #4A90E2;
			transform: translateY(-2px);
		}
	}

	.history-container {
		display: flex;
		flex-direction: column;
		gap: 8px;
	}

	.history-item {
		display: flex;
		align-items: center;
		padding: 8px 16px;
		background: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 20px;
		font-size: 14px;
		color: #495057;
		cursor: pointer;
		transition: all 0.3s ease;

		&:hover {
			background: #e3f2fd;
			border-color: #4A90E2;
		}

		&::before {
			content: "🕐";
			margin-right: 8px;
		}
	}

	.loading {
		text-align: center;
		padding: 25px;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 15px;
		margin: 20px 0;
		color: white;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.2);
	}

	.loading-content {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		gap: 15px;
	}

	.spinner {
		width: 40px;
		height: 40px;
		border: 4px solid rgba(255, 255, 255, 0.3);
		border-top: 4px solid white;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	.loading-text {
		font-size: 16px;
		font-weight: 500;
		letter-spacing: 0.5px;
	}

	.loading-sub-text {
		font-size: 12px;
		opacity: 0.8;
	}

	.loading-dots {
		display: inline-block;
		animation: dots 1.5s infinite;
	}

	.result-container {
		margin: 20px 0;
		padding: 25px;
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		border-radius: 15px;
		border-left: 4px solid #4A90E2;
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
		border: 1px solid #e9ecef;
		position: relative;
		overflow: hidden;

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 3px;
			background: linear-gradient(90deg, #4A90E2, #667eea, #764ba2);
		}
	}

	.result-title {
		display: block;
		font-size: 18px;
		font-weight: 600;
		color: #333;
		margin-bottom: 15px;
	}

	.result-content {
		font-size: 14px;
		line-height: 1.6;
		color: #666;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	@keyframes dots {

		0%,
		20% {
			content: '';
		}

		40% {
			content: '.';
		}

		60% {
			content: '..';
		}

		80%,
		100% {
			content: '...';
		}
	}

	/* 响应式设计 */
	@media (max-width: 768px) {
		.connection-status {
			top: 44px;
			padding: 6px 10px;
			font-size: 11px;
		}
		.container {
			padding: 20px;
			margin: 10px;
		}
		
		.title {
			font-size: 24px;
		}
		
		.tags-container {
			gap: 8px;
		}
		
		.tag {
			font-size: 12px;
			padding: 6px 12px;
		}
	}

	.search-btn.voice-btn.recording {
		animation: voice-breath 1.2s infinite alternate;
		box-shadow: 0 0 0 8px rgba(74,144,226,0.12);
	}

	@keyframes voice-breath {
		0% { transform: scale(1); box-shadow: 0 0 0 8px rgba(74,144,226,0.12); }
		100% { transform: scale(1.12); box-shadow: 0 0 0 16px rgba(74,144,226,0.18); }
	}
</style>