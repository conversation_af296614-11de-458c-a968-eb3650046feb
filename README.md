### 小羽佳-AI搜索-前端（uni-app项目）
## 一、项目说明
本项目使用基于 `Vue` 的 `uni-app` 框架来实现
主要用于小羽佳相关数据报表AI搜索等相关使用场景。


## 二、项目预览


## 三、主要功能
### 1、xxx



## 四、技术栈
### 1、后端
SpringAI + Maven

### 2、前端
uni-app + Vue2.0 + uni-ui + uView2.X


## 五、开发环境
操作系统：Windows 11 
  
JDK： jdk-1.8.0_201 
  
SQL Server：Microsoft SQL Server 2016 - 13.0.5830.85 (X64) （或更高版本）
  
Nodejs：v13.14.0  

Tomcat：Tomcat 9.0.37  

IDE：IntelliJ IDEA 2020 / HBuild X 


## 六、项目结构
api：用于请求相关api的封装 

common：工具类封装 

components：通用组件封装 

functions：微信云函数封装 

node_modules：项目相关依赖 

pages：主包页面文件 

pages-mine：个人中心分包页面文件 

static：静态资源文件 

uni_modules：uni-app自带组件 

unpackage：uni-app项目打包文件 

App.vue：页面主入口 

main.js：项目配置文件 

pages.json：页面配置文件 

## 七、启动与部署
- **启动客户端**：
```js
npm install // 安装依赖

npm run server // 启动项目
```

- **打包与部署**：
```js
npm run build // 项目打包
```




