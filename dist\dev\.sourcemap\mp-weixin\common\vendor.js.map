{"version": 3, "file": "common/vendor.js", "mappings": ";;;;;;;;AAAA,SAASA,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAI,CAACC,CAAC,GAAGF,CAAC,EAAE,IAAI,CAACG,CAAC,GAAGF,CAAC;AACxB;AACAG,MAAM,CAACC,OAAO,GAAGN,cAAc,EAAEK,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,C;;;;;;;;;;;;;;;ACH7G,SAASE,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC/B,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGD,CAAC,CAACE,MAAM,MAAMD,CAAC,GAAGD,CAAC,CAACE,MAAM,CAAC;EAC7C,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEW,CAAC,GAAGC,KAAK,CAACH,CAAC,CAAC,EAAET,CAAC,GAAGS,CAAC,EAAET,CAAC,EAAE,EAAEW,CAAC,CAACX,CAAC,CAAC,GAAGQ,CAAC,CAACR,CAAC,CAAC;EACrD,OAAOW,CAAC;AACV;;;;;;;;;;;;;;;;ACJA,SAASG,eAAeA,CAACN,CAAC,EAAE;EAC1B,IAAII,KAAK,CAACG,OAAO,CAACP,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;;;;;;;;;;;;;;;;;ACFqD;AACrD,SAASS,kBAAkBA,CAACT,CAAC,EAAE;EAC7B,IAAII,KAAK,CAACG,OAAO,CAACP,CAAC,CAAC,EAAE,OAAOQ,gEAAgB,CAACR,CAAC,CAAC;AAClD;;;;;;;;;;;;;;;;ACHA,SAASU,kBAAkBA,CAACP,CAAC,EAAEQ,CAAC,EAAEnB,CAAC,EAAEQ,CAAC,EAAEY,CAAC,EAAEX,CAAC,EAAEY,CAAC,EAAE;EAC/C,IAAI;IACF,IAAIC,CAAC,GAAGX,CAAC,CAACF,CAAC,CAAC,CAACY,CAAC,CAAC;MACbE,CAAC,GAAGD,CAAC,CAACE,KAAK;EACf,CAAC,CAAC,OAAOb,CAAC,EAAE;IACV,OAAO,KAAKX,CAAC,CAACW,CAAC,CAAC;EAClB;EACAW,CAAC,CAACG,IAAI,GAAGN,CAAC,CAACI,CAAC,CAAC,GAAGG,OAAO,CAACC,OAAO,CAACJ,CAAC,CAAC,CAACK,IAAI,CAACpB,CAAC,EAAEY,CAAC,CAAC;AAC/C;AACA,SAASS,iBAAiBA,CAAClB,CAAC,EAAE;EAC5B,OAAO,YAAY;IACjB,IAAIQ,CAAC,GAAG,IAAI;MACVnB,CAAC,GAAG8B,SAAS;IACf,OAAO,IAAIJ,OAAO,CAAC,UAAUlB,CAAC,EAAEY,CAAC,EAAE;MACjC,IAAIX,CAAC,GAAGE,CAAC,CAACoB,KAAK,CAACZ,CAAC,EAAEnB,CAAC,CAAC;MACrB,SAASgC,KAAKA,CAACrB,CAAC,EAAE;QAChBO,kBAAkB,CAACT,CAAC,EAAED,CAAC,EAAEY,CAAC,EAAEY,KAAK,EAAEC,MAAM,EAAE,MAAM,EAAEtB,CAAC,CAAC;MACvD;MACA,SAASsB,MAAMA,CAACtB,CAAC,EAAE;QACjBO,kBAAkB,CAACT,CAAC,EAAED,CAAC,EAAEY,CAAC,EAAEY,KAAK,EAAEC,MAAM,EAAE,OAAO,EAAEtB,CAAC,CAAC;MACxD;MACAqB,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC,CAAC;EACJ,CAAC;AACH;;;;;;;;;;;;;;;;ACxBA,SAASE,eAAeA,CAACzB,CAAC,EAAEE,CAAC,EAAE;EAC7B,IAAI,EAAEF,CAAC,YAAYE,CAAC,CAAC,EAAE,MAAM,IAAIwB,SAAS,CAAC,mCAAmC,CAAC;AACjF;;;;;;;;;;;;;;;;;;ACFqE;AACpB;AACjD,SAASG,UAAUA,CAACnB,CAAC,EAAEnB,CAAC,EAAEQ,CAAC,EAAE;EAC3B,IAAI4B,wEAAwB,CAAC,CAAC,EAAE,OAAOG,OAAO,CAACC,SAAS,CAACT,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;EAC/E,IAAIV,CAAC,GAAG,CAAC,IAAI,CAAC;EACdA,CAAC,CAACqB,IAAI,CAACV,KAAK,CAACX,CAAC,EAAEpB,CAAC,CAAC;EAClB,IAAI0C,CAAC,GAAG,KAAKvB,CAAC,CAACwB,IAAI,CAACZ,KAAK,CAACZ,CAAC,EAAEC,CAAC,CAAC,EAAE,CAAC;EAClC,OAAOZ,CAAC,IAAI6B,8DAAc,CAACK,CAAC,EAAElC,CAAC,CAACoC,SAAS,CAAC,EAAEF,CAAC;AAC/C;;;;;;;;;;;;;;;;;ACR+C;AAC/C,SAASI,iBAAiBA,CAAC9C,CAAC,EAAEQ,CAAC,EAAE;EAC/B,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,CAAC,CAACE,MAAM,EAAES,CAAC,EAAE,EAAE;IACjC,IAAIC,CAAC,GAAGZ,CAAC,CAACW,CAAC,CAAC;IACZC,CAAC,CAAC2B,UAAU,GAAG3B,CAAC,CAAC2B,UAAU,IAAI,CAAC,CAAC,EAAE3B,CAAC,CAAC4B,YAAY,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI5B,CAAC,KAAKA,CAAC,CAAC6B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAEC,MAAM,CAACC,cAAc,CAACnD,CAAC,EAAE6C,6DAAa,CAACzB,CAAC,CAACgC,GAAG,CAAC,EAAEhC,CAAC,CAAC;EAC9I;AACF;AACA,SAASiC,YAAYA,CAACrD,CAAC,EAAEQ,CAAC,EAAEW,CAAC,EAAE;EAC7B,OAAOX,CAAC,IAAIsC,iBAAiB,CAAC9C,CAAC,CAAC4C,SAAS,EAAEpC,CAAC,CAAC,EAAEW,CAAC,IAAI2B,iBAAiB,CAAC9C,CAAC,EAAEmB,CAAC,CAAC,EAAE+B,MAAM,CAACC,cAAc,CAACnD,CAAC,EAAE,WAAW,EAAE;IACjHiD,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,EAAEjD,CAAC;AACP;;;;;;;;;;;;;;;;;ACX+C;AAC/C,SAASsD,eAAeA,CAACtD,CAAC,EAAEQ,CAAC,EAAEW,CAAC,EAAE;EAChC,OAAO,CAACX,CAAC,GAAGqC,6DAAa,CAACrC,CAAC,CAAC,KAAKR,CAAC,GAAGkD,MAAM,CAACC,cAAc,CAACnD,CAAC,EAAEQ,CAAC,EAAE;IAC/DgB,KAAK,EAAEL,CAAC;IACR4B,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,CAAC,CAAC;IAChBC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,GAAGjD,CAAC,CAACQ,CAAC,CAAC,GAAGW,CAAC,EAAEnB,CAAC;AAClB;;;;;;;;;;;;;;;;ACRA,SAASuD,yBAAyBA,CAAA,EAAG;EACnC,IAAI;IACF,IAAIpC,CAAC,GAAG,CAACqC,OAAO,CAACZ,SAAS,CAACa,OAAO,CAACC,IAAI,CAACnB,OAAO,CAACC,SAAS,CAACgB,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EACzF,CAAC,CAAC,OAAOrC,CAAC,EAAE,CAAC;EACb,OAAO,CAACoC,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IACvE,OAAO,CAAC,CAACpC,CAAC;EACZ,CAAC,EAAE,CAAC;AACN;;;;;;;;;;;;;;;;ACPA,SAASwC,gBAAgBA,CAACnD,CAAC,EAAE;EAC3B,IAAI,WAAW,IAAI,OAAOoD,MAAM,IAAI,IAAI,IAAIpD,CAAC,CAACoD,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIrD,CAAC,CAAC,YAAY,CAAC,EAAE,OAAOI,KAAK,CAACkD,IAAI,CAACtD,CAAC,CAAC;AACjH;;;;;;;;;;;;;;;;ACFA,SAASuD,qBAAqBA,CAACvD,CAAC,EAAEwD,CAAC,EAAE;EACnC,IAAI7C,CAAC,GAAG,IAAI,IAAIX,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOoD,MAAM,IAAIpD,CAAC,CAACoD,MAAM,CAACC,QAAQ,CAAC,IAAIrD,CAAC,CAAC,YAAY,CAAC;EAChG,IAAI,IAAI,IAAIW,CAAC,EAAE;IACb,IAAInB,CAAC;MACHW,CAAC;MACDW,CAAC;MACDC,CAAC;MACDd,CAAC,GAAG,EAAE;MACNwD,CAAC,GAAG,CAAC,CAAC;MACN7C,CAAC,GAAG,CAAC,CAAC;IACR,IAAI;MACF,IAAIE,CAAC,GAAG,CAACH,CAAC,GAAGA,CAAC,CAACuC,IAAI,CAAClD,CAAC,CAAC,EAAE0D,IAAI,EAAE,CAAC,KAAKF,CAAC,EAAE;QACrC,IAAId,MAAM,CAAC/B,CAAC,CAAC,KAAKA,CAAC,EAAE;QACrB8C,CAAC,GAAG,CAAC,CAAC;MACR,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACjE,CAAC,GAAGsB,CAAC,CAACoC,IAAI,CAACvC,CAAC,CAAC,EAAEM,IAAI,CAAC,KAAKhB,CAAC,CAACgC,IAAI,CAACzC,CAAC,CAACwB,KAAK,CAAC,EAAEf,CAAC,CAACC,MAAM,KAAKsD,CAAC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,CAAC;IACzF,CAAC,CAAC,OAAOzD,CAAC,EAAE;MACVY,CAAC,GAAG,CAAC,CAAC,EAAET,CAAC,GAAGH,CAAC;IACf,CAAC,SAAS;MACR,IAAI;QACF,IAAI,CAACyD,CAAC,IAAI,IAAI,IAAI9C,CAAC,CAAC,QAAQ,CAAC,KAAKI,CAAC,GAAGJ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE+B,MAAM,CAAC3B,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MACzE,CAAC,SAAS;QACR,IAAIH,CAAC,EAAE,MAAMT,CAAC;MAChB;IACF;IACA,OAAOF,CAAC;EACV;AACF;;;;;;;;;;;;;;;;AC1BA,SAAS0D,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAIhC,SAAS,CAAC,2IAA2I,CAAC;AAClK;;;;;;;;;;;;;;;;ACFA,SAASiC,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAIjC,SAAS,CAAC,sIAAsI,CAAC;AAC7J;;;;;;;;;;;;;;;;ACFA,SAASkC,eAAeA,CAAClD,CAAC,EAAEnB,CAAC,EAAE;EAC7B,OAAOqE,eAAe,GAAGnB,MAAM,CAACb,cAAc,GAAGa,MAAM,CAACb,cAAc,CAACM,IAAI,CAAC,CAAC,GAAG,UAAUxB,CAAC,EAAEnB,CAAC,EAAE;IAC9F,OAAOmB,CAAC,CAACmD,SAAS,GAAGtE,CAAC,EAAEmB,CAAC;EAC3B,CAAC,EAAEkD,eAAe,CAAClD,CAAC,EAAEnB,CAAC,CAAC;AAC1B;;;;;;;;;;;;;;;;;;;;ACJiD;AACY;AACY;AACtB;AACnD,SAAS2E,cAAcA,CAACnE,CAAC,EAAER,CAAC,EAAE;EAC5B,OAAOuE,8DAAc,CAAC/D,CAAC,CAAC,IAAIgE,oEAAoB,CAAChE,CAAC,EAAER,CAAC,CAAC,IAAIyE,0EAA0B,CAACjE,CAAC,EAAER,CAAC,CAAC,IAAI0E,+DAAe,CAAC,CAAC;AACjH;;;;;;;;;;;;;;;;;;;;ACNuD;AACJ;AACsB;AAClB;AACvD,SAASK,kBAAkBA,CAACvE,CAAC,EAAE;EAC7B,OAAOoE,iEAAiB,CAACpE,CAAC,CAAC,IAAIqE,+DAAe,CAACrE,CAAC,CAAC,IAAIiE,0EAA0B,CAACjE,CAAC,CAAC,IAAIsE,iEAAiB,CAAC,CAAC;AAC3G;;;;;;;;;;;;;;;;;ACNkC;AAClC,SAASG,WAAWA,CAAC9D,CAAC,EAAEX,CAAC,EAAE;EACzB,IAAI,QAAQ,IAAIwE,sDAAO,CAAC7D,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAC1C,IAAInB,CAAC,GAAGmB,CAAC,CAACyC,MAAM,CAACqB,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKjF,CAAC,EAAE;IAChB,IAAIsB,CAAC,GAAGtB,CAAC,CAAC0D,IAAI,CAACvC,CAAC,EAAEX,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAIwE,sDAAO,CAAC1D,CAAC,CAAC,EAAE,OAAOA,CAAC;IACpC,MAAM,IAAIa,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAK3B,CAAC,GAAG0E,MAAM,GAAGC,MAAM,EAAEhE,CAAC,CAAC;AAC9C;;;;;;;;;;;;;;;;;;ACVkC;AACS;AAC3C,SAAS0B,aAAaA,CAAC1B,CAAC,EAAE;EACxB,IAAIG,CAAC,GAAG2D,2DAAW,CAAC9D,CAAC,EAAE,QAAQ,CAAC;EAChC,OAAO,QAAQ,IAAI6D,sDAAO,CAAC1D,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC5C;;;;;;;;;;;;;;;;ACLA,SAAS0D,OAAOA,CAAC5D,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAO4D,OAAO,GAAG,UAAU,IAAI,OAAOpB,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUzC,CAAC,EAAE;IAChG,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOwC,MAAM,IAAIxC,CAAC,CAACgE,WAAW,KAAKxB,MAAM,IAAIxC,CAAC,KAAKwC,MAAM,CAAChB,SAAS,GAAG,QAAQ,GAAG,OAAOxB,CAAC;EACrH,CAAC,EAAE4D,OAAO,CAAC5D,CAAC,CAAC;AACf;;;;;;;;;;;;;;;;;ACRqD;AACrD,SAASiE,2BAA2BA,CAAC7E,CAAC,EAAEC,CAAC,EAAE;EACzC,IAAID,CAAC,EAAE;IACL,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOQ,gEAAgB,CAACR,CAAC,EAAEC,CAAC,CAAC;IACvD,IAAIU,CAAC,GAAG,CAAC,CAAC,CAACmE,QAAQ,CAAC5B,IAAI,CAAClD,CAAC,CAAC,CAAC+E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,OAAO,QAAQ,KAAKpE,CAAC,IAAIX,CAAC,CAAC4E,WAAW,KAAKjE,CAAC,GAAGX,CAAC,CAAC4E,WAAW,CAACI,IAAI,CAAC,EAAE,KAAK,KAAKrE,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGP,KAAK,CAACkD,IAAI,CAACtD,CAAC,CAAC,GAAG,WAAW,KAAKW,CAAC,IAAI,0CAA0C,CAACsE,IAAI,CAACtE,CAAC,CAAC,GAAGH,gEAAgB,CAACR,CAAC,EAAEC,CAAC,CAAC,GAAG,KAAK,CAAC;EAC5N;AACF;;;;;;;;;;;ACPA,IAAIiF,iBAAiB,GAAGC,mBAAO,CAAC,0FAAwB,CAAC;AACzD,SAASC,YAAYA,CAAA,EAAG;EACtB;EACA,IAAI5F,CAAC;IACHmB,CAAC;IACDX,CAAC,GAAG,UAAU,IAAI,OAAOoD,MAAM,GAAGA,MAAM,GAAG,CAAC,CAAC;IAC7CjD,CAAC,GAAGH,CAAC,CAACqD,QAAQ,IAAI,YAAY;IAC9BzC,CAAC,GAAGZ,CAAC,CAACqF,WAAW,IAAI,eAAe;EACtC,SAASvE,CAACA,CAACd,CAAC,EAAEG,CAAC,EAAES,CAAC,EAAEE,CAAC,EAAE;IACrB,IAAID,CAAC,GAAGV,CAAC,IAAIA,CAAC,CAACiC,SAAS,YAAYkD,SAAS,GAAGnF,CAAC,GAAGmF,SAAS;MAC3DvE,CAAC,GAAG2B,MAAM,CAAC6C,MAAM,CAAC1E,CAAC,CAACuB,SAAS,CAAC;IAChC,OAAO8C,iBAAiB,CAACnE,CAAC,EAAE,SAAS,EAAE,UAAUf,CAAC,EAAEG,CAAC,EAAES,CAAC,EAAE;MACxD,IAAIE,CAAC;QACHD,CAAC;QACDE,CAAC;QACD0C,CAAC,GAAG,CAAC;QACLvB,CAAC,GAAGtB,CAAC,IAAI,EAAE;QACX4E,CAAC,GAAG,CAAC,CAAC;QACNC,CAAC,GAAG;UACFvD,CAAC,EAAE,CAAC;UACJ/B,CAAC,EAAE,CAAC;UACJT,CAAC,EAAEF,CAAC;UACJS,CAAC,EAAER,CAAC;UACJgE,CAAC,EAAEhE,CAAC,CAAC0C,IAAI,CAAC3C,CAAC,EAAE,CAAC,CAAC;UACfC,CAAC,EAAE,SAASA,CAACA,CAACkB,CAAC,EAAEX,CAAC,EAAE;YAClB,OAAOc,CAAC,GAAGH,CAAC,EAAEE,CAAC,GAAG,CAAC,EAAEE,CAAC,GAAGvB,CAAC,EAAEiG,CAAC,CAACtF,CAAC,GAAGH,CAAC,EAAEC,CAAC;UACxC;QACF,CAAC;MACH,SAASR,CAACA,CAACO,CAAC,EAAEG,CAAC,EAAE;QACf,KAAKU,CAAC,GAAGb,CAAC,EAAEe,CAAC,GAAGZ,CAAC,EAAEQ,CAAC,GAAG,CAAC,EAAE,CAAC6E,CAAC,IAAI/B,CAAC,IAAI,CAAC7C,CAAC,IAAID,CAAC,GAAGuB,CAAC,CAAChC,MAAM,EAAES,CAAC,EAAE,EAAE;UAC5D,IAAIC,CAAC;YACHE,CAAC,GAAGoB,CAAC,CAACvB,CAAC,CAAC;YACRlB,CAAC,GAAGgG,CAAC,CAACvD,CAAC;YACPsB,CAAC,GAAG1C,CAAC,CAAC,CAAC,CAAC;UACVd,CAAC,GAAG,CAAC,GAAG,CAACY,CAAC,GAAG4C,CAAC,KAAKrD,CAAC,MAAMY,CAAC,GAAGD,CAAC,CAAC,CAACD,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAID,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGtB,CAAC,CAAC,GAAGsB,CAAC,CAAC,CAAC,CAAC,IAAIrB,CAAC,KAAK,CAACmB,CAAC,GAAGZ,CAAC,GAAG,CAAC,IAAIP,CAAC,GAAGqB,CAAC,CAAC,CAAC,CAAC,KAAKD,CAAC,GAAG,CAAC,EAAE4E,CAAC,CAAC/F,CAAC,GAAGS,CAAC,EAAEsF,CAAC,CAACtF,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC,IAAIrB,CAAC,GAAG+D,CAAC,KAAK5C,CAAC,GAAGZ,CAAC,GAAG,CAAC,IAAIc,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,IAAIA,CAAC,GAAGqD,CAAC,CAAC,KAAK1C,CAAC,CAAC,CAAC,CAAC,GAAGd,CAAC,EAAEc,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,EAAEsF,CAAC,CAACtF,CAAC,GAAGqD,CAAC,EAAE3C,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5O;QACA,IAAID,CAAC,IAAIZ,CAAC,GAAG,CAAC,EAAE,OAAOC,CAAC;QACxB,MAAMuF,CAAC,GAAG,CAAC,CAAC,EAAErF,CAAC;MACjB;MACA,OAAO,UAAUS,CAAC,EAAEsB,CAAC,EAAEsB,CAAC,EAAE;QACxB,IAAIC,CAAC,GAAG,CAAC,EAAE,MAAM9B,SAAS,CAAC,8BAA8B,CAAC;QAC1D,KAAK6D,CAAC,IAAI,CAAC,KAAKtD,CAAC,IAAIzC,CAAC,CAACyC,CAAC,EAAEsB,CAAC,CAAC,EAAE3C,CAAC,GAAGqB,CAAC,EAAEnB,CAAC,GAAGyC,CAAC,EAAE,CAAC7C,CAAC,GAAGE,CAAC,GAAG,CAAC,GAAGrB,CAAC,GAAGuB,CAAC,KAAK,CAACyE,CAAC,GAAG;UACtE1E,CAAC,KAAKD,CAAC,GAAGA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,KAAK4E,CAAC,CAACtF,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEV,CAAC,CAACoB,CAAC,EAAEE,CAAC,CAAC,IAAI0E,CAAC,CAACtF,CAAC,GAAGY,CAAC,GAAG0E,CAAC,CAAC/F,CAAC,GAAGqB,CAAC,CAAC;UACrE,IAAI;YACF,IAAI0C,CAAC,GAAG,CAAC,EAAE3C,CAAC,EAAE;cACZ,IAAID,CAAC,KAAKD,CAAC,GAAG,MAAM,CAAC,EAAED,CAAC,GAAGG,CAAC,CAACF,CAAC,CAAC,EAAE;gBAC/B,IAAI,EAAED,CAAC,GAAGA,CAAC,CAACuC,IAAI,CAACpC,CAAC,EAAEC,CAAC,CAAC,CAAC,EAAE,MAAMY,SAAS,CAAC,kCAAkC,CAAC;gBAC5E,IAAI,CAAChB,CAAC,CAACM,IAAI,EAAE,OAAON,CAAC;gBACrBI,CAAC,GAAGJ,CAAC,CAACK,KAAK,EAAEH,CAAC,GAAG,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;cAC/B,CAAC,MAAM,CAAC,KAAKA,CAAC,KAAKF,CAAC,GAAGG,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAIH,CAAC,CAACuC,IAAI,CAACpC,CAAC,CAAC,EAAED,CAAC,GAAG,CAAC,KAAKE,CAAC,GAAGY,SAAS,CAAC,mCAAmC,GAAGf,CAAC,GAAG,UAAU,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC;cACvIC,CAAC,GAAGtB,CAAC;YACP,CAAC,MAAM,IAAI,CAACmB,CAAC,GAAG,CAAC6E,CAAC,GAAGC,CAAC,CAACtF,CAAC,GAAG,CAAC,IAAIY,CAAC,GAAGf,CAAC,CAACkD,IAAI,CAAC/C,CAAC,EAAEsF,CAAC,CAAC,MAAMxF,CAAC,EAAE;UAC3D,CAAC,CAAC,OAAOU,CAAC,EAAE;YACVG,CAAC,GAAGtB,CAAC,EAAEqB,CAAC,GAAG,CAAC,EAAEE,CAAC,GAAGJ,CAAC;UACrB,CAAC,SAAS;YACR8C,CAAC,GAAG,CAAC;UACP;QACF;QACA,OAAO;UACLzC,KAAK,EAAEL,CAAC;UACRM,IAAI,EAAEuE;QACR,CAAC;MACH,CAAC;IACH,CAAC,CAACxF,CAAC,EAAEY,CAAC,EAAEE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEC,CAAC;EACpB;EACA,IAAId,CAAC,GAAG,CAAC,CAAC;EACV,SAASqF,SAASA,CAAA,EAAG,CAAC;EACtB,SAASI,iBAAiBA,CAAA,EAAG,CAAC;EAC9B,SAASC,0BAA0BA,CAAA,EAAG,CAAC;EACvChF,CAAC,GAAG+B,MAAM,CAACkD,cAAc;EACzB,IAAI/E,CAAC,GAAG,EAAE,CAACV,CAAC,CAAC,GAAGQ,CAAC,CAACA,CAAC,CAAC,EAAE,CAACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI+E,iBAAiB,CAACvE,CAAC,GAAG,CAAC,CAAC,EAAER,CAAC,EAAE,YAAY;MACtE,OAAO,IAAI;IACb,CAAC,CAAC,EAAEQ,CAAC,CAAC;IACNI,CAAC,GAAG4E,0BAA0B,CAACvD,SAAS,GAAGkD,SAAS,CAAClD,SAAS,GAAGM,MAAM,CAAC6C,MAAM,CAAC1E,CAAC,CAAC;EACnF,SAAS4C,CAACA,CAACjE,CAAC,EAAE;IACZ,OAAOkD,MAAM,CAACb,cAAc,GAAGa,MAAM,CAACb,cAAc,CAACrC,CAAC,EAAEmG,0BAA0B,CAAC,IAAInG,CAAC,CAACsE,SAAS,GAAG6B,0BAA0B,EAAET,iBAAiB,CAAC1F,CAAC,EAAEoB,CAAC,EAAE,mBAAmB,CAAC,CAAC,EAAEpB,CAAC,CAAC4C,SAAS,GAAGM,MAAM,CAAC6C,MAAM,CAACxE,CAAC,CAAC,EAAEvB,CAAC;EACnN;EACA,OAAOkG,iBAAiB,CAACtD,SAAS,GAAGuD,0BAA0B,EAAET,iBAAiB,CAACnE,CAAC,EAAE,aAAa,EAAE4E,0BAA0B,CAAC,EAAET,iBAAiB,CAACS,0BAA0B,EAAE,aAAa,EAAED,iBAAiB,CAAC,EAAEA,iBAAiB,CAACG,WAAW,GAAG,mBAAmB,EAAEX,iBAAiB,CAACS,0BAA0B,EAAE/E,CAAC,EAAE,mBAAmB,CAAC,EAAEsE,iBAAiB,CAACnE,CAAC,CAAC,EAAEmE,iBAAiB,CAACnE,CAAC,EAAEH,CAAC,EAAE,WAAW,CAAC,EAAEsE,iBAAiB,CAACnE,CAAC,EAAEZ,CAAC,EAAE,YAAY;IAC7a,OAAO,IAAI;EACb,CAAC,CAAC,EAAE+E,iBAAiB,CAACnE,CAAC,EAAE,UAAU,EAAE,YAAY;IAC/C,OAAO,oBAAoB;EAC7B,CAAC,CAAC,EAAE,CAACnB,MAAM,CAACC,OAAO,GAAGuF,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IAC3D,OAAO;MACLU,CAAC,EAAEhF,CAAC;MACJiF,CAAC,EAAEtC;IACL,CAAC;EACH,CAAC,EAAE7D,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,EAAE,CAAC;AACpF;AACAD,MAAM,CAACC,OAAO,GAAGuF,YAAY,EAAExF,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,C;;;;;;;;;;ACxF3G,IAAImG,mBAAmB,GAAGb,mBAAO,CAAC,8FAA0B,CAAC;AAC7D,SAASc,iBAAiBA,CAAC9F,CAAC,EAAEX,CAAC,EAAEQ,CAAC,EAAEW,CAAC,EAAEC,CAAC,EAAE;EACxC,IAAIX,CAAC,GAAG+F,mBAAmB,CAAC7F,CAAC,EAAEX,CAAC,EAAEQ,CAAC,EAAEW,CAAC,EAAEC,CAAC,CAAC;EAC1C,OAAOX,CAAC,CAACyD,IAAI,CAAC,CAAC,CAACtC,IAAI,CAAC,UAAUjB,CAAC,EAAE;IAChC,OAAOA,CAAC,CAACc,IAAI,GAAGd,CAAC,CAACa,KAAK,GAAGf,CAAC,CAACyD,IAAI,CAAC,CAAC;EACpC,CAAC,CAAC;AACJ;AACA9D,MAAM,CAACC,OAAO,GAAGoG,iBAAiB,EAAErG,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,C;;;;;;;;;;ACPhH,IAAIqG,WAAW,GAAGf,mBAAO,CAAC,8EAAkB,CAAC;AAC7C,IAAIgB,wBAAwB,GAAGhB,mBAAO,CAAC,wGAA+B,CAAC;AACvE,SAASiB,oBAAoBA,CAACpG,CAAC,EAAER,CAAC,EAAEmB,CAAC,EAAEC,CAAC,EAAET,CAAC,EAAE;EAC3C,OAAO,IAAIgG,wBAAwB,CAACD,WAAW,CAAC,CAAC,CAACJ,CAAC,CAAC9F,CAAC,EAAER,CAAC,EAAEmB,CAAC,EAAEC,CAAC,CAAC,EAAET,CAAC,IAAIe,OAAO,CAAC;AAChF;AACAtB,MAAM,CAACC,OAAO,GAAGuG,oBAAoB,EAAExG,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,C;;;;;;;;;;ACLnH,IAAIwG,aAAa,GAAGlB,mBAAO,CAAC,kFAAoB,CAAC;AACjD,IAAID,iBAAiB,GAAGC,mBAAO,CAAC,0FAAwB,CAAC;AACzD,SAASmB,aAAaA,CAAC3F,CAAC,EAAEnB,CAAC,EAAE;EAC3B,SAASW,CAACA,CAACH,CAAC,EAAEY,CAAC,EAAEE,CAAC,EAAE2C,CAAC,EAAE;IACrB,IAAI;MACF,IAAI5C,CAAC,GAAGF,CAAC,CAACX,CAAC,CAAC,CAACY,CAAC,CAAC;QACbG,CAAC,GAAGF,CAAC,CAACG,KAAK;MACb,OAAOD,CAAC,YAAYsF,aAAa,GAAG7G,CAAC,CAAC2B,OAAO,CAACJ,CAAC,CAACrB,CAAC,CAAC,CAAC0B,IAAI,CAAC,UAAUT,CAAC,EAAE;QACnER,CAAC,CAAC,MAAM,EAAEQ,CAAC,EAAEG,CAAC,EAAE2C,CAAC,CAAC;MACpB,CAAC,EAAE,UAAU9C,CAAC,EAAE;QACdR,CAAC,CAAC,OAAO,EAAEQ,CAAC,EAAEG,CAAC,EAAE2C,CAAC,CAAC;MACrB,CAAC,CAAC,GAAGjE,CAAC,CAAC2B,OAAO,CAACJ,CAAC,CAAC,CAACK,IAAI,CAAC,UAAUT,CAAC,EAAE;QAClCE,CAAC,CAACG,KAAK,GAAGL,CAAC,EAAEG,CAAC,CAACD,CAAC,CAAC;MACnB,CAAC,EAAE,UAAUF,CAAC,EAAE;QACd,OAAOR,CAAC,CAAC,OAAO,EAAEQ,CAAC,EAAEG,CAAC,EAAE2C,CAAC,CAAC;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO9C,CAAC,EAAE;MACV8C,CAAC,CAAC9C,CAAC,CAAC;IACN;EACF;EACA,IAAIX,CAAC;EACL,IAAI,CAAC0D,IAAI,KAAKwB,iBAAiB,CAACoB,aAAa,CAAClE,SAAS,CAAC,EAAE8C,iBAAiB,CAACoB,aAAa,CAAClE,SAAS,EAAE,UAAU,IAAI,OAAOgB,MAAM,IAAIA,MAAM,CAACmD,aAAa,IAAI,gBAAgB,EAAE,YAAY;IACxL,OAAO,IAAI;EACb,CAAC,CAAC,CAAC,EAAErB,iBAAiB,CAAC,IAAI,EAAE,SAAS,EAAE,UAAUvE,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAE;IACzD,SAAS2C,CAACA,CAAA,EAAG;MACX,OAAO,IAAIjE,CAAC,CAAC,UAAUA,CAAC,EAAEQ,CAAC,EAAE;QAC3BG,CAAC,CAACQ,CAAC,EAAEG,CAAC,EAAEtB,CAAC,EAAEQ,CAAC,CAAC;MACf,CAAC,CAAC;IACJ;IACA,OAAOA,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAACoB,IAAI,CAACqC,CAAC,EAAEA,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC;EACnC,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA7D,MAAM,CAACC,OAAO,GAAGyG,aAAa,EAAE1G,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,C;;;;;;;;;;AChC5G,SAAS2G,kBAAkBA,CAAChH,CAAC,EAAEQ,CAAC,EAAEG,CAAC,EAAEQ,CAAC,EAAE;EACtC,IAAIG,CAAC,GAAG4B,MAAM,CAACC,cAAc;EAC7B,IAAI;IACF7B,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,OAAOtB,CAAC,EAAE;IACVsB,CAAC,GAAG,CAAC;EACP;EACAlB,MAAM,CAACC,OAAO,GAAG2G,kBAAkB,GAAG,SAAStB,iBAAiBA,CAAC1F,CAAC,EAAEQ,CAAC,EAAEG,CAAC,EAAEQ,CAAC,EAAE;IAC3E,IAAIX,CAAC,EAAEc,CAAC,GAAGA,CAAC,CAACtB,CAAC,EAAEQ,CAAC,EAAE;MACjBgB,KAAK,EAAEb,CAAC;MACRoC,UAAU,EAAE,CAAC5B,CAAC;MACd6B,YAAY,EAAE,CAAC7B,CAAC;MAChB8B,QAAQ,EAAE,CAAC9B;IACb,CAAC,CAAC,GAAGnB,CAAC,CAACQ,CAAC,CAAC,GAAGG,CAAC,CAAC,KAAK;MACjB,IAAIS,CAAC,GAAG,SAASA,CAACA,CAACZ,CAAC,EAAEG,CAAC,EAAE;QACvBqG,kBAAkB,CAAChH,CAAC,EAAEQ,CAAC,EAAE,UAAUR,CAAC,EAAE;UACpC,OAAO,IAAI,CAACiH,OAAO,CAACzG,CAAC,EAAEG,CAAC,EAAEX,CAAC,CAAC;QAC9B,CAAC,CAAC;MACJ,CAAC;MACDoB,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAEA,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAEA,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC7C;EACF,CAAC,EAAEhB,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,EAAE2G,kBAAkB,CAAChH,CAAC,EAAEQ,CAAC,EAAEG,CAAC,EAAEQ,CAAC,CAAC;AACjH;AACAf,MAAM,CAACC,OAAO,GAAG2G,kBAAkB,EAAE5G,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,C;;;;;;;;;;ACvBjH,SAAS6G,gBAAgBA,CAAClH,CAAC,EAAE;EAC3B,IAAIW,CAAC,GAAGuC,MAAM,CAAClD,CAAC,CAAC;IACfQ,CAAC,GAAG,EAAE;EACR,KAAK,IAAIW,CAAC,IAAIR,CAAC,EAAEH,CAAC,CAAC2G,OAAO,CAAChG,CAAC,CAAC;EAC7B,OAAO,SAASnB,CAACA,CAAA,EAAG;IAClB,OAAOQ,CAAC,CAACE,MAAM,GAAG,IAAI,CAACS,CAAC,GAAGX,CAAC,CAAC4G,GAAG,CAAC,CAAC,KAAKzG,CAAC,EAAE,OAAOX,CAAC,CAACwB,KAAK,GAAGL,CAAC,EAAEnB,CAAC,CAACyB,IAAI,GAAG,CAAC,CAAC,EAAEzB,CAAC;IAC5E,OAAOA,CAAC,CAACyB,IAAI,GAAG,CAAC,CAAC,EAAEzB,CAAC;EACvB,CAAC;AACH;AACAI,MAAM,CAACC,OAAO,GAAG6G,gBAAgB,EAAE9G,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,C;;;;;;;;;;ACT/G,IAAIwG,aAAa,GAAGlB,mBAAO,CAAC,kFAAoB,CAAC;AACjD,IAAIe,WAAW,GAAGf,mBAAO,CAAC,8EAAkB,CAAC;AAC7C,IAAI0B,gBAAgB,GAAG1B,mBAAO,CAAC,wFAAuB,CAAC;AACvD,IAAIa,mBAAmB,GAAGb,mBAAO,CAAC,8FAA0B,CAAC;AAC7D,IAAIgB,wBAAwB,GAAGhB,mBAAO,CAAC,wGAA+B,CAAC;AACvE,IAAI2B,eAAe,GAAG3B,mBAAO,CAAC,sFAAsB,CAAC;AACrD,IAAI4B,iBAAiB,GAAG5B,mBAAO,CAAC,0FAAwB,CAAC;AACzD,SAAS6B,mBAAmBA,CAAA,EAAG;EAC7B,YAAY;;EAEZ,IAAIhH,CAAC,GAAGkG,WAAW,CAAC,CAAC;IACnB1G,CAAC,GAAGQ,CAAC,CAAC+F,CAAC,CAACiB,mBAAmB,CAAC;IAC5BrG,CAAC,GAAG,CAAC+B,MAAM,CAACkD,cAAc,GAAGlD,MAAM,CAACkD,cAAc,CAACpG,CAAC,CAAC,GAAGA,CAAC,CAACsE,SAAS,EAAEc,WAAW;EAClF,SAASzE,CAACA,CAACH,CAAC,EAAE;IACZ,IAAIR,CAAC,GAAG,UAAU,IAAI,OAAOQ,CAAC,IAAIA,CAAC,CAAC4E,WAAW;IAC/C,OAAO,CAAC,CAACpF,CAAC,KAAKA,CAAC,KAAKmB,CAAC,IAAI,mBAAmB,MAAMnB,CAAC,CAACqG,WAAW,IAAIrG,CAAC,CAACwF,IAAI,CAAC,CAAC;EAC9E;EACA,IAAIpE,CAAC,GAAG;IACN,OAAO,EAAE,CAAC;IACV,QAAQ,EAAE,CAAC;IACX,OAAO,EAAE,CAAC;IACV,UAAU,EAAE;EACd,CAAC;EACD,SAASX,CAACA,CAACD,CAAC,EAAE;IACZ,IAAIR,CAAC,EAAEmB,CAAC;IACR,OAAO,UAAUR,CAAC,EAAE;MAClBX,CAAC,KAAKA,CAAC,GAAG;QACRyH,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;UACpB,OAAOtG,CAAC,CAACR,CAAC,CAACF,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC;QACD,OAAO,EAAE,SAASiH,MAAMA,CAAA,EAAG;UACzB,OAAO/G,CAAC,CAACT,CAAC;QACZ,CAAC;QACDyH,MAAM,EAAE,SAASA,MAAMA,CAACnH,CAAC,EAAER,CAAC,EAAE;UAC5B,OAAOmB,CAAC,CAACR,CAAC,CAACF,CAAC,EAAEW,CAAC,CAACZ,CAAC,CAAC,EAAER,CAAC,CAAC;QACxB,CAAC;QACD4H,aAAa,EAAE,SAASA,aAAaA,CAACpH,CAAC,EAAEY,CAAC,EAAEX,CAAC,EAAE;UAC7C,OAAOT,CAAC,CAAC6H,UAAU,GAAGzG,CAAC,EAAED,CAAC,CAACR,CAAC,CAACV,CAAC,EAAEsH,iBAAiB,CAAC/G,CAAC,CAAC,EAAEC,CAAC,CAAC;QAC1D,CAAC;QACDqH,MAAM,EAAE,SAASA,MAAMA,CAACtH,CAAC,EAAE;UACzB,OAAOW,CAAC,CAACR,CAAC,CAACsD,CAAC,EAAEzD,CAAC,CAAC;QAClB;MACF,CAAC,EAAEW,CAAC,GAAG,SAASA,CAACA,CAACX,CAAC,EAAEuH,EAAE,EAAE3G,CAAC,EAAE;QAC1BT,CAAC,CAAC+B,CAAC,GAAG1C,CAAC,CAACgI,IAAI,EAAErH,CAAC,CAACA,CAAC,GAAGX,CAAC,CAACkE,IAAI;QAC1B,IAAI;UACF,OAAO1D,CAAC,CAACuH,EAAE,EAAE3G,CAAC,CAAC;QACjB,CAAC,SAAS;UACRpB,CAAC,CAACkE,IAAI,GAAGvD,CAAC,CAACA,CAAC;QACd;MACF,CAAC,CAAC,EAAEX,CAAC,CAAC6H,UAAU,KAAK7H,CAAC,CAACA,CAAC,CAAC6H,UAAU,CAAC,GAAGlH,CAAC,CAACT,CAAC,EAAEF,CAAC,CAAC6H,UAAU,GAAG,KAAK,CAAC,CAAC,EAAE7H,CAAC,CAACiI,IAAI,GAAGtH,CAAC,CAACT,CAAC,EAAEF,CAAC,CAACkE,IAAI,GAAGvD,CAAC,CAACA,CAAC;MAC9F,IAAI;QACF,OAAOH,CAAC,CAACkD,IAAI,CAAC,IAAI,EAAE1D,CAAC,CAAC;MACxB,CAAC,SAAS;QACRW,CAAC,CAAC+B,CAAC,GAAG1C,CAAC,CAACgI,IAAI,EAAErH,CAAC,CAACA,CAAC,GAAGX,CAAC,CAACkE,IAAI;MAC5B;IACF,CAAC;EACH;EACA,OAAO,CAAC9D,MAAM,CAACC,OAAO,GAAGmH,mBAAmB,GAAG,SAASA,mBAAmBA,CAAA,EAAG;IAC5E,OAAO;MACLU,IAAI,EAAE,SAASA,IAAIA,CAAClI,CAAC,EAAEmB,CAAC,EAAER,CAAC,EAAES,CAAC,EAAE;QAC9B,OAAOZ,CAAC,CAAC8F,CAAC,CAAC7F,CAAC,CAACT,CAAC,CAAC,EAAEmB,CAAC,EAAER,CAAC,EAAES,CAAC,IAAIA,CAAC,CAAC+G,OAAO,CAAC,CAAC,CAAC;MAC1C,CAAC;MACDC,mBAAmB,EAAEzH,CAAC;MACtB0H,IAAI,EAAE7H,CAAC,CAAC+F,CAAC;MACT+B,KAAK,EAAE,SAASA,KAAKA,CAAC9H,CAAC,EAAER,CAAC,EAAE;QAC1B,OAAO,IAAI6G,aAAa,CAACrG,CAAC,EAAER,CAAC,CAAC;MAChC,CAAC;MACD8G,aAAa,EAAEH,wBAAwB;MACvC4B,KAAK,EAAE,SAASA,KAAKA,CAAC/H,CAAC,EAAER,CAAC,EAAEmB,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAE;QACnC,OAAO,CAACZ,CAAC,CAACX,CAAC,CAAC,GAAGwG,mBAAmB,GAAGa,gBAAgB,EAAE5G,CAAC,CAACD,CAAC,CAAC,EAAER,CAAC,EAAEmB,CAAC,EAAEC,CAAC,EAAEG,CAAC,CAAC;MAC1E,CAAC;MACDiH,IAAI,EAAElB,eAAe;MACrBmB,MAAM,EAAElB;IACV,CAAC;EACH,CAAC,EAAEnH,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,EAAE,CAAC;AACpF;AACAD,MAAM,CAACC,OAAO,GAAGmH,mBAAmB,EAAEpH,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,C;;;;;;;;;;AC5ElH,IAAI2E,OAAO,GAAGW,sGAAiC;AAC/C,SAAS+C,kBAAkBA,CAAC1I,CAAC,EAAE;EAC7B,IAAI,IAAI,IAAIA,CAAC,EAAE;IACb,IAAImB,CAAC,GAAGnB,CAAC,CAAC,UAAU,IAAI,OAAO4D,MAAM,IAAIA,MAAM,CAACC,QAAQ,IAAI,YAAY,CAAC;MACvErD,CAAC,GAAG,CAAC;IACP,IAAIW,CAAC,EAAE,OAAOA,CAAC,CAACuC,IAAI,CAAC1D,CAAC,CAAC;IACvB,IAAI,UAAU,IAAI,OAAOA,CAAC,CAACkE,IAAI,EAAE,OAAOlE,CAAC;IACzC,IAAI,CAAC2I,KAAK,CAAC3I,CAAC,CAACU,MAAM,CAAC,EAAE,OAAO;MAC3BwD,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpB,OAAOlE,CAAC,IAAIQ,CAAC,IAAIR,CAAC,CAACU,MAAM,KAAKV,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE;UACzCwB,KAAK,EAAExB,CAAC,IAAIA,CAAC,CAACQ,CAAC,EAAE,CAAC;UAClBiB,IAAI,EAAE,CAACzB;QACT,CAAC;MACH;IACF,CAAC;EACH;EACA,MAAM,IAAImC,SAAS,CAAC6C,OAAO,CAAChF,CAAC,CAAC,GAAG,kBAAkB,CAAC;AACtD;AACAI,MAAM,CAACC,OAAO,GAAGqI,kBAAkB,EAAEtI,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,C;;;;;;;;;;AClBjH,SAAS2E,OAAOA,CAAC5D,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAOhB,MAAM,CAACC,OAAO,GAAG2E,OAAO,GAAG,UAAU,IAAI,OAAOpB,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUzC,CAAC,EAAE;IACjH,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOwC,MAAM,IAAIxC,CAAC,CAACgE,WAAW,KAAKxB,MAAM,IAAIxC,CAAC,KAAKwC,MAAM,CAAChB,SAAS,GAAG,QAAQ,GAAG,OAAOxB,CAAC;EACrH,CAAC,EAAEhB,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,EAAE2E,OAAO,CAAC5D,CAAC,CAAC;AAC7F;AACAhB,MAAM,CAACC,OAAO,GAAG2E,OAAO,EAAE5E,yBAAyB,GAAG,IAAI,EAAEA,yBAAyB,GAAGA,MAAM,CAACC,OAAO,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTtG,IAAMuI,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,GAAG;EAAA,OAAKA,GAAG,KAAK,IAAI,IAAI7D,yEAAA,CAAO6D,GAAG,MAAK,QAAQ;AAAA;AACjE,IAAMC,iBAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AAAC,IAC/BC,aAAa;EACf,SAAAA,cAAA,EAAc;IAAA7G,iFAAA,OAAA6G,aAAA;IACV,IAAI,CAACC,OAAO,GAAG9F,MAAM,CAAC6C,MAAM,CAAC,IAAI,CAAC;EACtC;EAAC,OAAA1C,8EAAA,CAAA0F,aAAA;IAAA3F,GAAA;IAAA5B,KAAA,EACD,SAAAyH,WAAWA,CAACC,OAAO,EAAET,MAAM,EAAkC;MAAA,IAAhCU,UAAU,GAAArH,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAGgH,iBAAiB;MACvD,IAAI,CAACL,MAAM,EAAE;QACT,OAAO,CAACS,OAAO,CAAC;MACpB;MACA,IAAIG,MAAM,GAAG,IAAI,CAACL,OAAO,CAACE,OAAO,CAAC;MAClC,IAAI,CAACG,MAAM,EAAE;QACTA,MAAM,GAAGC,KAAK,CAACJ,OAAO,EAAEC,UAAU,CAAC;QACnC,IAAI,CAACH,OAAO,CAACE,OAAO,CAAC,GAAGG,MAAM;MAClC;MACA,OAAOE,OAAO,CAACF,MAAM,EAAEZ,MAAM,CAAC;IAClC;EAAC;AAAA;AAEL,IAAMe,mBAAmB,GAAG,UAAU;AACtC,IAAMC,oBAAoB,GAAG,UAAU;AACvC,SAASH,KAAKA,CAACI,MAAM,EAAAC,IAAA,EAAkC;EAAA,IAAAC,KAAA,GAAAjF,gFAAA,CAAAgF,IAAA;IAA/BE,cAAc,GAAAD,KAAA;IAAEE,YAAY,GAAAF,KAAA;EAChD,IAAMP,MAAM,GAAG,EAAE;EACjB,IAAIU,QAAQ,GAAG,CAAC;EAChB,IAAIC,IAAI,GAAG,EAAE;EACb,OAAOD,QAAQ,GAAGL,MAAM,CAAChJ,MAAM,EAAE;IAC7B,IAAIuJ,IAAI,GAAGP,MAAM,CAACK,QAAQ,EAAE,CAAC;IAC7B,IAAIE,IAAI,KAAKJ,cAAc,EAAE;MACzB,IAAIG,IAAI,EAAE;QACNX,MAAM,CAAC5G,IAAI,CAAC;UAAEyH,IAAI,EAAE,MAAM;UAAE1I,KAAK,EAAEwI;QAAK,CAAC,CAAC;MAC9C;MACAA,IAAI,GAAG,EAAE;MACT,IAAIG,GAAG,GAAG,EAAE;MACZF,IAAI,GAAGP,MAAM,CAACK,QAAQ,EAAE,CAAC;MACzB,OAAOE,IAAI,KAAKb,SAAS,IAAIa,IAAI,KAAKH,YAAY,EAAE;QAChDK,GAAG,IAAIF,IAAI;QACXA,IAAI,GAAGP,MAAM,CAACK,QAAQ,EAAE,CAAC;MAC7B;MACA,IAAMK,QAAQ,GAAGH,IAAI,KAAKH,YAAY;MACtC,IAAMI,IAAI,GAAGV,mBAAmB,CAAC/D,IAAI,CAAC0E,GAAG,CAAC,GACpC,MAAM,GACNC,QAAQ,IAAIX,oBAAoB,CAAChE,IAAI,CAAC0E,GAAG,CAAC,GACtC,OAAO,GACP,SAAS;MACnBd,MAAM,CAAC5G,IAAI,CAAC;QAAEjB,KAAK,EAAE2I,GAAG;QAAED,IAAI,EAAJA;MAAK,CAAC,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IAAA,KACK;MACDF,IAAI,IAAIC,IAAI;IAChB;EACJ;EACAD,IAAI,IAAIX,MAAM,CAAC5G,IAAI,CAAC;IAAEyH,IAAI,EAAE,MAAM;IAAE1I,KAAK,EAAEwI;EAAK,CAAC,CAAC;EAClD,OAAOX,MAAM;AACjB;AACA,SAASE,OAAOA,CAACF,MAAM,EAAEZ,MAAM,EAAE;EAC7B,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAIC,KAAK,GAAG,CAAC;EACb,IAAMC,IAAI,GAAG3J,KAAK,CAACG,OAAO,CAAC0H,MAAM,CAAC,GAC5B,MAAM,GACNG,QAAQ,CAACH,MAAM,CAAC,GACZ,OAAO,GACP,SAAS;EACnB,IAAI8B,IAAI,KAAK,SAAS,EAAE;IACpB,OAAOF,QAAQ;EACnB;EACA,OAAOC,KAAK,GAAGjB,MAAM,CAAC3I,MAAM,EAAE;IAC1B,IAAM8J,KAAK,GAAGnB,MAAM,CAACiB,KAAK,CAAC;IAC3B,QAAQE,KAAK,CAACN,IAAI;MACd,KAAK,MAAM;QACPG,QAAQ,CAAC5H,IAAI,CAAC+H,KAAK,CAAChJ,KAAK,CAAC;QAC1B;MACJ,KAAK,MAAM;QACP6I,QAAQ,CAAC5H,IAAI,CAACgG,MAAM,CAACgC,QAAQ,CAACD,KAAK,CAAChJ,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;QAChD;MACJ,KAAK,OAAO;QACR,IAAI+I,IAAI,KAAK,OAAO,EAAE;UAClBF,QAAQ,CAAC5H,IAAI,CAACgG,MAAM,CAAC+B,KAAK,CAAChJ,KAAK,CAAC,CAAC;QACtC,CAAC,MACI;UACD,IAAIkJ,IAAqC,EAAE;YACvCG,OAAO,CAACC,IAAI,mBAAAC,MAAA,CAAmBP,KAAK,CAACN,IAAI,6BAAAa,MAAA,CAA0BR,IAAI,mBAAgB,CAAC;UAC5F;QACJ;QACA;MACJ,KAAK,SAAS;QACV,IAAIG,IAAqC,EAAE;UACvCG,OAAO,CAACC,IAAI,kCAAkC,CAAC;QACnD;QACA;IACR;IACAR,KAAK,EAAE;EACX;EACA,OAAOD,QAAQ;AACnB;AAEA,IAAMW,cAAc,GAAG,SAAS;AAChC,IAAMC,cAAc,GAAG,SAAS;AAChC,IAAMC,SAAS,GAAG,IAAI;AACtB,IAAMC,SAAS,GAAG,IAAI;AACtB,IAAMC,SAAS,GAAG,IAAI;AACtB,IAAMC,cAAc,GAAGnI,MAAM,CAACN,SAAS,CAACyI,cAAc;AACtD,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAIzC,GAAG,EAAEzF,GAAG;EAAA,OAAKiI,cAAc,CAAC3H,IAAI,CAACmF,GAAG,EAAEzF,GAAG,CAAC;AAAA;AAC1D,IAAMmI,gBAAgB,GAAG,IAAIxC,aAAa,CAAC,CAAC;AAC5C,SAASyC,OAAOA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACzB,OAAO,CAAC,CAACA,KAAK,CAACC,IAAI,CAAC,UAACC,IAAI;IAAA,OAAKH,GAAG,CAACI,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC;EAAA,EAAC;AAC3D;AACA,SAASE,UAAUA,CAACL,GAAG,EAAEC,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACC,IAAI,CAAC,UAACC,IAAI;IAAA,OAAKH,GAAG,CAACI,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC;EAAA,EAAC;AACxD;AACA,SAASG,eAAeA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACvC,IAAI,CAACD,MAAM,EAAE;IACT;EACJ;EACAA,MAAM,GAAGA,MAAM,CAACE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACzC,IAAIF,QAAQ,IAAIA,QAAQ,CAACD,MAAM,CAAC,EAAE;IAC9B,OAAOA,MAAM;EACjB;EACAA,MAAM,GAAGA,MAAM,CAACI,WAAW,CAAC,CAAC;EAC7B,IAAIJ,MAAM,KAAK,SAAS,EAAE;IACtB;IACA,OAAOhB,cAAc;EACzB;EACA,IAAIgB,MAAM,CAACH,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;IAC5B,IAAIG,MAAM,CAACH,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAC9B,OAAOb,cAAc;IACzB;IACA,IAAIgB,MAAM,CAACH,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAC9B,OAAOZ,cAAc;IACzB;IACA,IAAIO,OAAO,CAACQ,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE;MAChD,OAAOf,cAAc;IACzB;IACA,OAAOD,cAAc;EACzB;EACA,IAAIqB,OAAO,GAAG,CAACnB,SAAS,EAAEC,SAAS,EAAEC,SAAS,CAAC;EAC/C,IAAIa,QAAQ,IAAI/I,MAAM,CAACsF,IAAI,CAACyD,QAAQ,CAAC,CAACvL,MAAM,GAAG,CAAC,EAAE;IAC9C2L,OAAO,GAAGnJ,MAAM,CAACsF,IAAI,CAACyD,QAAQ,CAAC;EACnC;EACA,IAAMK,IAAI,GAAGR,UAAU,CAACE,MAAM,EAAEK,OAAO,CAAC;EACxC,IAAIC,IAAI,EAAE;IACN,OAAOA,IAAI;EACf;AACJ;AAAC,IACKC,IAAI;EACN,SAAAA,KAAAC,KAAA,EAAsE;IAAA,IAAxDR,MAAM,GAAAQ,KAAA,CAANR,MAAM;MAAES,cAAc,GAAAD,KAAA,CAAdC,cAAc;MAAER,QAAQ,GAAAO,KAAA,CAARP,QAAQ;MAAES,OAAO,GAAAF,KAAA,CAAPE,OAAO;MAAEC,QAAQ,GAAAH,KAAA,CAARG,QAAQ;IAAAzK,iFAAA,OAAAqK,IAAA;IAC7D,IAAI,CAACP,MAAM,GAAGd,SAAS;IACvB,IAAI,CAACuB,cAAc,GAAGvB,SAAS;IAC/B,IAAI,CAAChC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAAC+C,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACW,QAAQ,GAAG,EAAE;IAClB,IAAIH,cAAc,EAAE;MAChB,IAAI,CAACA,cAAc,GAAGA,cAAc;IACxC;IACA,IAAI,CAACE,QAAQ,GAAGA,QAAQ,IAAIpB,gBAAgB;IAC5C,IAAI,CAACU,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC;IAC9B,IAAI,CAACY,SAAS,CAACb,MAAM,IAAId,SAAS,CAAC;IACnC,IAAIwB,OAAO,EAAE;MACT,IAAI,CAACI,WAAW,CAACJ,OAAO,CAAC;IAC7B;EACJ;EAAC,OAAArJ,8EAAA,CAAAkJ,IAAA;IAAAnJ,GAAA;IAAA5B,KAAA,EACD,SAAAqL,SAASA,CAACb,MAAM,EAAE;MAAA,IAAAe,KAAA;MACd,IAAMC,SAAS,GAAG,IAAI,CAAChB,MAAM;MAC7B,IAAI,CAACA,MAAM,GAAGD,eAAe,CAACC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAAC,IAAI,IAAI,CAACQ,cAAc;MAC3E,IAAI,CAAC,IAAI,CAACR,QAAQ,CAAC,IAAI,CAACD,MAAM,CAAC,EAAE;QAC7B;QACA,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACD,MAAM,CAAC,GAAG,CAAC,CAAC;MACnC;MACA,IAAI,CAAC9C,OAAO,GAAG,IAAI,CAAC+C,QAAQ,CAAC,IAAI,CAACD,MAAM,CAAC;MACzC;MACA,IAAIgB,SAAS,KAAK,IAAI,CAAChB,MAAM,EAAE;QAC3B,IAAI,CAACY,QAAQ,CAACK,OAAO,CAAC,UAACP,OAAO,EAAK;UAC/BA,OAAO,CAACK,KAAI,CAACf,MAAM,EAAEgB,SAAS,CAAC;QACnC,CAAC,CAAC;MACN;IACJ;EAAC;IAAA5J,GAAA;IAAA5B,KAAA,EACD,SAAA0L,SAASA,CAAA,EAAG;MACR,OAAO,IAAI,CAAClB,MAAM;IACtB;EAAC;IAAA5I,GAAA;IAAA5B,KAAA,EACD,SAAAsL,WAAWA,CAACK,EAAE,EAAE;MAAA,IAAAC,MAAA;MACZ,IAAM9C,KAAK,GAAG,IAAI,CAACsC,QAAQ,CAACnK,IAAI,CAAC0K,EAAE,CAAC,GAAG,CAAC;MACxC,OAAO,YAAM;QACTC,MAAI,CAACR,QAAQ,CAACS,MAAM,CAAC/C,KAAK,EAAE,CAAC,CAAC;MAClC,CAAC;IACL;EAAC;IAAAlH,GAAA;IAAA5B,KAAA,EACD,SAAA8L,GAAGA,CAACtB,MAAM,EAAE9C,OAAO,EAAmB;MAAA,IAAjBqE,QAAQ,GAAAzL,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,IAAI;MAChC,IAAM0L,WAAW,GAAG,IAAI,CAACvB,QAAQ,CAACD,MAAM,CAAC;MACzC,IAAIwB,WAAW,EAAE;QACb,IAAID,QAAQ,EAAE;UACVrK,MAAM,CAACuK,MAAM,CAACD,WAAW,EAAEtE,OAAO,CAAC;QACvC,CAAC,MACI;UACDhG,MAAM,CAACsF,IAAI,CAACU,OAAO,CAAC,CAAC+D,OAAO,CAAC,UAAC7J,GAAG,EAAK;YAClC,IAAI,CAACkI,MAAM,CAACkC,WAAW,EAAEpK,GAAG,CAAC,EAAE;cAC3BoK,WAAW,CAACpK,GAAG,CAAC,GAAG8F,OAAO,CAAC9F,GAAG,CAAC;YACnC;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,MACI;QACD,IAAI,CAAC6I,QAAQ,CAACD,MAAM,CAAC,GAAG9C,OAAO;MACnC;IACJ;EAAC;IAAA9F,GAAA;IAAA5B,KAAA,EACD,SAAAyC,CAACA,CAACiF,OAAO,EAAET,MAAM,EAAEU,UAAU,EAAE;MAC3B,OAAO,IAAI,CAACwD,QAAQ,CAAC1D,WAAW,CAACC,OAAO,EAAET,MAAM,EAAEU,UAAU,CAAC,CAACuE,IAAI,CAAC,EAAE,CAAC;IAC1E;EAAC;IAAAtK,GAAA;IAAA5B,KAAA,EACD,SAAAL,CAACA,CAACiC,GAAG,EAAE4I,MAAM,EAAEvD,MAAM,EAAE;MACnB,IAAIS,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAI,OAAO8C,MAAM,KAAK,QAAQ,EAAE;QAC5BA,MAAM,GAAGD,eAAe,CAACC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAAC;QAC/CD,MAAM,KAAK9C,OAAO,GAAG,IAAI,CAAC+C,QAAQ,CAACD,MAAM,CAAC,CAAC;MAC/C,CAAC,MACI;QACDvD,MAAM,GAAGuD,MAAM;MACnB;MACA,IAAI,CAACV,MAAM,CAACpC,OAAO,EAAE9F,GAAG,CAAC,EAAE;QACvByH,OAAO,CAACC,IAAI,0CAAAC,MAAA,CAA0C3H,GAAG,2CAAwC,CAAC;QAClG,OAAOA,GAAG;MACd;MACA,OAAO,IAAI,CAACuJ,QAAQ,CAAC1D,WAAW,CAACC,OAAO,CAAC9F,GAAG,CAAC,EAAEqF,MAAM,CAAC,CAACiF,IAAI,CAAC,EAAE,CAAC;IACnE;EAAC;AAAA;AAGL,SAASC,cAAcA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACjC;EACA,IAAID,KAAK,CAACE,YAAY,EAAE;IACpB;IACAF,KAAK,CAACE,YAAY,CAAC,UAACC,SAAS,EAAK;MAC9BF,IAAI,CAAChB,SAAS,CAACkB,SAAS,CAAC;IAC7B,CAAC,CAAC;EACN,CAAC,MACI;IACDH,KAAK,CAACI,MAAM,CAAC;MAAA,OAAMJ,KAAK,CAACK,OAAO;IAAA,GAAE,UAACF,SAAS,EAAK;MAC7CF,IAAI,CAAChB,SAAS,CAACkB,SAAS,CAAC;IAC7B,CAAC,CAAC;EACN;AACJ;AACA,SAASG,gBAAgBA,CAAA,EAAG;EACxB,IAAI,OAAOC,GAAG,KAAK,WAAW,IAAIA,GAAG,CAACjB,SAAS,EAAE;IAC7C,OAAOiB,GAAG,CAACjB,SAAS,CAAC,CAAC;EAC1B;EACA;EACA,IAAI,OAAOkB,qBAAM,KAAK,WAAW,IAAIA,qBAAM,CAAClB,SAAS,EAAE;IACnD,OAAOkB,qBAAM,CAAClB,SAAS,CAAC,CAAC;EAC7B;EACA,OAAOhC,SAAS;AACpB;AACA,SAASmD,WAAWA,CAACrC,MAAM,EAA0C;EAAA,IAAxCC,QAAQ,GAAAnK,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,CAAC,CAAC;EAAA,IAAE2K,cAAc,GAAA3K,SAAA,CAAApB,MAAA,OAAAoB,SAAA,MAAAsH,SAAA;EAAA,IAAEsD,OAAO,GAAA5K,SAAA,CAAApB,MAAA,OAAAoB,SAAA,MAAAsH,SAAA;EAC/D;EACA,IAAI,OAAO4C,MAAM,KAAK,QAAQ,EAAE;IAAA,IAAAsC,KAAA,GACP,CACjBrC,QAAQ,EACRD,MAAM,CACT;IAHAA,MAAM,GAAAsC,KAAA;IAAErC,QAAQ,GAAAqC,KAAA;EAIrB;EACA,IAAI,OAAOtC,MAAM,KAAK,QAAQ,EAAE;IAC5B;IACAA,MAAM,GAAGkC,gBAAgB,CAAC,CAAC;EAC/B;EACA,IAAI,OAAOzB,cAAc,KAAK,QAAQ,EAAE;IACpCA,cAAc,GACT,OAAO8B,WAAW,KAAK,WAAW,IAAIA,WAAW,CAAC9B,cAAc,IAC7DvB,SAAS;EACrB;EACA,IAAM2C,IAAI,GAAG,IAAItB,IAAI,CAAC;IAClBP,MAAM,EAANA,MAAM;IACNS,cAAc,EAAdA,cAAc;IACdR,QAAQ,EAARA,QAAQ;IACRS,OAAO,EAAPA;EACJ,CAAC,CAAC;EACF,IAAIvL,EAAC,GAAG,SAAJA,CAACA,CAAIiC,GAAG,EAAEqF,MAAM,EAAK;IACrB,IAAI,OAAO+F,MAAM,KAAK,UAAU,EAAE;MAC9B;MACA;MACArN,EAAC,GAAG,SAAJA,CAACA,CAAaiC,GAAG,EAAEqF,MAAM,EAAE;QACvB,OAAOoF,IAAI,CAAC1M,CAAC,CAACiC,GAAG,EAAEqF,MAAM,CAAC;MAC9B,CAAC;IACL,CAAC,MACI;MACD,IAAIgG,kBAAkB,GAAG,KAAK;MAC9BtN,EAAC,GAAG,SAAJA,CAACA,CAAaiC,GAAG,EAAEqF,MAAM,EAAE;QACvB,IAAMmF,KAAK,GAAGY,MAAM,CAAC,CAAC,CAACE,GAAG;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAId,KAAK,EAAE;UACP;UACAA,KAAK,CAACK,OAAO;UACb,IAAI,CAACQ,kBAAkB,EAAE;YACrBA,kBAAkB,GAAG,IAAI;YACzBd,cAAc,CAACC,KAAK,EAAEC,IAAI,CAAC;UAC/B;QACJ;QACA,OAAOA,IAAI,CAAC1M,CAAC,CAACiC,GAAG,EAAEqF,MAAM,CAAC;MAC9B,CAAC;IACL;IACA,OAAOtH,EAAC,CAACiC,GAAG,EAAEqF,MAAM,CAAC;EACzB,CAAC;EACD,OAAO;IACHoF,IAAI,EAAJA,IAAI;IACJ5J,CAAC,WAADA,CAACA,CAACiF,OAAO,EAAET,MAAM,EAAEU,UAAU,EAAE;MAC3B,OAAO0E,IAAI,CAAC5J,CAAC,CAACiF,OAAO,EAAET,MAAM,EAAEU,UAAU,CAAC;IAC9C,CAAC;IACDhI,CAAC,WAADA,CAACA,CAACiC,GAAG,EAAEqF,MAAM,EAAE;MACX,OAAOtH,EAAC,CAACiC,GAAG,EAAEqF,MAAM,CAAC;IACzB,CAAC;IACD6E,GAAG,WAAHA,GAAGA,CAACtB,MAAM,EAAE9C,OAAO,EAAmB;MAAA,IAAjBqE,QAAQ,GAAAzL,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,IAAI;MAChC,OAAO+L,IAAI,CAACP,GAAG,CAACtB,MAAM,EAAE9C,OAAO,EAAEqE,QAAQ,CAAC;IAC9C,CAAC;IACDoB,KAAK,WAALA,KAAKA,CAACxB,EAAE,EAAE;MACN,OAAOU,IAAI,CAACf,WAAW,CAACK,EAAE,CAAC;IAC/B,CAAC;IACDD,SAAS,WAATA,SAASA,CAAA,EAAG;MACR,OAAOW,IAAI,CAACX,SAAS,CAAC,CAAC;IAC3B,CAAC;IACDL,SAAS,WAATA,SAASA,CAACkB,SAAS,EAAE;MACjB,OAAOF,IAAI,CAAChB,SAAS,CAACkB,SAAS,CAAC;IACpC;EACJ,CAAC;AACL;AAEA,IAAMa,QAAQ,GAAG,SAAXA,QAAQA,CAAI/F,GAAG;EAAA,OAAK,OAAOA,GAAG,KAAK,QAAQ;AAAA;AACjD,IAAI8D,QAAQ;AACZ,SAASkC,WAAWA,CAACC,OAAO,EAAE3F,UAAU,EAAE;EACtC,IAAI,CAACwD,QAAQ,EAAE;IACXA,QAAQ,GAAG,IAAI5D,aAAa,CAAC,CAAC;EAClC;EACA,OAAOgG,WAAW,CAACD,OAAO,EAAE,UAACA,OAAO,EAAE1L,GAAG,EAAK;IAC1C,IAAM5B,KAAK,GAAGsN,OAAO,CAAC1L,GAAG,CAAC;IAC1B,IAAIwL,QAAQ,CAACpN,KAAK,CAAC,EAAE;MACjB,IAAIwN,SAAS,CAACxN,KAAK,EAAE2H,UAAU,CAAC,EAAE;QAC9B,OAAO,IAAI;MACf;IACJ,CAAC,MACI;MACD,OAAO0F,WAAW,CAACrN,KAAK,EAAE2H,UAAU,CAAC;IACzC;EACJ,CAAC,CAAC;AACN;AACA,SAAS8F,aAAaA,CAACH,OAAO,EAAErG,MAAM,EAAEU,UAAU,EAAE;EAChD,IAAI,CAACwD,QAAQ,EAAE;IACXA,QAAQ,GAAG,IAAI5D,aAAa,CAAC,CAAC;EAClC;EACAgG,WAAW,CAACD,OAAO,EAAE,UAACA,OAAO,EAAE1L,GAAG,EAAK;IACnC,IAAM5B,KAAK,GAAGsN,OAAO,CAAC1L,GAAG,CAAC;IAC1B,IAAIwL,QAAQ,CAACpN,KAAK,CAAC,EAAE;MACjB,IAAIwN,SAAS,CAACxN,KAAK,EAAE2H,UAAU,CAAC,EAAE;QAC9B2F,OAAO,CAAC1L,GAAG,CAAC,GAAG8L,UAAU,CAAC1N,KAAK,EAAEiH,MAAM,EAAEU,UAAU,CAAC;MACxD;IACJ,CAAC,MACI;MACD8F,aAAa,CAACzN,KAAK,EAAEiH,MAAM,EAAEU,UAAU,CAAC;IAC5C;EACJ,CAAC,CAAC;EACF,OAAO2F,OAAO;AAClB;AACA,SAASK,kBAAkBA,CAACC,OAAO,EAAAC,KAAA,EAAoC;EAAA,IAAhCrD,MAAM,GAAAqD,KAAA,CAANrD,MAAM;IAAEK,OAAO,GAAAgD,KAAA,CAAPhD,OAAO;IAAElD,UAAU,GAAAkG,KAAA,CAAVlG,UAAU;EAC9D,IAAI,CAAC6F,SAAS,CAACI,OAAO,EAAEjG,UAAU,CAAC,EAAE;IACjC,OAAOiG,OAAO;EAClB;EACA,IAAI,CAACzC,QAAQ,EAAE;IACXA,QAAQ,GAAG,IAAI5D,aAAa,CAAC,CAAC;EAClC;EACA,IAAMuG,YAAY,GAAG,EAAE;EACvBpM,MAAM,CAACsF,IAAI,CAAC6D,OAAO,CAAC,CAACY,OAAO,CAAC,UAACzH,IAAI,EAAK;IACnC,IAAIA,IAAI,KAAKwG,MAAM,EAAE;MACjBsD,YAAY,CAAC7M,IAAI,CAAC;QACduJ,MAAM,EAAExG,IAAI;QACZiD,MAAM,EAAE4D,OAAO,CAAC7G,IAAI;MACxB,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACF8J,YAAY,CAACnI,OAAO,CAAC;IAAE6E,MAAM,EAANA,MAAM;IAAEvD,MAAM,EAAE4D,OAAO,CAACL,MAAM;EAAE,CAAC,CAAC;EACzD,IAAI;IACA,OAAOuD,IAAI,CAACC,SAAS,CAACC,cAAc,CAACF,IAAI,CAACjG,KAAK,CAAC8F,OAAO,CAAC,EAAEE,YAAY,EAAEnG,UAAU,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;EACjG,CAAC,CACD,OAAOnJ,CAAC,EAAE,CAAE;EACZ,OAAOoP,OAAO;AAClB;AACA,SAASJ,SAASA,CAACxN,KAAK,EAAE2H,UAAU,EAAE;EAClC,OAAO3H,KAAK,CAACqK,OAAO,CAAC1C,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC5C;AACA,SAAS+F,UAAUA,CAAC1N,KAAK,EAAEiH,MAAM,EAAEU,UAAU,EAAE;EAC3C,OAAOwD,QAAQ,CAAC1D,WAAW,CAACzH,KAAK,EAAEiH,MAAM,EAAEU,UAAU,CAAC,CAACuE,IAAI,CAAC,EAAE,CAAC;AACnE;AACA,SAASgC,YAAYA,CAACZ,OAAO,EAAE1L,GAAG,EAAEkM,YAAY,EAAEnG,UAAU,EAAE;EAC1D,IAAM3H,KAAK,GAAGsN,OAAO,CAAC1L,GAAG,CAAC;EAC1B,IAAIwL,QAAQ,CAACpN,KAAK,CAAC,EAAE;IACjB;IACA,IAAIwN,SAAS,CAACxN,KAAK,EAAE2H,UAAU,CAAC,EAAE;MAC9B2F,OAAO,CAAC1L,GAAG,CAAC,GAAG8L,UAAU,CAAC1N,KAAK,EAAE8N,YAAY,CAAC,CAAC,CAAC,CAAC7G,MAAM,EAAEU,UAAU,CAAC;MACpE,IAAImG,YAAY,CAAC5O,MAAM,GAAG,CAAC,EAAE;QACzB;QACA,IAAMiP,YAAY,GAAIb,OAAO,CAAC1L,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAE;QACpDkM,YAAY,CAACrC,OAAO,CAAC,UAAC2C,UAAU,EAAK;UACjCD,YAAY,CAACC,UAAU,CAAC5D,MAAM,CAAC,GAAGkD,UAAU,CAAC1N,KAAK,EAAEoO,UAAU,CAACnH,MAAM,EAAEU,UAAU,CAAC;QACtF,CAAC,CAAC;MACN;IACJ;EACJ,CAAC,MACI;IACDsG,cAAc,CAACjO,KAAK,EAAE8N,YAAY,EAAEnG,UAAU,CAAC;EACnD;AACJ;AACA,SAASsG,cAAcA,CAACX,OAAO,EAAEQ,YAAY,EAAEnG,UAAU,EAAE;EACvD4F,WAAW,CAACD,OAAO,EAAE,UAACA,OAAO,EAAE1L,GAAG,EAAK;IACnCsM,YAAY,CAACZ,OAAO,EAAE1L,GAAG,EAAEkM,YAAY,EAAEnG,UAAU,CAAC;EACxD,CAAC,CAAC;EACF,OAAO2F,OAAO;AAClB;AACA,SAASC,WAAWA,CAACD,OAAO,EAAEe,IAAI,EAAE;EAChC,IAAIjP,KAAK,CAACG,OAAO,CAAC+N,OAAO,CAAC,EAAE;IACxB,KAAK,IAAIxN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwN,OAAO,CAACpO,MAAM,EAAEY,CAAC,EAAE,EAAE;MACrC,IAAIuO,IAAI,CAACf,OAAO,EAAExN,CAAC,CAAC,EAAE;QAClB,OAAO,IAAI;MACf;IACJ;EACJ,CAAC,MACI,IAAIsH,QAAQ,CAACkG,OAAO,CAAC,EAAE;IACxB,KAAK,IAAM1L,GAAG,IAAI0L,OAAO,EAAE;MACvB,IAAIe,IAAI,CAACf,OAAO,EAAE1L,GAAG,CAAC,EAAE;QACpB,OAAO,IAAI;MACf;IACJ;EACJ;EACA,OAAO,KAAK;AAChB;AAEA,SAAS0M,aAAaA,CAACzD,OAAO,EAAE;EAC5B,OAAO,UAACL,MAAM,EAAK;IACf,IAAI,CAACA,MAAM,EAAE;MACT,OAAOA,MAAM;IACjB;IACAA,MAAM,GAAGD,eAAe,CAACC,MAAM,CAAC,IAAIA,MAAM;IAC1C,OAAO+D,kBAAkB,CAAC/D,MAAM,CAAC,CAACL,IAAI,CAAC,UAACK,MAAM;MAAA,OAAKK,OAAO,CAACR,OAAO,CAACG,MAAM,CAAC,GAAG,CAAC,CAAC;IAAA,EAAC;EACpF,CAAC;AACL;AACA,SAAS+D,kBAAkBA,CAAC/D,MAAM,EAAE;EAChC,IAAMgE,KAAK,GAAG,EAAE;EAChB,IAAM3G,MAAM,GAAG2C,MAAM,CAACiE,KAAK,CAAC,GAAG,CAAC;EAChC,OAAO5G,MAAM,CAAC3I,MAAM,EAAE;IAClBsP,KAAK,CAACvN,IAAI,CAAC4G,MAAM,CAACqE,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5BrE,MAAM,CAACjC,GAAG,CAAC,CAAC;EAChB;EACA,OAAO4I,KAAK;AAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1ciD;AAC3B;AAEtB,IAAII,QAAQ;AAEZ,IAAMC,GAAG,GAAG,mEAAmE;AAC/E,IAAMC,KAAK,GAAG,sEAAsE;AAEpF,IAAI,OAAOC,IAAI,KAAK,UAAU,EAAE;EAC9BH,QAAQ,GAAG,SAAXA,QAAQA,CAAa3E,GAAG,EAAE;IACxBA,GAAG,GAAGvG,MAAM,CAACuG,GAAG,CAAC,CAACU,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;IAC9C,IAAI,CAACmE,KAAK,CAAC7K,IAAI,CAACgG,GAAG,CAAC,EAAE;MAAE,MAAM,IAAI+E,KAAK,CAAC,0FAA0F,CAAC;IAAC;;IAEpI;IACA/E,GAAG,IAAI,IAAI,CAAClG,KAAK,CAAC,CAAC,IAAIkG,GAAG,CAAC/K,MAAM,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI+P,MAAM;IAAE,IAAIC,MAAM,GAAG,EAAE;IAAE,IAAIC,EAAE;IAAE,IAAIC,EAAE;IAAE,IAAItP,CAAC,GAAG,CAAC;IACtD,OAAOA,CAAC,GAAGmK,GAAG,CAAC/K,MAAM,GAAG;MACtB+P,MAAM,GAAGJ,GAAG,CAACxE,OAAO,CAACJ,GAAG,CAACoF,MAAM,CAACvP,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG+O,GAAG,CAACxE,OAAO,CAACJ,GAAG,CAACoF,MAAM,CAACvP,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,GAClE,CAACqP,EAAE,GAAGN,GAAG,CAACxE,OAAO,CAACJ,GAAG,CAACoF,MAAM,CAACvP,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,IAAIsP,EAAE,GAAGP,GAAG,CAACxE,OAAO,CAACJ,GAAG,CAACoF,MAAM,CAACvP,CAAC,EAAE,CAAC,CAAC,CAAC;MAE5FoP,MAAM,IAAIC,EAAE,KAAK,EAAE,GAAGzL,MAAM,CAAC4L,YAAY,CAACL,MAAM,IAAI,EAAE,GAAG,GAAG,CAAC,GACzDG,EAAE,KAAK,EAAE,GAAG1L,MAAM,CAAC4L,YAAY,CAACL,MAAM,IAAI,EAAE,GAAG,GAAG,EAAEA,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,GACpEvL,MAAM,CAAC4L,YAAY,CAACL,MAAM,IAAI,EAAE,GAAG,GAAG,EAAEA,MAAM,IAAI,CAAC,GAAG,GAAG,EAAEA,MAAM,GAAG,GAAG,CAAC;IAChF;IACA,OAAOC,MAAM;EACf,CAAC;AACH,CAAC,MAAM;EACL;EACAN,QAAQ,GAAGG,IAAI;AACjB;AAEA,SAASQ,gBAAgBA,CAAEtF,GAAG,EAAE;EAC9B,OAAOuF,kBAAkB,CAACZ,QAAQ,CAAC3E,GAAG,CAAC,CAACwE,KAAK,CAAC,EAAE,CAAC,CAACgB,GAAG,CAAC,UAAU5P,CAAC,EAAE;IACjE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAAC6P,UAAU,CAAC,CAAC,CAAC,CAAC5L,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC,CAACmI,IAAI,CAAC,EAAE,CAAC,CAAC;AACd;AAEA,SAASyD,kBAAkBA,CAAA,EAAI;EAC7B,IAAM3G,KAAK,GAAK4G,EAAE,CAAEC,cAAc,CAAC,cAAc,CAAC,IAAI,EAAE;EACxD,IAAMC,QAAQ,GAAG9G,KAAK,CAACyF,KAAK,CAAC,GAAG,CAAC;EACjC,IAAI,CAACzF,KAAK,IAAI8G,QAAQ,CAAC5Q,MAAM,KAAK,CAAC,EAAE;IACnC,OAAO;MACL6Q,GAAG,EAAE,IAAI;MACTC,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE;IAChB,CAAC;EACH;EACA,IAAIC,QAAQ;EACZ,IAAI;IACFA,QAAQ,GAAGpC,IAAI,CAACjG,KAAK,CAACyH,gBAAgB,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC,CAAC,OAAOM,KAAK,EAAE;IACd,MAAM,IAAIpB,KAAK,CAAC,qBAAqB,GAAGoB,KAAK,CAAC1I,OAAO,CAAC;EACxD;EACAyI,QAAQ,CAACD,YAAY,GAAGC,QAAQ,CAACE,GAAG,GAAG,IAAI;EAC3C,OAAOF,QAAQ,CAACE,GAAG;EACnB,OAAOF,QAAQ,CAACG,GAAG;EACnB,OAAOH,QAAQ;AACjB;AAEA,SAASI,UAAUA,CAAE5B,GAAG,EAAE;EACxBA,GAAG,CAACvN,SAAS,CAACoP,YAAY,GAAG,UAAUC,MAAM,EAAE;IAC7C,IAAAC,mBAAA,GAEIf,kBAAkB,CAAC,CAAC;MADtBK,IAAI,GAAAU,mBAAA,CAAJV,IAAI;IAEN,OAAOA,IAAI,CAAC3F,OAAO,CAACoG,MAAM,CAAC,GAAG,CAAC,CAAC;EAClC,CAAC;EACD9B,GAAG,CAACvN,SAAS,CAACuP,kBAAkB,GAAG,UAAUC,YAAY,EAAE;IACzD,IAAAC,oBAAA,GAEIlB,kBAAkB,CAAC,CAAC;MADtBM,UAAU,GAAAY,oBAAA,CAAVZ,UAAU;IAEZ,OAAO,IAAI,CAACO,YAAY,CAAC,OAAO,CAAC,IAAIP,UAAU,CAAC5F,OAAO,CAACuG,YAAY,CAAC,GAAG,CAAC,CAAC;EAC5E,CAAC;EACDjC,GAAG,CAACvN,SAAS,CAAC0P,eAAe,GAAG,YAAY;IAC1C,IAAAC,oBAAA,GAEIpB,kBAAkB,CAAC,CAAC;MADtBO,YAAY,GAAAa,oBAAA,CAAZb,YAAY;IAEd,OAAOA,YAAY,GAAGc,IAAI,CAACC,GAAG,CAAC,CAAC;EAClC,CAAC;AACH;AAEA,IAAMC,SAAS,GAAGxP,MAAM,CAACN,SAAS,CAAC0C,QAAQ;AAC3C,IAAM+F,cAAc,GAAGnI,MAAM,CAACN,SAAS,CAACyI,cAAc;AAEtD,SAASsH,IAAIA,CAAExF,EAAE,EAAE;EACjB,OAAO,OAAOA,EAAE,KAAK,UAAU;AACjC;AAEA,SAASyF,KAAKA,CAAEnH,GAAG,EAAE;EACnB,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAChC;AAEA,SAAS7C,QAAQA,CAAEiK,GAAG,EAAE;EACtB,OAAOA,GAAG,KAAK,IAAI,IAAI7N,yEAAA,CAAO6N,GAAG,MAAK,QAAQ;AAChD;AAEA,SAASC,aAAaA,CAAED,GAAG,EAAE;EAC3B,OAAOH,SAAS,CAAChP,IAAI,CAACmP,GAAG,CAAC,KAAK,iBAAiB;AAClD;AAEA,SAASvH,MAAMA,CAAEuH,GAAG,EAAEzP,GAAG,EAAE;EACzB,OAAOiI,cAAc,CAAC3H,IAAI,CAACmP,GAAG,EAAEzP,GAAG,CAAC;AACtC;AAEA,SAAS2P,IAAIA,CAAA,EAAI,CAAC;;AAElB;AACA;AACA;AACA,SAASC,MAAMA,CAAE7F,EAAE,EAAE;EACnB,IAAM8F,KAAK,GAAG/P,MAAM,CAAC6C,MAAM,CAAC,IAAI,CAAC;EACjC,OAAO,SAASmN,QAAQA,CAAEzH,GAAG,EAAE;IAC7B,IAAM0H,GAAG,GAAGF,KAAK,CAACxH,GAAG,CAAC;IACtB,OAAO0H,GAAG,KAAKF,KAAK,CAACxH,GAAG,CAAC,GAAG0B,EAAE,CAAC1B,GAAG,CAAC,CAAC;EACtC,CAAC;AACH;;AAEA;AACA;AACA;AACA,IAAM2H,UAAU,GAAG,QAAQ;AAC3B,IAAMC,QAAQ,GAAGL,MAAM,CAAC,UAACvH,GAAG,EAAK;EAC/B,OAAOA,GAAG,CAACU,OAAO,CAACiH,UAAU,EAAE,UAACE,CAAC,EAAEjS,CAAC;IAAA,OAAKA,CAAC,GAAGA,CAAC,CAACkS,WAAW,CAAC,CAAC,GAAG,EAAE;EAAA,EAAC;AACpE,CAAC,CAAC;AAEF,SAASC,UAAUA,CAAEX,GAAG,EAAE;EACxB,IAAMY,OAAO,GAAG,CAAC,CAAC;EAClB,IAAIX,aAAa,CAACD,GAAG,CAAC,EAAE;IACtB3P,MAAM,CAACsF,IAAI,CAACqK,GAAG,CAAC,CAACa,IAAI,CAAC,CAAC,CAACzG,OAAO,CAAC,UAAA7J,GAAG,EAAI;MACrCqQ,OAAO,CAACrQ,GAAG,CAAC,GAAGyP,GAAG,CAACzP,GAAG,CAAC;IACzB,CAAC,CAAC;EACJ;EACA,OAAO,CAACF,MAAM,CAACsF,IAAI,CAACiL,OAAO,CAAC,GAAGZ,GAAG,GAAGY,OAAO;AAC9C;AAEA,IAAME,KAAK,GAAG,CACZ,QAAQ,EACR,SAAS,EACT,MAAM,EACN,UAAU,EACV,aAAa,CACd;AAED,IAAMC,kBAAkB,GAAG,CAAC,CAAC;AAC7B,IAAMC,kBAAkB,GAAG,CAAC,CAAC;AAE7B,SAASC,SAASA,CAAEC,SAAS,EAAEC,QAAQ,EAAE;EACvC,IAAMC,GAAG,GAAGD,QAAQ,GAChBD,SAAS,GACPA,SAAS,CAAChJ,MAAM,CAACiJ,QAAQ,CAAC,GAC1BpT,KAAK,CAACG,OAAO,CAACiT,QAAQ,CAAC,GACrBA,QAAQ,GAAG,CAACA,QAAQ,CAAC,GACzBD,SAAS;EACb,OAAOE,GAAG,GACNC,WAAW,CAACD,GAAG,CAAC,GAChBA,GAAG;AACT;AAEA,SAASC,WAAWA,CAAEC,KAAK,EAAE;EAC3B,IAAMF,GAAG,GAAG,EAAE;EACd,KAAK,IAAI3S,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6S,KAAK,CAACzT,MAAM,EAAEY,CAAC,EAAE,EAAE;IACrC,IAAI2S,GAAG,CAACpI,OAAO,CAACsI,KAAK,CAAC7S,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MAChC2S,GAAG,CAACxR,IAAI,CAAC0R,KAAK,CAAC7S,CAAC,CAAC,CAAC;IACpB;EACF;EACA,OAAO2S,GAAG;AACZ;AAEA,SAASG,UAAUA,CAAED,KAAK,EAAEE,IAAI,EAAE;EAChC,IAAM/J,KAAK,GAAG6J,KAAK,CAACtI,OAAO,CAACwI,IAAI,CAAC;EACjC,IAAI/J,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB6J,KAAK,CAAC9G,MAAM,CAAC/C,KAAK,EAAE,CAAC,CAAC;EACxB;AACF;AAEA,SAASgK,oBAAoBA,CAAEC,WAAW,EAAEC,MAAM,EAAE;EAClDtR,MAAM,CAACsF,IAAI,CAACgM,MAAM,CAAC,CAACvH,OAAO,CAAC,UAAAoH,IAAI,EAAI;IAClC,IAAIV,KAAK,CAAC9H,OAAO,CAACwI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI1B,IAAI,CAAC6B,MAAM,CAACH,IAAI,CAAC,CAAC,EAAE;MACpDE,WAAW,CAACF,IAAI,CAAC,GAAGP,SAAS,CAACS,WAAW,CAACF,IAAI,CAAC,EAAEG,MAAM,CAACH,IAAI,CAAC,CAAC;IAChE;EACF,CAAC,CAAC;AACJ;AAEA,SAASI,qBAAqBA,CAAEF,WAAW,EAAEC,MAAM,EAAE;EACnD,IAAI,CAACD,WAAW,IAAI,CAACC,MAAM,EAAE;IAC3B;EACF;EACAtR,MAAM,CAACsF,IAAI,CAACgM,MAAM,CAAC,CAACvH,OAAO,CAAC,UAAAoH,IAAI,EAAI;IAClC,IAAIV,KAAK,CAAC9H,OAAO,CAACwI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI1B,IAAI,CAAC6B,MAAM,CAACH,IAAI,CAAC,CAAC,EAAE;MACpDD,UAAU,CAACG,WAAW,CAACF,IAAI,CAAC,EAAEG,MAAM,CAACH,IAAI,CAAC,CAAC;IAC7C;EACF,CAAC,CAAC;AACJ;AAEA,SAASK,cAAcA,CAAEC,MAAM,EAAEH,MAAM,EAAE;EACvC,IAAI,OAAOG,MAAM,KAAK,QAAQ,IAAI7B,aAAa,CAAC0B,MAAM,CAAC,EAAE;IACvDF,oBAAoB,CAACT,kBAAkB,CAACc,MAAM,CAAC,KAAKd,kBAAkB,CAACc,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;EAC/F,CAAC,MAAM,IAAI1B,aAAa,CAAC6B,MAAM,CAAC,EAAE;IAChCL,oBAAoB,CAACV,kBAAkB,EAAEe,MAAM,CAAC;EAClD;AACF;AAEA,SAASC,iBAAiBA,CAAED,MAAM,EAAEH,MAAM,EAAE;EAC1C,IAAI,OAAOG,MAAM,KAAK,QAAQ,EAAE;IAC9B,IAAI7B,aAAa,CAAC0B,MAAM,CAAC,EAAE;MACzBC,qBAAqB,CAACZ,kBAAkB,CAACc,MAAM,CAAC,EAAEH,MAAM,CAAC;IAC3D,CAAC,MAAM;MACL,OAAOX,kBAAkB,CAACc,MAAM,CAAC;IACnC;EACF,CAAC,MAAM,IAAI7B,aAAa,CAAC6B,MAAM,CAAC,EAAE;IAChCF,qBAAqB,CAACb,kBAAkB,EAAEe,MAAM,CAAC;EACnD;AACF;AAEA,SAASE,WAAWA,CAAER,IAAI,EAAES,MAAM,EAAE;EAClC,OAAO,UAAUC,IAAI,EAAE;IACrB,OAAOV,IAAI,CAACU,IAAI,EAAED,MAAM,CAAC,IAAIC,IAAI;EACnC,CAAC;AACH;AAEA,SAASC,SAASA,CAAEnC,GAAG,EAAE;EACvB,OAAO,CAAC,CAACA,GAAG,KAAK7N,yEAAA,CAAO6N,GAAG,MAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IAAI,OAAOA,GAAG,CAACjR,IAAI,KAAK,UAAU;AAC1G;AAEA,SAASqT,KAAKA,CAAEd,KAAK,EAAEY,IAAI,EAAED,MAAM,EAAE;EACnC,IAAII,OAAO,GAAG,KAAK;EACnB,KAAK,IAAI5T,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6S,KAAK,CAACzT,MAAM,EAAEY,CAAC,EAAE,EAAE;IACrC,IAAM+S,IAAI,GAAGF,KAAK,CAAC7S,CAAC,CAAC;IACrB,IAAI4T,OAAO,EAAE;MACXA,OAAO,GAAGxT,OAAO,CAACC,OAAO,CAACkT,WAAW,CAACR,IAAI,EAAES,MAAM,CAAC,CAAC;IACtD,CAAC,MAAM;MACL,IAAMb,GAAG,GAAGI,IAAI,CAACU,IAAI,EAAED,MAAM,CAAC;MAC9B,IAAIE,SAAS,CAACf,GAAG,CAAC,EAAE;QAClBiB,OAAO,GAAGxT,OAAO,CAACC,OAAO,CAACsS,GAAG,CAAC;MAChC;MACA,IAAIA,GAAG,KAAK,KAAK,EAAE;QACjB,OAAO;UACLrS,IAAI,WAAJA,IAAIA,CAAA,EAAI,CAAE;QACZ,CAAC;MACH;IACF;EACF;EACA,OAAOsT,OAAO,IAAI;IAChBtT,IAAI,WAAJA,IAAIA,CAAEuT,QAAQ,EAAE;MACd,OAAOA,QAAQ,CAACJ,IAAI,CAAC;IACvB;EACF,CAAC;AACH;AAEA,SAASK,cAAcA,CAAEb,WAAW,EAAgB;EAAA,IAAdc,OAAO,GAAAvT,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,CAAC,CAAC;EAChD,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC,CAACmL,OAAO,CAAC,UAAAzH,IAAI,EAAI;IAC9C,IAAI5E,KAAK,CAACG,OAAO,CAACwT,WAAW,CAAC/O,IAAI,CAAC,CAAC,EAAE;MACpC,IAAM8P,WAAW,GAAGD,OAAO,CAAC7P,IAAI,CAAC;MACjC6P,OAAO,CAAC7P,IAAI,CAAC,GAAG,SAAS+P,mBAAmBA,CAAEtB,GAAG,EAAE;QACjDgB,KAAK,CAACV,WAAW,CAAC/O,IAAI,CAAC,EAAEyO,GAAG,EAAEoB,OAAO,CAAC,CAACzT,IAAI,CAAC,UAACqS,GAAG,EAAK;UACnD;UACA,OAAOtB,IAAI,CAAC2C,WAAW,CAAC,IAAIA,WAAW,CAACrB,GAAG,CAAC,IAAIA,GAAG;QACrD,CAAC,CAAC;MACJ,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAOoB,OAAO;AAChB;AAEA,SAASG,kBAAkBA,CAAEb,MAAM,EAAEc,WAAW,EAAE;EAChD,IAAMC,gBAAgB,GAAG,EAAE;EAC3B,IAAI9U,KAAK,CAACG,OAAO,CAAC6S,kBAAkB,CAAC6B,WAAW,CAAC,EAAE;IACjDC,gBAAgB,CAACjT,IAAI,CAAAV,KAAA,CAArB2T,gBAAgB,EAAA3Q,oFAAA,CAAS6O,kBAAkB,CAAC6B,WAAW,EAAC;EAC1D;EACA,IAAMlB,WAAW,GAAGV,kBAAkB,CAACc,MAAM,CAAC;EAC9C,IAAIJ,WAAW,IAAI3T,KAAK,CAACG,OAAO,CAACwT,WAAW,CAACkB,WAAW,CAAC,EAAE;IACzDC,gBAAgB,CAACjT,IAAI,CAAAV,KAAA,CAArB2T,gBAAgB,EAAA3Q,oFAAA,CAASwP,WAAW,CAACkB,WAAW,EAAC;EACnD;EACAC,gBAAgB,CAACzI,OAAO,CAAC,UAAAoH,IAAI,EAAI;IAC/BoB,WAAW,GAAGpB,IAAI,CAACoB,WAAW,CAAC,IAAIA,WAAW;EAChD,CAAC,CAAC;EACF,OAAOA,WAAW;AACpB;AAEA,SAASE,sBAAsBA,CAAEhB,MAAM,EAAE;EACvC,IAAMJ,WAAW,GAAGrR,MAAM,CAAC6C,MAAM,CAAC,IAAI,CAAC;EACvC7C,MAAM,CAACsF,IAAI,CAACoL,kBAAkB,CAAC,CAAC3G,OAAO,CAAC,UAAAoH,IAAI,EAAI;IAC9C,IAAIA,IAAI,KAAK,aAAa,EAAE;MAC1BE,WAAW,CAACF,IAAI,CAAC,GAAGT,kBAAkB,CAACS,IAAI,CAAC,CAAC9O,KAAK,CAAC,CAAC;IACtD;EACF,CAAC,CAAC;EACF,IAAMqQ,iBAAiB,GAAG/B,kBAAkB,CAACc,MAAM,CAAC;EACpD,IAAIiB,iBAAiB,EAAE;IACrB1S,MAAM,CAACsF,IAAI,CAACoN,iBAAiB,CAAC,CAAC3I,OAAO,CAAC,UAAAoH,IAAI,EAAI;MAC7C,IAAIA,IAAI,KAAK,aAAa,EAAE;QAC1BE,WAAW,CAACF,IAAI,CAAC,GAAG,CAACE,WAAW,CAACF,IAAI,CAAC,IAAI,EAAE,EAAEtJ,MAAM,CAAC6K,iBAAiB,CAACvB,IAAI,CAAC,CAAC;MAC/E;IACF,CAAC,CAAC;EACJ;EACA,OAAOE,WAAW;AACpB;AAEA,SAASsB,SAASA,CAAElB,MAAM,EAAEmB,GAAG,EAAET,OAAO,EAAa;EAAA,SAAAU,IAAA,GAAAjU,SAAA,CAAApB,MAAA,EAARoU,MAAM,OAAAlU,KAAA,CAAAmV,IAAA,OAAAA,IAAA,WAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;IAANlB,MAAM,CAAAkB,IAAA,QAAAlU,SAAA,CAAAkU,IAAA;EAAA;EACjD,IAAMzB,WAAW,GAAGoB,sBAAsB,CAAChB,MAAM,CAAC;EAClD,IAAIJ,WAAW,IAAIrR,MAAM,CAACsF,IAAI,CAAC+L,WAAW,CAAC,CAAC7T,MAAM,EAAE;IAClD,IAAIE,KAAK,CAACG,OAAO,CAACwT,WAAW,CAAC0B,MAAM,CAAC,EAAE;MACrC,IAAMhC,GAAG,GAAGgB,KAAK,CAACV,WAAW,CAAC0B,MAAM,EAAEZ,OAAO,CAAC;MAC9C,OAAOpB,GAAG,CAACrS,IAAI,CAAC,UAACyT,OAAO,EAAK;QAC3B;QACA,OAAOS,GAAG,CAAA/T,KAAA,UACRqT,cAAc,CAACO,sBAAsB,CAAChB,MAAM,CAAC,EAAEU,OAAO,CAAC,EAAAtK,MAAA,CACpD+J,MAAM,CACX,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAOgB,GAAG,CAAA/T,KAAA,UAACqT,cAAc,CAACb,WAAW,EAAEc,OAAO,CAAC,EAAAtK,MAAA,CAAK+J,MAAM,EAAC;IAC7D;EACF;EACA,OAAOgB,GAAG,CAAA/T,KAAA,UAACsT,OAAO,EAAAtK,MAAA,CAAK+J,MAAM,EAAC;AAChC;AAEA,IAAMoB,kBAAkB,GAAG;EACzBT,WAAW,WAAXA,WAAWA,CAAExB,GAAG,EAAE;IAChB,IAAI,CAACe,SAAS,CAACf,GAAG,CAAC,EAAE;MACnB,OAAOA,GAAG;IACZ;IACA,OAAO,IAAIvS,OAAO,CAAC,UAACC,OAAO,EAAEwU,MAAM,EAAK;MACtClC,GAAG,CAACrS,IAAI,CAAC,UAAAqS,GAAG,EAAI;QACd,IAAI,CAACA,GAAG,EAAE;UACRtS,OAAO,CAACsS,GAAG,CAAC;UACZ;QACF;QACA,IAAIA,GAAG,CAAC,CAAC,CAAC,EAAE;UACVkC,MAAM,CAAClC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,MAAM;UACLtS,OAAO,CAACsS,GAAG,CAAC,CAAC,CAAC,CAAC;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,CAAC;AAED,IAAMmC,WAAW,GACf,4cAA4c;AAE9c,IAAMC,cAAc,GAAG,kBAAkB;;AAEzC;AACA,IAAMC,kBAAkB,GAAG,CAAC,qBAAqB,CAAC;;AAElD;AACA,IAAMC,SAAS,GAAG,CAAC,qBAAqB,EAAE,mBAAmB,CAAC;AAE9D,IAAMC,eAAe,GAAG,UAAU;AAElC,SAASC,YAAYA,CAAEjR,IAAI,EAAE;EAC3B,OAAO6Q,cAAc,CAAC5Q,IAAI,CAACD,IAAI,CAAC,IAAI8Q,kBAAkB,CAACzK,OAAO,CAACrG,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7E;AACA,SAASkR,SAASA,CAAElR,IAAI,EAAE;EACxB,OAAO4Q,WAAW,CAAC3Q,IAAI,CAACD,IAAI,CAAC,IAAI+Q,SAAS,CAAC1K,OAAO,CAACrG,IAAI,CAAC,KAAK,CAAC,CAAC;AACjE;AAEA,SAASmR,aAAaA,CAAEnR,IAAI,EAAE;EAC5B,OAAOgR,eAAe,CAAC/Q,IAAI,CAACD,IAAI,CAAC,IAAIA,IAAI,KAAK,QAAQ;AACxD;AAEA,SAASoR,aAAaA,CAAE1B,OAAO,EAAE;EAC/B,OAAOA,OAAO,CAACtT,IAAI,CAAC,UAAAmT,IAAI,EAAI;IAC1B,OAAO,CAAC,IAAI,EAAEA,IAAI,CAAC;EACrB,CAAC,CAAC,CACC8B,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAI,CAACA,GAAG,CAAC;EAAA,EAAC;AACxB;AAEA,SAASC,aAAaA,CAAEvR,IAAI,EAAE;EAC5B,IACEiR,YAAY,CAACjR,IAAI,CAAC,IAClBkR,SAAS,CAAClR,IAAI,CAAC,IACfmR,aAAa,CAACnR,IAAI,CAAC,EACnB;IACA,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;;AAEA;AACA,IAAI,CAAC9D,OAAO,CAACkB,SAAS,CAACoU,OAAO,EAAE;EAC9BtV,OAAO,CAACkB,SAAS,CAACoU,OAAO,GAAG,UAAU7B,QAAQ,EAAE;IAC9C,IAAMD,OAAO,GAAG,IAAI,CAAC9P,WAAW;IAChC,OAAO,IAAI,CAACxD,IAAI,CACd,UAAAJ,KAAK;MAAA,OAAI0T,OAAO,CAACvT,OAAO,CAACwT,QAAQ,CAAC,CAAC,CAAC,CAACvT,IAAI,CAAC;QAAA,OAAMJ,KAAK;MAAA,EAAC;IAAA,GACtD,UAAAyV,MAAM;MAAA,OAAI/B,OAAO,CAACvT,OAAO,CAACwT,QAAQ,CAAC,CAAC,CAAC,CAACvT,IAAI,CAAC,YAAM;QAC/C,MAAMqV,MAAM;MACd,CAAC,CAAC;IAAA,CACJ,CAAC;EACH,CAAC;AACH;AAEA,SAASC,SAASA,CAAE1R,IAAI,EAAEsQ,GAAG,EAAE;EAC7B,IAAI,CAACiB,aAAa,CAACvR,IAAI,CAAC,IAAI,CAACmN,IAAI,CAACmD,GAAG,CAAC,EAAE;IACtC,OAAOA,GAAG;EACZ;EACA,OAAO,SAASqB,UAAUA,CAAA,EAA2B;IAAA,IAAzB9B,OAAO,GAAAvT,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,CAAC,CAAC;IAAA,SAAAsV,KAAA,GAAAtV,SAAA,CAAApB,MAAA,EAAKoU,MAAM,OAAAlU,KAAA,CAAAwW,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAANvC,MAAM,CAAAuC,KAAA,QAAAvV,SAAA,CAAAuV,KAAA;IAAA;IACjD,IAAI1E,IAAI,CAAC0C,OAAO,CAACiC,OAAO,CAAC,IAAI3E,IAAI,CAAC0C,OAAO,CAACkC,IAAI,CAAC,IAAI5E,IAAI,CAAC0C,OAAO,CAACmC,QAAQ,CAAC,EAAE;MACzE,OAAOhC,kBAAkB,CAAChQ,IAAI,EAAEqQ,SAAS,CAAA9T,KAAA,UAACyD,IAAI,EAAEsQ,GAAG,EAAET,OAAO,EAAAtK,MAAA,CAAK+J,MAAM,EAAC,CAAC;IAC3E;IACA,OAAOU,kBAAkB,CAAChQ,IAAI,EAAEoR,aAAa,CAAC,IAAIlV,OAAO,CAAC,UAACC,OAAO,EAAEwU,MAAM,EAAK;MAC7EN,SAAS,CAAA9T,KAAA,UAACyD,IAAI,EAAEsQ,GAAG,EAAE5S,MAAM,CAACuK,MAAM,CAAC,CAAC,CAAC,EAAE4H,OAAO,EAAE;QAC9CiC,OAAO,EAAE3V,OAAO;QAChB4V,IAAI,EAAEpB;MACR,CAAC,CAAC,EAAApL,MAAA,CAAK+J,MAAM,EAAC;IAChB,CAAC,CAAC,CAAC,CAAC;EACN,CAAC;AACH;AAEA,IAAM2C,GAAG,GAAG,IAAI;AAChB,IAAMC,iBAAiB,GAAG,GAAG;AAC7B,IAAIC,KAAK,GAAG,KAAK;AACjB,IAAIC,WAAW,GAAG,CAAC;AACnB,IAAIC,SAAS,GAAG,CAAC;AAEjB,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,IAAIC,WAAW,EAAEC,UAAU,EAAEC,QAAQ;EAErC;IACE,IAAMC,UAAU,GAAG,OAAO9G,EAAE,CAAC+G,aAAa,KAAK,UAAU,IAAI/G,EAAE,CAAC+G,aAAa,CAAC,CAAC,GAAG/G,EAAE,CAAC+G,aAAa,CAAC,CAAC,GAAG/G,EAAE,CAACgH,iBAAiB,CAAC,CAAC;IAC7H,IAAMC,UAAU,GAAG,OAAOjH,EAAE,CAACkH,aAAa,KAAK,UAAU,IAAIlH,EAAE,CAACkH,aAAa,CAAC,CAAC,GAAGlH,EAAE,CAACkH,aAAa,CAAC,CAAC,GAAGlH,EAAE,CAACgH,iBAAiB,CAAC,CAAC;IAE7HL,WAAW,GAAGG,UAAU,CAACH,WAAW;IACpCC,UAAU,GAAGE,UAAU,CAACF,UAAU;IAClCC,QAAQ,GAAGI,UAAU,CAACJ,QAAQ;EAChC;EAEAL,WAAW,GAAGG,WAAW;EACzBF,SAAS,GAAGG,UAAU;EACtBL,KAAK,GAAGM,QAAQ,KAAK,KAAK;AAC5B;AAEA,SAASM,MAAMA,CAACC,MAAM,EAAEC,cAAc,EAAE;EACtC,IAAIb,WAAW,KAAK,CAAC,EAAE;IACrBE,gBAAgB,CAAC,CAAC;EACpB;EAEAU,MAAM,GAAGrT,MAAM,CAACqT,MAAM,CAAC;EACvB,IAAIA,MAAM,KAAK,CAAC,EAAE;IAChB,OAAO,CAAC;EACV;EACA,IAAI9H,MAAM,GAAI8H,MAAM,GAAGd,iBAAiB,IAAKe,cAAc,IAAIb,WAAW,CAAC;EAC3E,IAAIlH,MAAM,GAAG,CAAC,EAAE;IACdA,MAAM,GAAG,CAACA,MAAM;EAClB;EACAA,MAAM,GAAGgI,IAAI,CAACC,KAAK,CAACjI,MAAM,GAAG+G,GAAG,CAAC;EACjC,IAAI/G,MAAM,KAAK,CAAC,EAAE;IAChB,IAAImH,SAAS,KAAK,CAAC,IAAI,CAACF,KAAK,EAAE;MAC7BjH,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM;MACLA,MAAM,GAAG,GAAG;IACd;EACF;EACA,OAAO8H,MAAM,GAAG,CAAC,GAAG,CAAC9H,MAAM,GAAGA,MAAM;AACtC;AAEA,IAAM1F,cAAc,GAAG,SAAS;AAChC,IAAMC,cAAc,GAAG,SAAS;AAChC,IAAMC,SAAS,GAAG,IAAI;AACtB,IAAMC,SAAS,GAAG,IAAI;AACtB,IAAMC,SAAS,GAAG,IAAI;AAEtB,IAAMa,QAAQ,GAAG,CAAC,CAAC;AAEnB,SAAS2M,iBAAiBA,CAAA,EAAI;EAC5B,IAAIC,cAAc,GAAG,EAAE;EACvB;IACE,IAAMC,WAAW,GAAG,OAAO1H,EAAE,CAAC2H,cAAc,KAAK,UAAU,IAAI3H,EAAE,CAAC2H,cAAc,CAAC,CAAC,GAAG3H,EAAE,CAAC2H,cAAc,CAAC,CAAC,GAAG3H,EAAE,CAACgH,iBAAiB,CAAC,CAAC;IACjI,IAAMY,QAAQ,GACZF,WAAW,IAAIA,WAAW,CAACE,QAAQ,GAAGF,WAAW,CAACE,QAAQ,GAAG9N,SAAS;IACxE2N,cAAc,GAAG9M,eAAe,CAACiN,QAAQ,CAAC,IAAI9N,SAAS;EACzD;EACA,OAAO2N,cAAc;AACvB;AAEA,IAAI7M,MAAM;AAEV;EACEA,MAAM,GAAG4M,iBAAiB,CAAC,CAAC;AAC9B;AAEA,SAASK,gBAAgBA,CAAA,EAAI;EAC3B,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE;IACrB;EACF;EACA,IAAMC,UAAU,GAAGjW,MAAM,CAACsF,IAAI,CAAC+F,WAAW,CAAClC,OAAO,CAAC;EACnD,IAAI8M,UAAU,CAACzY,MAAM,EAAE;IACrByY,UAAU,CAAClM,OAAO,CAAC,UAACjB,MAAM,EAAK;MAC7B,IAAMwB,WAAW,GAAGvB,QAAQ,CAACD,MAAM,CAAC;MACpC,IAAMoN,YAAY,GAAG7K,WAAW,CAAClC,OAAO,CAACL,MAAM,CAAC;MAChD,IAAIwB,WAAW,EAAE;QACftK,MAAM,CAACuK,MAAM,CAACD,WAAW,EAAE4L,YAAY,CAAC;MAC1C,CAAC,MAAM;QACLnN,QAAQ,CAACD,MAAM,CAAC,GAAGoN,YAAY;MACjC;IACF,CAAC,CAAC;EACJ;AACF;AAEAH,gBAAgB,CAAC,CAAC;AAElB,IAAMpL,IAAI,GAAGQ,+DAAW,CACtBrC,MAAM,EACL,CAAC,CACJ,CAAC;AACD,IAAM7K,CAAC,GAAG0M,IAAI,CAAC1M,CAAC;AAChB,IAAMkY,SAAS,GAAIxL,IAAI,CAACyL,KAAK,GAAG;EAC9BC,YAAY,WAAZA,YAAYA,CAAA,EAAI;IAAA,IAAAxM,KAAA;IACd,IAAMyM,OAAO,GAAG3L,IAAI,CAACA,IAAI,CAACf,WAAW,CAAC,YAAM;MAC1CC,KAAI,CAAC0M,YAAY,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,IAAI,CAACC,KAAK,CAAC,oBAAoB,EAAE,YAAY;MAC3CF,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ,CAAC;EACDG,OAAO,EAAE;IACPC,GAAG,WAAHA,GAAGA,CAAExW,GAAG,EAAEqF,MAAM,EAAE;MAChB,OAAOtH,CAAC,CAACiC,GAAG,EAAEqF,MAAM,CAAC;IACvB;EACF;AACF,CAAE;AACF,IAAMoE,SAAS,GAAGgB,IAAI,CAAChB,SAAS;AAChC,IAAMK,SAAS,GAAGW,IAAI,CAACX,SAAS;AAEhC,SAAS2M,aAAaA,CAAE1J,GAAG,EAAEvC,KAAK,EAAE5B,MAAM,EAAE;EAC1C,IAAM8N,KAAK,GAAG3J,GAAG,CAAC4J,UAAU,CAAC;IAC3B/N,MAAM,EAAEA,MAAM,IAAI6B,IAAI,CAACX,SAAS,CAAC;EACnC,CAAC,CAAC;EACF,IAAM8M,cAAc,GAAG,EAAE;EACzBpM,KAAK,CAACE,YAAY,GAAG,UAAAX,EAAE,EAAI;IACzB6M,cAAc,CAACvX,IAAI,CAAC0K,EAAE,CAAC;EACzB,CAAC;EACDjK,MAAM,CAACC,cAAc,CAACyK,KAAK,EAAE,SAAS,EAAE;IACtCqM,GAAG,WAAHA,GAAGA,CAAA,EAAI;MACL,OAAOH,KAAK,CAAC9N,MAAM;IACrB,CAAC;IACDkO,GAAG,WAAHA,GAAGA,CAAEha,CAAC,EAAE;MACN4Z,KAAK,CAAC9N,MAAM,GAAG9L,CAAC;MAChB8Z,cAAc,CAAC/M,OAAO,CAAC,UAAA0B,KAAK;QAAA,OAAIA,KAAK,CAACzO,CAAC,CAAC;MAAA,EAAC;IAC3C;EACF,CAAC,CAAC;AACJ;AAEA,SAASgZ,cAAcA,CAAA,EAAI;EACzB,OAAO,OAAO3K,WAAW,KAAK,WAAW,IAAIA,WAAW,CAAClC,OAAO,IAAI,CAAC,CAACnJ,MAAM,CAACsF,IAAI,CAAC+F,WAAW,CAAClC,OAAO,CAAC,CAAC3L,MAAM;AAC/G;AAEA,SAAS8K,OAAOA,CAAEC,GAAG,EAAEC,KAAK,EAAE;EAC5B,OAAO,CAAC,CAACA,KAAK,CAACC,IAAI,CAAC,UAACC,IAAI;IAAA,OAAKH,GAAG,CAACI,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC;EAAA,EAAC;AACzD;AAEA,SAASE,UAAUA,CAAEL,GAAG,EAAEC,KAAK,EAAE;EAC/B,OAAOA,KAAK,CAACC,IAAI,CAAC,UAACC,IAAI;IAAA,OAAKH,GAAG,CAACI,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC;EAAA,EAAC;AACtD;AAEA,SAASG,eAAeA,CAAEC,MAAM,EAAEC,QAAQ,EAAE;EAC1C,IAAI,CAACD,MAAM,EAAE;IACX;EACF;EACAA,MAAM,GAAGA,MAAM,CAACE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACzC,IAAIF,QAAQ,IAAIA,QAAQ,CAACD,MAAM,CAAC,EAAE;IAChC,OAAOA,MAAM;EACf;EACAA,MAAM,GAAGA,MAAM,CAACI,WAAW,CAAC,CAAC;EAC7B,IAAIJ,MAAM,KAAK,SAAS,EAAE;IACxB;IACA,OAAOhB,cAAc;EACvB;EACA,IAAIgB,MAAM,CAACH,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;IAC9B,IAAIG,MAAM,CAACH,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAChC,OAAOb,cAAc;IACvB;IACA,IAAIgB,MAAM,CAACH,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAChC,OAAOZ,cAAc;IACvB;IACA,IAAIO,OAAO,CAACQ,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE;MAClD,OAAOf,cAAc;IACvB;IACA,OAAOD,cAAc;EACvB;EACA,IAAMsB,IAAI,GAAGR,UAAU,CAACE,MAAM,EAAE,CAACd,SAAS,EAAEC,SAAS,EAAEC,SAAS,CAAC,CAAC;EAClE,IAAIkB,IAAI,EAAE;IACR,OAAOA,IAAI;EACb;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS6N,WAAWA,CAAA,EAAI;EACtB;EACA,IAAIxH,IAAI,CAACnE,MAAM,CAAC,EAAE;IAChB,IAAM4L,GAAG,GAAG5L,MAAM,CAAC;MACjB6L,YAAY,EAAE;IAChB,CAAC,CAAC;IACF,IAAID,GAAG,IAAIA,GAAG,CAAC1L,GAAG,EAAE;MAClB,OAAO0L,GAAG,CAAC1L,GAAG,CAACT,OAAO;IACxB;EACF;EACA,OAAO2K,iBAAiB,CAAC,CAAC;AAC5B;AAEA,SAAS0B,WAAWA,CAAEtO,MAAM,EAAE;EAC5B,IAAMoO,GAAG,GAAGzH,IAAI,CAACnE,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,GAAG,KAAK;EAC3C,IAAI,CAAC4L,GAAG,EAAE;IACR,OAAO,KAAK;EACd;EACA,IAAMpN,SAAS,GAAGoN,GAAG,CAAC1L,GAAG,CAACT,OAAO;EACjC,IAAIjB,SAAS,KAAKhB,MAAM,EAAE;IACxBoO,GAAG,CAAC1L,GAAG,CAACT,OAAO,GAAGjC,MAAM;IACxBuO,uBAAuB,CAACtN,OAAO,CAAC,UAACE,EAAE;MAAA,OAAKA,EAAE,CAAC;QACzCnB,MAAM,EAANA;MACF,CAAC,CAAC;IAAA,EAAC;IACH,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AAEA,IAAMuO,uBAAuB,GAAG,EAAE;AAClC,SAASC,cAAcA,CAAErN,EAAE,EAAE;EAC3B,IAAIoN,uBAAuB,CAAC1O,OAAO,CAACsB,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;IAC9CoN,uBAAuB,CAAC9X,IAAI,CAAC0K,EAAE,CAAC;EAClC;AACF;AAEA,IAAI,OAAOiB,qBAAM,KAAK,WAAW,EAAE;EACjCA,qBAAM,CAAClB,SAAS,GAAGiN,WAAW;AAChC;AAEA,IAAMM,YAAY,GAAG;EACnBvE,kBAAkB,EAAlBA;AACF,CAAC;AAED,IAAIwE,OAAO,GAAG,aAAaxX,MAAM,CAACyX,MAAM,CAAC;EACvCrW,SAAS,EAAE,IAAI;EACfiU,MAAM,EAAEA,MAAM;EACdqC,MAAM,EAAErC,MAAM;EACdrL,SAAS,EAAEiN,WAAW;EACtBtN,SAAS,EAAEyN,WAAW;EACtBE,cAAc,EAAEA,cAAc;EAC9B9F,cAAc,EAAEA,cAAc;EAC9BE,iBAAiB,EAAEA,iBAAiB;EACpC6F,YAAY,EAAEA;AAChB,CAAC,CAAC;AAEF,SAASI,mBAAmBA,CAAEC,GAAG,EAAE;EACjC,IAAMC,KAAK,GAAGC,eAAe,CAAC,CAAC;EAC/B,IAAIC,GAAG,GAAGF,KAAK,CAACra,MAAM;EACtB,OAAOua,GAAG,EAAE,EAAE;IACZ,IAAMC,IAAI,GAAGH,KAAK,CAACE,GAAG,CAAC;IACvB,IAAIC,IAAI,CAACC,KAAK,IAAID,IAAI,CAACC,KAAK,CAACC,QAAQ,KAAKN,GAAG,EAAE;MAC7C,OAAOG,GAAG;IACZ;EACF;EACA,OAAO,CAAC,CAAC;AACX;AAEA,IAAII,UAAU,GAAG;EACf7V,IAAI,WAAJA,IAAIA,CAAE8V,QAAQ,EAAE;IACd,IAAIA,QAAQ,CAACC,MAAM,KAAK,MAAM,IAAID,QAAQ,CAACE,KAAK,EAAE;MAChD,OAAO,cAAc;IACvB;IACA,OAAO,YAAY;EACrB,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAEH,QAAQ,EAAE;IACd,IAAIA,QAAQ,CAACC,MAAM,KAAK,MAAM,IAAID,QAAQ,CAACR,GAAG,EAAE;MAC9C,IAAMY,eAAe,GAAGb,mBAAmB,CAACS,QAAQ,CAACR,GAAG,CAAC;MACzD,IAAIY,eAAe,KAAK,CAAC,CAAC,EAAE;QAC1B,IAAMF,KAAK,GAAGR,eAAe,CAAC,CAAC,CAACta,MAAM,GAAG,CAAC,GAAGgb,eAAe;QAC5D,IAAIF,KAAK,GAAG,CAAC,EAAE;UACbF,QAAQ,CAACE,KAAK,GAAGA,KAAK;QACxB;MACF;IACF;EACF;AACF,CAAC;AAED,IAAIG,YAAY,GAAG;EACjBF,IAAI,WAAJA,IAAIA,CAAEH,QAAQ,EAAE;IACd,IAAIM,YAAY,GAAGnR,QAAQ,CAAC6Q,QAAQ,CAACO,OAAO,CAAC;IAC7C,IAAIlT,KAAK,CAACiT,YAAY,CAAC,EAAE;MACvB;IACF;IACA,IAAME,IAAI,GAAGR,QAAQ,CAACQ,IAAI;IAC1B,IAAI,CAAClb,KAAK,CAACG,OAAO,CAAC+a,IAAI,CAAC,EAAE;MACxB;IACF;IACA,IAAMb,GAAG,GAAGa,IAAI,CAACpb,MAAM;IACvB,IAAI,CAACua,GAAG,EAAE;MACR;IACF;IACA,IAAIW,YAAY,GAAG,CAAC,EAAE;MACpBA,YAAY,GAAG,CAAC;IAClB,CAAC,MAAM,IAAIA,YAAY,IAAIX,GAAG,EAAE;MAC9BW,YAAY,GAAGX,GAAG,GAAG,CAAC;IACxB;IACA,IAAIW,YAAY,GAAG,CAAC,EAAE;MACpBN,QAAQ,CAACO,OAAO,GAAGC,IAAI,CAACF,YAAY,CAAC;MACrCN,QAAQ,CAACQ,IAAI,GAAGA,IAAI,CAACC,MAAM,CACzB,UAACC,IAAI,EAAE1R,KAAK;QAAA,OAAKA,KAAK,GAAGsR,YAAY,GAAGI,IAAI,KAAKF,IAAI,CAACF,YAAY,CAAC,GAAG,IAAI;MAAA,CAC5E,CAAC;IACH,CAAC,MAAM;MACLN,QAAQ,CAACO,OAAO,GAAGC,IAAI,CAAC,CAAC,CAAC;IAC5B;IACA,OAAO;MACLG,SAAS,EAAE,KAAK;MAChBC,IAAI,EAAE;IACR,CAAC;EACH;AACF,CAAC;AAED,IAAMC,QAAQ,GAAG,gBAAgB;AACjC,IAAIC,QAAQ;AACZ,SAASC,WAAWA,CAAE3L,MAAM,EAAE;EAC5B0L,QAAQ,GAAGA,QAAQ,IAAIhL,EAAE,CAACC,cAAc,CAAC8K,QAAQ,CAAC;EAClD,IAAI,CAACC,QAAQ,EAAE;IACbA,QAAQ,GAAG5J,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAGiG,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC4D,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;IAC5DlL,EAAE,CAACmL,UAAU,CAAC;MACZnZ,GAAG,EAAE+Y,QAAQ;MACbpH,IAAI,EAAEqH;IACR,CAAC,CAAC;EACJ;EACA1L,MAAM,CAAC0L,QAAQ,GAAGA,QAAQ;AAC5B;AAEA,SAASI,iBAAiBA,CAAE9L,MAAM,EAAE;EAClC,IAAIA,MAAM,CAAC+L,QAAQ,EAAE;IACnB,IAAMA,QAAQ,GAAG/L,MAAM,CAAC+L,QAAQ;IAChC/L,MAAM,CAACgM,cAAc,GAAG;MACtBC,GAAG,EAAEF,QAAQ,CAACE,GAAG;MACjBC,IAAI,EAAEH,QAAQ,CAACG,IAAI;MACnBC,KAAK,EAAEnM,MAAM,CAACqH,WAAW,GAAG0E,QAAQ,CAACI,KAAK;MAC1CC,MAAM,EAAEpM,MAAM,CAACqM,YAAY,GAAGN,QAAQ,CAACK;IACzC,CAAC;EACH;AACF;AAEA,SAASE,SAASA,CAAEC,MAAM,EAAEhF,QAAQ,EAAE;EACpC,IAAIiF,MAAM,GAAG,EAAE;EACf,IAAIC,SAAS,GAAG,EAAE;EAElB,IACElF,QAAQ,IACN,WAAW,KAAK,UAAW,EAC7B;AAAA,EAGD,MAAM;IACLiF,MAAM,GAAGD,MAAM,CAAChN,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIgI,QAAQ;IACzCkF,SAAS,GAAGF,MAAM,CAAChN,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;EACxC;EAEAiN,MAAM,GAAGA,MAAM,CAACE,iBAAiB,CAAC,CAAC;EACnC,QAAQF,MAAM;IACZ,KAAK,SAAS,CAAC,CAAC;IAChB,KAAK,MAAM,CAAC,CAAC;IACb,KAAK,aAAa;MAAE;MAClBA,MAAM,GAAG,WAAW;MACpB;IACF,KAAK,WAAW;MAAE;MAChBA,MAAM,GAAG,KAAK;MACd;IACF,KAAK,KAAK,CAAC,CAAC;IACZ,KAAK,QAAQ;MAAE;MACbA,MAAM,GAAG,OAAO;MAChB;IACF,KAAK,YAAY;MAAE;MACjBA,MAAM,GAAG,SAAS;MAClB;EACJ;EAEA,OAAO;IACLA,MAAM,EAANA,MAAM;IACNC,SAAS,EAATA;EACF,CAAC;AACH;AAEA,SAASE,kBAAkBA,CAAE3M,MAAM,EAAE;EACnC,IAAA4M,aAAA,GAKI5M,MAAM,CAJR6M,KAAK;IAALA,KAAK,GAAAD,aAAA,cAAG,EAAE,GAAAA,aAAA;IAAAE,aAAA,GAIR9M,MAAM,CAJI+M,KAAK;IAALA,KAAK,GAAAD,aAAA,cAAG,EAAE,GAAAA,aAAA;IAAAE,cAAA,GAIpBhN,MAAM,CAJgBuM,MAAM;IAANA,MAAM,GAAAS,cAAA,cAAG,EAAE,GAAAA,cAAA;IAAAC,gBAAA,GAIjCjN,MAAM,CAHRsI,QAAQ;IAARA,QAAQ,GAAA2E,gBAAA,cAAG,EAAE,GAAAA,gBAAA;IAAEC,KAAK,GAGlBlN,MAAM,CAHOkN,KAAK;IAAEC,OAAO,GAG3BnN,MAAM,CAHcmN,OAAO;IAC7B5F,QAAQ,GAENvH,MAAM,CAFRuH,QAAQ;IAAE6F,eAAe,GAEvBpN,MAAM,CAFEoN,eAAe;IACzBC,UAAU,GACRrN,MAAM,CADRqN,UAAU;IAAE/F,UAAU,GACpBtH,MAAM,CADIsH,UAAU;IAAEgG,iBAAiB,GACvCtN,MAAM,CADgBsN,iBAAiB;EAE3C;;EAEA,IAAMC,UAAU,GAAG,CAAC,CAAC;;EAErB;EACA,IAAAC,UAAA,GAA8BlB,SAAS,CAACC,MAAM,EAAEhF,QAAQ,CAAC;IAAjDiF,MAAM,GAAAgB,UAAA,CAANhB,MAAM;IAAEC,SAAS,GAAAe,UAAA,CAATf,SAAS;EACzB,IAAIgB,WAAW,GAAGN,OAAO;;EAEzB;EACA,IAAMO,UAAU,GAAGC,gBAAgB,CAAC3N,MAAM,EAAE+M,KAAK,CAAC;;EAElD;EACA,IAAMa,WAAW,GAAGC,cAAc,CAAChB,KAAK,CAAC;;EAEzC;EACA,IAAMiB,SAAS,GAAGC,WAAW,CAAC/N,MAAM,CAAC;;EAErC;EACA,IAAIgO,kBAAkB,GAAGV,iBAAiB,CAAC,CAAC;;EAE5C;EACA,IAAIW,iBAAiB,GAAG3G,UAAU;;EAElC;EACA,IAAI4G,WAAW,GAAGb,UAAU;;EAE5B;EACA,IAAMc,YAAY,GAAG,CAAC7F,QAAQ,IAAI,EAAE,EAAE7M,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;;EAExD;;EAEA,IAAM2S,UAAU,GAAG;IACjBC,KAAK,EAAErU,EAAsB;IAC7BuU,OAAO,EAAEvU,EAAwB;IACjCyU,UAAU,EAAEzU,OAAgC;IAC5C2U,cAAc,EAAE3U,KAAgC;IAChD6U,WAAW,EAAEC,cAAc,CAACX,YAAY,CAAC;IACzCY,iBAAiB,EAAE/U,MAAgC;IACnDiV,kBAAkB,EAAEjV,MAAgC;IACpDkV,iBAAiB,EAAElV,MAAgC;IACnDmV,WAAW,EAAEnV,MAA4B,IAAIA,WAAwB;IACrE4T,WAAW,EAAXA,WAAW;IACX0B,WAAW,EAAEvC,KAAK;IAClBW,UAAU,EAAVA,UAAU;IACV6B,gBAAgB,EAAEtB,iBAAiB;IACnCX,iBAAiB,EAAEU,kBAAkB;IACrCxB,MAAM,EAAEA,MAAM,CAACE,iBAAiB,CAAC,CAAC;IAClCD,SAAS,EAATA,SAAS;IACT+C,SAAS,EAAEtC,KAAK;IAChBO,WAAW,EAAXA,WAAW;IACXU,YAAY,EAAZA,YAAY;IACZsB,QAAQ,EAAE3B,SAAS;IACnB4B,cAAc,EAAExB,WAAW;IAC3ByB,mBAAmB,EAAEvC,eAAe;IACpCwC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE,CAAC;IACf;IACAC,UAAU,EAAEpX,SAAS;IACrBqX,OAAO,EAAErX,SAAS;IAClBsX,EAAE,EAAEtX,SAAS;IACbuX,eAAe,EAAEvX,SAAS;IAC1BwX,WAAW,EAAExX,SAAS;IACtByX,cAAc,EAAEzX,SAAS;IACzB0X,SAAS,EAAE;EACb,CAAC;EAED5d,MAAM,CAACuK,MAAM,CAACiD,MAAM,EAAEoO,UAAU,EAAEb,UAAU,CAAC;AAC/C;AAEA,SAASI,gBAAgBA,CAAE3N,MAAM,EAAE+M,KAAK,EAAE;EACxC,IAAIW,UAAU,GAAG1N,MAAM,CAAC0N,UAAU,IAAI,OAAO;EAC7C;IACE,IAAM2C,cAAc,GAAG;MACrBC,IAAI,EAAE,KAAK;MACXC,OAAO,EAAE,IAAI;MACbC,GAAG,EAAE;IACP,CAAC;IACD,IAAMC,kBAAkB,GAAGje,MAAM,CAACsF,IAAI,CAACuY,cAAc,CAAC;IACtD,IAAMK,MAAM,GAAG3D,KAAK,CAACL,iBAAiB,CAAC,CAAC;IACxC,KAAK,IAAI9S,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG6W,kBAAkB,CAACzgB,MAAM,EAAE4J,KAAK,EAAE,EAAE;MAC9D,IAAM+W,EAAE,GAAGF,kBAAkB,CAAC7W,KAAK,CAAC;MACpC,IAAI8W,MAAM,CAACvV,OAAO,CAACwV,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAC7BjD,UAAU,GAAG2C,cAAc,CAACM,EAAE,CAAC;QAC/B;MACF;IACF;EACF;EACA,OAAOjD,UAAU;AACnB;AAEA,SAASG,cAAcA,CAAEhB,KAAK,EAAE;EAC9B,IAAIe,WAAW,GAAGf,KAAK;EACvB,IAAIe,WAAW,EAAE;IACfA,WAAW,GAAGf,KAAK,CAACH,iBAAiB,CAAC,CAAC;EACzC;EACA,OAAOkB,WAAW;AACpB;AAEA,SAASkB,cAAcA,CAAE8B,eAAe,EAAE;EACxC,OAAOnH,WAAW,GACdA,WAAW,CAAC,CAAC,GACbmH,eAAe;AACrB;AAEA,SAAS7C,WAAWA,CAAE/N,MAAM,EAAE;EAC5B,IAAM6Q,SAAS,GACZ,QAAQ;EAEX,IAAI/C,SAAS,GAAG9N,MAAM,CAACyP,QAAQ,IAAIoB,SAAS,CAAC,CAAC;EAC9C;IACE,IAAI7Q,MAAM,CAAC8Q,WAAW,EAAE;MACtBhD,SAAS,GAAG9N,MAAM,CAAC8Q,WAAW;IAChC,CAAC,MAAM,IAAI9Q,MAAM,CAAC+Q,IAAI,IAAI/Q,MAAM,CAAC+Q,IAAI,CAAC9W,GAAG,EAAE;MACzC6T,SAAS,GAAG9N,MAAM,CAAC+Q,IAAI,CAAC9W,GAAG;IAC7B;EACF;EAEA,OAAO6T,SAAS;AAClB;AAEA,IAAIkD,aAAa,GAAG;EAClBjM,WAAW,EAAE,SAAbA,WAAWA,CAAY/E,MAAM,EAAE;IAC7B2L,WAAW,CAAC3L,MAAM,CAAC;IACnB8L,iBAAiB,CAAC9L,MAAM,CAAC;IACzB2M,kBAAkB,CAAC3M,MAAM,CAAC;EAC5B;AACF,CAAC;AAED,IAAIiR,eAAe,GAAG;EACpBlG,IAAI,WAAJA,IAAIA,CAAEH,QAAQ,EAAE;IACd,IAAItW,yEAAA,CAAOsW,QAAQ,MAAK,QAAQ,EAAE;MAChCA,QAAQ,CAACsG,SAAS,GAAGtG,QAAQ,CAACuG,KAAK;IACrC;EACF;AACF,CAAC;AAED,IAAI9I,cAAc,GAAG;EACnBtD,WAAW,EAAE,SAAbA,WAAWA,CAAY/E,MAAM,EAAE;IAC7B,IAAAoR,OAAA,GAAiDpR,MAAM;MAA/CmN,OAAO,GAAAiE,OAAA,CAAPjE,OAAO;MAAE7E,QAAQ,GAAA8I,OAAA,CAAR9I,QAAQ;MAAE+E,UAAU,GAAA+D,OAAA,CAAV/D,UAAU;MAAEH,KAAK,GAAAkE,OAAA,CAALlE,KAAK;IAE5C,IAAMY,SAAS,GAAGC,WAAW,CAAC/N,MAAM,CAAC;IAErC,IAAMmO,YAAY,GAAG,CAAC7F,QAAQ,IAAI,EAAE,EAAE7M,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAEvDuE,MAAM,GAAG8C,UAAU,CAACtQ,MAAM,CAACuK,MAAM,CAACiD,MAAM,EAAE;MACxCqO,KAAK,EAAErU,EAAsB;MAC7BuU,OAAO,EAAEvU,EAAwB;MACjCyU,UAAU,EAAEzU,OAAgC;MAC5C2U,cAAc,EAAE3U,KAAgC;MAChD6U,WAAW,EAAEC,cAAc,CAACX,YAAY,CAAC;MACzCV,WAAW,EAAEN,OAAO;MACpBgB,YAAY,EAAZA,YAAY;MACZsB,QAAQ,EAAE3B,SAAS;MACnB4B,cAAc,EAAErC,UAAU;MAC1BmC,SAAS,EAAEtC,KAAK;MAChBkD,SAAS,EAAE,KAAK;MAChBjB,WAAW,EAAEnV,MAA4B,IAAIA,WAAwB;MACrE+U,iBAAiB,EAAE/U,MAAgC;MACnDiV,kBAAkB,EAAEjV,MAAgC;MACpDkV,iBAAiB,EAAElV,MAAgCgV;IACrD,CAAC,CAAC,CAAC;EACL;AACF,CAAC;AAED,IAAIpH,aAAa,GAAG;EAClB7C,WAAW,EAAE,SAAbA,WAAWA,CAAY/E,MAAM,EAAE;IAC7B,IAAAqR,QAAA,GAAqDrR,MAAM;MAAnD6M,KAAK,GAAAwE,QAAA,CAALxE,KAAK;MAAEE,KAAK,GAAAsE,QAAA,CAALtE,KAAK;MAAAuE,eAAA,GAAAD,QAAA,CAAE9E,MAAM;MAANA,MAAM,GAAA+E,eAAA,cAAG,EAAE,GAAAA,eAAA;MAAAC,iBAAA,GAAAF,QAAA,CAAE9J,QAAQ;MAARA,QAAQ,GAAAgK,iBAAA,cAAG,EAAE,GAAAA,iBAAA;IAChD,IAAM7D,UAAU,GAAGC,gBAAgB,CAAC3N,MAAM,EAAE+M,KAAK,CAAC;IAClD,IAAMa,WAAW,GAAGC,cAAc,CAAChB,KAAK,CAAC;IACzClB,WAAW,CAAC3L,MAAM,CAAC;IAEnB,IAAAwR,WAAA,GAA8BlF,SAAS,CAACC,MAAM,EAAEhF,QAAQ,CAAC;MAAjDiF,MAAM,GAAAgF,WAAA,CAANhF,MAAM;MAAEC,SAAS,GAAA+E,WAAA,CAAT/E,SAAS;IAEzBzM,MAAM,GAAG8C,UAAU,CAACtQ,MAAM,CAACuK,MAAM,CAACiD,MAAM,EAAE;MACxC0N,UAAU,EAAVA,UAAU;MACVE,WAAW,EAAXA,WAAW;MACX0B,WAAW,EAAEvC,KAAK;MAClBP,MAAM,EAANA,MAAM;MACNC,SAAS,EAATA;IACF,CAAC,CAAC,CAAC;EACL;AACF,CAAC;AAED,IAAIhF,aAAa,GAAG;EAClB1C,WAAW,EAAE,SAAbA,WAAWA,CAAY/E,MAAM,EAAE;IAC7B8L,iBAAiB,CAAC9L,MAAM,CAAC;IAEzBA,MAAM,GAAG8C,UAAU,CAACtQ,MAAM,CAACuK,MAAM,CAACiD,MAAM,EAAE;MACxC4P,SAAS,EAAE,CAAC;MACZC,YAAY,EAAE;IAChB,CAAC,CAAC,CAAC;EACL;AACF,CAAC;AAED,IAAI4B,sBAAsB,GAAG;EAC3B1M,WAAW,EAAE,SAAbA,WAAWA,CAAY/E,MAAM,EAAE;IAC7B,IAAQ0R,uBAAuB,GAAK1R,MAAM,CAAlC0R,uBAAuB;IAE/B1R,MAAM,CAAC2R,gBAAgB,GAAG,aAAa;IACvC,IAAID,uBAAuB,KAAK,IAAI,EAAE;MACpC1R,MAAM,CAAC2R,gBAAgB,GAAG,SAAS;IACrC,CAAC,MAAM,IAAID,uBAAuB,KAAK,KAAK,EAAE;MAC5C1R,MAAM,CAAC2R,gBAAgB,GAAG,MAAM;IAClC;EACF;AACF,CAAC;;AAED;;AAEA,IAAMC,aAAa,GAAG;EACpB7G,IAAI,WAAJA,IAAIA,CAAEH,QAAQ,EAAE;IACd;IACA,IAAIA,QAAQ,CAACiH,gBAAgB,IAAI,CAACjH,QAAQ,CAACkH,cAAc,EAAE;MACzDlH,QAAQ,CAACkH,cAAc,GAAGlH,QAAQ,CAACiH,gBAAgB;IACrD;IACA,IAAIjH,QAAQ,CAACmH,eAAe,IAAI,CAACnH,QAAQ,CAACoH,aAAa,EAAE;MACvDpH,QAAQ,CAACoH,aAAa,GAAGpH,QAAQ,CAACmH,eAAe;IACnD;EACF;AACF,CAAC;AAED,IAAME,SAAS,GAAG;EAChBtH,UAAU,EAAVA,UAAU;EACV;EACAM,YAAY,EAAZA,YAAY;EACZ+F,aAAa,EAAbA,aAAa;EACbtJ,iBAAiB,EAAEsJ,aAAa;EAChCC,eAAe,EAAfA,eAAe;EACf5I,cAAc,EAAdA,cAAc;EACdT,aAAa,EAAbA,aAAa;EACbH,aAAa,EAAbA,aAAa;EACbgK,sBAAsB,EAAtBA,sBAAsB;EACtBG,aAAa,EAAbA;AACF,CAAC;AACD,IAAMM,KAAK,GAAG,CACZ,SAAS,EACT,aAAa,EACb,eAAe,EACf,gBAAgB,CACjB;AACD,IAAMC,QAAQ,GAAG,EAAE;AAEnB,IAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC;AAE3D,SAASC,eAAeA,CAAEC,UAAU,EAAErO,MAAM,EAAEc,WAAW,EAAE;EACzD,OAAO,UAAUxB,GAAG,EAAE;IACpB,OAAOU,MAAM,CAACsO,kBAAkB,CAACD,UAAU,EAAE/O,GAAG,EAAEwB,WAAW,CAAC,CAAC;EACjE,CAAC;AACH;AAEA,SAASyN,WAAWA,CAAEF,UAAU,EAAE1H,QAAQ,EAA2D;EAAA,IAAzD6H,UAAU,GAAArhB,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,CAAC,CAAC;EAAA,IAAE2T,WAAW,GAAA3T,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEshB,YAAY,GAAAthB,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,KAAK;EACjG,IAAIgR,aAAa,CAACwI,QAAQ,CAAC,EAAE;IAAE;IAC7B,IAAM+H,MAAM,GAAGD,YAAY,KAAK,IAAI,GAAG9H,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;IACtD,IAAI3I,IAAI,CAACwQ,UAAU,CAAC,EAAE;MACpBA,UAAU,GAAGA,UAAU,CAAC7H,QAAQ,EAAE+H,MAAM,CAAC,IAAI,CAAC,CAAC;IACjD;IACA,KAAK,IAAMjgB,GAAG,IAAIkY,QAAQ,EAAE;MAC1B,IAAIhQ,MAAM,CAAC6X,UAAU,EAAE/f,GAAG,CAAC,EAAE;QAC3B,IAAIkgB,SAAS,GAAGH,UAAU,CAAC/f,GAAG,CAAC;QAC/B,IAAIuP,IAAI,CAAC2Q,SAAS,CAAC,EAAE;UACnBA,SAAS,GAAGA,SAAS,CAAChI,QAAQ,CAAClY,GAAG,CAAC,EAAEkY,QAAQ,EAAE+H,MAAM,CAAC;QACxD;QACA,IAAI,CAACC,SAAS,EAAE;UAAE;UAChBzY,OAAO,CAACC,IAAI,SAAAC,MAAA,CAASiY,UAAU,qFAAAjY,MAAA,CAAyD3H,GAAG,MAAG,CAAC;QACjG,CAAC,MAAM,IAAIwP,KAAK,CAAC0Q,SAAS,CAAC,EAAE;UAAE;UAC7BD,MAAM,CAACC,SAAS,CAAC,GAAGhI,QAAQ,CAAClY,GAAG,CAAC;QACnC,CAAC,MAAM,IAAI0P,aAAa,CAACwQ,SAAS,CAAC,EAAE;UAAE;UACrCD,MAAM,CAACC,SAAS,CAAC9d,IAAI,GAAG8d,SAAS,CAAC9d,IAAI,GAAGpC,GAAG,CAAC,GAAGkgB,SAAS,CAAC9hB,KAAK;QACjE;MACF,CAAC,MAAM,IAAIshB,SAAS,CAACjX,OAAO,CAACzI,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACxC,IAAIuP,IAAI,CAAC2I,QAAQ,CAAClY,GAAG,CAAC,CAAC,EAAE;UACvBigB,MAAM,CAACjgB,GAAG,CAAC,GAAG2f,eAAe,CAACC,UAAU,EAAE1H,QAAQ,CAAClY,GAAG,CAAC,EAAEqS,WAAW,CAAC;QACvE;MACF,CAAC,MAAM;QACL,IAAI,CAAC2N,YAAY,EAAE;UACjBC,MAAM,CAACjgB,GAAG,CAAC,GAAGkY,QAAQ,CAAClY,GAAG,CAAC;QAC7B;MACF;IACF;IACA,OAAOigB,MAAM;EACf,CAAC,MAAM,IAAI1Q,IAAI,CAAC2I,QAAQ,CAAC,EAAE;IACzBA,QAAQ,GAAGyH,eAAe,CAACC,UAAU,EAAE1H,QAAQ,EAAE7F,WAAW,CAAC;EAC/D;EACA,OAAO6F,QAAQ;AACjB;AAEA,SAAS2H,kBAAkBA,CAAED,UAAU,EAAE/O,GAAG,EAAEwB,WAAW,EAA2B;EAAA,IAAzB8N,eAAe,GAAAzhB,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,KAAK;EAChF,IAAI6Q,IAAI,CAACgQ,SAAS,CAAClN,WAAW,CAAC,EAAE;IAAE;IACjCxB,GAAG,GAAG0O,SAAS,CAAClN,WAAW,CAACuN,UAAU,EAAE/O,GAAG,CAAC;EAC9C;EACA,OAAOiP,WAAW,CAACF,UAAU,EAAE/O,GAAG,EAAEwB,WAAW,EAAE,CAAC,CAAC,EAAE8N,eAAe,CAAC;AACvE;AAEA,SAASC,OAAOA,CAAER,UAAU,EAAErO,MAAM,EAAE;EACpC,IAAIrJ,MAAM,CAACqX,SAAS,EAAEK,UAAU,CAAC,EAAE;IACjC,IAAMS,QAAQ,GAAGd,SAAS,CAACK,UAAU,CAAC;IACtC,IAAI,CAACS,QAAQ,EAAE;MAAE;MACf,OAAO,YAAY;QACjB5Y,OAAO,CAAC+G,KAAK,gEAAA7G,MAAA,CAAuCiY,UAAU,OAAI,CAAC;MACrE,CAAC;IACH;IACA,OAAO,UAAUU,IAAI,EAAEC,IAAI,EAAE;MAAE;MAC7B,IAAItO,OAAO,GAAGoO,QAAQ;MACtB,IAAI9Q,IAAI,CAAC8Q,QAAQ,CAAC,EAAE;QAClBpO,OAAO,GAAGoO,QAAQ,CAACC,IAAI,CAAC;MAC1B;MAEAA,IAAI,GAAGR,WAAW,CAACF,UAAU,EAAEU,IAAI,EAAErO,OAAO,CAACoG,IAAI,EAAEpG,OAAO,CAACI,WAAW,CAAC;MAEvE,IAAMgG,IAAI,GAAG,CAACiI,IAAI,CAAC;MACnB,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;QAC/BlI,IAAI,CAAChZ,IAAI,CAACkhB,IAAI,CAAC;MACjB;MACA,IAAIhR,IAAI,CAAC0C,OAAO,CAAC7P,IAAI,CAAC,EAAE;QACtBwd,UAAU,GAAG3N,OAAO,CAAC7P,IAAI,CAACke,IAAI,CAAC;MACjC,CAAC,MAAM,IAAI9Q,KAAK,CAACyC,OAAO,CAAC7P,IAAI,CAAC,EAAE;QAC9Bwd,UAAU,GAAG3N,OAAO,CAAC7P,IAAI;MAC3B;MACA,IAAMiQ,WAAW,GAAGrE,EAAE,CAAC4R,UAAU,CAAC,CAACjhB,KAAK,CAACqP,EAAE,EAAEqK,IAAI,CAAC;MAClD,IAAI/E,SAAS,CAACsM,UAAU,CAAC,EAAE;QAAE;QAC3B,OAAOC,kBAAkB,CAACD,UAAU,EAAEvN,WAAW,EAAEJ,OAAO,CAACI,WAAW,EAAEgB,YAAY,CAACuM,UAAU,CAAC,CAAC;MACnG;MACA,OAAOvN,WAAW;IACpB,CAAC;EACH;EACA,OAAOd,MAAM;AACf;AAEA,IAAMiP,QAAQ,GAAG1gB,MAAM,CAAC6C,MAAM,CAAC,IAAI,CAAC;AAEpC,IAAM8d,KAAK,GAAG,CACZ,sBAAsB,EACtB,eAAe,EACf,iBAAiB,EACjB,QAAQ,EACR,SAAS,EACT,OAAO,CACR;AAED,SAASC,aAAaA,CAAEte,IAAI,EAAE;EAC5B,OAAO,SAASue,OAAOA,CAAApa,IAAA,EAGpB;IAAA,IAFD4N,IAAI,GAAA5N,IAAA,CAAJ4N,IAAI;MACJC,QAAQ,GAAA7N,IAAA,CAAR6N,QAAQ;IAER,IAAMvD,GAAG,GAAG;MACV+P,MAAM,KAAAjZ,MAAA,CAAKvF,IAAI,oBAAAuF,MAAA,CAAiBvF,IAAI;IACtC,CAAC;IACDmN,IAAI,CAAC4E,IAAI,CAAC,IAAIA,IAAI,CAACtD,GAAG,CAAC;IACvBtB,IAAI,CAAC6E,QAAQ,CAAC,IAAIA,QAAQ,CAACvD,GAAG,CAAC;EACjC,CAAC;AACH;AAEA4P,KAAK,CAAC5W,OAAO,CAAC,UAAUzH,IAAI,EAAE;EAC5Boe,QAAQ,CAACpe,IAAI,CAAC,GAAGse,aAAa,CAACte,IAAI,CAAC;AACtC,CAAC,CAAC;AAEF,IAAIye,SAAS,GAAG;EACdC,KAAK,EAAE,CAAC,QAAQ,CAAC;EACjBC,KAAK,EAAE,CAAC,QAAQ,CAAC;EACjBC,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB3hB,IAAI,EAAE,CAAC,QAAQ;AACjB,CAAC;AAED,SAAS4hB,WAAWA,CAAAza,KAAA,EAKjB;EAAA,IAJD0a,OAAO,GAAA1a,KAAA,CAAP0a,OAAO;IACPhN,OAAO,GAAA1N,KAAA,CAAP0N,OAAO;IACPC,IAAI,GAAA3N,KAAA,CAAJ2N,IAAI;IACJC,QAAQ,GAAA5N,KAAA,CAAR4N,QAAQ;EAER,IAAIvD,GAAG,GAAG,KAAK;EACf,IAAIgQ,SAAS,CAACK,OAAO,CAAC,EAAE;IACtBrQ,GAAG,GAAG;MACJ+P,MAAM,EAAE,gBAAgB;MACxBM,OAAO,EAAPA,OAAO;MACPC,QAAQ,EAAEN,SAAS,CAACK,OAAO;IAC7B,CAAC;IACD3R,IAAI,CAAC2E,OAAO,CAAC,IAAIA,OAAO,CAACrD,GAAG,CAAC;EAC/B,CAAC,MAAM;IACLA,GAAG,GAAG;MACJ+P,MAAM,EAAE;IACV,CAAC;IACDrR,IAAI,CAAC4E,IAAI,CAAC,IAAIA,IAAI,CAACtD,GAAG,CAAC;EACzB;EACAtB,IAAI,CAAC6E,QAAQ,CAAC,IAAIA,QAAQ,CAACvD,GAAG,CAAC;AACjC;AAEA,IAAIuQ,QAAQ,GAAG,aAAathB,MAAM,CAACyX,MAAM,CAAC;EACxCrW,SAAS,EAAE,IAAI;EACf+f,WAAW,EAAEA;AACf,CAAC,CAAC;AAEF,IAAMI,UAAU,GAAI,YAAY;EAC9B,IAAIC,OAAO;EACX,OAAO,SAASC,aAAaA,CAAA,EAAI;IAC/B,IAAI,CAACD,OAAO,EAAE;MACZA,OAAO,GAAG,IAAIvU,2CAAG,CAAC,CAAC;IACrB;IACA,OAAOuU,OAAO;EAChB,CAAC;AACH,CAAC,CAAE,CAAC;AAEJ,SAAS3iB,KAAKA,CAAE6iB,GAAG,EAAEjQ,MAAM,EAAE8G,IAAI,EAAE;EACjC,OAAOmJ,GAAG,CAACjQ,MAAM,CAAC,CAAC5S,KAAK,CAAC6iB,GAAG,EAAEnJ,IAAI,CAAC;AACrC;AAEA,SAASoJ,GAAGA,CAAA,EAAI;EACd,OAAO9iB,KAAK,CAAC0iB,UAAU,CAAC,CAAC,EAAE,KAAK,EAAA7jB,KAAA,CAAAgC,SAAA,CAAA2C,KAAA,CAAA7B,IAAA,CAAM5B,SAAS,CAAC,CAAC;AACnD;AACA,SAASgjB,IAAIA,CAAA,EAAI;EACf,OAAO/iB,KAAK,CAAC0iB,UAAU,CAAC,CAAC,EAAE,MAAM,EAAA7jB,KAAA,CAAAgC,SAAA,CAAA2C,KAAA,CAAA7B,IAAA,CAAM5B,SAAS,CAAC,CAAC;AACpD;AACA,SAAS4X,KAAKA,CAAA,EAAI;EAChB,OAAO3X,KAAK,CAAC0iB,UAAU,CAAC,CAAC,EAAE,OAAO,EAAA7jB,KAAA,CAAAgC,SAAA,CAAA2C,KAAA,CAAA7B,IAAA,CAAM5B,SAAS,CAAC,CAAC;AACrD;AACA,SAASijB,KAAKA,CAAA,EAAI;EAChB,OAAOhjB,KAAK,CAAC0iB,UAAU,CAAC,CAAC,EAAE,OAAO,EAAA7jB,KAAA,CAAAgC,SAAA,CAAA2C,KAAA,CAAA7B,IAAA,CAAM5B,SAAS,CAAC,CAAC;AACrD;AAEA,IAAIkjB,QAAQ,GAAG,aAAa9hB,MAAM,CAACyX,MAAM,CAAC;EACxCrW,SAAS,EAAE,IAAI;EACfugB,GAAG,EAAEA,GAAG;EACRC,IAAI,EAAEA,IAAI;EACVpL,KAAK,EAAEA,KAAK;EACZqL,KAAK,EAAEA;AACT,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,QAAQA,CAAE9X,EAAE,EAAE;EACrB,OAAO,YAAY;IACjB,IAAI;MACF,OAAOA,EAAE,CAACpL,KAAK,CAACoL,EAAE,EAAErL,SAAS,CAAC;IAChC,CAAC,CAAC,OAAO9B,CAAC,EAAE;MACV;MACA6K,OAAO,CAAC+G,KAAK,CAAC5R,CAAC,CAAC;IAClB;EACF,CAAC;AACH;AAEA,SAASklB,eAAeA,CAAEpQ,MAAM,EAAE;EAChC,IAAMqQ,YAAY,GAAG,CAAC,CAAC;EACvB,KAAK,IAAM3f,IAAI,IAAIsP,MAAM,EAAE;IACzB,IAAMsQ,KAAK,GAAGtQ,MAAM,CAACtP,IAAI,CAAC;IAC1B,IAAImN,IAAI,CAACyS,KAAK,CAAC,EAAE;MACfD,YAAY,CAAC3f,IAAI,CAAC,GAAGyf,QAAQ,CAACG,KAAK,CAAC;MACpC,OAAOtQ,MAAM,CAACtP,IAAI,CAAC;IACrB;EACF;EACA,OAAO2f,YAAY;AACrB;AAEA,IAAIE,GAAG;AACP,IAAIC,SAAS;AACb,IAAIC,OAAO;AAEX,SAASC,oBAAoBA,CAAEtc,OAAO,EAAE;EACtC,IAAI;IACF,OAAOqG,IAAI,CAACjG,KAAK,CAACJ,OAAO,CAAC;EAC5B,CAAC,CAAC,OAAOlJ,CAAC,EAAE,CAAC;EACb,OAAOkJ,OAAO;AAChB;AAEA,SAASuc,kBAAkBA,CACzBhK,IAAI,EACJ;EACA,IAAIA,IAAI,CAACvR,IAAI,KAAK,SAAS,EAAE;IAC3Bqb,OAAO,GAAG,IAAI;EAChB,CAAC,MAAM,IAAI9J,IAAI,CAACvR,IAAI,KAAK,UAAU,EAAE;IACnCmb,GAAG,GAAG5J,IAAI,CAAC4J,GAAG;IACdC,SAAS,GAAG7J,IAAI,CAACuI,MAAM;IACvB0B,yBAAyB,CAACL,GAAG,EAAE5J,IAAI,CAACuI,MAAM,CAAC;EAC7C,CAAC,MAAM,IAAIvI,IAAI,CAACvR,IAAI,KAAK,SAAS,EAAE;IAClC,IAAMhB,OAAO,GAAG;MACdgB,IAAI,EAAE,SAAS;MACf6K,IAAI,EAAEyQ,oBAAoB,CAAC/J,IAAI,CAACvS,OAAO;IACzC,CAAC;IACD,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqkB,sBAAsB,CAACjlB,MAAM,EAAEY,CAAC,EAAE,EAAE;MACtD,IAAM6T,QAAQ,GAAGwQ,sBAAsB,CAACrkB,CAAC,CAAC;MAC1C6T,QAAQ,CAACjM,OAAO,CAAC;MACjB;MACA,IAAIA,OAAO,CAAC0c,OAAO,EAAE;QACnB;MACF;IACF;EACF,CAAC,MAAM,IAAInK,IAAI,CAACvR,IAAI,KAAK,OAAO,EAAE;IAChCyb,sBAAsB,CAAC1Y,OAAO,CAAC,UAACkI,QAAQ,EAAK;MAC3CA,QAAQ,CAAC;QACPjL,IAAI,EAAE,OAAO;QACb6K,IAAI,EAAEyQ,oBAAoB,CAAC/J,IAAI,CAACvS,OAAO;MACzC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF;AAEA,IAAM2c,mBAAmB,GAAG,EAAE;AAE9B,SAASH,yBAAyBA,CAAEL,GAAG,EAAErB,MAAM,EAAE;EAC/C6B,mBAAmB,CAAC5Y,OAAO,CAAC,UAACkI,QAAQ,EAAK;IACxCA,QAAQ,CAACkQ,GAAG,EAAErB,MAAM,CAAC;EACvB,CAAC,CAAC;EACF6B,mBAAmB,CAACnlB,MAAM,GAAG,CAAC;AAChC;AAEA,SAASolB,eAAeA,CAAErK,IAAI,EAAE;EAC9B,IAAI,CAAC3I,aAAa,CAAC2I,IAAI,CAAC,EAAE;IACxBA,IAAI,GAAG,CAAC,CAAC;EACX;EACA,IAAAsK,gBAAA,GAIIb,eAAe,CAACzJ,IAAI,CAAC;IAHvBnE,OAAO,GAAAyO,gBAAA,CAAPzO,OAAO;IACPC,IAAI,GAAAwO,gBAAA,CAAJxO,IAAI;IACJC,QAAQ,GAAAuO,gBAAA,CAARvO,QAAQ;EAEV,IAAMwO,UAAU,GAAGrT,IAAI,CAAC2E,OAAO,CAAC;EAChC,IAAM2O,OAAO,GAAGtT,IAAI,CAAC4E,IAAI,CAAC;EAC1B,IAAM2O,WAAW,GAAGvT,IAAI,CAAC6E,QAAQ,CAAC;EAElC9V,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAM;IAC3B,IAAI,OAAO2jB,OAAO,KAAK,WAAW,EAAE;MAClCA,OAAO,GAAG,KAAK;MACfF,GAAG,GAAG,EAAE;MACRC,SAAS,GAAG,wBAAwB;IACtC;IACAO,mBAAmB,CAACpjB,IAAI,CAAC,UAAC4iB,GAAG,EAAErB,MAAM,EAAK;MACxC,IAAI/P,GAAG;MACP,IAAIoR,GAAG,EAAE;QACPpR,GAAG,GAAG;UACJ+P,MAAM,EAAE,oBAAoB;UAC5BqB,GAAG,EAAHA;QACF,CAAC;QACDW,UAAU,IAAI1O,OAAO,CAACrD,GAAG,CAAC;MAC5B,CAAC,MAAM;QACLA,GAAG,GAAG;UACJ+P,MAAM,EAAE,sBAAsB,IAAIA,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE;QAC9D,CAAC;QACDiC,OAAO,IAAI1O,IAAI,CAACtD,GAAG,CAAC;MACtB;MACAiS,WAAW,IAAI1O,QAAQ,CAACvD,GAAG,CAAC;IAC9B,CAAC,CAAC;IACF,IAAI,OAAOoR,GAAG,KAAK,WAAW,EAAE;MAC9BK,yBAAyB,CAACL,GAAG,EAAEC,SAAS,CAAC;IAC3C;EACF,CAAC,CAAC;AACJ;AAEA,IAAMK,sBAAsB,GAAG,EAAE;AACjC;AACA,IAAMQ,aAAa,GAAG,SAAhBA,aAAaA,CAAIhZ,EAAE,EAAK;EAC5B,IAAIwY,sBAAsB,CAAC9Z,OAAO,CAACsB,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;IAC7CwY,sBAAsB,CAACljB,IAAI,CAAC0K,EAAE,CAAC;EACjC;AACF,CAAC;AAED,IAAMiZ,cAAc,GAAG,SAAjBA,cAAcA,CAAIjZ,EAAE,EAAK;EAC7B,IAAI,CAACA,EAAE,EAAE;IACPwY,sBAAsB,CAACjlB,MAAM,GAAG,CAAC;EACnC,CAAC,MAAM;IACL,IAAM4J,KAAK,GAAGqb,sBAAsB,CAAC9Z,OAAO,CAACsB,EAAE,CAAC;IAChD,IAAI7C,KAAK,GAAG,CAAC,CAAC,EAAE;MACdqb,sBAAsB,CAACtY,MAAM,CAAC/C,KAAK,EAAE,CAAC,CAAC;IACzC;EACF;AACF,CAAC;AAED,SAAS+b,KAAKA,CACZnc,IAAI,EAEJ;EAAA,SAAAoc,KAAA,GAAAxkB,SAAA,CAAApB,MAAA,EADG+a,IAAI,OAAA7a,KAAA,CAAA0lB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJ9K,IAAI,CAAA8K,KAAA,QAAAzkB,SAAA,CAAAykB,KAAA;EAAA;EAEP1b,OAAO,CAACX,IAAI,CAAC,CAACnI,KAAK,CAAC8I,OAAO,EAAE4Q,IAAI,CAAC;AACpC;AAEA,IAAI+K,QAAQ,GAAGpV,EAAE,CAAC2H,cAAc,IAAI3H,EAAE,CAAC2H,cAAc,CAAC,CAAC;AACvD,IAAI,CAACyN,QAAQ,EAAE;EACbA,QAAQ,GAAGpV,EAAE,CAACgH,iBAAiB,CAAC,CAAC;AACnC;AACA,IAAMqJ,IAAI,GAAG+E,QAAQ,GAAGA,QAAQ,CAAC/E,IAAI,GAAG,IAAI;AAC5C,IAAMgF,iBAAiB,GACrBhF,IAAI,IAAIA,IAAI,CAAC9W,GAAG,KAAK,SAAS,GAAGyG,EAAE,CAACsV,OAAO,CAACD,iBAAiB,GAAGrV,EAAE,CAACqV,iBAAiB;AAEtF,IAAI3Q,GAAG,GAAG,aAAa5S,MAAM,CAACyX,MAAM,CAAC;EACnCrW,SAAS,EAAE,IAAI;EACfmiB,iBAAiB,EAAEA,iBAAiB;EACpCX,eAAe,EAAEA,eAAe;EAChCK,aAAa,EAAEA,aAAa;EAC5BC,cAAc,EAAEA,cAAc;EAC9BX,kBAAkB,EAAEA,kBAAkB;EACtCY,KAAK,EAAEA;AACT,CAAC,CAAC;AAEF,IAAMM,KAAK,GAAG,CAAC,WAAW,EAAE,sBAAsB,EAAE,iBAAiB,CAAC;AAEtE,SAASC,aAAaA,CAAEC,EAAE,EAAEC,MAAM,EAAE;EAClC,IAAMC,SAAS,GAAGF,EAAE,CAACE,SAAS;EAC9B;EACA,KAAK,IAAIzlB,CAAC,GAAGylB,SAAS,CAACrmB,MAAM,GAAG,CAAC,EAAEY,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC9C,IAAM0lB,OAAO,GAAGD,SAAS,CAACzlB,CAAC,CAAC;IAC5B,IAAI0lB,OAAO,CAACC,MAAM,CAACC,OAAO,KAAKJ,MAAM,EAAE;MACrC,OAAOE,OAAO;IAChB;EACF;EACA;EACA,IAAIG,QAAQ;EACZ,KAAK,IAAI7lB,EAAC,GAAGylB,SAAS,CAACrmB,MAAM,GAAG,CAAC,EAAEY,EAAC,IAAI,CAAC,EAAEA,EAAC,EAAE,EAAE;IAC9C6lB,QAAQ,GAAGP,aAAa,CAACG,SAAS,CAACzlB,EAAC,CAAC,EAAEwlB,MAAM,CAAC;IAC9C,IAAIK,QAAQ,EAAE;MACZ,OAAOA,QAAQ;IACjB;EACF;AACF;AAEA,SAASC,YAAYA,CAAE/R,OAAO,EAAE;EAC9B,OAAOgS,QAAQ,CAAChS,OAAO,CAAC;AAC1B;AAEA,SAASiS,MAAMA,CAAA,EAAI;EACjB,OAAO,CAAC,CAAC,IAAI,CAACC,KAAK;AACrB;AAEA,SAASC,YAAYA,CAAEC,MAAM,EAAE;EAC7B,IAAI,CAACC,YAAY,CAAC,KAAK,EAAED,MAAM,CAAC;AAClC;AAEA,SAASE,mBAAmBA,CAAEC,UAAU,EAAEC,QAAQ,EAAEC,KAAK,EAAE;EACzD,IAAMC,UAAU,GAAGH,UAAU,CAACD,mBAAmB,CAACE,QAAQ,CAAC,IAAI,EAAE;EACjEE,UAAU,CAAC9a,OAAO,CAAC,UAAA+a,SAAS,EAAI;IAC9B,IAAMC,GAAG,GAAGD,SAAS,CAACE,OAAO,CAACD,GAAG;IACjCH,KAAK,CAACG,GAAG,CAAC,GAAGD,SAAS,CAACtZ,GAAG,IAAIyZ,MAAM,CAACH,SAAS,CAAC;IAC/C;MACE,IAAIA,SAAS,CAACE,OAAO,CAACE,UAAU,KAAK,QAAQ,EAAE;QAC7CJ,SAAS,CAACL,mBAAmB,CAAC,aAAa,CAAC,CAAC1a,OAAO,CAAC,UAAAob,eAAe,EAAI;UACtEV,mBAAmB,CAACU,eAAe,EAAER,QAAQ,EAAEC,KAAK,CAAC;QACvD,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC;AACJ;AAEA,SAASQ,QAAQA,CAAEC,IAAI,EAAEC,OAAO,EAAE;EAChC,IAAMC,OAAO,GAAAnmB,4EAAA,CAAOomB,GAAG,EAAA3jB,oFAAA,CAAI7B,MAAM,CAACsF,IAAI,CAAC+f,IAAI,CAAC,EAAC;EAC7C,IAAMI,OAAO,GAAGzlB,MAAM,CAACsF,IAAI,CAACggB,OAAO,CAAC;EACpCG,OAAO,CAAC1b,OAAO,CAAC,UAAA7J,GAAG,EAAI;IACrB,IAAMwlB,QAAQ,GAAGL,IAAI,CAACnlB,GAAG,CAAC;IAC1B,IAAMylB,QAAQ,GAAGL,OAAO,CAACplB,GAAG,CAAC;IAC7B,IAAIxC,KAAK,CAACG,OAAO,CAAC6nB,QAAQ,CAAC,IAAIhoB,KAAK,CAACG,OAAO,CAAC8nB,QAAQ,CAAC,IAAID,QAAQ,CAACloB,MAAM,KAAKmoB,QAAQ,CAACnoB,MAAM,IAAImoB,QAAQ,CAACC,KAAK,CAAC,UAAAtnB,KAAK;MAAA,OAAIonB,QAAQ,CAACG,QAAQ,CAACvnB,KAAK,CAAC;IAAA,EAAC,EAAE;MAClJ;IACF;IACA+mB,IAAI,CAACnlB,GAAG,CAAC,GAAGylB,QAAQ;IACpBJ,OAAO,CAACO,MAAM,CAAC5lB,GAAG,CAAC;EACrB,CAAC,CAAC;EACFqlB,OAAO,CAACxb,OAAO,CAAC,UAAA7J,GAAG,EAAI;IACrB,OAAOmlB,IAAI,CAACnlB,GAAG,CAAC;EAClB,CAAC,CAAC;EACF,OAAOmlB,IAAI;AACb;AAEA,SAASU,QAAQA,CAAEpC,EAAE,EAAE;EACrB,IAAMe,UAAU,GAAGf,EAAE,CAACI,MAAM;EAC5B,IAAMsB,IAAI,GAAG,CAAC,CAAC;EACfrlB,MAAM,CAACC,cAAc,CAAC0jB,EAAE,EAAE,OAAO,EAAE;IACjC5M,GAAG,WAAHA,GAAGA,CAAA,EAAI;MACL,IAAM6N,KAAK,GAAG,CAAC,CAAC;MAChBH,mBAAmB,CAACC,UAAU,EAAE,UAAU,EAAEE,KAAK,CAAC;MAClD;MACA,IAAMoB,aAAa,GAAGtB,UAAU,CAACD,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,EAAE;MAC7EuB,aAAa,CAACjc,OAAO,CAAC,UAAA+a,SAAS,EAAI;QACjC,IAAMC,GAAG,GAAGD,SAAS,CAACE,OAAO,CAACD,GAAG;QACjC,IAAI,CAACH,KAAK,CAACG,GAAG,CAAC,EAAE;UACfH,KAAK,CAACG,GAAG,CAAC,GAAG,EAAE;QACjB;QACAH,KAAK,CAACG,GAAG,CAAC,CAACxlB,IAAI,CAACulB,SAAS,CAACtZ,GAAG,IAAIyZ,MAAM,CAACH,SAAS,CAAC,CAAC;MACrD,CAAC,CAAC;MACF,OAAOM,QAAQ,CAACC,IAAI,EAAET,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;AACJ;AAEA,SAASqB,UAAUA,CAAEC,KAAK,EAAE;EAC1B,IAAA5c,KAAA,GAGI4c,KAAK,CAAC3B,MAAM,IAAI2B,KAAK,CAAC5nB,KAAK;IAF7BslB,MAAM,GAAAta,KAAA,CAANsa,MAAM;IACNuC,UAAU,GAAA7c,KAAA,CAAV6c,UAAU,CACoB,CAAC;;EAEjC,IAAIlC,QAAQ;EAEZ,IAAIL,MAAM,EAAE;IACVK,QAAQ,GAAGP,aAAa,CAAC,IAAI,CAAClY,GAAG,EAAEoY,MAAM,CAAC;EAC5C;EAEA,IAAI,CAACK,QAAQ,EAAE;IACbA,QAAQ,GAAG,IAAI,CAACzY,GAAG;EACrB;EAEA2a,UAAU,CAACC,MAAM,GAAGnC,QAAQ;AAC9B;AAEA,SAASoC,eAAeA,CAAEvB,SAAS,EAAE;EACnC;EACA,IAAMwB,KAAK,GAAG,mBAAmB;EACjCtmB,MAAM,CAACC,cAAc,CAAC6kB,SAAS,EAAEwB,KAAK,EAAE;IACtCxmB,YAAY,EAAE,IAAI;IAClBD,UAAU,EAAE,KAAK;IACjBvB,KAAK,EAAE;EACT,CAAC,CAAC;EACF,OAAOwmB,SAAS;AAClB;AAEA,SAASG,MAAMA,CAAEtV,GAAG,EAAE;EACpB,IAAM4W,EAAE,GAAG,QAAQ;EACnB,IAAMC,IAAI,GAAG,UAAU;EACvB,IAAI9gB,QAAQ,CAACiK,GAAG,CAAC,IAAI3P,MAAM,CAACymB,YAAY,CAAC9W,GAAG,CAAC,EAAE;IAC7C;IACA3P,MAAM,CAACC,cAAc,CAAC0P,GAAG,EAAE4W,EAAE,EAAE;MAC7BzmB,YAAY,EAAE,IAAI;MAClBD,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAA8B,iFAAA,KACFomB,IAAI,EAAG,IAAI;IAEhB,CAAC,CAAC;EACJ;EACA,OAAO7W,GAAG;AACZ;AAEA,IAAM+W,UAAU,GAAG,wBAAwB;AAC3C,SAASC,kBAAkBA,CAAEC,SAAS,EAAEC,UAAU,EAAE;EAClD,IAAIA,UAAU,EAAE;IACd7mB,MAAM,CAACsF,IAAI,CAACuhB,UAAU,CAAC,CAAC9c,OAAO,CAAC,UAACzH,IAAI,EAAK;MACxC,IAAMwkB,OAAO,GAAGxkB,IAAI,CAACykB,KAAK,CAACL,UAAU,CAAC;MACtC,IAAII,OAAO,EAAE;QACX,IAAME,WAAW,GAAGF,OAAO,CAAC,CAAC,CAAC;QAC9BF,SAAS,CAACtkB,IAAI,CAAC,GAAGukB,UAAU,CAACvkB,IAAI,CAAC;QAClCskB,SAAS,CAACI,WAAW,CAAC,GAAGH,UAAU,CAACG,WAAW,CAAC;MAClD;IACF,CAAC,CAAC;EACJ;AACF;AAEA,IAAMC,MAAM,GAAGC,IAAI;AACnB,IAAMC,WAAW,GAAGC,SAAS;AAE7B,IAAMC,WAAW,GAAG,IAAI;AAExB,IAAMC,SAAS,GAAGxX,MAAM,CAAC,UAACvH,GAAG,EAAK;EAChC,OAAO4H,QAAQ,CAAC5H,GAAG,CAACU,OAAO,CAACoe,WAAW,EAAE,GAAG,CAAC,CAAC;AAChD,CAAC,CAAC;AAEF,SAASE,gBAAgBA,CAAE7C,UAAU,EAAE;EACrC,IAAM8C,eAAe,GAAG9C,UAAU,CAACF,YAAY;EAC/C,IAAMiD,eAAe,GAAG,SAAlBA,eAAeA,CAAavB,KAAK,EAAW;IAAA,SAAAwB,KAAA,GAAA9oB,SAAA,CAAApB,MAAA,EAAN+a,IAAI,OAAA7a,KAAA,CAAAgqB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJpP,IAAI,CAAAoP,KAAA,QAAA/oB,SAAA,CAAA+oB,KAAA;IAAA;IAC9C;IACA,IAAI,IAAI,CAACnc,GAAG,IAAK,IAAI,CAACwZ,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC4C,OAAQ,EAAE;MACtD1B,KAAK,GAAGoB,SAAS,CAACpB,KAAK,CAAC;IAC1B,CAAC,MAAM;MACL;MACA,IAAM2B,QAAQ,GAAGP,SAAS,CAACpB,KAAK,CAAC;MACjC,IAAI2B,QAAQ,KAAK3B,KAAK,EAAE;QACtBsB,eAAe,CAAC3oB,KAAK,CAAC,IAAI,GAAGgpB,QAAQ,EAAAhgB,MAAA,CAAK0Q,IAAI,CAAC,CAAC;MAClD;IACF;IACA,OAAOiP,eAAe,CAAC3oB,KAAK,CAAC,IAAI,GAAGqnB,KAAK,EAAAre,MAAA,CAAK0Q,IAAI,CAAC,CAAC;EACtD,CAAC;EACD,IAAI;IACF;IACAmM,UAAU,CAACF,YAAY,GAAGiD,eAAe;EAC3C,CAAC,CAAC,OAAO/Y,KAAK,EAAE;IACdgW,UAAU,CAACoD,aAAa,GAAGL,eAAe;EAC5C;AACF;AAEA,SAASM,QAAQA,CAAEzlB,IAAI,EAAE6P,OAAO,EAAE6V,WAAW,EAAE;EAC7C,IAAMC,OAAO,GAAG9V,OAAO,CAAC7P,IAAI,CAAC;EAC7B6P,OAAO,CAAC7P,IAAI,CAAC,GAAG,YAAmB;IACjC+jB,eAAe,CAAC,IAAI,CAAC;IACrBkB,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAIU,OAAO,EAAE;MAAA,SAAAC,KAAA,GAAAtpB,SAAA,CAAApB,MAAA,EAHc+a,IAAI,OAAA7a,KAAA,CAAAwqB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJ5P,IAAI,CAAA4P,KAAA,IAAAvpB,SAAA,CAAAupB,KAAA;MAAA;MAI7B,OAAOF,OAAO,CAACppB,KAAK,CAAC,IAAI,EAAE0Z,IAAI,CAAC;IAClC;EACF,CAAC;AACH;AACA,IAAI,CAAC0O,MAAM,CAACmB,YAAY,EAAE;EACxBnB,MAAM,CAACmB,YAAY,GAAG,IAAI;EAC1BlB,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAA2B;IAAA,IAAd/U,OAAO,GAAAvT,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,CAAC,CAAC;IAC3BmpB,QAAQ,CAAC,QAAQ,EAAE5V,OAAO,CAAC;IAC3B,OAAO8U,MAAM,CAAC9U,OAAO,CAAC;EACxB,CAAC;EACD+U,IAAI,CAACmB,KAAK,GAAGpB,MAAM,CAACoB,KAAK;EAEzBjB,SAAS,GAAG,SAAZA,SAASA,CAAA,EAA2B;IAAA,IAAdjV,OAAO,GAAAvT,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,CAAC,CAAC;IAChCmpB,QAAQ,CAAC,SAAS,EAAE5V,OAAO,CAAC;IAC5B,OAAOgV,WAAW,CAAChV,OAAO,CAAC;EAC7B,CAAC;AACH;AAEA,IAAMmW,gBAAgB,GAAG,CACvB,mBAAmB,EACnB,eAAe,EACf,kBAAkB,EAClB,iBAAiB,EACjB,mBAAmB,EACnB,cAAc,EACd,UAAU,EACV,cAAc,CACf;AAED,SAASC,SAASA,CAAE5E,EAAE,EAAEF,KAAK,EAAE;EAC7B,IAAMiB,UAAU,GAAGf,EAAE,CAAC6E,GAAG,CAAC7E,EAAE,CAAC8E,MAAM,CAAC;EACpChF,KAAK,CAAC1Z,OAAO,CAAC,UAAA2e,IAAI,EAAI;IACpB,IAAItgB,MAAM,CAACsc,UAAU,EAAEgE,IAAI,CAAC,EAAE;MAC5B/E,EAAE,CAAC+E,IAAI,CAAC,GAAGhE,UAAU,CAACgE,IAAI,CAAC;IAC7B;EACF,CAAC,CAAC;AACJ;AAEA,SAASC,OAAOA,CAAExX,IAAI,EAAEgV,UAAU,EAAE;EAClC,IAAI,CAACA,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EAEA,IAAIlZ,2CAAG,CAACkF,OAAO,IAAIzU,KAAK,CAACG,OAAO,CAACoP,2CAAG,CAACkF,OAAO,CAAChB,IAAI,CAAC,CAAC,EAAE;IACnD,OAAO,IAAI;EACb;EAEAgV,UAAU,GAAGA,UAAU,CAACxoB,OAAO,IAAIwoB,UAAU;EAE7C,IAAI1W,IAAI,CAAC0W,UAAU,CAAC,EAAE;IACpB,IAAI1W,IAAI,CAAC0W,UAAU,CAACyC,aAAa,CAACzX,IAAI,CAAC,CAAC,EAAE;MACxC,OAAO,IAAI;IACb;IACA,IAAIgV,UAAU,CAAC0C,KAAK,IAClB1C,UAAU,CAAC0C,KAAK,CAAC1W,OAAO,IACxBzU,KAAK,CAACG,OAAO,CAACsoB,UAAU,CAAC0C,KAAK,CAAC1W,OAAO,CAAChB,IAAI,CAAC,CAAC,EAAE;MAC/C,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEA,IAAI1B,IAAI,CAAC0W,UAAU,CAAChV,IAAI,CAAC,CAAC,IAAIzT,KAAK,CAACG,OAAO,CAACsoB,UAAU,CAAChV,IAAI,CAAC,CAAC,EAAE;IAC7D,OAAO,IAAI;EACb;EACA,IAAM2X,MAAM,GAAG3C,UAAU,CAAC2C,MAAM;EAChC,IAAIprB,KAAK,CAACG,OAAO,CAACirB,MAAM,CAAC,EAAE;IACzB,OAAO,CAAC,CAACA,MAAM,CAACrgB,IAAI,CAAC,UAAA2N,KAAK;MAAA,OAAIuS,OAAO,CAACxX,IAAI,EAAEiF,KAAK,CAAC;IAAA,EAAC;EACrD;AACF;AAEA,SAAS2S,SAASA,CAAEC,SAAS,EAAE/X,KAAK,EAAEkV,UAAU,EAAE;EAChDlV,KAAK,CAAClH,OAAO,CAAC,UAAAoH,IAAI,EAAI;IACpB,IAAIwX,OAAO,CAACxX,IAAI,EAAEgV,UAAU,CAAC,EAAE;MAC7B6C,SAAS,CAAC7X,IAAI,CAAC,GAAG,UAAUoH,IAAI,EAAE;QAChC,OAAO,IAAI,CAAC/M,GAAG,IAAI,IAAI,CAACA,GAAG,CAACyd,WAAW,CAAC9X,IAAI,EAAEoH,IAAI,CAAC;MACrD,CAAC;IACH;EACF,CAAC,CAAC;AACJ;AAEA,SAAS2Q,gBAAgBA,CAAEF,SAAS,EAAE7C,UAAU,EAAiB;EAAA,IAAfgD,QAAQ,GAAAvqB,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,EAAE;EAC7DwqB,SAAS,CAACjD,UAAU,CAAC,CAACpc,OAAO,CAAC,UAACoH,IAAI;IAAA,OAAKkY,UAAU,CAACL,SAAS,EAAE7X,IAAI,EAAEgY,QAAQ,CAAC;EAAA,EAAC;AAChF;AAEA,SAASC,SAASA,CAAEjD,UAAU,EAAc;EAAA,IAAZlV,KAAK,GAAArS,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,EAAE;EACxC,IAAIunB,UAAU,EAAE;IACdnmB,MAAM,CAACsF,IAAI,CAAC6gB,UAAU,CAAC,CAACpc,OAAO,CAAC,UAACzH,IAAI,EAAK;MACxC,IAAIA,IAAI,CAACqG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI8G,IAAI,CAAC0W,UAAU,CAAC7jB,IAAI,CAAC,CAAC,EAAE;QACtD2O,KAAK,CAAC1R,IAAI,CAAC+C,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;EACJ;EACA,OAAO2O,KAAK;AACd;AAEA,SAASoY,UAAUA,CAAEL,SAAS,EAAE7X,IAAI,EAAEgY,QAAQ,EAAE;EAC9C,IAAIA,QAAQ,CAACxgB,OAAO,CAACwI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC/I,MAAM,CAAC4gB,SAAS,EAAE7X,IAAI,CAAC,EAAE;IAC7D6X,SAAS,CAAC7X,IAAI,CAAC,GAAG,UAAUoH,IAAI,EAAE;MAChC,OAAO,IAAI,CAAC/M,GAAG,IAAI,IAAI,CAACA,GAAG,CAACyd,WAAW,CAAC9X,IAAI,EAAEoH,IAAI,CAAC;IACrD,CAAC;EACH;AACF;AAEA,SAAS+Q,gBAAgBA,CAAErc,GAAG,EAAEkZ,UAAU,EAAE;EAC1CA,UAAU,GAAGA,UAAU,CAACxoB,OAAO,IAAIwoB,UAAU;EAC7C,IAAIoD,YAAY;EAChB,IAAI9Z,IAAI,CAAC0W,UAAU,CAAC,EAAE;IACpBoD,YAAY,GAAGpD,UAAU;EAC3B,CAAC,MAAM;IACLoD,YAAY,GAAGtc,GAAG,CAACuc,MAAM,CAACrD,UAAU,CAAC;EACvC;EACAA,UAAU,GAAGoD,YAAY,CAACpX,OAAO;EACjC,OAAO,CAACoX,YAAY,EAAEpD,UAAU,CAAC;AACnC;AAEA,SAASsD,SAASA,CAAE9F,EAAE,EAAE+F,QAAQ,EAAE;EAChC,IAAIhsB,KAAK,CAACG,OAAO,CAAC6rB,QAAQ,CAAC,IAAIA,QAAQ,CAAClsB,MAAM,EAAE;IAC9C,IAAMmsB,MAAM,GAAG3pB,MAAM,CAAC6C,MAAM,CAAC,IAAI,CAAC;IAClC6mB,QAAQ,CAAC3f,OAAO,CAAC,UAAA6f,QAAQ,EAAI;MAC3BD,MAAM,CAACC,QAAQ,CAAC,GAAG,IAAI;IACzB,CAAC,CAAC;IACFjG,EAAE,CAACkG,YAAY,GAAGlG,EAAE,CAACgG,MAAM,GAAGA,MAAM;EACtC;AACF;AAEA,SAASG,UAAUA,CAAEC,MAAM,EAAErF,UAAU,EAAE;EACvCqF,MAAM,GAAG,CAACA,MAAM,IAAI,EAAE,EAAEhd,KAAK,CAAC,GAAG,CAAC;EAClC,IAAMgL,GAAG,GAAGgS,MAAM,CAACvsB,MAAM;EAEzB,IAAIua,GAAG,KAAK,CAAC,EAAE;IACb2M,UAAU,CAACV,OAAO,GAAG+F,MAAM,CAAC,CAAC,CAAC;EAChC,CAAC,MAAM,IAAIhS,GAAG,KAAK,CAAC,EAAE;IACpB2M,UAAU,CAACV,OAAO,GAAG+F,MAAM,CAAC,CAAC,CAAC;IAC9BrF,UAAU,CAACsF,QAAQ,GAAGD,MAAM,CAAC,CAAC,CAAC;EACjC;AACF;AAEA,SAASE,QAAQA,CAAE9D,UAAU,EAAE+D,OAAO,EAAE;EACtC,IAAIrY,IAAI,GAAGsU,UAAU,CAACtU,IAAI,IAAI,CAAC,CAAC;EAChC,IAAM4E,OAAO,GAAG0P,UAAU,CAAC1P,OAAO,IAAI,CAAC,CAAC;EAExC,IAAI,OAAO5E,IAAI,KAAK,UAAU,EAAE;IAC9B,IAAI;MACFA,IAAI,GAAGA,IAAI,CAACrR,IAAI,CAAC0pB,OAAO,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAOptB,CAAC,EAAE;MACV,IAAI0K,wHAAW,CAAC2iB,aAAa,EAAE;QAC7BxiB,OAAO,CAACC,IAAI,CAAC,wEAAwE,EAAEiK,IAAI,CAAC;MAC9F;IACF;EACF,CAAC,MAAM;IACL,IAAI;MACF;MACAA,IAAI,GAAGxF,IAAI,CAACjG,KAAK,CAACiG,IAAI,CAACC,SAAS,CAACuF,IAAI,CAAC,CAAC;IACzC,CAAC,CAAC,OAAO/U,CAAC,EAAE,CAAE;EAChB;EAEA,IAAI,CAAC8S,aAAa,CAACiC,IAAI,CAAC,EAAE;IACxBA,IAAI,GAAG,CAAC,CAAC;EACX;EAEA7R,MAAM,CAACsF,IAAI,CAACmR,OAAO,CAAC,CAAC1M,OAAO,CAAC,UAAA+V,UAAU,EAAI;IACzC,IAAIoK,OAAO,CAACE,mBAAmB,CAACzhB,OAAO,CAACmX,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC1X,MAAM,CAACyJ,IAAI,EAAEiO,UAAU,CAAC,EAAE;MACvFjO,IAAI,CAACiO,UAAU,CAAC,GAAGrJ,OAAO,CAACqJ,UAAU,CAAC;IACxC;EACF,CAAC,CAAC;EAEF,OAAOjO,IAAI;AACb;AAEA,IAAMwY,UAAU,GAAG,CAACroB,MAAM,EAAEC,MAAM,EAAE3B,OAAO,EAAEN,MAAM,EAAEtC,KAAK,EAAE,IAAI,CAAC;AAEjE,SAAS4sB,cAAcA,CAAEhoB,IAAI,EAAE;EAC7B,OAAO,SAASioB,QAAQA,CAAEC,MAAM,EAAEC,MAAM,EAAE;IACxC,IAAI,IAAI,CAACjf,GAAG,EAAE;MACZ,IAAI,CAACA,GAAG,CAAClJ,IAAI,CAAC,GAAGkoB,MAAM,CAAC,CAAC;IAC3B;EACF,CAAC;AACH;AAEA,SAASE,aAAaA,CAAEvE,UAAU,EAAEjC,YAAY,EAAE;EAChD,IAAMyG,YAAY,GAAGxE,UAAU,CAACyE,SAAS;EACzC,IAAMC,UAAU,GAAG1E,UAAU,CAAC2E,OAAO;EACrC,IAAMC,SAAS,GAAG5E,UAAU,CAAC2C,MAAM;EAEnC,IAAIkC,QAAQ,GAAG7E,UAAU,CAAC8E,KAAK;EAE/B,IAAI,CAACD,QAAQ,EAAE;IACb7E,UAAU,CAAC8E,KAAK,GAAGD,QAAQ,GAAG,EAAE;EAClC;EAEA,IAAMJ,SAAS,GAAG,EAAE;EACpB,IAAIltB,KAAK,CAACG,OAAO,CAAC8sB,YAAY,CAAC,EAAE;IAC/BA,YAAY,CAAC5gB,OAAO,CAAC,UAAAmhB,QAAQ,EAAI;MAC/BN,SAAS,CAACrrB,IAAI,CAAC2rB,QAAQ,CAACjiB,OAAO,CAAC,QAAQ,EAAK,IAAI,CAAApB,MAAA,OAAK,CAAC,CAAC;MACxD,IAAIqjB,QAAQ,KAAK,kBAAkB,EAAE;QACnC,IAAIxtB,KAAK,CAACG,OAAO,CAACmtB,QAAQ,CAAC,EAAE;UAC3BA,QAAQ,CAACzrB,IAAI,CAAC,MAAM,CAAC;UACrByrB,QAAQ,CAACzrB,IAAI,CAAC,OAAO,CAAC;QACxB,CAAC,MAAM;UACLyrB,QAAQ,CAAC1oB,IAAI,GAAG;YACd0E,IAAI,EAAEhF,MAAM;YACZrE,OAAO,EAAE;UACX,CAAC;UACDqtB,QAAQ,CAAC1sB,KAAK,GAAG;YACf0I,IAAI,EAAE,CAAChF,MAAM,EAAEC,MAAM,EAAE3B,OAAO,EAAE5C,KAAK,EAAEsC,MAAM,EAAEsP,IAAI,CAAC;YACpD3R,OAAO,EAAE;UACX,CAAC;QACH;MACF;IACF,CAAC,CAAC;EACJ;EACA,IAAIiS,aAAa,CAACib,UAAU,CAAC,IAAIA,UAAU,CAACI,KAAK,EAAE;IACjDL,SAAS,CAACrrB,IAAI,CACZ2kB,YAAY,CAAC;MACXiH,UAAU,EAAEC,cAAc,CAACP,UAAU,CAACI,KAAK,EAAE,IAAI;IACnD,CAAC,CACH,CAAC;EACH;EACA,IAAIvtB,KAAK,CAACG,OAAO,CAACktB,SAAS,CAAC,EAAE;IAC5BA,SAAS,CAAChhB,OAAO,CAAC,UAAAshB,QAAQ,EAAI;MAC5B,IAAIzb,aAAa,CAACyb,QAAQ,CAAC,IAAIA,QAAQ,CAACJ,KAAK,EAAE;QAC7CL,SAAS,CAACrrB,IAAI,CACZ2kB,YAAY,CAAC;UACXiH,UAAU,EAAEC,cAAc,CAACC,QAAQ,CAACJ,KAAK,EAAE,IAAI;QACjD,CAAC,CACH,CAAC;MACH;IACF,CAAC,CAAC;EACJ;EACA,OAAOL,SAAS;AAClB;AAEA,SAASU,aAAaA,CAAEprB,GAAG,EAAE8G,IAAI,EAAEukB,YAAY,EAAEC,IAAI,EAAE;EACrD;EACA,IAAI9tB,KAAK,CAACG,OAAO,CAACmJ,IAAI,CAAC,IAAIA,IAAI,CAACxJ,MAAM,KAAK,CAAC,EAAE;IAC5C,OAAOwJ,IAAI,CAAC,CAAC,CAAC;EAChB;EACA,OAAOA,IAAI;AACb;AAEA,SAASokB,cAAcA,CAAEH,KAAK,EAA0C;EAAA,IAAxCQ,UAAU,GAAA7sB,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,KAAK;EAAA,IAAE4sB,IAAI,GAAA5sB,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,EAAE;EAAA,IAAEuT,OAAO,GAAAvT,SAAA,CAAApB,MAAA,OAAAoB,SAAA,MAAAsH,SAAA;EACpE,IAAMilB,UAAU,GAAG,CAAC,CAAC;EACrB,IAAI,CAACM,UAAU,EAAE;IACfN,UAAU,CAACO,KAAK,GAAG;MACjB1kB,IAAI,EAAEhF,MAAM;MACZ1D,KAAK,EAAE;IACT,CAAC;IACD;MACE,IAAK6T,OAAO,CAACwZ,WAAW,EAAE;QACxBR,UAAU,CAACS,gBAAgB,GAAG;UAC5B5kB,IAAI,EAAE,IAAI;UACV1I,KAAK,EAAE;QACT,CAAC;QACD6sB,UAAU,CAACU,gBAAgB,GAAG;UAC5B7kB,IAAI,EAAE,IAAI;UACV1I,KAAK,EAAE;QACT,CAAC;MACH;IACF;IACA;IACA6sB,UAAU,CAACW,mBAAmB,GAAG;MAC/B9kB,IAAI,EAAEhF,MAAM;MACZ1D,KAAK,EAAE;IACT,CAAC;IACD6sB,UAAU,CAACzB,QAAQ,GAAG;MAAE;MACtB1iB,IAAI,EAAE,IAAI;MACV1I,KAAK,EAAE,EAAE;MACTisB,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,MAAM,EAAEC,MAAM,EAAE;QAClC,IAAMd,MAAM,GAAG3pB,MAAM,CAAC6C,MAAM,CAAC,IAAI,CAAC;QAClC2nB,MAAM,CAACzgB,OAAO,CAAC,UAAA6f,QAAQ,EAAI;UACzBD,MAAM,CAACC,QAAQ,CAAC,GAAG,IAAI;QACzB,CAAC,CAAC;QACF,IAAI,CAACmC,OAAO,CAAC;UACXpC,MAAM,EAANA;QACF,CAAC,CAAC;MACJ;IACF,CAAC;EACH;EACA,IAAIjsB,KAAK,CAACG,OAAO,CAACotB,KAAK,CAAC,EAAE;IAAE;IAC1BA,KAAK,CAAClhB,OAAO,CAAC,UAAA7J,GAAG,EAAI;MACnBirB,UAAU,CAACjrB,GAAG,CAAC,GAAG;QAChB8G,IAAI,EAAE,IAAI;QACVujB,QAAQ,EAAED,cAAc,CAACpqB,GAAG;MAC9B,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,MAAM,IAAI0P,aAAa,CAACqb,KAAK,CAAC,EAAE;IAAE;IACjCjrB,MAAM,CAACsF,IAAI,CAAC2lB,KAAK,CAAC,CAAClhB,OAAO,CAAC,UAAA7J,GAAG,EAAI;MAChC,IAAM8rB,IAAI,GAAGf,KAAK,CAAC/qB,GAAG,CAAC;MACvB,IAAI0P,aAAa,CAACoc,IAAI,CAAC,EAAE;QAAE;QACzB,IAAI1tB,KAAK,GAAG0tB,IAAI,CAACruB,OAAO;QACxB,IAAI8R,IAAI,CAACnR,KAAK,CAAC,EAAE;UACfA,KAAK,GAAGA,KAAK,CAAC,CAAC;QACjB;QAEA0tB,IAAI,CAAChlB,IAAI,GAAGskB,aAAa,CAACprB,GAAG,EAAE8rB,IAAI,CAAChlB,IAAI,CAAC;QAEzCmkB,UAAU,CAACjrB,GAAG,CAAC,GAAG;UAChB8G,IAAI,EAAEqjB,UAAU,CAAC1hB,OAAO,CAACqjB,IAAI,CAAChlB,IAAI,CAAC,KAAK,CAAC,CAAC,GAAGglB,IAAI,CAAChlB,IAAI,GAAG,IAAI;UAC7D1I,KAAK,EAALA,KAAK;UACLisB,QAAQ,EAAED,cAAc,CAACpqB,GAAG;QAC9B,CAAC;MACH,CAAC,MAAM;QAAE;QACP,IAAM8G,IAAI,GAAGskB,aAAa,CAACprB,GAAG,EAAE8rB,IAAI,CAAC;QACrCb,UAAU,CAACjrB,GAAG,CAAC,GAAG;UAChB8G,IAAI,EAAEqjB,UAAU,CAAC1hB,OAAO,CAAC3B,IAAI,CAAC,KAAK,CAAC,CAAC,GAAGA,IAAI,GAAG,IAAI;UACnDujB,QAAQ,EAAED,cAAc,CAACpqB,GAAG;QAC9B,CAAC;MACH;IACF,CAAC,CAAC;EACJ;EACA,OAAOirB,UAAU;AACnB;AAEA,SAASc,SAASA,CAAE/F,KAAK,EAAE;EACzB;EACA,IAAI;IACFA,KAAK,CAACgG,EAAE,GAAG7f,IAAI,CAACjG,KAAK,CAACiG,IAAI,CAACC,SAAS,CAAC4Z,KAAK,CAAC,CAAC;EAC9C,CAAC,CAAC,OAAOppB,CAAC,EAAE,CAAE;EAEdopB,KAAK,CAACiG,eAAe,GAAGtc,IAAI;EAC5BqW,KAAK,CAACkG,cAAc,GAAGvc,IAAI;EAE3BqW,KAAK,CAACmG,MAAM,GAAGnG,KAAK,CAACmG,MAAM,IAAI,CAAC,CAAC;EAEjC,IAAI,CAACjkB,MAAM,CAAC8d,KAAK,EAAE,QAAQ,CAAC,EAAE;IAC5BA,KAAK,CAAC3B,MAAM,GAAG,CAAC,CAAC;EACnB;EAEA,IAAInc,MAAM,CAAC8d,KAAK,EAAE,UAAU,CAAC,EAAE;IAC7BA,KAAK,CAAC3B,MAAM,GAAGziB,yEAAA,CAAOokB,KAAK,CAAC3B,MAAM,MAAK,QAAQ,GAAG2B,KAAK,CAAC3B,MAAM,GAAG,CAAC,CAAC;IACnE2B,KAAK,CAAC3B,MAAM,CAAC+H,QAAQ,GAAGpG,KAAK,CAACoG,QAAQ;EACxC;EAEA,IAAI1c,aAAa,CAACsW,KAAK,CAAC3B,MAAM,CAAC,EAAE;IAC/B2B,KAAK,CAACmG,MAAM,GAAGrsB,MAAM,CAACuK,MAAM,CAAC,CAAC,CAAC,EAAE2b,KAAK,CAACmG,MAAM,EAAEnG,KAAK,CAAC3B,MAAM,CAAC;EAC9D;EAEA,OAAO2B,KAAK;AACd;AAEA,SAASqG,aAAaA,CAAE5I,EAAE,EAAE6I,cAAc,EAAE;EAC1C,IAAItC,OAAO,GAAGvG,EAAE;EAChB6I,cAAc,CAACziB,OAAO,CAAC,UAAA0iB,aAAa,EAAI;IACtC,IAAMC,QAAQ,GAAGD,aAAa,CAAC,CAAC,CAAC;IACjC,IAAMnuB,KAAK,GAAGmuB,aAAa,CAAC,CAAC,CAAC;IAC9B,IAAIC,QAAQ,IAAI,OAAOpuB,KAAK,KAAK,WAAW,EAAE;MAAE;MAC9C,IAAMquB,QAAQ,GAAGF,aAAa,CAAC,CAAC,CAAC;MACjC,IAAMG,SAAS,GAAGH,aAAa,CAAC,CAAC,CAAC;MAElC,IAAII,IAAI;MACR,IAAI5qB,MAAM,CAAC6qB,SAAS,CAACJ,QAAQ,CAAC,EAAE;QAC9BG,IAAI,GAAGH,QAAQ;MACjB,CAAC,MAAM,IAAI,CAACA,QAAQ,EAAE;QACpBG,IAAI,GAAG3C,OAAO;MAChB,CAAC,MAAM,IAAI,OAAOwC,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,EAAE;QACnD,IAAIA,QAAQ,CAAC/jB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;UACjCkkB,IAAI,GAAGH,QAAQ,CAACK,MAAM,CAAC,CAAC,CAAC;QAC3B,CAAC,MAAM;UACLF,IAAI,GAAGlJ,EAAE,CAACqJ,WAAW,CAACN,QAAQ,EAAExC,OAAO,CAAC;QAC1C;MACF;MAEA,IAAIjoB,MAAM,CAAC6qB,SAAS,CAACD,IAAI,CAAC,EAAE;QAC1B3C,OAAO,GAAG5rB,KAAK;MACjB,CAAC,MAAM,IAAI,CAACquB,QAAQ,EAAE;QACpBzC,OAAO,GAAG2C,IAAI,CAACvuB,KAAK,CAAC;MACvB,CAAC,MAAM;QACL,IAAIZ,KAAK,CAACG,OAAO,CAACgvB,IAAI,CAAC,EAAE;UACvB3C,OAAO,GAAG2C,IAAI,CAACpkB,IAAI,CAAC,UAAAwkB,QAAQ,EAAI;YAC9B,OAAOtJ,EAAE,CAACqJ,WAAW,CAACL,QAAQ,EAAEM,QAAQ,CAAC,KAAK3uB,KAAK;UACrD,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIsR,aAAa,CAACid,IAAI,CAAC,EAAE;UAC9B3C,OAAO,GAAGlqB,MAAM,CAACsF,IAAI,CAACunB,IAAI,CAAC,CAACpkB,IAAI,CAAC,UAAAykB,OAAO,EAAI;YAC1C,OAAOvJ,EAAE,CAACqJ,WAAW,CAACL,QAAQ,EAAEE,IAAI,CAACK,OAAO,CAAC,CAAC,KAAK5uB,KAAK;UAC1D,CAAC,CAAC;QACJ,CAAC,MAAM;UACLqJ,OAAO,CAAC+G,KAAK,CAAC,iBAAiB,EAAEme,IAAI,CAAC;QACxC;MACF;MAEA,IAAID,SAAS,EAAE;QACb1C,OAAO,GAAGvG,EAAE,CAACqJ,WAAW,CAACJ,SAAS,EAAE1C,OAAO,CAAC;MAC9C;IACF;EACF,CAAC,CAAC;EACF,OAAOA,OAAO;AAChB;AAEA,SAASiD,iBAAiBA,CAAExJ,EAAE,EAAEyJ,KAAK,EAAElH,KAAK,EAAEmH,QAAQ,EAAE;EACtD,IAAMC,QAAQ,GAAG,CAAC,CAAC;EAEnB,IAAI5vB,KAAK,CAACG,OAAO,CAACuvB,KAAK,CAAC,IAAIA,KAAK,CAAC5vB,MAAM,EAAE;IACxC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI4vB,KAAK,CAACrjB,OAAO,CAAC,UAAC2iB,QAAQ,EAAEtlB,KAAK,EAAK;MACjC,IAAI,OAAOslB,QAAQ,KAAK,QAAQ,EAAE;QAChC,IAAI,CAACA,QAAQ,EAAE;UAAE;UACfY,QAAQ,CAAC,GAAG,GAAGlmB,KAAK,CAAC,GAAGuc,EAAE;QAC5B,CAAC,MAAM;UACL,IAAI+I,QAAQ,KAAK,QAAQ,EAAE;YAAE;YAC3BY,QAAQ,CAAC,GAAG,GAAGlmB,KAAK,CAAC,GAAG8e,KAAK;UAC/B,CAAC,MAAM,IAAIwG,QAAQ,KAAK,WAAW,EAAE;YACnCY,QAAQ,CAAC,GAAG,GAAGlmB,KAAK,CAAC,GAAG8e,KAAK,CAAC3B,MAAM,GAAG2B,KAAK,CAAC3B,MAAM,CAAC8I,QAAQ,IAAIA,QAAQ,GAAGA,QAAQ;UACrF,CAAC,MAAM,IAAIX,QAAQ,CAAC/jB,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAAE;YAC9C2kB,QAAQ,CAAC,GAAG,GAAGlmB,KAAK,CAAC,GAAGuc,EAAE,CAACqJ,WAAW,CAACN,QAAQ,CAACzjB,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAEid,KAAK,CAAC;UAChF,CAAC,MAAM;YACLoH,QAAQ,CAAC,GAAG,GAAGlmB,KAAK,CAAC,GAAGuc,EAAE,CAACqJ,WAAW,CAACN,QAAQ,CAAC;UAClD;QACF;MACF,CAAC,MAAM;QACLY,QAAQ,CAAC,GAAG,GAAGlmB,KAAK,CAAC,GAAGmlB,aAAa,CAAC5I,EAAE,EAAE+I,QAAQ,CAAC;MACrD;IACF,CAAC,CAAC;EACJ;EAEA,OAAOY,QAAQ;AACjB;AAEA,SAASC,aAAaA,CAAEC,GAAG,EAAE;EAC3B,IAAM7d,GAAG,GAAG,CAAC,CAAC;EACd,KAAK,IAAIvR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGovB,GAAG,CAAChwB,MAAM,EAAEY,CAAC,EAAE,EAAE;IACnC,IAAMqvB,OAAO,GAAGD,GAAG,CAACpvB,CAAC,CAAC;IACtBuR,GAAG,CAAC8d,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;EAC9B;EACA,OAAO9d,GAAG;AACZ;AAEA,SAAS+d,gBAAgBA,CAAE/J,EAAE,EAAEuC,KAAK,EAA+C;EAAA,IAA7C3N,IAAI,GAAA3Z,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,EAAE;EAAA,IAAEwuB,KAAK,GAAAxuB,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAG,EAAE;EAAA,IAAE+uB,QAAQ,GAAA/uB,SAAA,CAAApB,MAAA,OAAAoB,SAAA,MAAAsH,SAAA;EAAA,IAAE4Z,UAAU,GAAAlhB,SAAA,CAAApB,MAAA,OAAAoB,SAAA,MAAAsH,SAAA;EAC/E,IAAI0nB,eAAe,GAAG,KAAK,CAAC,CAAC;;EAE7B;EACA,IAAMP,QAAQ,GAAGzd,aAAa,CAACsW,KAAK,CAAC3B,MAAM,CAAC,GACxC2B,KAAK,CAAC3B,MAAM,CAAC8I,QAAQ,IAAI,CAACnH,KAAK,CAAC3B,MAAM,CAAC,GACvC,CAAC2B,KAAK,CAAC3B,MAAM,CAAC;EAElB,IAAIoJ,QAAQ,EAAE;IAAE;IACdC,eAAe,GAAG1H,KAAK,CAAC2H,aAAa,IACnC3H,KAAK,CAAC2H,aAAa,CAAC7I,OAAO,IAC3BkB,KAAK,CAAC2H,aAAa,CAAC7I,OAAO,CAAC4C,OAAO,KAAK,IAAI;IAC9C,IAAI,CAACrP,IAAI,CAAC/a,MAAM,EAAE;MAAE;MAClB,IAAIowB,eAAe,EAAE;QACnB,OAAO,CAAC1H,KAAK,CAAC;MAChB;MACA,OAAOmH,QAAQ;IACjB;EACF;EAEA,IAAMC,QAAQ,GAAGH,iBAAiB,CAACxJ,EAAE,EAAEyJ,KAAK,EAAElH,KAAK,EAAEmH,QAAQ,CAAC;EAE9D,IAAMS,GAAG,GAAG,EAAE;EACdvV,IAAI,CAACxO,OAAO,CAAC,UAAAgkB,GAAG,EAAI;IAClB,IAAIA,GAAG,KAAK,QAAQ,EAAE;MACpB,IAAIjO,UAAU,KAAK,aAAa,IAAI,CAAC6N,QAAQ,EAAE;QAAE;QAC/CG,GAAG,CAACvuB,IAAI,CAAC2mB,KAAK,CAACmG,MAAM,CAAC/tB,KAAK,CAAC;MAC9B,CAAC,MAAM;QACL,IAAIqvB,QAAQ,IAAI,CAACC,eAAe,EAAE;UAChCE,GAAG,CAACvuB,IAAI,CAAC8tB,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,MAAM;UAAE;UACPS,GAAG,CAACvuB,IAAI,CAAC2mB,KAAK,CAAC;QACjB;MACF;IACF,CAAC,MAAM;MACL,IAAIxoB,KAAK,CAACG,OAAO,CAACkwB,GAAG,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACxCD,GAAG,CAACvuB,IAAI,CAACguB,aAAa,CAACQ,GAAG,CAAC,CAAC;MAC9B,CAAC,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI3lB,MAAM,CAACklB,QAAQ,EAAES,GAAG,CAAC,EAAE;QAC3DD,GAAG,CAACvuB,IAAI,CAAC+tB,QAAQ,CAACS,GAAG,CAAC,CAAC;MACzB,CAAC,MAAM;QACLD,GAAG,CAACvuB,IAAI,CAACwuB,GAAG,CAAC;MACf;IACF;EACF,CAAC,CAAC;EAEF,OAAOD,GAAG;AACZ;AAEA,IAAME,IAAI,GAAG,GAAG;AAChB,IAAMC,MAAM,GAAG,GAAG;AAElB,SAASC,gBAAgBA,CAAEC,SAAS,EAAEC,OAAO,EAAE;EAC7C,OAAQD,SAAS,KAAKC,OAAO,IAEzBA,OAAO,KAAK,cAAc,KAExBD,SAAS,KAAK,OAAO,IACrBA,SAAS,KAAK,KAAK,CAEtB;AACL;AAEA,SAASE,YAAYA,CAAE1K,EAAE,EAAE;EACzB,IAAI2K,OAAO,GAAG3K,EAAE,CAAC2K,OAAO;EACxB;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACA,OAAO,KAAKA,OAAO,CAACC,QAAQ,CAACC,OAAO,IAAIF,OAAO,CAACA,OAAO,CAACC,QAAQ,CAACC,OAAO,IAAIF,OAAO,CAACvK,MAAM,CAACiG,QAAQ,CAAC,EAAE;IAC9HsE,OAAO,GAAGA,OAAO,CAACA,OAAO;EAC3B;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACA,OAAO;AACnC;AAEA,SAASG,WAAWA,CAAEvI,KAAK,EAAE;EAAA,IAAAhc,MAAA;EAC3Bgc,KAAK,GAAG+F,SAAS,CAAC/F,KAAK,CAAC;;EAExB;EACA,IAAMlB,OAAO,GAAG,CAACkB,KAAK,CAAC2H,aAAa,IAAI3H,KAAK,CAACmG,MAAM,EAAErH,OAAO;EAC7D,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOrd,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;EAChC;EACA,IAAM8mB,SAAS,GAAG1J,OAAO,CAAC0J,SAAS,IAAI1J,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;EAC9D,IAAI,CAAC0J,SAAS,EAAE;IACd,OAAO/mB,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;EAChC;;EAEA;EACA,IAAMumB,SAAS,GAAGjI,KAAK,CAAClf,IAAI;EAE5B,IAAM8mB,GAAG,GAAG,EAAE;EAEdY,SAAS,CAAC3kB,OAAO,CAAC,UAAA4kB,QAAQ,EAAI;IAC5B,IAAI3nB,IAAI,GAAG2nB,QAAQ,CAAC,CAAC,CAAC;IACtB,IAAMC,WAAW,GAAGD,QAAQ,CAAC,CAAC,CAAC;IAE/B,IAAMhB,QAAQ,GAAG3mB,IAAI,CAAC2G,MAAM,CAAC,CAAC,CAAC,KAAKsgB,MAAM;IAC1CjnB,IAAI,GAAG2mB,QAAQ,GAAG3mB,IAAI,CAAC3E,KAAK,CAAC,CAAC,CAAC,GAAG2E,IAAI;IACtC,IAAM6nB,MAAM,GAAG7nB,IAAI,CAAC2G,MAAM,CAAC,CAAC,CAAC,KAAKqgB,IAAI;IACtChnB,IAAI,GAAG6nB,MAAM,GAAG7nB,IAAI,CAAC3E,KAAK,CAAC,CAAC,CAAC,GAAG2E,IAAI;IAEpC,IAAI4nB,WAAW,IAAIV,gBAAgB,CAACC,SAAS,EAAEnnB,IAAI,CAAC,EAAE;MACpD4nB,WAAW,CAAC7kB,OAAO,CAAC,UAAA+kB,UAAU,EAAI;QAChC,IAAMhP,UAAU,GAAGgP,UAAU,CAAC,CAAC,CAAC;QAChC,IAAIhP,UAAU,EAAE;UACd,IAAIiP,UAAU,GAAG7kB,MAAI,CAACsB,GAAG;UACzB,IAAIujB,UAAU,CAACR,QAAQ,CAACC,OAAO,EAAE;YAAE;YACjCO,UAAU,GAAGV,YAAY,CAACU,UAAU,CAAC,IAAIA,UAAU;UACrD;UACA,IAAIjP,UAAU,KAAK,OAAO,EAAE;YAC1BiP,UAAU,CAAClN,KAAK,CAAChjB,KAAK,CAACkwB,UAAU,EAC/BrB,gBAAgB,CACdxjB,MAAI,CAACsB,GAAG,EACR0a,KAAK,EACL4I,UAAU,CAAC,CAAC,CAAC,EACbA,UAAU,CAAC,CAAC,CAAC,EACbnB,QAAQ,EACR7N,UACF,CAAC,CAAC;YACJ;UACF;UACA,IAAMkP,OAAO,GAAGD,UAAU,CAACjP,UAAU,CAAC;UACtC,IAAI,CAACrQ,IAAI,CAACuf,OAAO,CAAC,EAAE;YAClB,IAAMhoB,KAAI,GAAGkD,MAAI,CAACsB,GAAG,CAACid,MAAM,KAAK,MAAM,GAAG,MAAM,GAAG,WAAW;YAC9D,IAAMwG,IAAI,GAAG/kB,MAAI,CAACma,KAAK,IAAIna,MAAI,CAACglB,EAAE;YAClC,MAAM,IAAI5hB,KAAK,IAAAzF,MAAA,CAAIb,KAAI,SAAAa,MAAA,CAAKonB,IAAI,kCAAApnB,MAAA,CAA6BiY,UAAU,OAAG,CAAC;UAC7E;UACA,IAAI+O,MAAM,EAAE;YACV,IAAIG,OAAO,CAACG,IAAI,EAAE;cAChB;YACF;YACAH,OAAO,CAACG,IAAI,GAAG,IAAI;UACrB;UACA,IAAIvd,MAAM,GAAG8b,gBAAgB,CAC3BxjB,MAAI,CAACsB,GAAG,EACR0a,KAAK,EACL4I,UAAU,CAAC,CAAC,CAAC,EACbA,UAAU,CAAC,CAAC,CAAC,EACbnB,QAAQ,EACR7N,UACF,CAAC;UACDlO,MAAM,GAAGlU,KAAK,CAACG,OAAO,CAAC+T,MAAM,CAAC,GAAGA,MAAM,GAAG,EAAE;UAC5C;UACA,IAAI,2DAA2D,CAACrP,IAAI,CAACysB,OAAO,CAAC5sB,QAAQ,CAAC,CAAC,CAAC,EAAE;YACxF;YACAwP,MAAM,GAAGA,MAAM,CAAC/J,MAAM,CAAC,YAAqBqe,KAAK,CAAC,CAAC;UACrD;UACA4H,GAAG,CAACvuB,IAAI,CAACyvB,OAAO,CAACnwB,KAAK,CAACkwB,UAAU,EAAEnd,MAAM,CAAC,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,IACEuc,SAAS,KAAK,OAAO,IACrBL,GAAG,CAACtwB,MAAM,KAAK,CAAC,IAChB,OAAOswB,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW,EAC7B;IACA,OAAOA,GAAG,CAAC,CAAC,CAAC;EACf;AACF;AAEA,IAAMsB,aAAa,GAAG,CAAC,CAAC;AAExB,SAASC,eAAeA,CAAEC,EAAE,EAAE;EAC5B,IAAMC,YAAY,GAAGH,aAAa,CAACE,EAAE,CAAC;EACtC,OAAOF,aAAa,CAACE,EAAE,CAAC;EACxB,OAAOC,YAAY;AACrB;AAEA,IAAMte,KAAK,GAAG,CACZ,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,gBAAgB,EAChB,eAAe,EACf,sBAAsB,CACvB;AAED,SAASue,gBAAgBA,CAAA,EAAI;EAC3BviB,2CAAG,CAACvN,SAAS,CAAC+vB,qBAAqB,GAAG,YAAY;IAChD;IACA;MACE,OAAO,IAAI,CAAC1L,MAAM,CAAC0L,qBAAqB,CAAC,CAAC;IAC5C;EACF,CAAC;EACD,IAAMC,QAAQ,GAAGziB,2CAAG,CAACvN,SAAS,CAACupB,WAAW;EAC1Chc,2CAAG,CAACvN,SAAS,CAACupB,WAAW,GAAG,UAAU9X,IAAI,EAAEoH,IAAI,EAAE;IAChD,IAAIpH,IAAI,KAAK,QAAQ,IAAIoH,IAAI,IAAIA,IAAI,CAACoX,MAAM,EAAE;MAC5C,IAAI,CAACC,gBAAgB,GAAGP,eAAe,CAAC9W,IAAI,CAACoX,MAAM,CAAC;MACpD,OAAOpX,IAAI,CAACoX,MAAM;IACpB;IACA,OAAOD,QAAQ,CAAClvB,IAAI,CAAC,IAAI,EAAE2Q,IAAI,EAAEoH,IAAI,CAAC;EACxC,CAAC;AACH;AAEA,SAASsX,qBAAqBA,CAAA,EAAI;EAChC,IAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,IAAMC,OAAO,GAAG,CAAC,CAAC;EAElB,SAASC,SAASA,CAAE/lB,EAAE,EAAE;IACtB,IAAM8f,MAAM,GAAG,IAAI,CAACwE,QAAQ,CAAC0B,SAAS,CAACvE,KAAK;IAC5C,IAAI3B,MAAM,EAAE;MACV,IAAM2B,KAAK,GAAG3B,MAAM,CAAChd,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAClC9C,EAAE,CAACyhB,KAAK,CAAC;IACX;EACF;EAEAze,2CAAG,CAACvN,SAAS,CAACwwB,OAAO,GAAG,UAAUxE,KAAK,EAAE;IACvC,IAAMyE,IAAI,GAAGL,MAAM,CAACpE,KAAK,CAAC;IAC1B,IAAI,CAACyE,IAAI,EAAE;MACTJ,OAAO,CAACrE,KAAK,CAAC,GAAG,IAAI;MACrB,IAAI,CAAC/J,GAAG,CAAC,gBAAgB,EAAE,YAAM;QAC/B,OAAOoO,OAAO,CAACrE,KAAK,CAAC;MACvB,CAAC,CAAC;IACJ;IACA,OAAOyE,IAAI;EACb,CAAC;EAEDljB,2CAAG,CAACvN,SAAS,CAAC0wB,OAAO,GAAG,UAAU1E,KAAK,EAAEppB,IAAI,EAAE+tB,OAAO,EAAE;IACtD,IAAMF,IAAI,GAAGL,MAAM,CAACpE,KAAK,CAAC;IAC1B,IAAIyE,IAAI,EAAE;MACR,IAAMve,MAAM,GAAGue,IAAI,CAAC7tB,IAAI,CAAC,IAAI,EAAE;MAC/B,IAAI+tB,OAAO,EAAE;QACX,OAAOze,MAAM;MACf;MACA,OAAOA,MAAM,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;EAED3E,2CAAG,CAACvN,SAAS,CAAC4wB,OAAO,GAAG,UAAUhuB,IAAI,EAAEhE,KAAK,EAAE;IAC7C,IAAI8I,KAAK,GAAG,CAAC;IACb4oB,SAAS,CAACxvB,IAAI,CAAC,IAAI,EAAE,UAAAkrB,KAAK,EAAI;MAC5B,IAAMyE,IAAI,GAAGL,MAAM,CAACpE,KAAK,CAAC;MAC1B,IAAM9Z,MAAM,GAAGue,IAAI,CAAC7tB,IAAI,CAAC,GAAG6tB,IAAI,CAAC7tB,IAAI,CAAC,IAAI,EAAE;MAC5CsP,MAAM,CAACrS,IAAI,CAACjB,KAAK,CAAC;MAClB8I,KAAK,GAAGwK,MAAM,CAACpU,MAAM,GAAG,CAAC;IAC3B,CAAC,CAAC;IACF,OAAO4J,KAAK;EACd,CAAC;EAED6F,2CAAG,CAACvN,SAAS,CAAC6wB,QAAQ,GAAG,YAAY;IACnCP,SAAS,CAACxvB,IAAI,CAAC,IAAI,EAAE,UAAAkrB,KAAK,EAAI;MAC5BoE,MAAM,CAACpE,KAAK,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;EAEDze,2CAAG,CAACvN,SAAS,CAAC8wB,QAAQ,GAAG,YAAY;IACnCR,SAAS,CAACxvB,IAAI,CAAC,IAAI,EAAE,UAAAkrB,KAAK,EAAI;MAC5B,IAAIqE,OAAO,CAACrE,KAAK,CAAC,EAAE;QAClBqE,OAAO,CAACrE,KAAK,CAAC,CAACnV,YAAY,CAAC,CAAC;MAC/B;IACF,CAAC,CAAC;EACJ,CAAC;EAEDtJ,2CAAG,CAACmJ,KAAK,CAAC;IACRqa,SAAS,WAATA,SAASA,CAAA,EAAI;MACX,IAAMR,SAAS,GAAG,IAAI,CAAC1B,QAAQ,CAAC0B,SAAS;MACzC,IAAMvE,KAAK,GAAGuE,SAAS,IAAIA,SAAS,CAACvE,KAAK;MAC1C,IAAIA,KAAK,EAAE;QACT,OAAOoE,MAAM,CAACpE,KAAK,CAAC;QACpB,OAAOqE,OAAO,CAACrE,KAAK,CAAC;MACvB;IACF;EACF,CAAC,CAAC;AACJ;AAEA,SAASgF,YAAYA,CAAE/M,EAAE,EAAAvY,KAAA,EAGtB;EAAA,IAFDqY,KAAK,GAAArY,KAAA,CAALqY,KAAK;IACLsC,QAAQ,GAAA3a,KAAA,CAAR2a,QAAQ;EAERyJ,gBAAgB,CAAC,CAAC;EAClB;IACEK,qBAAqB,CAAC,CAAC;EACzB;EACA,IAAIlM,EAAE,CAAC4K,QAAQ,CAACoC,KAAK,EAAE;IACrB1jB,2CAAG,CAACvN,SAAS,CAACkxB,MAAM,GAAGjN,EAAE,CAAC4K,QAAQ,CAACoC,KAAK;EAC1C;EACA9hB,UAAU,CAAC5B,2CAAG,CAAC;EAEfA,2CAAG,CAACvN,SAAS,CAACmxB,MAAM,GAAG,WAAW;EAElC5jB,2CAAG,CAACmJ,KAAK,CAAC;IACRC,YAAY,WAAZA,YAAYA,CAAA,EAAI;MACd,IAAI,CAAC,IAAI,CAACkY,QAAQ,CAAC9F,MAAM,EAAE;QACzB;MACF;MAEA,IAAI,CAACA,MAAM,GAAG,IAAI,CAAC8F,QAAQ,CAAC9F,MAAM;MAElC,IAAI,CAACD,GAAG,GAAApoB,iFAAA;QACNyR,IAAI,EAAE,CAAC;MAAC,GACP,IAAI,CAAC4W,MAAM,EAAG,IAAI,CAAC8F,QAAQ,CAAC7J,UAAU,CACxC;MAED,IAAI,CAACX,MAAM,GAAG,IAAI,CAACwK,QAAQ,CAAC7J,UAAU;MAEtC,OAAO,IAAI,CAAC6J,QAAQ,CAAC9F,MAAM;MAC3B,OAAO,IAAI,CAAC8F,QAAQ,CAAC7J,UAAU;MAC/B,IACI,IAAI,CAAC+D,MAAM,KAAK,MAAM,IACxB,OAAOnd,MAAM,KAAK,UAAU,EAC5B;QAAE;QACF,IAAM4L,GAAG,GAAG5L,MAAM,CAAC,CAAC;QACpB,IAAI4L,GAAG,CAAC1L,GAAG,IAAI0L,GAAG,CAAC1L,GAAG,CAACslB,KAAK,EAAE;UAC5B,IAAI,CAACC,KAAK,GAAG7Z,GAAG,CAAC1L,GAAG,CAACslB,KAAK;QAC5B;MACF;MACA,IAAI,IAAI,CAACrI,MAAM,KAAK,KAAK,EAAE;QACzB1C,QAAQ,CAAC,IAAI,CAAC;QACdwC,SAAS,CAAC,IAAI,EAAE9E,KAAK,CAAC;MACxB;IACF;EACF,CAAC,CAAC;EAEF,IAAMuN,UAAU,GAAG;IACjBC,QAAQ,WAARA,QAAQA,CAAE1Y,IAAI,EAAE;MACd,IAAI,IAAI,CAAC/M,GAAG,EAAE;QAAE;QACd;MACF;MACA;QACE,IAAI0C,EAAE,CAACgjB,OAAO,IAAI,CAAChjB,EAAE,CAACgjB,OAAO,CAAC,UAAU,CAAC,EAAE;UAAE;UAC3CvpB,OAAO,CAAC+G,KAAK,CAAC,qDAAqD,CAAC;QACtE;MACF;MAEA,IAAI,CAAClD,GAAG,GAAGmY,EAAE;MAEb,IAAI,CAACnY,GAAG,CAACgd,GAAG,GAAG;QACbtR,GAAG,EAAE;MACP,CAAC;MAED,IAAI,CAAC1L,GAAG,CAACuY,MAAM,GAAG,IAAI;MACtB;MACA,IAAI,CAACvY,GAAG,CAAC2lB,UAAU,GAAG,IAAI,CAACA,UAAU;MAErC,IAAI,CAAC3lB,GAAG,CAAC4lB,UAAU,GAAG,IAAI;MAC1B,IAAI,CAAC5lB,GAAG,CAACyd,WAAW,CAAC,SAAS,EAAE1Q,IAAI,CAAC;MAErC,IAAI,CAAC/M,GAAG,CAACyd,WAAW,CAAC,UAAU,EAAE1Q,IAAI,CAAC;IACxC;EACF,CAAC;;EAED;EACAyY,UAAU,CAACG,UAAU,GAAGxN,EAAE,CAAC4K,QAAQ,CAAC4C,UAAU,IAAI,CAAC,CAAC;EACpD;EACA,IAAM1a,OAAO,GAAGkN,EAAE,CAAC4K,QAAQ,CAAC9X,OAAO;EACnC,IAAIA,OAAO,EAAE;IACXzW,MAAM,CAACsF,IAAI,CAACmR,OAAO,CAAC,CAAC1M,OAAO,CAAC,UAAAzH,IAAI,EAAI;MACnC0uB,UAAU,CAAC1uB,IAAI,CAAC,GAAGmU,OAAO,CAACnU,IAAI,CAAC;IAClC,CAAC,CAAC;EACJ;EAEAqU,aAAa,CAAC1J,2CAAG,EAAE0W,EAAE,EAAE0N,mBAAmB,CAAC,CAAC,CAAC;EAE7CtI,SAAS,CAACiI,UAAU,EAAE/f,KAAK,CAAC;EAC5BiY,gBAAgB,CAAC8H,UAAU,EAAErN,EAAE,CAAC4K,QAAQ,CAAC;EAEzC,OAAOyC,UAAU;AACnB;AAEA,SAASK,mBAAmBA,CAAA,EAAI;EAC9B,IAAI1b,cAAc,GAAG,EAAE;EACvB;IACE,IAAMC,WAAW,GAAG1H,EAAE,CAAC2H,cAAc,CAAC,CAAC;IACvC,IAAMC,QAAQ,GACZF,WAAW,IAAIA,WAAW,CAACE,QAAQ,GAAGF,WAAW,CAACE,QAAQ,GAAG9N,SAAS;IACxE2N,cAAc,GAAG9M,eAAe,CAACiN,QAAQ,CAAC,IAAI9N,SAAS;EACzD;EACA,OAAO2N,cAAc;AACvB;AAEA,SAAS2b,QAAQA,CAAE3N,EAAE,EAAE;EACrB,OAAO+M,YAAY,CAAC/M,EAAE,EAAE;IACtBF,KAAK,EAALA,KAAK;IACLsC,QAAQ,EAARA;EACF,CAAC,CAAC;AACJ;AAEA,SAASwL,SAASA,CAAE5N,EAAE,EAAE;EACtB6N,GAAG,CAACF,QAAQ,CAAC3N,EAAE,CAAC,CAAC;EACjB,OAAOA,EAAE;AACX;AAEA,IAAM8N,eAAe,GAAG,UAAU;AAClC,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAGvzB,CAAC;EAAA,OAAI,GAAG,GAAGA,CAAC,CAAC6P,UAAU,CAAC,CAAC,CAAC,CAAC5L,QAAQ,CAAC,EAAE,CAAC;AAAA;AACrE,IAAMuvB,OAAO,GAAG,MAAM;;AAEtB;AACA;AACA;AACA,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAGrpB,GAAG;EAAA,OAAIspB,kBAAkB,CAACtpB,GAAG,CAAC,CAC1CU,OAAO,CAACwoB,eAAe,EAAEC,qBAAqB,CAAC,CAC/CzoB,OAAO,CAAC0oB,OAAO,EAAE,GAAG,CAAC;AAAA;AAExB,SAASG,cAAcA,CAAEniB,GAAG,EAAsB;EAAA,IAApBoiB,SAAS,GAAAnzB,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAAGgzB,MAAM;EAC9C,IAAM7gB,GAAG,GAAGpB,GAAG,GAAG3P,MAAM,CAACsF,IAAI,CAACqK,GAAG,CAAC,CAAC5B,GAAG,CAAC,UAAA7N,GAAG,EAAI;IAC5C,IAAMyF,GAAG,GAAGgK,GAAG,CAACzP,GAAG,CAAC;IAEpB,IAAIyF,GAAG,KAAKO,SAAS,EAAE;MACrB,OAAO,EAAE;IACX;IAEA,IAAIP,GAAG,KAAK,IAAI,EAAE;MAChB,OAAOosB,SAAS,CAAC7xB,GAAG,CAAC;IACvB;IAEA,IAAIxC,KAAK,CAACG,OAAO,CAAC8H,GAAG,CAAC,EAAE;MACtB,IAAM6H,MAAM,GAAG,EAAE;MACjB7H,GAAG,CAACoE,OAAO,CAAC,UAAAioB,IAAI,EAAI;QAClB,IAAIA,IAAI,KAAK9rB,SAAS,EAAE;UACtB;QACF;QACA,IAAI8rB,IAAI,KAAK,IAAI,EAAE;UACjBxkB,MAAM,CAACjO,IAAI,CAACwyB,SAAS,CAAC7xB,GAAG,CAAC,CAAC;QAC7B,CAAC,MAAM;UACLsN,MAAM,CAACjO,IAAI,CAACwyB,SAAS,CAAC7xB,GAAG,CAAC,GAAG,GAAG,GAAG6xB,SAAS,CAACC,IAAI,CAAC,CAAC;QACrD;MACF,CAAC,CAAC;MACF,OAAOxkB,MAAM,CAAChD,IAAI,CAAC,GAAG,CAAC;IACzB;IAEA,OAAOunB,SAAS,CAAC7xB,GAAG,CAAC,GAAG,GAAG,GAAG6xB,SAAS,CAACpsB,GAAG,CAAC;EAC9C,CAAC,CAAC,CAACkT,MAAM,CAAC,UAAAoZ,CAAC;IAAA,OAAIA,CAAC,CAACz0B,MAAM,GAAG,CAAC;EAAA,EAAC,CAACgN,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;EAC7C,OAAOuG,GAAG,OAAAlJ,MAAA,CAAOkJ,GAAG,IAAK,EAAE;AAC7B;AAEA,SAASmhB,kBAAkBA,CAAEC,mBAAmB,EAGxB;EAAA,IAAAhmB,KAAA,GAAAvN,SAAA,CAAApB,MAAA,QAAAoB,SAAA,QAAAsH,SAAA,GAAAtH,SAAA,MAApB,CAAC,CAAC;IAFJwlB,MAAM,GAAAjY,KAAA,CAANiY,MAAM;IACNE,YAAY,GAAAnY,KAAA,CAAZmY,YAAY;EAAA,IACN8N,cAAc,GAAAxzB,SAAA,CAAApB,MAAA,OAAAoB,SAAA,MAAAsH,SAAA;EACpB,IAAAmsB,iBAAA,GAAmC/I,gBAAgB,CAACrc,2CAAG,EAAEklB,mBAAmB,CAAC;IAAAG,kBAAA,GAAA7wB,gFAAA,CAAA4wB,iBAAA;IAAtE9I,YAAY,GAAA+I,kBAAA;IAAEnM,UAAU,GAAAmM,kBAAA;EAE/B,IAAMngB,OAAO,GAAAogB,aAAA;IACXC,aAAa,EAAE,IAAI;IACnB;IACAC,cAAc,EAAE;EAAI,GAChBtM,UAAU,CAAChU,OAAO,IAAI,CAAC,CAAC,CAC7B;EAED;IACE;IACA,IAAIgU,UAAU,CAAC,WAAW,CAAC,IAAIA,UAAU,CAAC,WAAW,CAAC,CAAChU,OAAO,EAAE;MAC9DnS,MAAM,CAACuK,MAAM,CAAC4H,OAAO,EAAEgU,UAAU,CAAC,WAAW,CAAC,CAAChU,OAAO,CAAC;IACzD;EACF;EAEA,IAAMugB,gBAAgB,GAAG;IACvBvgB,OAAO,EAAPA,OAAO;IACPN,IAAI,EAAEoY,QAAQ,CAAC9D,UAAU,EAAElZ,2CAAG,CAACvN,SAAS,CAAC;IACzCkrB,SAAS,EAAEF,aAAa,CAACvE,UAAU,EAAEjC,YAAY,CAAC;IAClDiH,UAAU,EAAEC,cAAc,CAACjF,UAAU,CAAC8E,KAAK,EAAE,KAAK,EAAE9E,UAAU,CAACwM,MAAM,EAAExgB,OAAO,CAAC;IAC/EygB,SAAS,EAAE;MACTC,QAAQ,WAARA,QAAQA,CAAA,EAAI;QACV,IAAM1H,UAAU,GAAG,IAAI,CAACA,UAAU;QAElC,IAAMhZ,OAAO,GAAG;UACdsW,MAAM,EAAErE,MAAM,CAAC5jB,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,WAAW;UAChDkkB,UAAU,EAAE,IAAI;UAChBuL,SAAS,EAAE9E;QACb,CAAC;QAEDrB,UAAU,CAACqB,UAAU,CAACO,KAAK,EAAE,IAAI,CAAC;;QAElC;QACApH,YAAY,CAAC9jB,IAAI,CAAC,IAAI,EAAE;UACtBojB,MAAM,EAAE,IAAI,CAACoG,QAAQ;UACrB7D,UAAU,EAAEhU;QACd,CAAC,CAAC;;QAEF;QACA,IAAI,CAAC3G,GAAG,GAAG,IAAI+d,YAAY,CAACpX,OAAO,CAAC;;QAEpC;QACAsX,SAAS,CAAC,IAAI,CAACje,GAAG,EAAE2f,UAAU,CAACzB,QAAQ,CAAC;;QAExC;QACA,IAAI,CAACle,GAAG,CAACsnB,MAAM,CAAC,CAAC;MACnB,CAAC;MACDC,KAAK,WAALA,KAAKA,CAAA,EAAI;QACP;QACA;QACA,IAAI,IAAI,CAACvnB,GAAG,EAAE;UACZ,IAAI,CAACA,GAAG,CAAC4lB,UAAU,GAAG,IAAI;UAC1B,IAAI,CAAC5lB,GAAG,CAACyd,WAAW,CAAC,SAAS,CAAC;UAC/B,IAAI,CAACzd,GAAG,CAACyd,WAAW,CAAC,SAAS,CAAC;QACjC;MACF,CAAC;MACD+J,QAAQ,WAARA,QAAQA,CAAA,EAAI;QACV,IAAI,CAACxnB,GAAG,IAAI,IAAI,CAACA,GAAG,CAACynB,QAAQ,CAAC,CAAC;MACjC;IACF,CAAC;IACDC,aAAa,EAAE;MACbC,IAAI,WAAJA,IAAIA,CAAE5a,IAAI,EAAE;QACV,IAAI,CAAC/M,GAAG,IAAI,IAAI,CAACA,GAAG,CAACyd,WAAW,CAAC,YAAY,EAAE1Q,IAAI,CAAC;MACtD,CAAC;MACD6a,IAAI,WAAJA,IAAIA,CAAA,EAAI;QACN,IAAI,CAAC5nB,GAAG,IAAI,IAAI,CAACA,GAAG,CAACyd,WAAW,CAAC,YAAY,CAAC;MAChD,CAAC;MACDoK,MAAM,WAANA,MAAMA,CAAEC,IAAI,EAAE;QACZ,IAAI,CAAC9nB,GAAG,IAAI,IAAI,CAACA,GAAG,CAACyd,WAAW,CAAC,cAAc,EAAEqK,IAAI,CAAC;MACxD;IACF,CAAC;IACD7c,OAAO,EAAE;MACP8c,GAAG,EAAEtN,UAAU;MACfuN,GAAG,EAAE/E;IACP;EACF,CAAC;EACD;EACA,IAAItI,UAAU,CAACsN,eAAe,EAAE;IAC9Bf,gBAAgB,CAACe,eAAe,GAAGtN,UAAU,CAACsN,eAAe;EAC/D;EAEA,IAAI/1B,KAAK,CAACG,OAAO,CAACsoB,UAAU,CAACuN,cAAc,CAAC,EAAE;IAC5CvN,UAAU,CAACuN,cAAc,CAAC3pB,OAAO,CAAC,UAAA4pB,UAAU,EAAI;MAC9CjB,gBAAgB,CAACjc,OAAO,CAACkd,UAAU,CAAC,GAAG,UAAUpb,IAAI,EAAE;QACrD,OAAO,IAAI,CAAC/M,GAAG,CAACmoB,UAAU,CAAC,CAACpb,IAAI,CAAC;MACnC,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,IAAI6Z,cAAc,EAAE;IAClB,OAAO,CAACM,gBAAgB,EAAEvM,UAAU,EAAEoD,YAAY,CAAC;EACrD;EACA,IAAInF,MAAM,EAAE;IACV,OAAOsO,gBAAgB;EACzB;EACA,OAAO,CAACA,gBAAgB,EAAEnJ,YAAY,CAAC;AACzC;AAEA,SAASqK,cAAcA,CAAEzB,mBAAmB,EAAEC,cAAc,EAAE;EAC5D,OAAOF,kBAAkB,CAACC,mBAAmB,EAAE;IAC7C/N,MAAM,EAANA,MAAM;IACNE,YAAY,EAAZA;EACF,CAAC,EAAE8N,cAAc,CAAC;AACpB;AAEA,IAAMyB,OAAO,GAAG,CACd,QAAQ,EACR,QAAQ,EACR,UAAU,CACX;AAEDA,OAAO,CAACt0B,IAAI,CAAAV,KAAA,CAAZg1B,OAAO,EAASvL,gBAAgB,CAAC;AAEjC,SAASwL,aAAaA,CAAEC,cAAc,EAAE;EACtC,IAAAC,eAAA,GAAkCJ,cAAc,CAACG,cAAc,EAAE,IAAI,CAAC;IAAAE,gBAAA,GAAAxyB,gFAAA,CAAAuyB,eAAA;IAA/DE,WAAW,GAAAD,gBAAA;IAAE9N,UAAU,GAAA8N,gBAAA;EAE9BlL,SAAS,CAACmL,WAAW,CAACzd,OAAO,EAAEod,OAAO,EAAE1N,UAAU,CAAC;EAEnD+N,WAAW,CAACzd,OAAO,CAAC0d,MAAM,GAAG,UAAUC,KAAK,EAAE;IAC5C,IAAI,CAACjiB,OAAO,GAAGiiB,KAAK;IACpB,IAAMC,SAAS,GAAGr0B,MAAM,CAACuK,MAAM,CAAC,CAAC,CAAC,EAAE6pB,KAAK,CAAC;IAC1C,OAAOC,SAAS,CAAC1E,MAAM;IACvB,IAAI,CAAC1X,KAAK,GAAG;MACXC,QAAQ,EAAE,GAAG,IAAI,IAAI,CAACmM,KAAK,IAAI,IAAI,CAAC6K,EAAE,CAAC,GAAG4C,cAAc,CAACuC,SAAS;IACpE,CAAC;IACD,IAAI,CAAC7oB,GAAG,CAACgd,GAAG,CAAC4L,KAAK,GAAGA,KAAK,CAAC,CAAC;IAC5B,IAAI,CAAC5oB,GAAG,CAACyd,WAAW,CAAC,QAAQ,EAAEmL,KAAK,CAAC;EACvC,CAAC;EACD;IACElL,gBAAgB,CAACgL,WAAW,CAACzd,OAAO,EAAEsd,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC;EACpE;EACA;IACEpN,kBAAkB,CAACuN,WAAW,CAACzd,OAAO,EAAE0P,UAAU,CAAC1P,OAAO,CAAC;EAC7D;EAEA,OAAOyd,WAAW;AACpB;AAEA,SAASI,SAASA,CAAEP,cAAc,EAAE;EAClC,OAAOD,aAAa,CAACC,cAAc,CAAC;AACtC;AAEA,SAASQ,UAAUA,CAAER,cAAc,EAAE;EACnC;IACE,OAAO3M,SAAS,CAACkN,SAAS,CAACP,cAAc,CAAC,CAAC;EAC7C;AACF;AAEA,SAASS,eAAeA,CAAErO,UAAU,EAAE;EACpC;IACE,OAAOiB,SAAS,CAACwM,cAAc,CAACzN,UAAU,CAAC,CAAC;EAC9C;AACF;AAEA,SAASsO,mBAAmBA,CAAE9Q,EAAE,EAAE;EAChC,IAAMqN,UAAU,GAAGM,QAAQ,CAAC3N,EAAE,CAAC;EAC/B,IAAMzM,GAAG,GAAG5L,MAAM,CAAC;IACjB6L,YAAY,EAAE;EAChB,CAAC,CAAC;EACFwM,EAAE,CAACI,MAAM,GAAG7M,GAAG;EACf,IAAMia,UAAU,GAAGja,GAAG,CAACia,UAAU;EACjC,IAAIA,UAAU,EAAE;IACdnxB,MAAM,CAACsF,IAAI,CAAC0rB,UAAU,CAACG,UAAU,CAAC,CAACpnB,OAAO,CAAC,UAAAzH,IAAI,EAAI;MACjD,IAAI,CAAC8F,MAAM,CAAC+oB,UAAU,EAAE7uB,IAAI,CAAC,EAAE;QAC7B6uB,UAAU,CAAC7uB,IAAI,CAAC,GAAG0uB,UAAU,CAACG,UAAU,CAAC7uB,IAAI,CAAC;MAChD;IACF,CAAC,CAAC;EACJ;EACAtC,MAAM,CAACsF,IAAI,CAAC0rB,UAAU,CAAC,CAACjnB,OAAO,CAAC,UAAAzH,IAAI,EAAI;IACtC,IAAI,CAAC8F,MAAM,CAAC8O,GAAG,EAAE5U,IAAI,CAAC,EAAE;MACtB4U,GAAG,CAAC5U,IAAI,CAAC,GAAG0uB,UAAU,CAAC1uB,IAAI,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,IAAImN,IAAI,CAACuhB,UAAU,CAAC0D,MAAM,CAAC,IAAIxmB,EAAE,CAACymB,SAAS,EAAE;IAC3CzmB,EAAE,CAACymB,SAAS,CAAC,YAAa;MAAA,SAAAC,KAAA,GAAAh2B,SAAA,CAAApB,MAAA,EAAT+a,IAAI,OAAA7a,KAAA,CAAAk3B,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJtc,IAAI,CAAAsc,KAAA,IAAAj2B,SAAA,CAAAi2B,KAAA;MAAA;MACnBlR,EAAE,CAACsF,WAAW,CAAC,QAAQ,EAAE1Q,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ;EACA,IAAI9I,IAAI,CAACuhB,UAAU,CAAC8D,MAAM,CAAC,IAAI5mB,EAAE,CAAC6mB,SAAS,EAAE;IAC3C7mB,EAAE,CAAC6mB,SAAS,CAAC,YAAa;MAAA,SAAAC,KAAA,GAAAp2B,SAAA,CAAApB,MAAA,EAAT+a,IAAI,OAAA7a,KAAA,CAAAs3B,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJ1c,IAAI,CAAA0c,KAAA,IAAAr2B,SAAA,CAAAq2B,KAAA;MAAA;MACnBtR,EAAE,CAACsF,WAAW,CAAC,QAAQ,EAAE1Q,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ;EACA,IAAI9I,IAAI,CAACuhB,UAAU,CAACC,QAAQ,CAAC,EAAE;IAC7B,IAAM1Y,IAAI,GAAGrK,EAAE,CAACgnB,oBAAoB,IAAIhnB,EAAE,CAACgnB,oBAAoB,CAAC,CAAC;IACjEvR,EAAE,CAACsF,WAAW,CAAC,UAAU,EAAE1Q,IAAI,CAAC;EAClC;EACA,OAAOoL,EAAE;AACX;AAEA,SAASwR,YAAYA,CAAExR,EAAE,EAAE;EACzB,IAAMqN,UAAU,GAAGM,QAAQ,CAAC3N,EAAE,CAAC;EAC/B,IAAIlU,IAAI,CAACuhB,UAAU,CAAC0D,MAAM,CAAC,IAAIxmB,EAAE,CAACymB,SAAS,EAAE;IAC3CzmB,EAAE,CAACymB,SAAS,CAAC,YAAa;MAAA,SAAAS,KAAA,GAAAx2B,SAAA,CAAApB,MAAA,EAAT+a,IAAI,OAAA7a,KAAA,CAAA03B,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJ9c,IAAI,CAAA8c,KAAA,IAAAz2B,SAAA,CAAAy2B,KAAA;MAAA;MACnB1R,EAAE,CAACsF,WAAW,CAAC,QAAQ,EAAE1Q,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ;EACA,IAAI9I,IAAI,CAACuhB,UAAU,CAAC8D,MAAM,CAAC,IAAI5mB,EAAE,CAAC6mB,SAAS,EAAE;IAC3C7mB,EAAE,CAAC6mB,SAAS,CAAC,YAAa;MAAA,SAAAO,KAAA,GAAA12B,SAAA,CAAApB,MAAA,EAAT+a,IAAI,OAAA7a,KAAA,CAAA43B,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJhd,IAAI,CAAAgd,KAAA,IAAA32B,SAAA,CAAA22B,KAAA;MAAA;MACnB5R,EAAE,CAACsF,WAAW,CAAC,QAAQ,EAAE1Q,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ;EACA,IAAI9I,IAAI,CAACuhB,UAAU,CAACC,QAAQ,CAAC,EAAE;IAC7B,IAAM1Y,IAAI,GAAGrK,EAAE,CAACgnB,oBAAoB,IAAIhnB,EAAE,CAACgnB,oBAAoB,CAAC,CAAC;IACjEvR,EAAE,CAACsF,WAAW,CAAC,UAAU,EAAE1Q,IAAI,CAAC;EAClC;EACA,OAAOoL,EAAE;AACX;AAEAjE,KAAK,CAAC3V,OAAO,CAAC,UAAA8W,OAAO,EAAI;EACvBpB,SAAS,CAACoB,OAAO,CAAC,GAAG,KAAK;AAC5B,CAAC,CAAC;AAEFlB,QAAQ,CAAC5V,OAAO,CAAC,UAAAyrB,UAAU,EAAI;EAC7B,IAAMC,OAAO,GAAGhW,SAAS,CAAC+V,UAAU,CAAC,IAAI/V,SAAS,CAAC+V,UAAU,CAAC,CAAClzB,IAAI,GAAGmd,SAAS,CAAC+V,UAAU,CAAC,CAAClzB,IAAI,GAC5FkzB,UAAU;EACd,IAAI,CAACtnB,EAAE,CAACgjB,OAAO,CAACuE,OAAO,CAAC,EAAE;IACxBhW,SAAS,CAAC+V,UAAU,CAAC,GAAG,KAAK;EAC/B;AACF,CAAC,CAAC;AAEF,IAAIvqB,GAAG,GAAG,CAAC,CAAC;AAEZ,IAAI,OAAOyqB,KAAK,KAAK,WAAW,IAAI,WAAW,KAAK,UAAU,EAAE;EAC9DzqB,GAAG,GAAG,IAAIyqB,KAAK,CAAC,CAAC,CAAC,EAAE;IAClB3e,GAAG,WAAHA,GAAGA,CAAEsV,MAAM,EAAE/pB,IAAI,EAAE;MACjB,IAAI8F,MAAM,CAACikB,MAAM,EAAE/pB,IAAI,CAAC,EAAE;QACxB,OAAO+pB,MAAM,CAAC/pB,IAAI,CAAC;MACrB;MACA,IAAIkV,OAAO,CAAClV,IAAI,CAAC,EAAE;QACjB,OAAOkV,OAAO,CAAClV,IAAI,CAAC;MACtB;MACA,IAAIsQ,GAAG,CAACtQ,IAAI,CAAC,EAAE;QACb,OAAO0R,SAAS,CAAC1R,IAAI,EAAEsQ,GAAG,CAACtQ,IAAI,CAAC,CAAC;MACnC;MACA;QACE,IAAIgf,QAAQ,CAAChf,IAAI,CAAC,EAAE;UAClB,OAAO0R,SAAS,CAAC1R,IAAI,EAAEgf,QAAQ,CAAChf,IAAI,CAAC,CAAC;QACxC;QACA,IAAIoe,QAAQ,CAACpe,IAAI,CAAC,EAAE;UAClB,OAAO0R,SAAS,CAAC1R,IAAI,EAAEoe,QAAQ,CAACpe,IAAI,CAAC,CAAC;QACxC;MACF;MACA,IAAIwf,QAAQ,CAACxf,IAAI,CAAC,EAAE;QAClB,OAAOwf,QAAQ,CAACxf,IAAI,CAAC;MACvB;MACA,OAAO0R,SAAS,CAAC1R,IAAI,EAAEge,OAAO,CAAChe,IAAI,EAAE4L,EAAE,CAAC5L,IAAI,CAAC,CAAC,CAAC;IACjD,CAAC;IACD0U,GAAG,WAAHA,GAAGA,CAAEqV,MAAM,EAAE/pB,IAAI,EAAEhE,KAAK,EAAE;MACxB+tB,MAAM,CAAC/pB,IAAI,CAAC,GAAGhE,KAAK;MACpB,OAAO,IAAI;IACb;EACF,CAAC,CAAC;AACJ,CAAC,MAAM;EACL0B,MAAM,CAACsF,IAAI,CAACkS,OAAO,CAAC,CAACzN,OAAO,CAAC,UAAAzH,IAAI,EAAI;IACnC2I,GAAG,CAAC3I,IAAI,CAAC,GAAGkV,OAAO,CAAClV,IAAI,CAAC;EAC3B,CAAC,CAAC;EAEF;IACEtC,MAAM,CAACsF,IAAI,CAACob,QAAQ,CAAC,CAAC3W,OAAO,CAAC,UAAAzH,IAAI,EAAI;MACpC2I,GAAG,CAAC3I,IAAI,CAAC,GAAG0R,SAAS,CAAC1R,IAAI,EAAEoe,QAAQ,CAACpe,IAAI,CAAC,CAAC;IAC7C,CAAC,CAAC;IACFtC,MAAM,CAACsF,IAAI,CAACgc,QAAQ,CAAC,CAACvX,OAAO,CAAC,UAAAzH,IAAI,EAAI;MACpC2I,GAAG,CAAC3I,IAAI,CAAC,GAAG0R,SAAS,CAAC1R,IAAI,EAAEgf,QAAQ,CAAChf,IAAI,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ;EAEAtC,MAAM,CAACsF,IAAI,CAACwc,QAAQ,CAAC,CAAC/X,OAAO,CAAC,UAAAzH,IAAI,EAAI;IACpC2I,GAAG,CAAC3I,IAAI,CAAC,GAAGwf,QAAQ,CAACxf,IAAI,CAAC;EAC5B,CAAC,CAAC;EAEFtC,MAAM,CAACsF,IAAI,CAACsN,GAAG,CAAC,CAAC7I,OAAO,CAAC,UAAAzH,IAAI,EAAI;IAC/B2I,GAAG,CAAC3I,IAAI,CAAC,GAAG0R,SAAS,CAAC1R,IAAI,EAAEsQ,GAAG,CAACtQ,IAAI,CAAC,CAAC;EACxC,CAAC,CAAC;EAEFtC,MAAM,CAACsF,IAAI,CAAC4I,EAAE,CAAC,CAACnE,OAAO,CAAC,UAAAzH,IAAI,EAAI;IAC9B,IAAI8F,MAAM,CAAC8F,EAAE,EAAE5L,IAAI,CAAC,IAAI8F,MAAM,CAACqX,SAAS,EAAEnd,IAAI,CAAC,EAAE;MAC/C2I,GAAG,CAAC3I,IAAI,CAAC,GAAG0R,SAAS,CAAC1R,IAAI,EAAEge,OAAO,CAAChe,IAAI,EAAE4L,EAAE,CAAC5L,IAAI,CAAC,CAAC,CAAC;IACtD;EACF,CAAC,CAAC;AACJ;AAEA4L,EAAE,CAACqjB,SAAS,GAAGA,SAAS;AACxBrjB,EAAE,CAACqmB,UAAU,GAAGA,UAAU;AAC1BrmB,EAAE,CAACsmB,eAAe,GAAGA,eAAe;AACpCtmB,EAAE,CAACumB,mBAAmB,GAAGA,mBAAmB;AAC5CvmB,EAAE,CAACinB,YAAY,GAAGA,YAAY;AAE9B,IAAIQ,KAAK,GAAG1qB,GAAG;AAEf,+DAAe0qB,KAAK,EAAC;;;;;;;;;;;;;ACjqFrB,IAAMC,UAAU,GAAG,CACjB,IAAI,EACJ,KAAK,EACL,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,eAAe,EACf,QAAQ,EACR,SAAS,EACT,mCAAmC,CACpC;AACD,IAAMC,oBAAoB,GAAG,CAC3B,UAAU,EACV,QAAQ,EACR,SAAS,CACV;AACD,IAAMxJ,MAAM,GAAG,OAAOyJ,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAI,YAAY;EAC3E,OAAO,IAAI;AACb,CAAC,CAAE,CAAC;AAEJ,IAAM51B,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAACsK,IAAI,CAAC,EAAE,CAAC;AAC/B,IAAMurB,KAAK,GAAG1J,MAAM,CAACnsB,GAAG,CAAC;AACzB,IAAM81B,YAAY,GAAGD,KAAK,CAACb,oBAAoB,GAAGa,KAAK,CAACb,oBAAoB,CAAC,CAAC,GAAG,IAAI;AAErF,SAASe,OAAOA,CAAE/1B,GAAG,EAAE;EACrB,IAAI81B,YAAY,IAAIA,YAAY,CAACE,KAAK,KAAK,IAAI,IAAIL,oBAAoB,CAAChQ,QAAQ,CAAC3lB,GAAG,CAAC,EAAE;IACrF,OAAO,KAAK;EACd;EACA,OAAO01B,UAAU,CAACjtB,OAAO,CAACzI,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO61B,KAAK,CAAC71B,GAAG,CAAC,KAAK,UAAU;AACzE;AAEA,SAASi2B,MAAMA,CAAA,EAAI;EACjB,IAAMC,KAAK,GAAG,CAAC,CAAC;EAChB,KAAK,IAAMl2B,IAAG,IAAI61B,KAAK,EAAE;IACvB,IAAIE,OAAO,CAAC/1B,IAAG,CAAC,EAAE;MAChB;MACAk2B,KAAK,CAACl2B,IAAG,CAAC,GAAG61B,KAAK,CAAC71B,IAAG,CAAC;IACzB;EACF;EACA,OAAOk2B,KAAK;AACd;AACA/J,MAAM,CAACnsB,GAAG,CAAC,GAAGi2B,MAAM,CAAC,CAAC;AACtB,IAAI,CAAC9J,MAAM,CAACnsB,GAAG,CAAC,CAACgxB,OAAO,CAAC,gBAAgB,CAAC,EAAE;EAC1C7E,MAAM,CAACnsB,GAAG,CAAC,CAAC2V,cAAc,GAAGwW,MAAM,CAACnsB,GAAG,CAAC,CAACgV,iBAAiB;AAC5D;AAEA,IAAI,CAACmX,MAAM,CAACnsB,GAAG,CAAC,CAACgxB,OAAO,CAAC,eAAe,CAAC,EAAE;EACzC7E,MAAM,CAACnsB,GAAG,CAAC,CAAC+U,aAAa,GAAGoX,MAAM,CAACnsB,GAAG,CAAC,CAACgV,iBAAiB;AAC3D;AAEA,IAAI,CAACmX,MAAM,CAACnsB,GAAG,CAAC,CAACgxB,OAAO,CAAC,eAAe,CAAC,EAAE;EACzC7E,MAAM,CAACnsB,GAAG,CAAC,CAACkV,aAAa,GAAGiX,MAAM,CAACnsB,GAAG,CAAC,CAACgV,iBAAiB;AAC3D;AACA,+DAAemX,MAAM,CAACnsB,GAAG,CAAC,E;;;;;;;;;;ACtD1B;;AAEA,cAAc,mBAAO,CAAC,8GAA2C;AACjE;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;;AAEA,kCAAkC;;AAElC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,iBAAiB;AACnC;AACA;AACA;AACA,uBAAuB;AACvB,uBAAuB;AACvB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,mDAAmD,kCAAkC;AACrF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,kBAAkB,gBAAgB;AAClC;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,8BAA8B;;AAE9B;;AAEA;AACA;AACA;AACA,8BAA8B;;AAE9B;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ;AACR;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,gBAAgB;AAClC,mCAAmC;AACnC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,aAAoB;;AAErC;AACA;AACA;AACA,YAAY,aAAoB;;AAEhC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,qBAAqB;AACzC,kBAAkB;AAClB;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,IAAI;AACT;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,qBAAM;AAC9C;AACA;AACA,kBAAkB,qBAAM,eAAe,qBAAM;AAC7C,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;AACA,qCAAqC;AACrC;;AAEA,IAAI,IAAqC;AACzC;AACA;AACA,kCAAkC;AAClC,wCAAwC,yBAAyB;AACjE;;AAEA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,yBAAyB;AACzB,mBAAmB;AACnB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA,0CAA0C;AAC1C;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,gCAAgC,qBAAqB;AACrD;AACA,mCAAmC,OAAO;AAC1C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,2BAA2B,SAAS;;AAEpC;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA,GAAG;AACH,CAAC;;AAED;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,iBAAiB;AACnC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oCAAoC,OAAO;AAC3C;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,OAAO;AAC1C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,kDAAkD,OAAO;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI,IAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,eAAe;AACf;;AAEA;AACA;AACA;;AAEA,kBAAkB,iBAAiB;AACnC;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC,kCAAkC;AAClC;AACA,mBAAmB;AACnB,MAAM,IAAqC;AAC3C;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,aAAoB;AACtC;AACA;AACA,oBAAoB;AACpB;AACA;AACA,kBAAkB;AAClB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB,QAAQ,SAAS,IAAqC;AACtD;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA,IAAI,SAAS,IAAqC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,oBAAoB,mBAAmB;AACvC,gCAAgC;AAChC;AACA,IAAI;AACJ;AACA;AACA;AACA,mBAAmB,WAAW;AAC9B,YAAY;AACZ;AACA,IAAI,SAAS,IAAqC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,OAAO;AACtD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA,qCAAqC;AACrC;AACA,sCAAsC;AACtC;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAEQ;AACZ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,2BAA2B;AAC/C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,8CAA8C,SAAS;AACvD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA,8CAA8C,sCAAsC;AACpF;;AAEA;AACA;AACA;;AAEA,qCAAqC,0CAA0C;AAC/E;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,kBAAkB;AAC5C;AACA;AACA,6BAA6B;AAC7B,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,uDAAuD;AACtF;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,kBAAkB,mBAAmB;AACrC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;;AAEA;;AAEA,IAAI,IAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC,eAAe;AACf;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mCAAmC;AACnC,eAAe;AACf;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB,IAAI;AACJ;AACA;AACA,kBAAkB;AAClB;AACA;;AAEA;AACA;;AAEA,IAAI,IAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACA,sBAAsB,mBAAmB;AACzC;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,IAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,cAAc,qBAAqB;AACnC;AACA,gDAAgD;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,IAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ,KAAK;AAAA,EAEN;AACP,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,oBAAoB,iBAAiB;AACrC;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,SAAS,IAAqC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,OAAO;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kEAAkE;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA,uBAAuB;AACvB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,OAAO;AACvC,wCAAwC;AACxC;AACA,IAAI;AACJ;AACA,gBAAgB,SAAS;AACzB,uCAAuC;AACvC;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,4DAA4D;AAC5D;AACA;AACA,MAAM;AACN;AACA;AACA,mCAAmC,OAAO;AAC1C;AACA,8CAA8C;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA,6CAA6C,cAAc;AAC3D,IAAI;AACJ;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,kDAAkD;AAClD,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;;AAEA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,iBAAiB;AACrC;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,MAAM;AACN,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,kBAAkB,gBAAgB;AAClC;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,kBAAkB,mBAAmB;AACrC;AACA;AACA;AACA,MAAM,SAAS,KAAqC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,sCAAsC;AACtC;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,6BAA6B;AAC7B,6BAA6B;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA,oBAAoB,mBAAmB;AACvC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C,kDAAkD;AAClD;AACA;AACA,mCAAmC;AACnC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,uEAAuE;;AAEvE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM,sFAAsF;AAC5F;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0CAA0C;AAC1C,kBAAkB,yBAAyB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG,+BAA+B;AAClC,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,qBAAqB;AACrB,uBAAuB;AACvB;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,OAAO;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,oBAAoB;AACpB,0BAA0B;AAC1B;AACA,sDAAsD;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA,8CAA8C;;AAE9C;AACA;AACA;;AAEA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL,IAAI,KAAK;AAAA,EAGN;AACH;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,KAAK,4CAA4C,+BAA+B;;AAEhF;AACA,yCAAyC,OAAO;AAChD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;;AAEL;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gBAAgB,KAAqC;AACrD;AACA,oBAAoB,CAAI;AACxB;AACA;AACA,WAAW;AACX;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,wCAAwC,OAAO;AAC/C;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,SAAS;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,OAAO;AAC7C;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,2BAA2B;;AAE3B,mBAAmB;AACnB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,oBAAoB,qBAAqB;AACzC;AACA,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAM,IAAqC;AAC3C;AACA;AACA;;AAEA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,oBAAoB,yBAAyB;AAC7C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,yBAAyB;AAC7C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,OAAO;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,qBAAqB;;AAEpD;AACA;AACA,kBAAkB,sBAAsB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,qBAAqB;AACrB;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA,oBAAoB,KAAqC;AACzD;AACA,MAAM,CAAE;AACR;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,MAAM;AACN;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,oBAAoB;AACpB,sBAAsB;AACtB;AACA;AACA,IAAI;AACJ,yBAAyB;AACzB;AACA,uBAAuB;AACvB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kGAAkG;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM,KAAK;AAAA,EAEN;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA,+BAA+B;;AAE/B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM,SAAS,IAAqC;AACpD;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,sBAAsB,oBAAoB;AAC1C;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA,+BAA+B;AAC/B,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA,MAAM,KAAK;AAAA,EAEN;AACL;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;AACA,uCAAuC;AACvC;;AAEA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sEAAsE;AACtE;AACA;AACA;;AAEA;AACA,QAAQ,KAAqC;AAC7C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iCAAiC;;AAEjC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,YAAY,KAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA,2CAA2C,4BAA4B;AACvE,KAAK;AACL;AACA,2CAA2C,6BAA6B;AACxE,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,gCAAgC;AAChC,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,sBAAsB;AACtB;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,6BAA6B;AAC7B;AACA;AACA,kBAAkB;AAClB;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA,UAAU;AACV;AACA;AACA,cAAc;AACd;AACA;AACA,iBAAiB;AACjB;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,YAAY,wHAAW;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAmB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,iCAAiC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,wHAAW;AACtB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,WAAW,wHAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,UAAU;AACV;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,0CAA0C;AAC1C;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AACA,KAAK;AACL;AACA;AACA,UAAU,wHAAW;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,oCAAoC,OAAO;AAC3C;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,OAAO;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD,sDAAsD,SAAS;AACtH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,OAAO;AACzC;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,mCAAmC,OAAO;AAC1C;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,2BAA2B,OAAO;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,+DAAe,GAAG,EAAC;;;;;;;;;;;;;;;;AC97LnB;;AAEA;AACA;AACA;;AAEe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,sBAAsB;AACtB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;ACrHA+K,GAAG,CAACuG,cAAc,CAAC;EACjBe,WAAW,WAAXA,WAAWA,CAAExB,GAAG,EAAE;IAChB,IAAI,EAAE,CAAC,CAACA,GAAG,KAAKjP,OAAA,CAAOiP,GAAG,MAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IAAI,OAAOA,GAAG,CAACrS,IAAI,KAAK,UAAU,CAAC,EAAE;MACxG,OAAOqS,GAAG;IACZ;IACA,OAAO,IAAIvS,OAAO,CAAC,UAACC,OAAO,EAAEwU,MAAM,EAAK;MACtClC,GAAG,CAACrS,IAAI,CAAC,UAACqS,GAAG;QAAA,OAAKA,GAAG,CAAC,CAAC,CAAC,GAAGkC,MAAM,CAAClC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGtS,OAAO,CAACsS,GAAG,CAAC,CAAC,CAAC,CAAC;MAAA,EAAC;IAC9D,CAAC,CAAC;EACJ;AACF,CAAC,CAAC,C", "sources": ["uni-app:///node_modules/@babel/runtime/helpers/OverloadYield.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/arrayWithHoles.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/classCallCheck.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/construct.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/createClass.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/defineProperty.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/iterableToArray.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/iterableToArrayLimit.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/nonIterableRest.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/slicedToArray.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/toConsumableArray.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/toPrimitive.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/toPropertyKey.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/typeof.js", "uni-app:///node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js", "uni-app:///node_modules/@babel/runtime/helpers/regenerator.js", "uni-app:///node_modules/@babel/runtime/helpers/regeneratorAsync.js", "uni-app:///node_modules/@babel/runtime/helpers/regeneratorAsyncGen.js", "uni-app:///node_modules/@babel/runtime/helpers/regeneratorAsyncIterator.js", "uni-app:///node_modules/@babel/runtime/helpers/regeneratorDefine.js", "uni-app:///node_modules/@babel/runtime/helpers/regeneratorKeys.js", "uni-app:///node_modules/@babel/runtime/helpers/regeneratorRuntime.js", "uni-app:///node_modules/@babel/runtime/helpers/regeneratorValues.js", "uni-app:///node_modules/@babel/runtime/helpers/typeof.js", "uni-app:///node_modules/@dcloudio/uni-i18n/dist/uni-i18n.es.js", "uni-app:///node_modules/@dcloudio/uni-mp-weixin/dist/index.js", "uni-app:///node_modules/@dcloudio/uni-mp-weixin/dist/wx.js", "uni-app:///node_modules/@dcloudio/vue-cli-plugin-uni/packages/@babel/runtime/regenerator/index.js", "uni-app:///node_modules/@dcloudio/vue-cli-plugin-uni/packages/mp-vue/dist/mp.runtime.esm.js", "uni-app:///node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js", "uni-app:///src/uni.promisify.adaptor.js"], "sourcesContent": ["function _OverloadYield(e, d) {\n  this.v = e, this.k = d;\n}\nmodule.exports = _OverloadYield, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nexport { _arrayLikeToArray as default };", "function _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nexport { _arrayWithHoles as default };", "import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return arrayLikeToArray(r);\n}\nexport { _arrayWithoutHoles as default };", "function asyncGeneratorStep(n, t, e, r, o, a, c) {\n  try {\n    var i = n[a](c),\n      u = i.value;\n  } catch (n) {\n    return void e(n);\n  }\n  i.done ? t(u) : Promise.resolve(u).then(r, o);\n}\nfunction _asyncToGenerator(n) {\n  return function () {\n    var t = this,\n      e = arguments;\n    return new Promise(function (r, o) {\n      var a = n.apply(t, e);\n      function _next(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n);\n      }\n      function _throw(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n);\n      }\n      _next(void 0);\n    });\n  };\n}\nexport { _asyncToGenerator as default };", "function _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nexport { _classCallCheck as default };", "import isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _construct(t, e, r) {\n  if (isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);\n  var o = [null];\n  o.push.apply(o, e);\n  var p = new (t.bind.apply(t, o))();\n  return r && setPrototypeOf(p, r.prototype), p;\n}\nexport { _construct as default };", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nexport { _createClass as default };", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nexport { _defineProperty as default };", "function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nexport { _isNativeReflectConstruct as default };", "function _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nexport { _iterableToArray as default };", "function _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nexport { _iterableToArrayLimit as default };", "function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableRest as default };", "function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableSpread as default };", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import arrayWithHoles from \"./arrayWithHoles.js\";\nimport iterableToArrayLimit from \"./iterableToArrayLimit.js\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nimport nonIterableRest from \"./nonIterableRest.js\";\nfunction _slicedToArray(r, e) {\n  return arrayWithHoles(r) || iterableToArrayLimit(r, e) || unsupportedIterableToArray(r, e) || nonIterableRest();\n}\nexport { _slicedToArray as default };", "import arrayWithoutHoles from \"./arrayWithoutHoles.js\";\nimport iterableToArray from \"./iterableToArray.js\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nimport nonIterableSpread from \"./nonIterableSpread.js\";\nfunction _toConsumableArray(r) {\n  return arrayWithoutHoles(r) || iterableToArray(r) || unsupportedIterableToArray(r) || nonIterableSpread();\n}\nexport { _toConsumableArray as default };", "import _typeof from \"./typeof.js\";\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nexport { toPrimitive as default };", "import _typeof from \"./typeof.js\";\nimport toPrimitive from \"./toPrimitive.js\";\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nexport { toPropertyKey as default };", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nexport { _typeof as default };", "import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? arrayLikeToArray(r, a) : void 0;\n  }\n}\nexport { _unsupportedIterableToArray as default };", "var regeneratorDefine = require(\"./regeneratorDefine.js\");\nfunction _regenerator() {\n  /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */\n  var e,\n    t,\n    r = \"function\" == typeof Symbol ? Symbol : {},\n    n = r.iterator || \"@@iterator\",\n    o = r.toStringTag || \"@@toStringTag\";\n  function i(r, n, o, i) {\n    var c = n && n.prototype instanceof Generator ? n : Generator,\n      u = Object.create(c.prototype);\n    return regeneratorDefine(u, \"_invoke\", function (r, n, o) {\n      var i,\n        c,\n        u,\n        f = 0,\n        p = o || [],\n        y = !1,\n        G = {\n          p: 0,\n          n: 0,\n          v: e,\n          a: d,\n          f: d.bind(e, 4),\n          d: function d(t, r) {\n            return i = t, c = 0, u = e, G.n = r, a;\n          }\n        };\n      function d(r, n) {\n        for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) {\n          var o,\n            i = p[t],\n            d = G.p,\n            l = i[2];\n          r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0));\n        }\n        if (o || r > 1) return a;\n        throw y = !0, n;\n      }\n      return function (o, p, l) {\n        if (f > 1) throw TypeError(\"Generator is already running\");\n        for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) {\n          i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u);\n          try {\n            if (f = 2, i) {\n              if (c || (o = \"next\"), t = i[o]) {\n                if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\");\n                if (!t.done) return t;\n                u = t.value, c < 2 && (c = 0);\n              } else 1 === c && (t = i[\"return\"]) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1);\n              i = e;\n            } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break;\n          } catch (t) {\n            i = e, c = 1, u = t;\n          } finally {\n            f = 1;\n          }\n        }\n        return {\n          value: t,\n          done: y\n        };\n      };\n    }(r, o, i), !0), u;\n  }\n  var a = {};\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n  t = Object.getPrototypeOf;\n  var c = [][n] ? t(t([][n]())) : (regeneratorDefine(t = {}, n, function () {\n      return this;\n    }), t),\n    u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c);\n  function f(e) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, regeneratorDefine(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e;\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, regeneratorDefine(u, \"constructor\", GeneratorFunctionPrototype), regeneratorDefine(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", regeneratorDefine(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), regeneratorDefine(u), regeneratorDefine(u, o, \"Generator\"), regeneratorDefine(u, n, function () {\n    return this;\n  }), regeneratorDefine(u, \"toString\", function () {\n    return \"[object Generator]\";\n  }), (module.exports = _regenerator = function _regenerator() {\n    return {\n      w: i,\n      m: f\n    };\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _regenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var regeneratorAsyncGen = require(\"./regeneratorAsyncGen.js\");\nfunction _regeneratorAsync(n, e, r, t, o) {\n  var a = regeneratorAsyncGen(n, e, r, t, o);\n  return a.next().then(function (n) {\n    return n.done ? n.value : a.next();\n  });\n}\nmodule.exports = _regeneratorAsync, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var regenerator = require(\"./regenerator.js\");\nvar regeneratorAsyncIterator = require(\"./regeneratorAsyncIterator.js\");\nfunction _regeneratorAsyncGen(r, e, t, o, n) {\n  return new regeneratorAsyncIterator(regenerator().w(r, e, t, o), n || Promise);\n}\nmodule.exports = _regeneratorAsyncGen, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nvar regeneratorDefine = require(\"./regeneratorDefine.js\");\nfunction AsyncIterator(t, e) {\n  function n(r, o, i, f) {\n    try {\n      var c = t[r](o),\n        u = c.value;\n      return u instanceof OverloadYield ? e.resolve(u.v).then(function (t) {\n        n(\"next\", t, i, f);\n      }, function (t) {\n        n(\"throw\", t, i, f);\n      }) : e.resolve(u).then(function (t) {\n        c.value = t, i(c);\n      }, function (t) {\n        return n(\"throw\", t, i, f);\n      });\n    } catch (t) {\n      f(t);\n    }\n  }\n  var r;\n  this.next || (regeneratorDefine(AsyncIterator.prototype), regeneratorDefine(AsyncIterator.prototype, \"function\" == typeof Symbol && Symbol.asyncIterator || \"@asyncIterator\", function () {\n    return this;\n  })), regeneratorDefine(this, \"_invoke\", function (t, o, i) {\n    function f() {\n      return new e(function (e, r) {\n        n(t, i, e, r);\n      });\n    }\n    return r = r ? r.then(f, f) : f();\n  }, !0);\n}\nmodule.exports = AsyncIterator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _regeneratorDefine(e, r, n, t) {\n  var i = Object.defineProperty;\n  try {\n    i({}, \"\", {});\n  } catch (e) {\n    i = 0;\n  }\n  module.exports = _regeneratorDefine = function regeneratorDefine(e, r, n, t) {\n    if (r) i ? i(e, r, {\n      value: n,\n      enumerable: !t,\n      configurable: !t,\n      writable: !t\n    }) : e[r] = n;else {\n      var o = function o(r, n) {\n        _regeneratorDefine(e, r, function (e) {\n          return this._invoke(r, n, e);\n        });\n      };\n      o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2);\n    }\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _regeneratorDefine(e, r, n, t);\n}\nmodule.exports = _regeneratorDefine, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _regeneratorKeys(e) {\n  var n = Object(e),\n    r = [];\n  for (var t in n) r.unshift(t);\n  return function e() {\n    for (; r.length;) if ((t = r.pop()) in n) return e.value = t, e.done = !1, e;\n    return e.done = !0, e;\n  };\n}\nmodule.exports = _regeneratorKeys, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nvar regenerator = require(\"./regenerator.js\");\nvar regeneratorAsync = require(\"./regeneratorAsync.js\");\nvar regeneratorAsyncGen = require(\"./regeneratorAsyncGen.js\");\nvar regeneratorAsyncIterator = require(\"./regeneratorAsyncIterator.js\");\nvar regeneratorKeys = require(\"./regeneratorKeys.js\");\nvar regeneratorValues = require(\"./regeneratorValues.js\");\nfunction _regeneratorRuntime() {\n  \"use strict\";\n\n  var r = regenerator(),\n    e = r.m(_regeneratorRuntime),\n    t = (Object.getPrototypeOf ? Object.getPrototypeOf(e) : e.__proto__).constructor;\n  function n(r) {\n    var e = \"function\" == typeof r && r.constructor;\n    return !!e && (e === t || \"GeneratorFunction\" === (e.displayName || e.name));\n  }\n  var o = {\n    \"throw\": 1,\n    \"return\": 2,\n    \"break\": 3,\n    \"continue\": 3\n  };\n  function a(r) {\n    var e, t;\n    return function (n) {\n      e || (e = {\n        stop: function stop() {\n          return t(n.a, 2);\n        },\n        \"catch\": function _catch() {\n          return n.v;\n        },\n        abrupt: function abrupt(r, e) {\n          return t(n.a, o[r], e);\n        },\n        delegateYield: function delegateYield(r, o, a) {\n          return e.resultName = o, t(n.d, regeneratorValues(r), a);\n        },\n        finish: function finish(r) {\n          return t(n.f, r);\n        }\n      }, t = function t(r, _t, o) {\n        n.p = e.prev, n.n = e.next;\n        try {\n          return r(_t, o);\n        } finally {\n          e.next = n.n;\n        }\n      }), e.resultName && (e[e.resultName] = n.v, e.resultName = void 0), e.sent = n.v, e.next = n.n;\n      try {\n        return r.call(this, e);\n      } finally {\n        n.p = e.prev, n.n = e.next;\n      }\n    };\n  }\n  return (module.exports = _regeneratorRuntime = function _regeneratorRuntime() {\n    return {\n      wrap: function wrap(e, t, n, o) {\n        return r.w(a(e), t, n, o && o.reverse());\n      },\n      isGeneratorFunction: n,\n      mark: r.m,\n      awrap: function awrap(r, e) {\n        return new OverloadYield(r, e);\n      },\n      AsyncIterator: regeneratorAsyncIterator,\n      async: function async(r, e, t, o, u) {\n        return (n(e) ? regeneratorAsyncGen : regeneratorAsync)(a(r), e, t, o, u);\n      },\n      keys: regeneratorKeys,\n      values: regeneratorValues\n    };\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _regeneratorRuntime, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction _regeneratorValues(e) {\n  if (null != e) {\n    var t = e[\"function\" == typeof Symbol && Symbol.iterator || \"@@iterator\"],\n      r = 0;\n    if (t) return t.call(e);\n    if (\"function\" == typeof e.next) return e;\n    if (!isNaN(e.length)) return {\n      next: function next() {\n        return e && r >= e.length && (e = void 0), {\n          value: e && e[r++],\n          done: !e\n        };\n      }\n    };\n  }\n  throw new TypeError(_typeof(e) + \" is not iterable\");\n}\nmodule.exports = _regeneratorValues, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "const isObject = (val) => val !== null && typeof val === 'object';\nconst defaultDelimiters = ['{', '}'];\nclass BaseFormatter {\n    constructor() {\n        this._caches = Object.create(null);\n    }\n    interpolate(message, values, delimiters = defaultDelimiters) {\n        if (!values) {\n            return [message];\n        }\n        let tokens = this._caches[message];\n        if (!tokens) {\n            tokens = parse(message, delimiters);\n            this._caches[message] = tokens;\n        }\n        return compile(tokens, values);\n    }\n}\nconst RE_TOKEN_LIST_VALUE = /^(?:\\d)+/;\nconst RE_TOKEN_NAMED_VALUE = /^(?:\\w)+/;\nfunction parse(format, [startDelimiter, endDelimiter]) {\n    const tokens = [];\n    let position = 0;\n    let text = '';\n    while (position < format.length) {\n        let char = format[position++];\n        if (char === startDelimiter) {\n            if (text) {\n                tokens.push({ type: 'text', value: text });\n            }\n            text = '';\n            let sub = '';\n            char = format[position++];\n            while (char !== undefined && char !== endDelimiter) {\n                sub += char;\n                char = format[position++];\n            }\n            const isClosed = char === endDelimiter;\n            const type = RE_TOKEN_LIST_VALUE.test(sub)\n                ? 'list'\n                : isClosed && RE_TOKEN_NAMED_VALUE.test(sub)\n                    ? 'named'\n                    : 'unknown';\n            tokens.push({ value: sub, type });\n        }\n        //  else if (char === '%') {\n        //   // when found rails i18n syntax, skip text capture\n        //   if (format[position] !== '{') {\n        //     text += char\n        //   }\n        // }\n        else {\n            text += char;\n        }\n    }\n    text && tokens.push({ type: 'text', value: text });\n    return tokens;\n}\nfunction compile(tokens, values) {\n    const compiled = [];\n    let index = 0;\n    const mode = Array.isArray(values)\n        ? 'list'\n        : isObject(values)\n            ? 'named'\n            : 'unknown';\n    if (mode === 'unknown') {\n        return compiled;\n    }\n    while (index < tokens.length) {\n        const token = tokens[index];\n        switch (token.type) {\n            case 'text':\n                compiled.push(token.value);\n                break;\n            case 'list':\n                compiled.push(values[parseInt(token.value, 10)]);\n                break;\n            case 'named':\n                if (mode === 'named') {\n                    compiled.push(values[token.value]);\n                }\n                else {\n                    if (process.env.NODE_ENV !== 'production') {\n                        console.warn(`Type of token '${token.type}' and format of value '${mode}' don't match!`);\n                    }\n                }\n                break;\n            case 'unknown':\n                if (process.env.NODE_ENV !== 'production') {\n                    console.warn(`Detect 'unknown' type of token!`);\n                }\n                break;\n        }\n        index++;\n    }\n    return compiled;\n}\n\nconst LOCALE_ZH_HANS = 'zh-Hans';\nconst LOCALE_ZH_HANT = 'zh-Hant';\nconst LOCALE_EN = 'en';\nconst LOCALE_FR = 'fr';\nconst LOCALE_ES = 'es';\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\nconst hasOwn = (val, key) => hasOwnProperty.call(val, key);\nconst defaultFormatter = new BaseFormatter();\nfunction include(str, parts) {\n    return !!parts.find((part) => str.indexOf(part) !== -1);\n}\nfunction startsWith(str, parts) {\n    return parts.find((part) => str.indexOf(part) === 0);\n}\nfunction normalizeLocale(locale, messages) {\n    if (!locale) {\n        return;\n    }\n    locale = locale.trim().replace(/_/g, '-');\n    if (messages && messages[locale]) {\n        return locale;\n    }\n    locale = locale.toLowerCase();\n    if (locale === 'chinese') {\n        // 支付宝\n        return LOCALE_ZH_HANS;\n    }\n    if (locale.indexOf('zh') === 0) {\n        if (locale.indexOf('-hans') > -1) {\n            return LOCALE_ZH_HANS;\n        }\n        if (locale.indexOf('-hant') > -1) {\n            return LOCALE_ZH_HANT;\n        }\n        if (include(locale, ['-tw', '-hk', '-mo', '-cht'])) {\n            return LOCALE_ZH_HANT;\n        }\n        return LOCALE_ZH_HANS;\n    }\n    let locales = [LOCALE_EN, LOCALE_FR, LOCALE_ES];\n    if (messages && Object.keys(messages).length > 0) {\n        locales = Object.keys(messages);\n    }\n    const lang = startsWith(locale, locales);\n    if (lang) {\n        return lang;\n    }\n}\nclass I18n {\n    constructor({ locale, fallbackLocale, messages, watcher, formater, }) {\n        this.locale = LOCALE_EN;\n        this.fallbackLocale = LOCALE_EN;\n        this.message = {};\n        this.messages = {};\n        this.watchers = [];\n        if (fallbackLocale) {\n            this.fallbackLocale = fallbackLocale;\n        }\n        this.formater = formater || defaultFormatter;\n        this.messages = messages || {};\n        this.setLocale(locale || LOCALE_EN);\n        if (watcher) {\n            this.watchLocale(watcher);\n        }\n    }\n    setLocale(locale) {\n        const oldLocale = this.locale;\n        this.locale = normalizeLocale(locale, this.messages) || this.fallbackLocale;\n        if (!this.messages[this.locale]) {\n            // 可能初始化时不存在\n            this.messages[this.locale] = {};\n        }\n        this.message = this.messages[this.locale];\n        // 仅发生变化时，通知\n        if (oldLocale !== this.locale) {\n            this.watchers.forEach((watcher) => {\n                watcher(this.locale, oldLocale);\n            });\n        }\n    }\n    getLocale() {\n        return this.locale;\n    }\n    watchLocale(fn) {\n        const index = this.watchers.push(fn) - 1;\n        return () => {\n            this.watchers.splice(index, 1);\n        };\n    }\n    add(locale, message, override = true) {\n        const curMessages = this.messages[locale];\n        if (curMessages) {\n            if (override) {\n                Object.assign(curMessages, message);\n            }\n            else {\n                Object.keys(message).forEach((key) => {\n                    if (!hasOwn(curMessages, key)) {\n                        curMessages[key] = message[key];\n                    }\n                });\n            }\n        }\n        else {\n            this.messages[locale] = message;\n        }\n    }\n    f(message, values, delimiters) {\n        return this.formater.interpolate(message, values, delimiters).join('');\n    }\n    t(key, locale, values) {\n        let message = this.message;\n        if (typeof locale === 'string') {\n            locale = normalizeLocale(locale, this.messages);\n            locale && (message = this.messages[locale]);\n        }\n        else {\n            values = locale;\n        }\n        if (!hasOwn(message, key)) {\n            console.warn(`Cannot translate the value of keypath ${key}. Use the value of keypath as default.`);\n            return key;\n        }\n        return this.formater.interpolate(message[key], values).join('');\n    }\n}\n\nfunction watchAppLocale(appVm, i18n) {\n    // 需要保证 watch 的触发在组件渲染之前\n    if (appVm.$watchLocale) {\n        // vue2\n        appVm.$watchLocale((newLocale) => {\n            i18n.setLocale(newLocale);\n        });\n    }\n    else {\n        appVm.$watch(() => appVm.$locale, (newLocale) => {\n            i18n.setLocale(newLocale);\n        });\n    }\n}\nfunction getDefaultLocale() {\n    if (typeof uni !== 'undefined' && uni.getLocale) {\n        return uni.getLocale();\n    }\n    // 小程序平台，uni 和 uni-i18n 互相引用，导致访问不到 uni，故在 global 上挂了 getLocale\n    if (typeof global !== 'undefined' && global.getLocale) {\n        return global.getLocale();\n    }\n    return LOCALE_EN;\n}\nfunction initVueI18n(locale, messages = {}, fallbackLocale, watcher) {\n    // 兼容旧版本入参\n    if (typeof locale !== 'string') {\n        [locale, messages] = [\n            messages,\n            locale,\n        ];\n    }\n    if (typeof locale !== 'string') {\n        // 因为小程序平台，uni-i18n 和 uni 互相引用，导致此时访问 uni 时，为 undefined\n        locale = getDefaultLocale();\n    }\n    if (typeof fallbackLocale !== 'string') {\n        fallbackLocale =\n            (typeof __uniConfig !== 'undefined' && __uniConfig.fallbackLocale) ||\n                LOCALE_EN;\n    }\n    const i18n = new I18n({\n        locale,\n        fallbackLocale,\n        messages,\n        watcher,\n    });\n    let t = (key, values) => {\n        if (typeof getApp !== 'function') {\n            // app view\n            /* eslint-disable no-func-assign */\n            t = function (key, values) {\n                return i18n.t(key, values);\n            };\n        }\n        else {\n            let isWatchedAppLocale = false;\n            t = function (key, values) {\n                const appVm = getApp().$vm;\n                // 可能$vm还不存在，比如在支付宝小程序中，组件定义较早，在props的default里使用了t()函数（如uni-goods-nav），此时app还未初始化\n                // options: {\n                // \ttype: Array,\n                // \tdefault () {\n                // \t\treturn [{\n                // \t\t\ticon: 'shop',\n                // \t\t\ttext: t(\"uni-goods-nav.options.shop\"),\n                // \t\t}, {\n                // \t\t\ticon: 'cart',\n                // \t\t\ttext: t(\"uni-goods-nav.options.cart\")\n                // \t\t}]\n                // \t}\n                // },\n                if (appVm) {\n                    // 触发响应式\n                    appVm.$locale;\n                    if (!isWatchedAppLocale) {\n                        isWatchedAppLocale = true;\n                        watchAppLocale(appVm, i18n);\n                    }\n                }\n                return i18n.t(key, values);\n            };\n        }\n        return t(key, values);\n    };\n    return {\n        i18n,\n        f(message, values, delimiters) {\n            return i18n.f(message, values, delimiters);\n        },\n        t(key, values) {\n            return t(key, values);\n        },\n        add(locale, message, override = true) {\n            return i18n.add(locale, message, override);\n        },\n        watch(fn) {\n            return i18n.watchLocale(fn);\n        },\n        getLocale() {\n            return i18n.getLocale();\n        },\n        setLocale(newLocale) {\n            return i18n.setLocale(newLocale);\n        },\n    };\n}\n\nconst isString = (val) => typeof val === 'string';\nlet formater;\nfunction hasI18nJson(jsonObj, delimiters) {\n    if (!formater) {\n        formater = new BaseFormatter();\n    }\n    return walkJsonObj(jsonObj, (jsonObj, key) => {\n        const value = jsonObj[key];\n        if (isString(value)) {\n            if (isI18nStr(value, delimiters)) {\n                return true;\n            }\n        }\n        else {\n            return hasI18nJson(value, delimiters);\n        }\n    });\n}\nfunction parseI18nJson(jsonObj, values, delimiters) {\n    if (!formater) {\n        formater = new BaseFormatter();\n    }\n    walkJsonObj(jsonObj, (jsonObj, key) => {\n        const value = jsonObj[key];\n        if (isString(value)) {\n            if (isI18nStr(value, delimiters)) {\n                jsonObj[key] = compileStr(value, values, delimiters);\n            }\n        }\n        else {\n            parseI18nJson(value, values, delimiters);\n        }\n    });\n    return jsonObj;\n}\nfunction compileI18nJsonStr(jsonStr, { locale, locales, delimiters, }) {\n    if (!isI18nStr(jsonStr, delimiters)) {\n        return jsonStr;\n    }\n    if (!formater) {\n        formater = new BaseFormatter();\n    }\n    const localeValues = [];\n    Object.keys(locales).forEach((name) => {\n        if (name !== locale) {\n            localeValues.push({\n                locale: name,\n                values: locales[name],\n            });\n        }\n    });\n    localeValues.unshift({ locale, values: locales[locale] });\n    try {\n        return JSON.stringify(compileJsonObj(JSON.parse(jsonStr), localeValues, delimiters), null, 2);\n    }\n    catch (e) { }\n    return jsonStr;\n}\nfunction isI18nStr(value, delimiters) {\n    return value.indexOf(delimiters[0]) > -1;\n}\nfunction compileStr(value, values, delimiters) {\n    return formater.interpolate(value, values, delimiters).join('');\n}\nfunction compileValue(jsonObj, key, localeValues, delimiters) {\n    const value = jsonObj[key];\n    if (isString(value)) {\n        // 存在国际化\n        if (isI18nStr(value, delimiters)) {\n            jsonObj[key] = compileStr(value, localeValues[0].values, delimiters);\n            if (localeValues.length > 1) {\n                // 格式化国际化语言\n                const valueLocales = (jsonObj[key + 'Locales'] = {});\n                localeValues.forEach((localValue) => {\n                    valueLocales[localValue.locale] = compileStr(value, localValue.values, delimiters);\n                });\n            }\n        }\n    }\n    else {\n        compileJsonObj(value, localeValues, delimiters);\n    }\n}\nfunction compileJsonObj(jsonObj, localeValues, delimiters) {\n    walkJsonObj(jsonObj, (jsonObj, key) => {\n        compileValue(jsonObj, key, localeValues, delimiters);\n    });\n    return jsonObj;\n}\nfunction walkJsonObj(jsonObj, walk) {\n    if (Array.isArray(jsonObj)) {\n        for (let i = 0; i < jsonObj.length; i++) {\n            if (walk(jsonObj, i)) {\n                return true;\n            }\n        }\n    }\n    else if (isObject(jsonObj)) {\n        for (const key in jsonObj) {\n            if (walk(jsonObj, key)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\nfunction resolveLocale(locales) {\n    return (locale) => {\n        if (!locale) {\n            return locale;\n        }\n        locale = normalizeLocale(locale) || locale;\n        return resolveLocaleChain(locale).find((locale) => locales.indexOf(locale) > -1);\n    };\n}\nfunction resolveLocaleChain(locale) {\n    const chain = [];\n    const tokens = locale.split('-');\n    while (tokens.length) {\n        chain.push(tokens.join('-'));\n        tokens.pop();\n    }\n    return chain;\n}\n\nexport { BaseFormatter as Formatter, I18n, LOCALE_EN, LOCALE_ES, LOCALE_FR, LOCALE_ZH_HANS, LOCALE_ZH_HANT, compileI18nJsonStr, hasI18nJson, initVueI18n, isI18nStr, isString, normalizeLocale, parseI18nJson, resolveLocale };\n", "import { initVueI18n } from '@dcloudio/uni-i18n';\r\nimport Vue from 'vue';\r\n\r\nlet realAtob;\r\n\r\nconst b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\r\nconst b64re = /^(?:[A-Za-z\\d+/]{4})*?(?:[A-Za-z\\d+/]{2}(?:==)?|[A-Za-z\\d+/]{3}=?)?$/;\r\n\r\nif (typeof atob !== 'function') {\r\n  realAtob = function (str) {\r\n    str = String(str).replace(/[\\t\\n\\f\\r ]+/g, '');\r\n    if (!b64re.test(str)) { throw new Error(\"Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.\") }\r\n\r\n    // Adding the padding if missing, for semplicity\r\n    str += '=='.slice(2 - (str.length & 3));\r\n    var bitmap; var result = ''; var r1; var r2; var i = 0;\r\n    for (; i < str.length;) {\r\n      bitmap = b64.indexOf(str.charAt(i++)) << 18 | b64.indexOf(str.charAt(i++)) << 12 |\r\n                    (r1 = b64.indexOf(str.charAt(i++))) << 6 | (r2 = b64.indexOf(str.charAt(i++)));\r\n\r\n      result += r1 === 64 ? String.fromCharCode(bitmap >> 16 & 255)\r\n        : r2 === 64 ? String.fromCharCode(bitmap >> 16 & 255, bitmap >> 8 & 255)\r\n          : String.fromCharCode(bitmap >> 16 & 255, bitmap >> 8 & 255, bitmap & 255);\r\n    }\r\n    return result\r\n  };\r\n} else {\r\n  // 注意atob只能在全局对象上调用，例如：`const Base64 = {atob};Base64.atob('xxxx')`是错误的用法\r\n  realAtob = atob;\r\n}\r\n\r\nfunction b64DecodeUnicode (str) {\r\n  return decodeURIComponent(realAtob(str).split('').map(function (c) {\r\n    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)\r\n  }).join(''))\r\n}\r\n\r\nfunction getCurrentUserInfo () {\r\n  const token = ( wx).getStorageSync('uni_id_token') || '';\r\n  const tokenArr = token.split('.');\r\n  if (!token || tokenArr.length !== 3) {\r\n    return {\r\n      uid: null,\r\n      role: [],\r\n      permission: [],\r\n      tokenExpired: 0\r\n    }\r\n  }\r\n  let userInfo;\r\n  try {\r\n    userInfo = JSON.parse(b64DecodeUnicode(tokenArr[1]));\r\n  } catch (error) {\r\n    throw new Error('获取当前用户信息出错，详细错误信息为：' + error.message)\r\n  }\r\n  userInfo.tokenExpired = userInfo.exp * 1000;\r\n  delete userInfo.exp;\r\n  delete userInfo.iat;\r\n  return userInfo\r\n}\r\n\r\nfunction uniIdMixin (Vue) {\r\n  Vue.prototype.uniIDHasRole = function (roleId) {\r\n    const {\r\n      role\r\n    } = getCurrentUserInfo();\r\n    return role.indexOf(roleId) > -1\r\n  };\r\n  Vue.prototype.uniIDHasPermission = function (permissionId) {\r\n    const {\r\n      permission\r\n    } = getCurrentUserInfo();\r\n    return this.uniIDHasRole('admin') || permission.indexOf(permissionId) > -1\r\n  };\r\n  Vue.prototype.uniIDTokenValid = function () {\r\n    const {\r\n      tokenExpired\r\n    } = getCurrentUserInfo();\r\n    return tokenExpired > Date.now()\r\n  };\r\n}\r\n\r\nconst _toString = Object.prototype.toString;\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\n\r\nfunction isFn (fn) {\r\n  return typeof fn === 'function'\r\n}\r\n\r\nfunction isStr (str) {\r\n  return typeof str === 'string'\r\n}\r\n\r\nfunction isObject (obj) {\r\n  return obj !== null && typeof obj === 'object'\r\n}\r\n\r\nfunction isPlainObject (obj) {\r\n  return _toString.call(obj) === '[object Object]'\r\n}\r\n\r\nfunction hasOwn (obj, key) {\r\n  return hasOwnProperty.call(obj, key)\r\n}\r\n\r\nfunction noop () {}\r\n\r\n/**\r\n * Create a cached version of a pure function.\r\n */\r\nfunction cached (fn) {\r\n  const cache = Object.create(null);\r\n  return function cachedFn (str) {\r\n    const hit = cache[str];\r\n    return hit || (cache[str] = fn(str))\r\n  }\r\n}\r\n\r\n/**\r\n * Camelize a hyphen-delimited string.\r\n */\r\nconst camelizeRE = /-(\\w)/g;\r\nconst camelize = cached((str) => {\r\n  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : '')\r\n});\r\n\r\nfunction sortObject (obj) {\r\n  const sortObj = {};\r\n  if (isPlainObject(obj)) {\r\n    Object.keys(obj).sort().forEach(key => {\r\n      sortObj[key] = obj[key];\r\n    });\r\n  }\r\n  return !Object.keys(sortObj) ? obj : sortObj\r\n}\r\n\r\nconst HOOKS = [\r\n  'invoke',\r\n  'success',\r\n  'fail',\r\n  'complete',\r\n  'returnValue'\r\n];\r\n\r\nconst globalInterceptors = {};\r\nconst scopedInterceptors = {};\r\n\r\nfunction mergeHook (parentVal, childVal) {\r\n  const res = childVal\r\n    ? parentVal\r\n      ? parentVal.concat(childVal)\r\n      : Array.isArray(childVal)\r\n        ? childVal : [childVal]\r\n    : parentVal;\r\n  return res\r\n    ? dedupeHooks(res)\r\n    : res\r\n}\r\n\r\nfunction dedupeHooks (hooks) {\r\n  const res = [];\r\n  for (let i = 0; i < hooks.length; i++) {\r\n    if (res.indexOf(hooks[i]) === -1) {\r\n      res.push(hooks[i]);\r\n    }\r\n  }\r\n  return res\r\n}\r\n\r\nfunction removeHook (hooks, hook) {\r\n  const index = hooks.indexOf(hook);\r\n  if (index !== -1) {\r\n    hooks.splice(index, 1);\r\n  }\r\n}\r\n\r\nfunction mergeInterceptorHook (interceptor, option) {\r\n  Object.keys(option).forEach(hook => {\r\n    if (HOOKS.indexOf(hook) !== -1 && isFn(option[hook])) {\r\n      interceptor[hook] = mergeHook(interceptor[hook], option[hook]);\r\n    }\r\n  });\r\n}\r\n\r\nfunction removeInterceptorHook (interceptor, option) {\r\n  if (!interceptor || !option) {\r\n    return\r\n  }\r\n  Object.keys(option).forEach(hook => {\r\n    if (HOOKS.indexOf(hook) !== -1 && isFn(option[hook])) {\r\n      removeHook(interceptor[hook], option[hook]);\r\n    }\r\n  });\r\n}\r\n\r\nfunction addInterceptor (method, option) {\r\n  if (typeof method === 'string' && isPlainObject(option)) {\r\n    mergeInterceptorHook(scopedInterceptors[method] || (scopedInterceptors[method] = {}), option);\r\n  } else if (isPlainObject(method)) {\r\n    mergeInterceptorHook(globalInterceptors, method);\r\n  }\r\n}\r\n\r\nfunction removeInterceptor (method, option) {\r\n  if (typeof method === 'string') {\r\n    if (isPlainObject(option)) {\r\n      removeInterceptorHook(scopedInterceptors[method], option);\r\n    } else {\r\n      delete scopedInterceptors[method];\r\n    }\r\n  } else if (isPlainObject(method)) {\r\n    removeInterceptorHook(globalInterceptors, method);\r\n  }\r\n}\r\n\r\nfunction wrapperHook (hook, params) {\r\n  return function (data) {\r\n    return hook(data, params) || data\r\n  }\r\n}\r\n\r\nfunction isPromise (obj) {\r\n  return !!obj && (typeof obj === 'object' || typeof obj === 'function') && typeof obj.then === 'function'\r\n}\r\n\r\nfunction queue (hooks, data, params) {\r\n  let promise = false;\r\n  for (let i = 0; i < hooks.length; i++) {\r\n    const hook = hooks[i];\r\n    if (promise) {\r\n      promise = Promise.resolve(wrapperHook(hook, params));\r\n    } else {\r\n      const res = hook(data, params);\r\n      if (isPromise(res)) {\r\n        promise = Promise.resolve(res);\r\n      }\r\n      if (res === false) {\r\n        return {\r\n          then () { }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  return promise || {\r\n    then (callback) {\r\n      return callback(data)\r\n    }\r\n  }\r\n}\r\n\r\nfunction wrapperOptions (interceptor, options = {}) {\r\n  ['success', 'fail', 'complete'].forEach(name => {\r\n    if (Array.isArray(interceptor[name])) {\r\n      const oldCallback = options[name];\r\n      options[name] = function callbackInterceptor (res) {\r\n        queue(interceptor[name], res, options).then((res) => {\r\n          /* eslint-disable no-mixed-operators */\r\n          return isFn(oldCallback) && oldCallback(res) || res\r\n        });\r\n      };\r\n    }\r\n  });\r\n  return options\r\n}\r\n\r\nfunction wrapperReturnValue (method, returnValue) {\r\n  const returnValueHooks = [];\r\n  if (Array.isArray(globalInterceptors.returnValue)) {\r\n    returnValueHooks.push(...globalInterceptors.returnValue);\r\n  }\r\n  const interceptor = scopedInterceptors[method];\r\n  if (interceptor && Array.isArray(interceptor.returnValue)) {\r\n    returnValueHooks.push(...interceptor.returnValue);\r\n  }\r\n  returnValueHooks.forEach(hook => {\r\n    returnValue = hook(returnValue) || returnValue;\r\n  });\r\n  return returnValue\r\n}\r\n\r\nfunction getApiInterceptorHooks (method) {\r\n  const interceptor = Object.create(null);\r\n  Object.keys(globalInterceptors).forEach(hook => {\r\n    if (hook !== 'returnValue') {\r\n      interceptor[hook] = globalInterceptors[hook].slice();\r\n    }\r\n  });\r\n  const scopedInterceptor = scopedInterceptors[method];\r\n  if (scopedInterceptor) {\r\n    Object.keys(scopedInterceptor).forEach(hook => {\r\n      if (hook !== 'returnValue') {\r\n        interceptor[hook] = (interceptor[hook] || []).concat(scopedInterceptor[hook]);\r\n      }\r\n    });\r\n  }\r\n  return interceptor\r\n}\r\n\r\nfunction invokeApi (method, api, options, ...params) {\r\n  const interceptor = getApiInterceptorHooks(method);\r\n  if (interceptor && Object.keys(interceptor).length) {\r\n    if (Array.isArray(interceptor.invoke)) {\r\n      const res = queue(interceptor.invoke, options);\r\n      return res.then((options) => {\r\n        // 重新访问 getApiInterceptorHooks, 允许 invoke 中再次调用 addInterceptor,removeInterceptor\r\n        return api(\r\n          wrapperOptions(getApiInterceptorHooks(method), options),\r\n          ...params\r\n        )\r\n      })\r\n    } else {\r\n      return api(wrapperOptions(interceptor, options), ...params)\r\n    }\r\n  }\r\n  return api(options, ...params)\r\n}\r\n\r\nconst promiseInterceptor = {\r\n  returnValue (res) {\r\n    if (!isPromise(res)) {\r\n      return res\r\n    }\r\n    return new Promise((resolve, reject) => {\r\n      res.then(res => {\r\n        if (!res) {\r\n          resolve(res);\r\n          return\r\n        }\r\n        if (res[0]) {\r\n          reject(res[0]);\r\n        } else {\r\n          resolve(res[1]);\r\n        }\r\n      });\r\n    })\r\n  }\r\n};\r\n\r\nconst SYNC_API_RE =\r\n  /^\\$|__f__|Window$|WindowStyle$|sendHostEvent|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|rpx2px|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getLocale|setLocale|invokePushCallback|getWindowInfo|getDeviceInfo|getAppBaseInfo|getSystemSetting|getAppAuthorizeSetting|initUTS|requireUTS|registerUTS/;\r\n\r\nconst CONTEXT_API_RE = /^create|Manager$/;\r\n\r\n// Context例外情况\r\nconst CONTEXT_API_RE_EXC = ['createBLEConnection'];\r\n\r\n// 同步例外情况\r\nconst ASYNC_API = ['createBLEConnection', 'createPushMessage'];\r\n\r\nconst CALLBACK_API_RE = /^on|^off/;\r\n\r\nfunction isContextApi (name) {\r\n  return CONTEXT_API_RE.test(name) && CONTEXT_API_RE_EXC.indexOf(name) === -1\r\n}\r\nfunction isSyncApi (name) {\r\n  return SYNC_API_RE.test(name) && ASYNC_API.indexOf(name) === -1\r\n}\r\n\r\nfunction isCallbackApi (name) {\r\n  return CALLBACK_API_RE.test(name) && name !== 'onPush'\r\n}\r\n\r\nfunction handlePromise (promise) {\r\n  return promise.then(data => {\r\n    return [null, data]\r\n  })\r\n    .catch(err => [err])\r\n}\r\n\r\nfunction shouldPromise (name) {\r\n  if (\r\n    isContextApi(name) ||\r\n    isSyncApi(name) ||\r\n    isCallbackApi(name)\r\n  ) {\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\n/* eslint-disable no-extend-native */\r\nif (!Promise.prototype.finally) {\r\n  Promise.prototype.finally = function (callback) {\r\n    const promise = this.constructor;\r\n    return this.then(\r\n      value => promise.resolve(callback()).then(() => value),\r\n      reason => promise.resolve(callback()).then(() => {\r\n        throw reason\r\n      })\r\n    )\r\n  };\r\n}\r\n\r\nfunction promisify (name, api) {\r\n  if (!shouldPromise(name) || !isFn(api)) {\r\n    return api\r\n  }\r\n  return function promiseApi (options = {}, ...params) {\r\n    if (isFn(options.success) || isFn(options.fail) || isFn(options.complete)) {\r\n      return wrapperReturnValue(name, invokeApi(name, api, options, ...params))\r\n    }\r\n    return wrapperReturnValue(name, handlePromise(new Promise((resolve, reject) => {\r\n      invokeApi(name, api, Object.assign({}, options, {\r\n        success: resolve,\r\n        fail: reject\r\n      }), ...params);\r\n    })))\r\n  }\r\n}\r\n\r\nconst EPS = 1e-4;\r\nconst BASE_DEVICE_WIDTH = 750;\r\nlet isIOS = false;\r\nlet deviceWidth = 0;\r\nlet deviceDPR = 0;\r\n\r\nfunction checkDeviceWidth() {\r\n  let windowWidth, pixelRatio, platform;\r\n\r\n  {\r\n    const windowInfo = typeof wx.getWindowInfo === 'function' && wx.getWindowInfo() ? wx.getWindowInfo() : wx.getSystemInfoSync();\r\n    const deviceInfo = typeof wx.getDeviceInfo === 'function' && wx.getDeviceInfo() ? wx.getDeviceInfo() : wx.getSystemInfoSync();\r\n\r\n    windowWidth = windowInfo.windowWidth;\r\n    pixelRatio = windowInfo.pixelRatio;\r\n    platform = deviceInfo.platform;\r\n  }\r\n\r\n  deviceWidth = windowWidth;\r\n  deviceDPR = pixelRatio;\r\n  isIOS = platform === 'ios';\r\n}\r\n\r\nfunction upx2px(number, newDeviceWidth) {\r\n  if (deviceWidth === 0) {\r\n    checkDeviceWidth();\r\n  }\r\n\r\n  number = Number(number);\r\n  if (number === 0) {\r\n    return 0\r\n  }\r\n  let result = (number / BASE_DEVICE_WIDTH) * (newDeviceWidth || deviceWidth);\r\n  if (result < 0) {\r\n    result = -result;\r\n  }\r\n  result = Math.floor(result + EPS);\r\n  if (result === 0) {\r\n    if (deviceDPR === 1 || !isIOS) {\r\n      result = 1;\r\n    } else {\r\n      result = 0.5;\r\n    }\r\n  }\r\n  return number < 0 ? -result : result\r\n}\r\n\r\nconst LOCALE_ZH_HANS = 'zh-Hans';\r\nconst LOCALE_ZH_HANT = 'zh-Hant';\r\nconst LOCALE_EN = 'en';\r\nconst LOCALE_FR = 'fr';\r\nconst LOCALE_ES = 'es';\r\n\r\nconst messages = {};\r\n\r\nfunction getLocaleLanguage () {\r\n  let localeLanguage = '';\r\n  {\r\n    const appBaseInfo = typeof wx.getAppBaseInfo === 'function' && wx.getAppBaseInfo() ? wx.getAppBaseInfo() : wx.getSystemInfoSync();\r\n    const language =\r\n      appBaseInfo && appBaseInfo.language ? appBaseInfo.language : LOCALE_EN;\r\n    localeLanguage = normalizeLocale(language) || LOCALE_EN;\r\n  }\r\n  return localeLanguage\r\n}\r\n\r\nlet locale;\r\n\r\n{\r\n  locale = getLocaleLanguage();\r\n}\r\n\r\nfunction initI18nMessages () {\r\n  if (!isEnableLocale()) {\r\n    return\r\n  }\r\n  const localeKeys = Object.keys(__uniConfig.locales);\r\n  if (localeKeys.length) {\r\n    localeKeys.forEach((locale) => {\r\n      const curMessages = messages[locale];\r\n      const userMessages = __uniConfig.locales[locale];\r\n      if (curMessages) {\r\n        Object.assign(curMessages, userMessages);\r\n      } else {\r\n        messages[locale] = userMessages;\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\ninitI18nMessages();\r\n\r\nconst i18n = initVueI18n(\r\n  locale,\r\n   {}\r\n);\r\nconst t = i18n.t;\r\nconst i18nMixin = (i18n.mixin = {\r\n  beforeCreate () {\r\n    const unwatch = i18n.i18n.watchLocale(() => {\r\n      this.$forceUpdate();\r\n    });\r\n    this.$once('hook:beforeDestroy', function () {\r\n      unwatch();\r\n    });\r\n  },\r\n  methods: {\r\n    $$t (key, values) {\r\n      return t(key, values)\r\n    }\r\n  }\r\n});\r\nconst setLocale = i18n.setLocale;\r\nconst getLocale = i18n.getLocale;\r\n\r\nfunction initAppLocale (Vue, appVm, locale) {\r\n  const state = Vue.observable({\r\n    locale: locale || i18n.getLocale()\r\n  });\r\n  const localeWatchers = [];\r\n  appVm.$watchLocale = fn => {\r\n    localeWatchers.push(fn);\r\n  };\r\n  Object.defineProperty(appVm, '$locale', {\r\n    get () {\r\n      return state.locale\r\n    },\r\n    set (v) {\r\n      state.locale = v;\r\n      localeWatchers.forEach(watch => watch(v));\r\n    }\r\n  });\r\n}\r\n\r\nfunction isEnableLocale () {\r\n  return typeof __uniConfig !== 'undefined' && __uniConfig.locales && !!Object.keys(__uniConfig.locales).length\r\n}\r\n\r\nfunction include (str, parts) {\r\n  return !!parts.find((part) => str.indexOf(part) !== -1)\r\n}\r\n\r\nfunction startsWith (str, parts) {\r\n  return parts.find((part) => str.indexOf(part) === 0)\r\n}\r\n\r\nfunction normalizeLocale (locale, messages) {\r\n  if (!locale) {\r\n    return\r\n  }\r\n  locale = locale.trim().replace(/_/g, '-');\r\n  if (messages && messages[locale]) {\r\n    return locale\r\n  }\r\n  locale = locale.toLowerCase();\r\n  if (locale === 'chinese') {\r\n    // 支付宝\r\n    return LOCALE_ZH_HANS\r\n  }\r\n  if (locale.indexOf('zh') === 0) {\r\n    if (locale.indexOf('-hans') > -1) {\r\n      return LOCALE_ZH_HANS\r\n    }\r\n    if (locale.indexOf('-hant') > -1) {\r\n      return LOCALE_ZH_HANT\r\n    }\r\n    if (include(locale, ['-tw', '-hk', '-mo', '-cht'])) {\r\n      return LOCALE_ZH_HANT\r\n    }\r\n    return LOCALE_ZH_HANS\r\n  }\r\n  const lang = startsWith(locale, [LOCALE_EN, LOCALE_FR, LOCALE_ES]);\r\n  if (lang) {\r\n    return lang\r\n  }\r\n}\r\n// export function initI18n() {\r\n//   const localeKeys = Object.keys(__uniConfig.locales || {})\r\n//   if (localeKeys.length) {\r\n//     localeKeys.forEach((locale) =>\r\n//       i18n.add(locale, __uniConfig.locales[locale])\r\n//     )\r\n//   }\r\n// }\r\n\r\nfunction getLocale$1 () {\r\n  // 优先使用 $locale\r\n  if (isFn(getApp)) {\r\n    const app = getApp({\r\n      allowDefault: true\r\n    });\r\n    if (app && app.$vm) {\r\n      return app.$vm.$locale\r\n    }\r\n  }\r\n  return getLocaleLanguage()\r\n}\r\n\r\nfunction setLocale$1 (locale) {\r\n  const app = isFn(getApp) ? getApp() : false;\r\n  if (!app) {\r\n    return false\r\n  }\r\n  const oldLocale = app.$vm.$locale;\r\n  if (oldLocale !== locale) {\r\n    app.$vm.$locale = locale;\r\n    onLocaleChangeCallbacks.forEach((fn) => fn({\r\n      locale\r\n    }));\r\n    return true\r\n  }\r\n  return false\r\n}\r\n\r\nconst onLocaleChangeCallbacks = [];\r\nfunction onLocaleChange (fn) {\r\n  if (onLocaleChangeCallbacks.indexOf(fn) === -1) {\r\n    onLocaleChangeCallbacks.push(fn);\r\n  }\r\n}\r\n\r\nif (typeof global !== 'undefined') {\r\n  global.getLocale = getLocale$1;\r\n}\r\n\r\nconst interceptors = {\r\n  promiseInterceptor\r\n};\r\n\r\nvar baseApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  upx2px: upx2px,\r\n  rpx2px: upx2px,\r\n  getLocale: getLocale$1,\r\n  setLocale: setLocale$1,\r\n  onLocaleChange: onLocaleChange,\r\n  addInterceptor: addInterceptor,\r\n  removeInterceptor: removeInterceptor,\r\n  interceptors: interceptors\r\n});\r\n\r\nfunction findExistsPageIndex (url) {\r\n  const pages = getCurrentPages();\r\n  let len = pages.length;\r\n  while (len--) {\r\n    const page = pages[len];\r\n    if (page.$page && page.$page.fullPath === url) {\r\n      return len\r\n    }\r\n  }\r\n  return -1\r\n}\r\n\r\nvar redirectTo = {\r\n  name (fromArgs) {\r\n    if (fromArgs.exists === 'back' && fromArgs.delta) {\r\n      return 'navigateBack'\r\n    }\r\n    return 'redirectTo'\r\n  },\r\n  args (fromArgs) {\r\n    if (fromArgs.exists === 'back' && fromArgs.url) {\r\n      const existsPageIndex = findExistsPageIndex(fromArgs.url);\r\n      if (existsPageIndex !== -1) {\r\n        const delta = getCurrentPages().length - 1 - existsPageIndex;\r\n        if (delta > 0) {\r\n          fromArgs.delta = delta;\r\n        }\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nvar previewImage = {\r\n  args (fromArgs) {\r\n    let currentIndex = parseInt(fromArgs.current);\r\n    if (isNaN(currentIndex)) {\r\n      return\r\n    }\r\n    const urls = fromArgs.urls;\r\n    if (!Array.isArray(urls)) {\r\n      return\r\n    }\r\n    const len = urls.length;\r\n    if (!len) {\r\n      return\r\n    }\r\n    if (currentIndex < 0) {\r\n      currentIndex = 0;\r\n    } else if (currentIndex >= len) {\r\n      currentIndex = len - 1;\r\n    }\r\n    if (currentIndex > 0) {\r\n      fromArgs.current = urls[currentIndex];\r\n      fromArgs.urls = urls.filter(\r\n        (item, index) => index < currentIndex ? item !== urls[currentIndex] : true\r\n      );\r\n    } else {\r\n      fromArgs.current = urls[0];\r\n    }\r\n    return {\r\n      indicator: false,\r\n      loop: false\r\n    }\r\n  }\r\n};\r\n\r\nconst UUID_KEY = '__DC_STAT_UUID';\r\nlet deviceId;\r\nfunction useDeviceId (result) {\r\n  deviceId = deviceId || wx.getStorageSync(UUID_KEY);\r\n  if (!deviceId) {\r\n    deviceId = Date.now() + '' + Math.floor(Math.random() * 1e7);\r\n    wx.setStorage({\r\n      key: UUID_KEY,\r\n      data: deviceId\r\n    });\r\n  }\r\n  result.deviceId = deviceId;\r\n}\r\n\r\nfunction addSafeAreaInsets (result) {\r\n  if (result.safeArea) {\r\n    const safeArea = result.safeArea;\r\n    result.safeAreaInsets = {\r\n      top: safeArea.top,\r\n      left: safeArea.left,\r\n      right: result.windowWidth - safeArea.right,\r\n      bottom: result.screenHeight - safeArea.bottom\r\n    };\r\n  }\r\n}\r\n\r\nfunction getOSInfo (system, platform) {\r\n  let osName = '';\r\n  let osVersion = '';\r\n\r\n  if (\r\n    platform &&\r\n    ( \"mp-weixin\" === 'mp-baidu')\r\n  ) {\r\n    osName = platform;\r\n    osVersion = system;\r\n  } else {\r\n    osName = system.split(' ')[0] || platform;\r\n    osVersion = system.split(' ')[1] || '';\r\n  }\r\n\r\n  osName = osName.toLocaleLowerCase();\r\n  switch (osName) {\r\n    case 'harmony': // alipay\r\n    case 'ohos': // weixin\r\n    case 'openharmony': // feishu\r\n      osName = 'harmonyos';\r\n      break\r\n    case 'iphone os': // alipay\r\n      osName = 'ios';\r\n      break\r\n    case 'mac': // weixin qq\r\n    case 'darwin': // feishu\r\n      osName = 'macos';\r\n      break\r\n    case 'windows_nt': // feishu\r\n      osName = 'windows';\r\n      break\r\n  }\r\n\r\n  return {\r\n    osName,\r\n    osVersion\r\n  }\r\n}\r\n\r\nfunction populateParameters (result) {\r\n  const {\r\n    brand = '', model = '', system = '',\r\n    language = '', theme, version,\r\n    platform, fontSizeSetting,\r\n    SDKVersion, pixelRatio, deviceOrientation\r\n  } = result;\r\n  // const isQuickApp = \"mp-weixin\".indexOf('quickapp-webview') !== -1\r\n\r\n  const extraParam = {};\r\n\r\n  // osName osVersion\r\n  const { osName, osVersion } = getOSInfo(system, platform);\r\n  let hostVersion = version;\r\n\r\n  // deviceType\r\n  const deviceType = getGetDeviceType(result, model);\r\n\r\n  // deviceModel\r\n  const deviceBrand = getDeviceBrand(brand);\r\n\r\n  // hostName\r\n  const _hostName = getHostName(result);\r\n\r\n  // deviceOrientation\r\n  let _deviceOrientation = deviceOrientation; // 仅 微信 百度 支持\r\n\r\n  // devicePixelRatio\r\n  let _devicePixelRatio = pixelRatio;\r\n\r\n  // SDKVersion\r\n  let _SDKVersion = SDKVersion;\r\n\r\n  // hostLanguage\r\n  const hostLanguage = (language || '').replace(/_/g, '-');\r\n\r\n  // wx.getAccountInfoSync\r\n\r\n  const parameters = {\r\n    appId: process.env.UNI_APP_ID,\r\n    appName: process.env.UNI_APP_NAME,\r\n    appVersion: process.env.UNI_APP_VERSION_NAME,\r\n    appVersionCode: process.env.UNI_APP_VERSION_CODE,\r\n    appLanguage: getAppLanguage(hostLanguage),\r\n    uniCompileVersion: process.env.UNI_COMPILER_VERSION,\r\n    uniCompilerVersion: process.env.UNI_COMPILER_VERSION,\r\n    uniRuntimeVersion: process.env.UNI_COMPILER_VERSION,\r\n    uniPlatform: process.env.UNI_SUB_PLATFORM || process.env.UNI_PLATFORM,\r\n    deviceBrand,\r\n    deviceModel: model,\r\n    deviceType,\r\n    devicePixelRatio: _devicePixelRatio,\r\n    deviceOrientation: _deviceOrientation,\r\n    osName: osName.toLocaleLowerCase(),\r\n    osVersion,\r\n    hostTheme: theme,\r\n    hostVersion,\r\n    hostLanguage,\r\n    hostName: _hostName,\r\n    hostSDKVersion: _SDKVersion,\r\n    hostFontSizeSetting: fontSizeSetting,\r\n    windowTop: 0,\r\n    windowBottom: 0,\r\n    // TODO\r\n    osLanguage: undefined,\r\n    osTheme: undefined,\r\n    ua: undefined,\r\n    hostPackageName: undefined,\r\n    browserName: undefined,\r\n    browserVersion: undefined,\r\n    isUniAppX: false\r\n  };\r\n\r\n  Object.assign(result, parameters, extraParam);\r\n}\r\n\r\nfunction getGetDeviceType (result, model) {\r\n  let deviceType = result.deviceType || 'phone';\r\n  {\r\n    const deviceTypeMaps = {\r\n      ipad: 'pad',\r\n      windows: 'pc',\r\n      mac: 'pc'\r\n    };\r\n    const deviceTypeMapsKeys = Object.keys(deviceTypeMaps);\r\n    const _model = model.toLocaleLowerCase();\r\n    for (let index = 0; index < deviceTypeMapsKeys.length; index++) {\r\n      const _m = deviceTypeMapsKeys[index];\r\n      if (_model.indexOf(_m) !== -1) {\r\n        deviceType = deviceTypeMaps[_m];\r\n        break\r\n      }\r\n    }\r\n  }\r\n  return deviceType\r\n}\r\n\r\nfunction getDeviceBrand (brand) {\r\n  let deviceBrand = brand;\r\n  if (deviceBrand) {\r\n    deviceBrand = brand.toLocaleLowerCase();\r\n  }\r\n  return deviceBrand\r\n}\r\n\r\nfunction getAppLanguage (defaultLanguage) {\r\n  return getLocale$1\r\n    ? getLocale$1()\r\n    : defaultLanguage\r\n}\r\n\r\nfunction getHostName (result) {\r\n  const _platform =\r\n     'WeChat'\r\n      ;\r\n  let _hostName = result.hostName || _platform; // mp-jd\r\n  {\r\n    if (result.environment) {\r\n      _hostName = result.environment;\r\n    } else if (result.host && result.host.env) {\r\n      _hostName = result.host.env;\r\n    }\r\n  }\r\n\r\n  return _hostName\r\n}\r\n\r\nvar getSystemInfo = {\r\n  returnValue: function (result) {\r\n    useDeviceId(result);\r\n    addSafeAreaInsets(result);\r\n    populateParameters(result);\r\n  }\r\n};\r\n\r\nvar showActionSheet = {\r\n  args (fromArgs) {\r\n    if (typeof fromArgs === 'object') {\r\n      fromArgs.alertText = fromArgs.title;\r\n    }\r\n  }\r\n};\r\n\r\nvar getAppBaseInfo = {\r\n  returnValue: function (result) {\r\n    const { version, language, SDKVersion, theme } = result;\r\n\r\n    const _hostName = getHostName(result);\r\n\r\n    const hostLanguage = (language || '').replace('_', '-');\r\n\r\n    result = sortObject(Object.assign(result, {\r\n      appId: process.env.UNI_APP_ID,\r\n      appName: process.env.UNI_APP_NAME,\r\n      appVersion: process.env.UNI_APP_VERSION_NAME,\r\n      appVersionCode: process.env.UNI_APP_VERSION_CODE,\r\n      appLanguage: getAppLanguage(hostLanguage),\r\n      hostVersion: version,\r\n      hostLanguage,\r\n      hostName: _hostName,\r\n      hostSDKVersion: SDKVersion,\r\n      hostTheme: theme,\r\n      isUniAppX: false,\r\n      uniPlatform: process.env.UNI_SUB_PLATFORM || process.env.UNI_PLATFORM,\r\n      uniCompileVersion: process.env.UNI_COMPILER_VERSION,\r\n      uniCompilerVersion: process.env.UNI_COMPILER_VERSION,\r\n      uniRuntimeVersion: process.env.UNI_COMPILER_VERSION\r\n    }));\r\n  }\r\n};\r\n\r\nvar getDeviceInfo = {\r\n  returnValue: function (result) {\r\n    const { brand, model, system = '', platform = '' } = result;\r\n    const deviceType = getGetDeviceType(result, model);\r\n    const deviceBrand = getDeviceBrand(brand);\r\n    useDeviceId(result);\r\n\r\n    const { osName, osVersion } = getOSInfo(system, platform);\r\n\r\n    result = sortObject(Object.assign(result, {\r\n      deviceType,\r\n      deviceBrand,\r\n      deviceModel: model,\r\n      osName,\r\n      osVersion\r\n    }));\r\n  }\r\n};\r\n\r\nvar getWindowInfo = {\r\n  returnValue: function (result) {\r\n    addSafeAreaInsets(result);\r\n\r\n    result = sortObject(Object.assign(result, {\r\n      windowTop: 0,\r\n      windowBottom: 0\r\n    }));\r\n  }\r\n};\r\n\r\nvar getAppAuthorizeSetting = {\r\n  returnValue: function (result) {\r\n    const { locationReducedAccuracy } = result;\r\n\r\n    result.locationAccuracy = 'unsupported';\r\n    if (locationReducedAccuracy === true) {\r\n      result.locationAccuracy = 'reduced';\r\n    } else if (locationReducedAccuracy === false) {\r\n      result.locationAccuracy = 'full';\r\n    }\r\n  }\r\n};\r\n\r\n// import navigateTo from 'uni-helpers/navigate-to'\r\n\r\nconst compressImage = {\r\n  args (fromArgs) {\r\n    // https://developers.weixin.qq.com/community/develop/doc/000c08940c865011298e0a43256800?highLine=compressHeight\r\n    if (fromArgs.compressedHeight && !fromArgs.compressHeight) {\r\n      fromArgs.compressHeight = fromArgs.compressedHeight;\r\n    }\r\n    if (fromArgs.compressedWidth && !fromArgs.compressWidth) {\r\n      fromArgs.compressWidth = fromArgs.compressedWidth;\r\n    }\r\n  }\r\n};\r\n\r\nconst protocols = {\r\n  redirectTo,\r\n  // navigateTo,  // 由于在微信开发者工具的页面参数，会显示__id__参数，因此暂时关闭mp-weixin对于navigateTo的AOP\r\n  previewImage,\r\n  getSystemInfo,\r\n  getSystemInfoSync: getSystemInfo,\r\n  showActionSheet,\r\n  getAppBaseInfo,\r\n  getDeviceInfo,\r\n  getWindowInfo,\r\n  getAppAuthorizeSetting,\r\n  compressImage\r\n};\r\nconst todos = [\r\n  'vibrate',\r\n  'preloadPage',\r\n  'unPreloadPage',\r\n  'loadSubPackage'\r\n];\r\nconst canIUses = [];\r\n\r\nconst CALLBACKS = ['success', 'fail', 'cancel', 'complete'];\r\n\r\nfunction processCallback (methodName, method, returnValue) {\r\n  return function (res) {\r\n    return method(processReturnValue(methodName, res, returnValue))\r\n  }\r\n}\r\n\r\nfunction processArgs (methodName, fromArgs, argsOption = {}, returnValue = {}, keepFromArgs = false) {\r\n  if (isPlainObject(fromArgs)) { // 一般 api 的参数解析\r\n    const toArgs = keepFromArgs === true ? fromArgs : {}; // returnValue 为 false 时，说明是格式化返回值，直接在返回值对象上修改赋值\r\n    if (isFn(argsOption)) {\r\n      argsOption = argsOption(fromArgs, toArgs) || {};\r\n    }\r\n    for (const key in fromArgs) {\r\n      if (hasOwn(argsOption, key)) {\r\n        let keyOption = argsOption[key];\r\n        if (isFn(keyOption)) {\r\n          keyOption = keyOption(fromArgs[key], fromArgs, toArgs);\r\n        }\r\n        if (!keyOption) { // 不支持的参数\r\n          console.warn(`The '${methodName}' method of platform '微信小程序' does not support option '${key}'`);\r\n        } else if (isStr(keyOption)) { // 重写参数 key\r\n          toArgs[keyOption] = fromArgs[key];\r\n        } else if (isPlainObject(keyOption)) { // {name:newName,value:value}可重新指定参数 key:value\r\n          toArgs[keyOption.name ? keyOption.name : key] = keyOption.value;\r\n        }\r\n      } else if (CALLBACKS.indexOf(key) !== -1) {\r\n        if (isFn(fromArgs[key])) {\r\n          toArgs[key] = processCallback(methodName, fromArgs[key], returnValue);\r\n        }\r\n      } else {\r\n        if (!keepFromArgs) {\r\n          toArgs[key] = fromArgs[key];\r\n        }\r\n      }\r\n    }\r\n    return toArgs\r\n  } else if (isFn(fromArgs)) {\r\n    fromArgs = processCallback(methodName, fromArgs, returnValue);\r\n  }\r\n  return fromArgs\r\n}\r\n\r\nfunction processReturnValue (methodName, res, returnValue, keepReturnValue = false) {\r\n  if (isFn(protocols.returnValue)) { // 处理通用 returnValue\r\n    res = protocols.returnValue(methodName, res);\r\n  }\r\n  return processArgs(methodName, res, returnValue, {}, keepReturnValue)\r\n}\r\n\r\nfunction wrapper (methodName, method) {\r\n  if (hasOwn(protocols, methodName)) {\r\n    const protocol = protocols[methodName];\r\n    if (!protocol) { // 暂不支持的 api\r\n      return function () {\r\n        console.error(`Platform '微信小程序' does not support '${methodName}'.`);\r\n      }\r\n    }\r\n    return function (arg1, arg2) { // 目前 api 最多两个参数\r\n      let options = protocol;\r\n      if (isFn(protocol)) {\r\n        options = protocol(arg1);\r\n      }\r\n\r\n      arg1 = processArgs(methodName, arg1, options.args, options.returnValue);\r\n\r\n      const args = [arg1];\r\n      if (typeof arg2 !== 'undefined') {\r\n        args.push(arg2);\r\n      }\r\n      if (isFn(options.name)) {\r\n        methodName = options.name(arg1);\r\n      } else if (isStr(options.name)) {\r\n        methodName = options.name;\r\n      }\r\n      const returnValue = wx[methodName].apply(wx, args);\r\n      if (isSyncApi(methodName)) { // 同步 api\r\n        return processReturnValue(methodName, returnValue, options.returnValue, isContextApi(methodName))\r\n      }\r\n      return returnValue\r\n    }\r\n  }\r\n  return method\r\n}\r\n\r\nconst todoApis = Object.create(null);\r\n\r\nconst TODOS = [\r\n  'onTabBarMidButtonTap',\r\n  'subscribePush',\r\n  'unsubscribePush',\r\n  'onPush',\r\n  'offPush',\r\n  'share'\r\n];\r\n\r\nfunction createTodoApi (name) {\r\n  return function todoApi ({\r\n    fail,\r\n    complete\r\n  }) {\r\n    const res = {\r\n      errMsg: `${name}:fail method '${name}' not supported`\r\n    };\r\n    isFn(fail) && fail(res);\r\n    isFn(complete) && complete(res);\r\n  }\r\n}\r\n\r\nTODOS.forEach(function (name) {\r\n  todoApis[name] = createTodoApi(name);\r\n});\r\n\r\nvar providers = {\r\n  oauth: ['weixin'],\r\n  share: ['weixin'],\r\n  payment: ['wxpay'],\r\n  push: ['weixin']\r\n};\r\n\r\nfunction getProvider ({\r\n  service,\r\n  success,\r\n  fail,\r\n  complete\r\n}) {\r\n  let res = false;\r\n  if (providers[service]) {\r\n    res = {\r\n      errMsg: 'getProvider:ok',\r\n      service,\r\n      provider: providers[service]\r\n    };\r\n    isFn(success) && success(res);\r\n  } else {\r\n    res = {\r\n      errMsg: 'getProvider:fail service not found'\r\n    };\r\n    isFn(fail) && fail(res);\r\n  }\r\n  isFn(complete) && complete(res);\r\n}\r\n\r\nvar extraApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  getProvider: getProvider\r\n});\r\n\r\nconst getEmitter = (function () {\r\n  let Emitter;\r\n  return function getUniEmitter () {\r\n    if (!Emitter) {\r\n      Emitter = new Vue();\r\n    }\r\n    return Emitter\r\n  }\r\n})();\r\n\r\nfunction apply (ctx, method, args) {\r\n  return ctx[method].apply(ctx, args)\r\n}\r\n\r\nfunction $on () {\r\n  return apply(getEmitter(), '$on', [...arguments])\r\n}\r\nfunction $off () {\r\n  return apply(getEmitter(), '$off', [...arguments])\r\n}\r\nfunction $once () {\r\n  return apply(getEmitter(), '$once', [...arguments])\r\n}\r\nfunction $emit () {\r\n  return apply(getEmitter(), '$emit', [...arguments])\r\n}\r\n\r\nvar eventApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  $on: $on,\r\n  $off: $off,\r\n  $once: $once,\r\n  $emit: $emit\r\n});\r\n\r\n/**\r\n * 框架内 try-catch\r\n */\r\n/**\r\n * 开发者 try-catch\r\n */\r\nfunction tryCatch (fn) {\r\n  return function () {\r\n    try {\r\n      return fn.apply(fn, arguments)\r\n    } catch (e) {\r\n      // TODO\r\n      console.error(e);\r\n    }\r\n  }\r\n}\r\n\r\nfunction getApiCallbacks (params) {\r\n  const apiCallbacks = {};\r\n  for (const name in params) {\r\n    const param = params[name];\r\n    if (isFn(param)) {\r\n      apiCallbacks[name] = tryCatch(param);\r\n      delete params[name];\r\n    }\r\n  }\r\n  return apiCallbacks\r\n}\r\n\r\nlet cid;\r\nlet cidErrMsg;\r\nlet enabled;\r\n\r\nfunction normalizePushMessage (message) {\r\n  try {\r\n    return JSON.parse(message)\r\n  } catch (e) {}\r\n  return message\r\n}\r\n\r\nfunction invokePushCallback (\r\n  args\r\n) {\r\n  if (args.type === 'enabled') {\r\n    enabled = true;\r\n  } else if (args.type === 'clientId') {\r\n    cid = args.cid;\r\n    cidErrMsg = args.errMsg;\r\n    invokeGetPushCidCallbacks(cid, args.errMsg);\r\n  } else if (args.type === 'pushMsg') {\r\n    const message = {\r\n      type: 'receive',\r\n      data: normalizePushMessage(args.message)\r\n    };\r\n    for (let i = 0; i < onPushMessageCallbacks.length; i++) {\r\n      const callback = onPushMessageCallbacks[i];\r\n      callback(message);\r\n      // 该消息已被阻止\r\n      if (message.stopped) {\r\n        break\r\n      }\r\n    }\r\n  } else if (args.type === 'click') {\r\n    onPushMessageCallbacks.forEach((callback) => {\r\n      callback({\r\n        type: 'click',\r\n        data: normalizePushMessage(args.message)\r\n      });\r\n    });\r\n  }\r\n}\r\n\r\nconst getPushCidCallbacks = [];\r\n\r\nfunction invokeGetPushCidCallbacks (cid, errMsg) {\r\n  getPushCidCallbacks.forEach((callback) => {\r\n    callback(cid, errMsg);\r\n  });\r\n  getPushCidCallbacks.length = 0;\r\n}\r\n\r\nfunction getPushClientId (args) {\r\n  if (!isPlainObject(args)) {\r\n    args = {};\r\n  }\r\n  const {\r\n    success,\r\n    fail,\r\n    complete\r\n  } = getApiCallbacks(args);\r\n  const hasSuccess = isFn(success);\r\n  const hasFail = isFn(fail);\r\n  const hasComplete = isFn(complete);\r\n\r\n  Promise.resolve().then(() => {\r\n    if (typeof enabled === 'undefined') {\r\n      enabled = false;\r\n      cid = '';\r\n      cidErrMsg = 'uniPush is not enabled';\r\n    }\r\n    getPushCidCallbacks.push((cid, errMsg) => {\r\n      let res;\r\n      if (cid) {\r\n        res = {\r\n          errMsg: 'getPushClientId:ok',\r\n          cid\r\n        };\r\n        hasSuccess && success(res);\r\n      } else {\r\n        res = {\r\n          errMsg: 'getPushClientId:fail' + (errMsg ? ' ' + errMsg : '')\r\n        };\r\n        hasFail && fail(res);\r\n      }\r\n      hasComplete && complete(res);\r\n    });\r\n    if (typeof cid !== 'undefined') {\r\n      invokeGetPushCidCallbacks(cid, cidErrMsg);\r\n    }\r\n  });\r\n}\r\n\r\nconst onPushMessageCallbacks = [];\r\n// 不使用 defineOnApi 实现，是因为 defineOnApi 依赖 UniServiceJSBridge ，该对象目前在小程序上未提供，故简单实现\r\nconst onPushMessage = (fn) => {\r\n  if (onPushMessageCallbacks.indexOf(fn) === -1) {\r\n    onPushMessageCallbacks.push(fn);\r\n  }\r\n};\r\n\r\nconst offPushMessage = (fn) => {\r\n  if (!fn) {\r\n    onPushMessageCallbacks.length = 0;\r\n  } else {\r\n    const index = onPushMessageCallbacks.indexOf(fn);\r\n    if (index > -1) {\r\n      onPushMessageCallbacks.splice(index, 1);\r\n    }\r\n  }\r\n};\r\n\r\nfunction __f__ (\r\n  type,\r\n  ...args\r\n) {\r\n  console[type].apply(console, args);\r\n}\r\n\r\nlet baseInfo = wx.getAppBaseInfo && wx.getAppBaseInfo();\r\nif (!baseInfo) {\r\n  baseInfo = wx.getSystemInfoSync();\r\n}\r\nconst host = baseInfo ? baseInfo.host : null;\r\nconst shareVideoMessage =\r\n  host && host.env === 'SAAASDK' ? wx.miniapp.shareVideoMessage : wx.shareVideoMessage;\r\n\r\nvar api = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  shareVideoMessage: shareVideoMessage,\r\n  getPushClientId: getPushClientId,\r\n  onPushMessage: onPushMessage,\r\n  offPushMessage: offPushMessage,\r\n  invokePushCallback: invokePushCallback,\r\n  __f__: __f__\r\n});\r\n\r\nconst mocks = ['__route__', '__wxExparserNodeId__', '__wxWebviewId__'];\r\n\r\nfunction findVmByVueId (vm, vuePid) {\r\n  const $children = vm.$children;\r\n  // 优先查找直属(反向查找:https://github.com/dcloudio/uni-app/issues/1200)\r\n  for (let i = $children.length - 1; i >= 0; i--) {\r\n    const childVm = $children[i];\r\n    if (childVm.$scope._$vueId === vuePid) {\r\n      return childVm\r\n    }\r\n  }\r\n  // 反向递归查找\r\n  let parentVm;\r\n  for (let i = $children.length - 1; i >= 0; i--) {\r\n    parentVm = findVmByVueId($children[i], vuePid);\r\n    if (parentVm) {\r\n      return parentVm\r\n    }\r\n  }\r\n}\r\n\r\nfunction initBehavior (options) {\r\n  return Behavior(options)\r\n}\r\n\r\nfunction isPage () {\r\n  return !!this.route\r\n}\r\n\r\nfunction initRelation (detail) {\r\n  this.triggerEvent('__l', detail);\r\n}\r\n\r\nfunction selectAllComponents (mpInstance, selector, $refs) {\r\n  const components = mpInstance.selectAllComponents(selector) || [];\r\n  components.forEach(component => {\r\n    const ref = component.dataset.ref;\r\n    $refs[ref] = component.$vm || toSkip(component);\r\n    {\r\n      if (component.dataset.vueGeneric === 'scoped') {\r\n        component.selectAllComponents('.scoped-ref').forEach(scopedComponent => {\r\n          selectAllComponents(scopedComponent, selector, $refs);\r\n        });\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nfunction syncRefs (refs, newRefs) {\r\n  const oldKeys = new Set(...Object.keys(refs));\r\n  const newKeys = Object.keys(newRefs);\r\n  newKeys.forEach(key => {\r\n    const oldValue = refs[key];\r\n    const newValue = newRefs[key];\r\n    if (Array.isArray(oldValue) && Array.isArray(newValue) && oldValue.length === newValue.length && newValue.every(value => oldValue.includes(value))) {\r\n      return\r\n    }\r\n    refs[key] = newValue;\r\n    oldKeys.delete(key);\r\n  });\r\n  oldKeys.forEach(key => {\r\n    delete refs[key];\r\n  });\r\n  return refs\r\n}\r\n\r\nfunction initRefs (vm) {\r\n  const mpInstance = vm.$scope;\r\n  const refs = {};\r\n  Object.defineProperty(vm, '$refs', {\r\n    get () {\r\n      const $refs = {};\r\n      selectAllComponents(mpInstance, '.vue-ref', $refs);\r\n      // TODO 暂不考虑 for 中的 scoped\r\n      const forComponents = mpInstance.selectAllComponents('.vue-ref-in-for') || [];\r\n      forComponents.forEach(component => {\r\n        const ref = component.dataset.ref;\r\n        if (!$refs[ref]) {\r\n          $refs[ref] = [];\r\n        }\r\n        $refs[ref].push(component.$vm || toSkip(component));\r\n      });\r\n      return syncRefs(refs, $refs)\r\n    }\r\n  });\r\n}\r\n\r\nfunction handleLink (event) {\r\n  const {\r\n    vuePid,\r\n    vueOptions\r\n  } = event.detail || event.value; // detail 是微信,value 是百度(dipatch)\r\n\r\n  let parentVm;\r\n\r\n  if (vuePid) {\r\n    parentVm = findVmByVueId(this.$vm, vuePid);\r\n  }\r\n\r\n  if (!parentVm) {\r\n    parentVm = this.$vm;\r\n  }\r\n\r\n  vueOptions.parent = parentVm;\r\n}\r\n\r\nfunction markMPComponent (component) {\r\n  // 在 Vue 中标记为小程序组件\r\n  const IS_MP = '__v_isMPComponent';\r\n  Object.defineProperty(component, IS_MP, {\r\n    configurable: true,\r\n    enumerable: false,\r\n    value: true\r\n  });\r\n  return component\r\n}\r\n\r\nfunction toSkip (obj) {\r\n  const OB = '__ob__';\r\n  const SKIP = '__v_skip';\r\n  if (isObject(obj) && Object.isExtensible(obj)) {\r\n    // 避免被 @vue/composition-api 观测\r\n    Object.defineProperty(obj, OB, {\r\n      configurable: true,\r\n      enumerable: false,\r\n      value: {\r\n        [SKIP]: true\r\n      }\r\n    });\r\n  }\r\n  return obj\r\n}\r\n\r\nconst WORKLET_RE = /_(.*)_worklet_factory_/;\r\nfunction initWorkletMethods (mpMethods, vueMethods) {\r\n  if (vueMethods) {\r\n    Object.keys(vueMethods).forEach((name) => {\r\n      const matches = name.match(WORKLET_RE);\r\n      if (matches) {\r\n        const workletName = matches[1];\r\n        mpMethods[name] = vueMethods[name];\r\n        mpMethods[workletName] = vueMethods[workletName];\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nconst MPPage = Page;\r\nconst MPComponent = Component;\r\n\r\nconst customizeRE = /:/g;\r\n\r\nconst customize = cached((str) => {\r\n  return camelize(str.replace(customizeRE, '-'))\r\n});\r\n\r\nfunction initTriggerEvent (mpInstance) {\r\n  const oldTriggerEvent = mpInstance.triggerEvent;\r\n  const newTriggerEvent = function (event, ...args) {\r\n    // 事件名统一转驼峰格式，仅处理：当前组件为 vue 组件、当前组件为 vue 组件子组件\r\n    if (this.$vm || (this.dataset && this.dataset.comType)) {\r\n      event = customize(event);\r\n    } else {\r\n      // 针对微信/QQ小程序单独补充驼峰格式事件，以兼容历史项目\r\n      const newEvent = customize(event);\r\n      if (newEvent !== event) {\r\n        oldTriggerEvent.apply(this, [newEvent, ...args]);\r\n      }\r\n    }\r\n    return oldTriggerEvent.apply(this, [event, ...args])\r\n  };\r\n  try {\r\n    // 京东小程序 triggerEvent 为只读\r\n    mpInstance.triggerEvent = newTriggerEvent;\r\n  } catch (error) {\r\n    mpInstance._triggerEvent = newTriggerEvent;\r\n  }\r\n}\r\n\r\nfunction initHook (name, options, isComponent) {\r\n  const oldHook = options[name];\r\n  options[name] = function (...args) {\r\n    markMPComponent(this);\r\n    initTriggerEvent(this);\r\n    if (oldHook) {\r\n      return oldHook.apply(this, args)\r\n    }\r\n  };\r\n}\r\nif (!MPPage.__$wrappered) {\r\n  MPPage.__$wrappered = true;\r\n  Page = function (options = {}) {\r\n    initHook('onLoad', options);\r\n    return MPPage(options)\r\n  };\r\n  Page.after = MPPage.after;\r\n\r\n  Component = function (options = {}) {\r\n    initHook('created', options);\r\n    return MPComponent(options)\r\n  };\r\n}\r\n\r\nconst PAGE_EVENT_HOOKS = [\r\n  'onPullDownRefresh',\r\n  'onReachBottom',\r\n  'onAddToFavorites',\r\n  'onShareTimeline',\r\n  'onShareAppMessage',\r\n  'onPageScroll',\r\n  'onResize',\r\n  'onTabItemTap'\r\n];\r\n\r\nfunction initMocks (vm, mocks) {\r\n  const mpInstance = vm.$mp[vm.mpType];\r\n  mocks.forEach(mock => {\r\n    if (hasOwn(mpInstance, mock)) {\r\n      vm[mock] = mpInstance[mock];\r\n    }\r\n  });\r\n}\r\n\r\nfunction hasHook (hook, vueOptions) {\r\n  if (!vueOptions) {\r\n    return true\r\n  }\r\n\r\n  if (Vue.options && Array.isArray(Vue.options[hook])) {\r\n    return true\r\n  }\r\n\r\n  vueOptions = vueOptions.default || vueOptions;\r\n\r\n  if (isFn(vueOptions)) {\r\n    if (isFn(vueOptions.extendOptions[hook])) {\r\n      return true\r\n    }\r\n    if (vueOptions.super &&\r\n      vueOptions.super.options &&\r\n      Array.isArray(vueOptions.super.options[hook])) {\r\n      return true\r\n    }\r\n    return false\r\n  }\r\n\r\n  if (isFn(vueOptions[hook]) || Array.isArray(vueOptions[hook])) {\r\n    return true\r\n  }\r\n  const mixins = vueOptions.mixins;\r\n  if (Array.isArray(mixins)) {\r\n    return !!mixins.find(mixin => hasHook(hook, mixin))\r\n  }\r\n}\r\n\r\nfunction initHooks (mpOptions, hooks, vueOptions) {\r\n  hooks.forEach(hook => {\r\n    if (hasHook(hook, vueOptions)) {\r\n      mpOptions[hook] = function (args) {\r\n        return this.$vm && this.$vm.__call_hook(hook, args)\r\n      };\r\n    }\r\n  });\r\n}\r\n\r\nfunction initUnknownHooks (mpOptions, vueOptions, excludes = []) {\r\n  findHooks(vueOptions).forEach((hook) => initHook$1(mpOptions, hook, excludes));\r\n}\r\n\r\nfunction findHooks (vueOptions, hooks = []) {\r\n  if (vueOptions) {\r\n    Object.keys(vueOptions).forEach((name) => {\r\n      if (name.indexOf('on') === 0 && isFn(vueOptions[name])) {\r\n        hooks.push(name);\r\n      }\r\n    });\r\n  }\r\n  return hooks\r\n}\r\n\r\nfunction initHook$1 (mpOptions, hook, excludes) {\r\n  if (excludes.indexOf(hook) === -1 && !hasOwn(mpOptions, hook)) {\r\n    mpOptions[hook] = function (args) {\r\n      return this.$vm && this.$vm.__call_hook(hook, args)\r\n    };\r\n  }\r\n}\r\n\r\nfunction initVueComponent (Vue, vueOptions) {\r\n  vueOptions = vueOptions.default || vueOptions;\r\n  let VueComponent;\r\n  if (isFn(vueOptions)) {\r\n    VueComponent = vueOptions;\r\n  } else {\r\n    VueComponent = Vue.extend(vueOptions);\r\n  }\r\n  vueOptions = VueComponent.options;\r\n  return [VueComponent, vueOptions]\r\n}\r\n\r\nfunction initSlots (vm, vueSlots) {\r\n  if (Array.isArray(vueSlots) && vueSlots.length) {\r\n    const $slots = Object.create(null);\r\n    vueSlots.forEach(slotName => {\r\n      $slots[slotName] = true;\r\n    });\r\n    vm.$scopedSlots = vm.$slots = $slots;\r\n  }\r\n}\r\n\r\nfunction initVueIds (vueIds, mpInstance) {\r\n  vueIds = (vueIds || '').split(',');\r\n  const len = vueIds.length;\r\n\r\n  if (len === 1) {\r\n    mpInstance._$vueId = vueIds[0];\r\n  } else if (len === 2) {\r\n    mpInstance._$vueId = vueIds[0];\r\n    mpInstance._$vuePid = vueIds[1];\r\n  }\r\n}\r\n\r\nfunction initData (vueOptions, context) {\r\n  let data = vueOptions.data || {};\r\n  const methods = vueOptions.methods || {};\r\n\r\n  if (typeof data === 'function') {\r\n    try {\r\n      data = data.call(context); // 支持 Vue.prototype 上挂的数据\r\n    } catch (e) {\r\n      if (process.env.VUE_APP_DEBUG) {\r\n        console.warn('根据 Vue 的 data 函数初始化小程序 data 失败，请尽量确保 data 函数中不访问 vm 对象，否则可能影响首次数据渲染速度。', data);\r\n      }\r\n    }\r\n  } else {\r\n    try {\r\n      // 对 data 格式化\r\n      data = JSON.parse(JSON.stringify(data));\r\n    } catch (e) { }\r\n  }\r\n\r\n  if (!isPlainObject(data)) {\r\n    data = {};\r\n  }\r\n\r\n  Object.keys(methods).forEach(methodName => {\r\n    if (context.__lifecycle_hooks__.indexOf(methodName) === -1 && !hasOwn(data, methodName)) {\r\n      data[methodName] = methods[methodName];\r\n    }\r\n  });\r\n\r\n  return data\r\n}\r\n\r\nconst PROP_TYPES = [String, Number, Boolean, Object, Array, null];\r\n\r\nfunction createObserver (name) {\r\n  return function observer (newVal, oldVal) {\r\n    if (this.$vm) {\r\n      this.$vm[name] = newVal; // 为了触发其他非 render watcher\r\n    }\r\n  }\r\n}\r\n\r\nfunction initBehaviors (vueOptions, initBehavior) {\r\n  const vueBehaviors = vueOptions.behaviors;\r\n  const vueExtends = vueOptions.extends;\r\n  const vueMixins = vueOptions.mixins;\r\n\r\n  let vueProps = vueOptions.props;\r\n\r\n  if (!vueProps) {\r\n    vueOptions.props = vueProps = [];\r\n  }\r\n\r\n  const behaviors = [];\r\n  if (Array.isArray(vueBehaviors)) {\r\n    vueBehaviors.forEach(behavior => {\r\n      behaviors.push(behavior.replace('uni://', `${\"wx\"}://`));\r\n      if (behavior === 'uni://form-field') {\r\n        if (Array.isArray(vueProps)) {\r\n          vueProps.push('name');\r\n          vueProps.push('value');\r\n        } else {\r\n          vueProps.name = {\r\n            type: String,\r\n            default: ''\r\n          };\r\n          vueProps.value = {\r\n            type: [String, Number, Boolean, Array, Object, Date],\r\n            default: ''\r\n          };\r\n        }\r\n      }\r\n    });\r\n  }\r\n  if (isPlainObject(vueExtends) && vueExtends.props) {\r\n    behaviors.push(\r\n      initBehavior({\r\n        properties: initProperties(vueExtends.props, true)\r\n      })\r\n    );\r\n  }\r\n  if (Array.isArray(vueMixins)) {\r\n    vueMixins.forEach(vueMixin => {\r\n      if (isPlainObject(vueMixin) && vueMixin.props) {\r\n        behaviors.push(\r\n          initBehavior({\r\n            properties: initProperties(vueMixin.props, true)\r\n          })\r\n        );\r\n      }\r\n    });\r\n  }\r\n  return behaviors\r\n}\r\n\r\nfunction parsePropType (key, type, defaultValue, file) {\r\n  // [String]=>String\r\n  if (Array.isArray(type) && type.length === 1) {\r\n    return type[0]\r\n  }\r\n  return type\r\n}\r\n\r\nfunction initProperties (props, isBehavior = false, file = '', options) {\r\n  const properties = {};\r\n  if (!isBehavior) {\r\n    properties.vueId = {\r\n      type: String,\r\n      value: ''\r\n    };\r\n    {\r\n      if ( options.virtualHost) {\r\n        properties.virtualHostStyle = {\r\n          type: null,\r\n          value: ''\r\n        };\r\n        properties.virtualHostClass = {\r\n          type: null,\r\n          value: ''\r\n        };\r\n      }\r\n    }\r\n    // scopedSlotsCompiler auto\r\n    properties.scopedSlotsCompiler = {\r\n      type: String,\r\n      value: ''\r\n    };\r\n    properties.vueSlots = { // 小程序不能直接定义 $slots 的 props，所以通过 vueSlots 转换到 $slots\r\n      type: null,\r\n      value: [],\r\n      observer: function (newVal, oldVal) {\r\n        const $slots = Object.create(null);\r\n        newVal.forEach(slotName => {\r\n          $slots[slotName] = true;\r\n        });\r\n        this.setData({\r\n          $slots\r\n        });\r\n      }\r\n    };\r\n  }\r\n  if (Array.isArray(props)) { // ['title']\r\n    props.forEach(key => {\r\n      properties[key] = {\r\n        type: null,\r\n        observer: createObserver(key)\r\n      };\r\n    });\r\n  } else if (isPlainObject(props)) { // {title:{type:String,default:''},content:String}\r\n    Object.keys(props).forEach(key => {\r\n      const opts = props[key];\r\n      if (isPlainObject(opts)) { // title:{type:String,default:''}\r\n        let value = opts.default;\r\n        if (isFn(value)) {\r\n          value = value();\r\n        }\r\n\r\n        opts.type = parsePropType(key, opts.type);\r\n\r\n        properties[key] = {\r\n          type: PROP_TYPES.indexOf(opts.type) !== -1 ? opts.type : null,\r\n          value,\r\n          observer: createObserver(key)\r\n        };\r\n      } else { // content:String\r\n        const type = parsePropType(key, opts);\r\n        properties[key] = {\r\n          type: PROP_TYPES.indexOf(type) !== -1 ? type : null,\r\n          observer: createObserver(key)\r\n        };\r\n      }\r\n    });\r\n  }\r\n  return properties\r\n}\r\n\r\nfunction wrapper$1 (event) {\r\n  // TODO 又得兼容 mpvue 的 mp 对象\r\n  try {\r\n    event.mp = JSON.parse(JSON.stringify(event));\r\n  } catch (e) { }\r\n\r\n  event.stopPropagation = noop;\r\n  event.preventDefault = noop;\r\n\r\n  event.target = event.target || {};\r\n\r\n  if (!hasOwn(event, 'detail')) {\r\n    event.detail = {};\r\n  }\r\n\r\n  if (hasOwn(event, 'markerId')) {\r\n    event.detail = typeof event.detail === 'object' ? event.detail : {};\r\n    event.detail.markerId = event.markerId;\r\n  }\r\n\r\n  if (isPlainObject(event.detail)) {\r\n    event.target = Object.assign({}, event.target, event.detail);\r\n  }\r\n\r\n  return event\r\n}\r\n\r\nfunction getExtraValue (vm, dataPathsArray) {\r\n  let context = vm;\r\n  dataPathsArray.forEach(dataPathArray => {\r\n    const dataPath = dataPathArray[0];\r\n    const value = dataPathArray[2];\r\n    if (dataPath || typeof value !== 'undefined') { // ['','',index,'disable']\r\n      const propPath = dataPathArray[1];\r\n      const valuePath = dataPathArray[3];\r\n\r\n      let vFor;\r\n      if (Number.isInteger(dataPath)) {\r\n        vFor = dataPath;\r\n      } else if (!dataPath) {\r\n        vFor = context;\r\n      } else if (typeof dataPath === 'string' && dataPath) {\r\n        if (dataPath.indexOf('#s#') === 0) {\r\n          vFor = dataPath.substr(3);\r\n        } else {\r\n          vFor = vm.__get_value(dataPath, context);\r\n        }\r\n      }\r\n\r\n      if (Number.isInteger(vFor)) {\r\n        context = value;\r\n      } else if (!propPath) {\r\n        context = vFor[value];\r\n      } else {\r\n        if (Array.isArray(vFor)) {\r\n          context = vFor.find(vForItem => {\r\n            return vm.__get_value(propPath, vForItem) === value\r\n          });\r\n        } else if (isPlainObject(vFor)) {\r\n          context = Object.keys(vFor).find(vForKey => {\r\n            return vm.__get_value(propPath, vFor[vForKey]) === value\r\n          });\r\n        } else {\r\n          console.error('v-for 暂不支持循环数据：', vFor);\r\n        }\r\n      }\r\n\r\n      if (valuePath) {\r\n        context = vm.__get_value(valuePath, context);\r\n      }\r\n    }\r\n  });\r\n  return context\r\n}\r\n\r\nfunction processEventExtra (vm, extra, event, __args__) {\r\n  const extraObj = {};\r\n\r\n  if (Array.isArray(extra) && extra.length) {\r\n    /**\r\n     *[\r\n     *    ['data.items', 'data.id', item.data.id],\r\n     *    ['metas', 'id', meta.id]\r\n     *],\r\n     *[\r\n     *    ['data.items', 'data.id', item.data.id],\r\n     *    ['metas', 'id', meta.id]\r\n     *],\r\n     *'test'\r\n     */\r\n    extra.forEach((dataPath, index) => {\r\n      if (typeof dataPath === 'string') {\r\n        if (!dataPath) { // model,prop.sync\r\n          extraObj['$' + index] = vm;\r\n        } else {\r\n          if (dataPath === '$event') { // $event\r\n            extraObj['$' + index] = event;\r\n          } else if (dataPath === 'arguments') {\r\n            extraObj['$' + index] = event.detail ? event.detail.__args__ || __args__ : __args__;\r\n          } else if (dataPath.indexOf('$event.') === 0) { // $event.target.value\r\n            extraObj['$' + index] = vm.__get_value(dataPath.replace('$event.', ''), event);\r\n          } else {\r\n            extraObj['$' + index] = vm.__get_value(dataPath);\r\n          }\r\n        }\r\n      } else {\r\n        extraObj['$' + index] = getExtraValue(vm, dataPath);\r\n      }\r\n    });\r\n  }\r\n\r\n  return extraObj\r\n}\r\n\r\nfunction getObjByArray (arr) {\r\n  const obj = {};\r\n  for (let i = 1; i < arr.length; i++) {\r\n    const element = arr[i];\r\n    obj[element[0]] = element[1];\r\n  }\r\n  return obj\r\n}\r\n\r\nfunction processEventArgs (vm, event, args = [], extra = [], isCustom, methodName) {\r\n  let isCustomMPEvent = false; // wxcomponent 组件，传递原始 event 对象\r\n\r\n  // fixed 用户直接触发 mpInstance.triggerEvent\r\n  const __args__ = isPlainObject(event.detail)\r\n    ? event.detail.__args__ || [event.detail]\r\n    : [event.detail];\r\n\r\n  if (isCustom) { // 自定义事件\r\n    isCustomMPEvent = event.currentTarget &&\r\n      event.currentTarget.dataset &&\r\n      event.currentTarget.dataset.comType === 'wx';\r\n    if (!args.length) { // 无参数，直接传入 event 或 detail 数组\r\n      if (isCustomMPEvent) {\r\n        return [event]\r\n      }\r\n      return __args__\r\n    }\r\n  }\r\n\r\n  const extraObj = processEventExtra(vm, extra, event, __args__);\r\n\r\n  const ret = [];\r\n  args.forEach(arg => {\r\n    if (arg === '$event') {\r\n      if (methodName === '__set_model' && !isCustom) { // input v-model value\r\n        ret.push(event.target.value);\r\n      } else {\r\n        if (isCustom && !isCustomMPEvent) {\r\n          ret.push(__args__[0]);\r\n        } else { // wxcomponent 组件或内置组件\r\n          ret.push(event);\r\n        }\r\n      }\r\n    } else {\r\n      if (Array.isArray(arg) && arg[0] === 'o') {\r\n        ret.push(getObjByArray(arg));\r\n      } else if (typeof arg === 'string' && hasOwn(extraObj, arg)) {\r\n        ret.push(extraObj[arg]);\r\n      } else {\r\n        ret.push(arg);\r\n      }\r\n    }\r\n  });\r\n\r\n  return ret\r\n}\r\n\r\nconst ONCE = '~';\r\nconst CUSTOM = '^';\r\n\r\nfunction isMatchEventType (eventType, optType) {\r\n  return (eventType === optType) ||\r\n    (\r\n      optType === 'regionchange' &&\r\n      (\r\n        eventType === 'begin' ||\r\n        eventType === 'end'\r\n      )\r\n    )\r\n}\r\n\r\nfunction getContextVm (vm) {\r\n  let $parent = vm.$parent;\r\n  // 父组件是 scoped slots 或者其他自定义组件时继续查找\r\n  while ($parent && $parent.$parent && ($parent.$options.generic || $parent.$parent.$options.generic || $parent.$scope._$vuePid)) {\r\n    $parent = $parent.$parent;\r\n  }\r\n  return $parent && $parent.$parent\r\n}\r\n\r\nfunction handleEvent (event) {\r\n  event = wrapper$1(event);\r\n\r\n  // [['tap',[['handle',[1,2,a]],['handle1',[1,2,a]]]]]\r\n  const dataset = (event.currentTarget || event.target).dataset;\r\n  if (!dataset) {\r\n    return console.warn('事件信息不存在')\r\n  }\r\n  const eventOpts = dataset.eventOpts || dataset['event-opts']; // 支付宝 web-view 组件 dataset 非驼峰\r\n  if (!eventOpts) {\r\n    return console.warn('事件信息不存在')\r\n  }\r\n\r\n  // [['handle',[1,2,a]],['handle1',[1,2,a]]]\r\n  const eventType = event.type;\r\n\r\n  const ret = [];\r\n\r\n  eventOpts.forEach(eventOpt => {\r\n    let type = eventOpt[0];\r\n    const eventsArray = eventOpt[1];\r\n\r\n    const isCustom = type.charAt(0) === CUSTOM;\r\n    type = isCustom ? type.slice(1) : type;\r\n    const isOnce = type.charAt(0) === ONCE;\r\n    type = isOnce ? type.slice(1) : type;\r\n\r\n    if (eventsArray && isMatchEventType(eventType, type)) {\r\n      eventsArray.forEach(eventArray => {\r\n        const methodName = eventArray[0];\r\n        if (methodName) {\r\n          let handlerCtx = this.$vm;\r\n          if (handlerCtx.$options.generic) { // mp-weixin,mp-toutiao 抽象节点模拟 scoped slots\r\n            handlerCtx = getContextVm(handlerCtx) || handlerCtx;\r\n          }\r\n          if (methodName === '$emit') {\r\n            handlerCtx.$emit.apply(handlerCtx,\r\n              processEventArgs(\r\n                this.$vm,\r\n                event,\r\n                eventArray[1],\r\n                eventArray[2],\r\n                isCustom,\r\n                methodName\r\n              ));\r\n            return\r\n          }\r\n          const handler = handlerCtx[methodName];\r\n          if (!isFn(handler)) {\r\n            const type = this.$vm.mpType === 'page' ? 'Page' : 'Component';\r\n            const path = this.route || this.is;\r\n            throw new Error(`${type} \"${path}\" does not have a method \"${methodName}\"`)\r\n          }\r\n          if (isOnce) {\r\n            if (handler.once) {\r\n              return\r\n            }\r\n            handler.once = true;\r\n          }\r\n          let params = processEventArgs(\r\n            this.$vm,\r\n            event,\r\n            eventArray[1],\r\n            eventArray[2],\r\n            isCustom,\r\n            methodName\r\n          );\r\n          params = Array.isArray(params) ? params : [];\r\n          // 参数尾部增加原始事件对象用于复杂表达式内获取额外数据\r\n          if (/=\\s*\\S+\\.eventParams\\s*\\|\\|\\s*\\S+\\[['\"]event-params['\"]\\]/.test(handler.toString())) {\r\n            // eslint-disable-next-line no-sparse-arrays\r\n            params = params.concat([, , , , , , , , , , event]);\r\n          }\r\n          ret.push(handler.apply(handlerCtx, params));\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  if (\r\n    eventType === 'input' &&\r\n    ret.length === 1 &&\r\n    typeof ret[0] !== 'undefined'\r\n  ) {\r\n    return ret[0]\r\n  }\r\n}\r\n\r\nconst eventChannels = {};\r\n\r\nfunction getEventChannel (id) {\r\n  const eventChannel = eventChannels[id];\r\n  delete eventChannels[id];\r\n  return eventChannel\r\n}\r\n\r\nconst hooks = [\r\n  'onShow',\r\n  'onHide',\r\n  'onError',\r\n  'onPageNotFound',\r\n  'onThemeChange',\r\n  'onUnhandledRejection'\r\n];\r\n\r\nfunction initEventChannel () {\r\n  Vue.prototype.getOpenerEventChannel = function () {\r\n    // 微信小程序使用自身getOpenerEventChannel\r\n    {\r\n      return this.$scope.getOpenerEventChannel()\r\n    }\r\n  };\r\n  const callHook = Vue.prototype.__call_hook;\r\n  Vue.prototype.__call_hook = function (hook, args) {\r\n    if (hook === 'onLoad' && args && args.__id__) {\r\n      this.__eventChannel__ = getEventChannel(args.__id__);\r\n      delete args.__id__;\r\n    }\r\n    return callHook.call(this, hook, args)\r\n  };\r\n}\r\n\r\nfunction initScopedSlotsParams () {\r\n  const center = {};\r\n  const parents = {};\r\n\r\n  function currentId (fn) {\r\n    const vueIds = this.$options.propsData.vueId;\r\n    if (vueIds) {\r\n      const vueId = vueIds.split(',')[0];\r\n      fn(vueId);\r\n    }\r\n  }\r\n\r\n  Vue.prototype.$hasSSP = function (vueId) {\r\n    const slot = center[vueId];\r\n    if (!slot) {\r\n      parents[vueId] = this;\r\n      this.$on('hook:destroyed', () => {\r\n        delete parents[vueId];\r\n      });\r\n    }\r\n    return slot\r\n  };\r\n\r\n  Vue.prototype.$getSSP = function (vueId, name, needAll) {\r\n    const slot = center[vueId];\r\n    if (slot) {\r\n      const params = slot[name] || [];\r\n      if (needAll) {\r\n        return params\r\n      }\r\n      return params[0]\r\n    }\r\n  };\r\n\r\n  Vue.prototype.$setSSP = function (name, value) {\r\n    let index = 0;\r\n    currentId.call(this, vueId => {\r\n      const slot = center[vueId];\r\n      const params = slot[name] = slot[name] || [];\r\n      params.push(value);\r\n      index = params.length - 1;\r\n    });\r\n    return index\r\n  };\r\n\r\n  Vue.prototype.$initSSP = function () {\r\n    currentId.call(this, vueId => {\r\n      center[vueId] = {};\r\n    });\r\n  };\r\n\r\n  Vue.prototype.$callSSP = function () {\r\n    currentId.call(this, vueId => {\r\n      if (parents[vueId]) {\r\n        parents[vueId].$forceUpdate();\r\n      }\r\n    });\r\n  };\r\n\r\n  Vue.mixin({\r\n    destroyed () {\r\n      const propsData = this.$options.propsData;\r\n      const vueId = propsData && propsData.vueId;\r\n      if (vueId) {\r\n        delete center[vueId];\r\n        delete parents[vueId];\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nfunction parseBaseApp (vm, {\r\n  mocks,\r\n  initRefs\r\n}) {\r\n  initEventChannel();\r\n  {\r\n    initScopedSlotsParams();\r\n  }\r\n  if (vm.$options.store) {\r\n    Vue.prototype.$store = vm.$options.store;\r\n  }\r\n  uniIdMixin(Vue);\r\n\r\n  Vue.prototype.mpHost = \"mp-weixin\";\r\n\r\n  Vue.mixin({\r\n    beforeCreate () {\r\n      if (!this.$options.mpType) {\r\n        return\r\n      }\r\n\r\n      this.mpType = this.$options.mpType;\r\n\r\n      this.$mp = {\r\n        data: {},\r\n        [this.mpType]: this.$options.mpInstance\r\n      };\r\n\r\n      this.$scope = this.$options.mpInstance;\r\n\r\n      delete this.$options.mpType;\r\n      delete this.$options.mpInstance;\r\n      if (\r\n        ( this.mpType === 'page') &&\r\n        typeof getApp === 'function'\r\n      ) { // hack vue-i18n\r\n        const app = getApp();\r\n        if (app.$vm && app.$vm.$i18n) {\r\n          this._i18n = app.$vm.$i18n;\r\n        }\r\n      }\r\n      if (this.mpType !== 'app') {\r\n        initRefs(this);\r\n        initMocks(this, mocks);\r\n      }\r\n    }\r\n  });\r\n\r\n  const appOptions = {\r\n    onLaunch (args) {\r\n      if (this.$vm) { // 已经初始化过了，主要是为了百度，百度 onShow 在 onLaunch 之前\r\n        return\r\n      }\r\n      {\r\n        if (wx.canIUse && !wx.canIUse('nextTick')) { // 事实 上2.2.3 即可，简单使用 2.3.0 的 nextTick 判断\r\n          console.error('当前微信基础库版本过低，请将 微信开发者工具-详情-项目设置-调试基础库版本 更换为`2.3.0`以上');\r\n        }\r\n      }\r\n\r\n      this.$vm = vm;\r\n\r\n      this.$vm.$mp = {\r\n        app: this\r\n      };\r\n\r\n      this.$vm.$scope = this;\r\n      // vm 上也挂载 globalData\r\n      this.$vm.globalData = this.globalData;\r\n\r\n      this.$vm._isMounted = true;\r\n      this.$vm.__call_hook('mounted', args);\r\n\r\n      this.$vm.__call_hook('onLaunch', args);\r\n    }\r\n  };\r\n\r\n  // 兼容旧版本 globalData\r\n  appOptions.globalData = vm.$options.globalData || {};\r\n  // 将 methods 中的方法挂在 getApp() 中\r\n  const methods = vm.$options.methods;\r\n  if (methods) {\r\n    Object.keys(methods).forEach(name => {\r\n      appOptions[name] = methods[name];\r\n    });\r\n  }\r\n\r\n  initAppLocale(Vue, vm, getLocaleLanguage$1());\r\n\r\n  initHooks(appOptions, hooks);\r\n  initUnknownHooks(appOptions, vm.$options);\r\n\r\n  return appOptions\r\n}\r\n\r\nfunction getLocaleLanguage$1 () {\r\n  let localeLanguage = '';\r\n  {\r\n    const appBaseInfo = wx.getAppBaseInfo();\r\n    const language =\r\n      appBaseInfo && appBaseInfo.language ? appBaseInfo.language : LOCALE_EN;\r\n    localeLanguage = normalizeLocale(language) || LOCALE_EN;\r\n  }\r\n  return localeLanguage\r\n}\r\n\r\nfunction parseApp (vm) {\r\n  return parseBaseApp(vm, {\r\n    mocks,\r\n    initRefs\r\n  })\r\n}\r\n\r\nfunction createApp (vm) {\r\n  App(parseApp(vm));\r\n  return vm\r\n}\r\n\r\nconst encodeReserveRE = /[!'()*]/g;\r\nconst encodeReserveReplacer = c => '%' + c.charCodeAt(0).toString(16);\r\nconst commaRE = /%2C/g;\r\n\r\n// fixed encodeURIComponent which is more conformant to RFC3986:\r\n// - escapes [!'()*]\r\n// - preserve commas\r\nconst encode = str => encodeURIComponent(str)\r\n  .replace(encodeReserveRE, encodeReserveReplacer)\r\n  .replace(commaRE, ',');\r\n\r\nfunction stringifyQuery (obj, encodeStr = encode) {\r\n  const res = obj ? Object.keys(obj).map(key => {\r\n    const val = obj[key];\r\n\r\n    if (val === undefined) {\r\n      return ''\r\n    }\r\n\r\n    if (val === null) {\r\n      return encodeStr(key)\r\n    }\r\n\r\n    if (Array.isArray(val)) {\r\n      const result = [];\r\n      val.forEach(val2 => {\r\n        if (val2 === undefined) {\r\n          return\r\n        }\r\n        if (val2 === null) {\r\n          result.push(encodeStr(key));\r\n        } else {\r\n          result.push(encodeStr(key) + '=' + encodeStr(val2));\r\n        }\r\n      });\r\n      return result.join('&')\r\n    }\r\n\r\n    return encodeStr(key) + '=' + encodeStr(val)\r\n  }).filter(x => x.length > 0).join('&') : null;\r\n  return res ? `?${res}` : ''\r\n}\r\n\r\nfunction parseBaseComponent (vueComponentOptions, {\r\n  isPage,\r\n  initRelation\r\n} = {}, needVueOptions) {\r\n  const [VueComponent, vueOptions] = initVueComponent(Vue, vueComponentOptions);\r\n\r\n  const options = {\r\n    multipleSlots: true,\r\n    // styleIsolation: 'apply-shared',\r\n    addGlobalClass: true,\r\n    ...(vueOptions.options || {})\r\n  };\r\n\r\n  {\r\n    // 微信 multipleSlots 部分情况有 bug，导致内容顺序错乱 如 u-list，提供覆盖选项\r\n    if (vueOptions['mp-weixin'] && vueOptions['mp-weixin'].options) {\r\n      Object.assign(options, vueOptions['mp-weixin'].options);\r\n    }\r\n  }\r\n\r\n  const componentOptions = {\r\n    options,\r\n    data: initData(vueOptions, Vue.prototype),\r\n    behaviors: initBehaviors(vueOptions, initBehavior),\r\n    properties: initProperties(vueOptions.props, false, vueOptions.__file, options),\r\n    lifetimes: {\r\n      attached () {\r\n        const properties = this.properties;\r\n\r\n        const options = {\r\n          mpType: isPage.call(this) ? 'page' : 'component',\r\n          mpInstance: this,\r\n          propsData: properties\r\n        };\r\n\r\n        initVueIds(properties.vueId, this);\r\n\r\n        // 处理父子关系\r\n        initRelation.call(this, {\r\n          vuePid: this._$vuePid,\r\n          vueOptions: options\r\n        });\r\n\r\n        // 初始化 vue 实例\r\n        this.$vm = new VueComponent(options);\r\n\r\n        // 处理$slots,$scopedSlots（暂不支持动态变化$slots）\r\n        initSlots(this.$vm, properties.vueSlots);\r\n\r\n        // 触发首次 setData\r\n        this.$vm.$mount();\r\n      },\r\n      ready () {\r\n        // 当组件 props 默认值为 true，初始化时传入 false 会导致 created,ready 触发, 但 attached 不触发\r\n        // https://developers.weixin.qq.com/community/develop/doc/00066ae2844cc0f8eb883e2a557800\r\n        if (this.$vm) {\r\n          this.$vm._isMounted = true;\r\n          this.$vm.__call_hook('mounted');\r\n          this.$vm.__call_hook('onReady');\r\n        }\r\n      },\r\n      detached () {\r\n        this.$vm && this.$vm.$destroy();\r\n      }\r\n    },\r\n    pageLifetimes: {\r\n      show (args) {\r\n        this.$vm && this.$vm.__call_hook('onPageShow', args);\r\n      },\r\n      hide () {\r\n        this.$vm && this.$vm.__call_hook('onPageHide');\r\n      },\r\n      resize (size) {\r\n        this.$vm && this.$vm.__call_hook('onPageResize', size);\r\n      }\r\n    },\r\n    methods: {\r\n      __l: handleLink,\r\n      __e: handleEvent\r\n    }\r\n  };\r\n  // externalClasses\r\n  if (vueOptions.externalClasses) {\r\n    componentOptions.externalClasses = vueOptions.externalClasses;\r\n  }\r\n\r\n  if (Array.isArray(vueOptions.wxsCallMethods)) {\r\n    vueOptions.wxsCallMethods.forEach(callMethod => {\r\n      componentOptions.methods[callMethod] = function (args) {\r\n        return this.$vm[callMethod](args)\r\n      };\r\n    });\r\n  }\r\n\r\n  if (needVueOptions) {\r\n    return [componentOptions, vueOptions, VueComponent]\r\n  }\r\n  if (isPage) {\r\n    return componentOptions\r\n  }\r\n  return [componentOptions, VueComponent]\r\n}\r\n\r\nfunction parseComponent (vueComponentOptions, needVueOptions) {\r\n  return parseBaseComponent(vueComponentOptions, {\r\n    isPage,\r\n    initRelation\r\n  }, needVueOptions)\r\n}\r\n\r\nconst hooks$1 = [\r\n  'onShow',\r\n  'onHide',\r\n  'onUnload'\r\n];\r\n\r\nhooks$1.push(...PAGE_EVENT_HOOKS);\r\n\r\nfunction parseBasePage (vuePageOptions) {\r\n  const [pageOptions, vueOptions] = parseComponent(vuePageOptions, true);\r\n\r\n  initHooks(pageOptions.methods, hooks$1, vueOptions);\r\n\r\n  pageOptions.methods.onLoad = function (query) {\r\n    this.options = query;\r\n    const copyQuery = Object.assign({}, query);\r\n    delete copyQuery.__id__;\r\n    this.$page = {\r\n      fullPath: '/' + (this.route || this.is) + stringifyQuery(copyQuery)\r\n    };\r\n    this.$vm.$mp.query = query; // 兼容 mpvue\r\n    this.$vm.__call_hook('onLoad', query);\r\n  };\r\n  {\r\n    initUnknownHooks(pageOptions.methods, vuePageOptions, ['onReady']);\r\n  }\r\n  {\r\n    initWorkletMethods(pageOptions.methods, vueOptions.methods);\r\n  }\r\n\r\n  return pageOptions\r\n}\r\n\r\nfunction parsePage (vuePageOptions) {\r\n  return parseBasePage(vuePageOptions)\r\n}\r\n\r\nfunction createPage (vuePageOptions) {\r\n  {\r\n    return Component(parsePage(vuePageOptions))\r\n  }\r\n}\r\n\r\nfunction createComponent (vueOptions) {\r\n  {\r\n    return Component(parseComponent(vueOptions))\r\n  }\r\n}\r\n\r\nfunction createSubpackageApp (vm) {\r\n  const appOptions = parseApp(vm);\r\n  const app = getApp({\r\n    allowDefault: true\r\n  });\r\n  vm.$scope = app;\r\n  const globalData = app.globalData;\r\n  if (globalData) {\r\n    Object.keys(appOptions.globalData).forEach(name => {\r\n      if (!hasOwn(globalData, name)) {\r\n        globalData[name] = appOptions.globalData[name];\r\n      }\r\n    });\r\n  }\r\n  Object.keys(appOptions).forEach(name => {\r\n    if (!hasOwn(app, name)) {\r\n      app[name] = appOptions[name];\r\n    }\r\n  });\r\n  if (isFn(appOptions.onShow) && wx.onAppShow) {\r\n    wx.onAppShow((...args) => {\r\n      vm.__call_hook('onShow', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onHide) && wx.onAppHide) {\r\n    wx.onAppHide((...args) => {\r\n      vm.__call_hook('onHide', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onLaunch)) {\r\n    const args = wx.getLaunchOptionsSync && wx.getLaunchOptionsSync();\r\n    vm.__call_hook('onLaunch', args);\r\n  }\r\n  return vm\r\n}\r\n\r\nfunction createPlugin (vm) {\r\n  const appOptions = parseApp(vm);\r\n  if (isFn(appOptions.onShow) && wx.onAppShow) {\r\n    wx.onAppShow((...args) => {\r\n      vm.__call_hook('onShow', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onHide) && wx.onAppHide) {\r\n    wx.onAppHide((...args) => {\r\n      vm.__call_hook('onHide', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onLaunch)) {\r\n    const args = wx.getLaunchOptionsSync && wx.getLaunchOptionsSync();\r\n    vm.__call_hook('onLaunch', args);\r\n  }\r\n  return vm\r\n}\r\n\r\ntodos.forEach(todoApi => {\r\n  protocols[todoApi] = false;\r\n});\r\n\r\ncanIUses.forEach(canIUseApi => {\r\n  const apiName = protocols[canIUseApi] && protocols[canIUseApi].name ? protocols[canIUseApi].name\r\n    : canIUseApi;\r\n  if (!wx.canIUse(apiName)) {\r\n    protocols[canIUseApi] = false;\r\n  }\r\n});\r\n\r\nlet uni = {};\r\n\r\nif (typeof Proxy !== 'undefined' && \"mp-weixin\" !== 'app-plus') {\r\n  uni = new Proxy({}, {\r\n    get (target, name) {\r\n      if (hasOwn(target, name)) {\r\n        return target[name]\r\n      }\r\n      if (baseApi[name]) {\r\n        return baseApi[name]\r\n      }\r\n      if (api[name]) {\r\n        return promisify(name, api[name])\r\n      }\r\n      {\r\n        if (extraApi[name]) {\r\n          return promisify(name, extraApi[name])\r\n        }\r\n        if (todoApis[name]) {\r\n          return promisify(name, todoApis[name])\r\n        }\r\n      }\r\n      if (eventApi[name]) {\r\n        return eventApi[name]\r\n      }\r\n      return promisify(name, wrapper(name, wx[name]))\r\n    },\r\n    set (target, name, value) {\r\n      target[name] = value;\r\n      return true\r\n    }\r\n  });\r\n} else {\r\n  Object.keys(baseApi).forEach(name => {\r\n    uni[name] = baseApi[name];\r\n  });\r\n\r\n  {\r\n    Object.keys(todoApis).forEach(name => {\r\n      uni[name] = promisify(name, todoApis[name]);\r\n    });\r\n    Object.keys(extraApi).forEach(name => {\r\n      uni[name] = promisify(name, extraApi[name]);\r\n    });\r\n  }\r\n\r\n  Object.keys(eventApi).forEach(name => {\r\n    uni[name] = eventApi[name];\r\n  });\r\n\r\n  Object.keys(api).forEach(name => {\r\n    uni[name] = promisify(name, api[name]);\r\n  });\r\n\r\n  Object.keys(wx).forEach(name => {\r\n    if (hasOwn(wx, name) || hasOwn(protocols, name)) {\r\n      uni[name] = promisify(name, wrapper(name, wx[name]));\r\n    }\r\n  });\r\n}\r\n\r\nwx.createApp = createApp;\r\nwx.createPage = createPage;\r\nwx.createComponent = createComponent;\r\nwx.createSubpackageApp = createSubpackageApp;\r\nwx.createPlugin = createPlugin;\r\n\r\nvar uni$1 = uni;\r\n\r\nexport default uni$1;\r\nexport { createApp, createComponent, createPage, createPlugin, createSubpackageApp };\r\n", "const objectKeys = [\r\n  'qy',\r\n  'env',\r\n  'error',\r\n  'version',\r\n  'lanDebug',\r\n  'cloud',\r\n  'serviceMarket',\r\n  'router',\r\n  'worklet',\r\n  '__webpack_require_UNI_MP_PLUGIN__'\r\n]\r\nconst singlePageDisableKey = [\r\n  'lanDebug',\r\n  'router',\r\n  'worklet'\r\n]\r\nconst target = typeof globalThis !== 'undefined' ? globalThis : (function () {\r\n  return this\r\n})()\r\n\r\nconst key = ['w', 'x'].join('')\r\nconst oldWx = target[key]\r\nconst launchOption = oldWx.getLaunchOptionsSync ? oldWx.getLaunchOptionsSync() : null\r\n\r\nfunction isWxKey (key) {\r\n  if (launchOption && launchOption.scene === 1154 && singlePageDisableKey.includes(key)) {\r\n    return false\r\n  }\r\n  return objectKeys.indexOf(key) > -1 || typeof oldWx[key] === 'function'\r\n}\r\n\r\nfunction initWx () {\r\n  const newWx = {}\r\n  for (const key in oldWx) {\r\n    if (isWxKey(key)) {\r\n      // TODO wrapper function\r\n      newWx[key] = oldWx[key]\r\n    }\r\n  }\r\n  return newWx\r\n}\r\ntarget[key] = initWx()\r\nif (!target[key].canIUse('getAppBaseInfo')) {\r\n  target[key].getAppBaseInfo = target[key].getSystemInfoSync\r\n}\r\n\r\nif (!target[key].canIUse('getWindowInfo')) {\r\n  target[key].getWindowInfo = target[key].getSystemInfoSync\r\n}\r\n\r\nif (!target[key].canIUse('getDeviceInfo')) {\r\n  target[key].getDeviceInfo = target[key].getSystemInfoSync\r\n}\r\nexport default target[key]\r\n", "// TODO(Babel 8): Remove this file.\n\nvar runtime = require('@babel/runtime/helpers/regeneratorRuntime')()\nmodule.exports = runtime\n", "/*!\n * Vue.js v2.6.11\n * (c) 2014-2024 Evan You\n * Released under the MIT License.\n */\n/*  */\n\nvar emptyObject = Object.freeze({});\n\n// These helpers produce better VM code in JS engines due to their\n// explicitness and function inlining.\nfunction isUndef (v) {\n  return v === undefined || v === null\n}\n\nfunction isDef (v) {\n  return v !== undefined && v !== null\n}\n\nfunction isTrue (v) {\n  return v === true\n}\n\nfunction isFalse (v) {\n  return v === false\n}\n\n/**\n * Check if value is primitive.\n */\nfunction isPrimitive (value) {\n  return (\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    // $flow-disable-line\n    typeof value === 'symbol' ||\n    typeof value === 'boolean'\n  )\n}\n\n/**\n * Quick object check - this is primarily used to tell\n * Objects from primitive values when we know the value\n * is a JSON-compliant type.\n */\nfunction isObject (obj) {\n  return obj !== null && typeof obj === 'object'\n}\n\n/**\n * Get the raw type string of a value, e.g., [object Object].\n */\nvar _toString = Object.prototype.toString;\n\nfunction toRawType (value) {\n  return _toString.call(value).slice(8, -1)\n}\n\n/**\n * Strict object type check. Only returns true\n * for plain JavaScript objects.\n */\nfunction isPlainObject (obj) {\n  return _toString.call(obj) === '[object Object]'\n}\n\nfunction isRegExp (v) {\n  return _toString.call(v) === '[object RegExp]'\n}\n\n/**\n * Check if val is a valid array index.\n */\nfunction isValidArrayIndex (val) {\n  var n = parseFloat(String(val));\n  return n >= 0 && Math.floor(n) === n && isFinite(val)\n}\n\nfunction isPromise (val) {\n  return (\n    isDef(val) &&\n    typeof val.then === 'function' &&\n    typeof val.catch === 'function'\n  )\n}\n\n/**\n * Convert a value to a string that is actually rendered.\n */\nfunction toString (val) {\n  return val == null\n    ? ''\n    : Array.isArray(val) || (isPlainObject(val) && val.toString === _toString)\n      ? JSON.stringify(val, null, 2)\n      : String(val)\n}\n\n/**\n * Convert an input value to a number for persistence.\n * If the conversion fails, return original string.\n */\nfunction toNumber (val) {\n  var n = parseFloat(val);\n  return isNaN(n) ? val : n\n}\n\n/**\n * Make a map and return a function for checking if a key\n * is in that map.\n */\nfunction makeMap (\n  str,\n  expectsLowerCase\n) {\n  var map = Object.create(null);\n  var list = str.split(',');\n  for (var i = 0; i < list.length; i++) {\n    map[list[i]] = true;\n  }\n  return expectsLowerCase\n    ? function (val) { return map[val.toLowerCase()]; }\n    : function (val) { return map[val]; }\n}\n\n/**\n * Check if a tag is a built-in tag.\n */\nvar isBuiltInTag = makeMap('slot,component', true);\n\n/**\n * Check if an attribute is a reserved attribute.\n */\nvar isReservedAttribute = makeMap('key,ref,slot,slot-scope,is');\n\n/**\n * Remove an item from an array.\n */\nfunction remove (arr, item) {\n  if (arr.length) {\n    var index = arr.indexOf(item);\n    if (index > -1) {\n      return arr.splice(index, 1)\n    }\n  }\n}\n\n/**\n * Check whether an object has the property.\n */\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction hasOwn (obj, key) {\n  return hasOwnProperty.call(obj, key)\n}\n\n/**\n * Create a cached version of a pure function.\n */\nfunction cached (fn) {\n  var cache = Object.create(null);\n  return (function cachedFn (str) {\n    var hit = cache[str];\n    return hit || (cache[str] = fn(str))\n  })\n}\n\n/**\n * Camelize a hyphen-delimited string.\n */\nvar camelizeRE = /-(\\w)/g;\nvar camelize = cached(function (str) {\n  return str.replace(camelizeRE, function (_, c) { return c ? c.toUpperCase() : ''; })\n});\n\n/**\n * Capitalize a string.\n */\nvar capitalize = cached(function (str) {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n});\n\n/**\n * Hyphenate a camelCase string.\n */\nvar hyphenateRE = /\\B([A-Z])/g;\nvar hyphenate = cached(function (str) {\n  return str.replace(hyphenateRE, '-$1').toLowerCase()\n});\n\n/**\n * Simple bind polyfill for environments that do not support it,\n * e.g., PhantomJS 1.x. Technically, we don't need this anymore\n * since native bind is now performant enough in most browsers.\n * But removing it would mean breaking code that was able to run in\n * PhantomJS 1.x, so this must be kept for backward compatibility.\n */\n\n/* istanbul ignore next */\nfunction polyfillBind (fn, ctx) {\n  function boundFn (a) {\n    var l = arguments.length;\n    return l\n      ? l > 1\n        ? fn.apply(ctx, arguments)\n        : fn.call(ctx, a)\n      : fn.call(ctx)\n  }\n\n  boundFn._length = fn.length;\n  return boundFn\n}\n\nfunction nativeBind (fn, ctx) {\n  return fn.bind(ctx)\n}\n\nvar bind = Function.prototype.bind\n  ? nativeBind\n  : polyfillBind;\n\n/**\n * Convert an Array-like object to a real Array.\n */\nfunction toArray (list, start) {\n  start = start || 0;\n  var i = list.length - start;\n  var ret = new Array(i);\n  while (i--) {\n    ret[i] = list[i + start];\n  }\n  return ret\n}\n\n/**\n * Mix properties into target object.\n */\nfunction extend (to, _from) {\n  for (var key in _from) {\n    to[key] = _from[key];\n  }\n  return to\n}\n\n/**\n * Merge an Array of Objects into a single Object.\n */\nfunction toObject (arr) {\n  var res = {};\n  for (var i = 0; i < arr.length; i++) {\n    if (arr[i]) {\n      extend(res, arr[i]);\n    }\n  }\n  return res\n}\n\n/* eslint-disable no-unused-vars */\n\n/**\n * Perform no operation.\n * Stubbing args to make Flow happy without leaving useless transpiled code\n * with ...rest (https://flow.org/blog/2017/05/07/Strict-Function-Call-Arity/).\n */\nfunction noop (a, b, c) {}\n\n/**\n * Always return false.\n */\nvar no = function (a, b, c) { return false; };\n\n/* eslint-enable no-unused-vars */\n\n/**\n * Return the same value.\n */\nvar identity = function (_) { return _; };\n\n/**\n * Check if two values are loosely equal - that is,\n * if they are plain objects, do they have the same shape?\n */\nfunction looseEqual (a, b) {\n  if (a === b) { return true }\n  var isObjectA = isObject(a);\n  var isObjectB = isObject(b);\n  if (isObjectA && isObjectB) {\n    try {\n      var isArrayA = Array.isArray(a);\n      var isArrayB = Array.isArray(b);\n      if (isArrayA && isArrayB) {\n        return a.length === b.length && a.every(function (e, i) {\n          return looseEqual(e, b[i])\n        })\n      } else if (a instanceof Date && b instanceof Date) {\n        return a.getTime() === b.getTime()\n      } else if (!isArrayA && !isArrayB) {\n        var keysA = Object.keys(a);\n        var keysB = Object.keys(b);\n        return keysA.length === keysB.length && keysA.every(function (key) {\n          return looseEqual(a[key], b[key])\n        })\n      } else {\n        /* istanbul ignore next */\n        return false\n      }\n    } catch (e) {\n      /* istanbul ignore next */\n      return false\n    }\n  } else if (!isObjectA && !isObjectB) {\n    return String(a) === String(b)\n  } else {\n    return false\n  }\n}\n\n/**\n * Return the first index at which a loosely equal value can be\n * found in the array (if value is a plain object, the array must\n * contain an object of the same shape), or -1 if it is not present.\n */\nfunction looseIndexOf (arr, val) {\n  for (var i = 0; i < arr.length; i++) {\n    if (looseEqual(arr[i], val)) { return i }\n  }\n  return -1\n}\n\n/**\n * Ensure a function is called only once.\n */\nfunction once (fn) {\n  var called = false;\n  return function () {\n    if (!called) {\n      called = true;\n      fn.apply(this, arguments);\n    }\n  }\n}\n\nvar ASSET_TYPES = [\n  'component',\n  'directive',\n  'filter'\n];\n\nvar LIFECYCLE_HOOKS = [\n  'beforeCreate',\n  'created',\n  'beforeMount',\n  'mounted',\n  'beforeUpdate',\n  'updated',\n  'beforeDestroy',\n  'destroyed',\n  'activated',\n  'deactivated',\n  'errorCaptured',\n  'serverPrefetch'\n];\n\n/*  */\n\n\n\nvar config = ({\n  /**\n   * Option merge strategies (used in core/util/options)\n   */\n  // $flow-disable-line\n  optionMergeStrategies: Object.create(null),\n\n  /**\n   * Whether to suppress warnings.\n   */\n  silent: false,\n\n  /**\n   * Show production mode tip message on boot?\n   */\n  productionTip: process.env.NODE_ENV !== 'production',\n\n  /**\n   * Whether to enable devtools\n   */\n  devtools: process.env.NODE_ENV !== 'production',\n\n  /**\n   * Whether to record perf\n   */\n  performance: false,\n\n  /**\n   * Error handler for watcher errors\n   */\n  errorHandler: null,\n\n  /**\n   * Warn handler for watcher warns\n   */\n  warnHandler: null,\n\n  /**\n   * Ignore certain custom elements\n   */\n  ignoredElements: [],\n\n  /**\n   * Custom user key aliases for v-on\n   */\n  // $flow-disable-line\n  keyCodes: Object.create(null),\n\n  /**\n   * Check if a tag is reserved so that it cannot be registered as a\n   * component. This is platform-dependent and may be overwritten.\n   */\n  isReservedTag: no,\n\n  /**\n   * Check if an attribute is reserved so that it cannot be used as a component\n   * prop. This is platform-dependent and may be overwritten.\n   */\n  isReservedAttr: no,\n\n  /**\n   * Check if a tag is an unknown element.\n   * Platform-dependent.\n   */\n  isUnknownElement: no,\n\n  /**\n   * Get the namespace of an element\n   */\n  getTagNamespace: noop,\n\n  /**\n   * Parse the real tag name for the specific platform.\n   */\n  parsePlatformTagName: identity,\n\n  /**\n   * Check if an attribute must be bound using property, e.g. value\n   * Platform-dependent.\n   */\n  mustUseProp: no,\n\n  /**\n   * Perform updates asynchronously. Intended to be used by Vue Test Utils\n   * This will significantly reduce performance if set to false.\n   */\n  async: true,\n\n  /**\n   * Exposed for legacy reasons\n   */\n  _lifecycleHooks: LIFECYCLE_HOOKS\n});\n\n/*  */\n\n/**\n * unicode letters used for parsing html tags, component names and property paths.\n * using https://www.w3.org/TR/html53/semantics-scripting.html#potentialcustomelementname\n * skipping \\u10000-\\uEFFFF due to it freezing up PhantomJS\n */\nvar unicodeRegExp = /a-zA-Z\\u00B7\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u203F-\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD/;\n\n/**\n * Check if a string starts with $ or _\n */\nfunction isReserved (str) {\n  var c = (str + '').charCodeAt(0);\n  return c === 0x24 || c === 0x5F\n}\n\n/**\n * Define a property.\n */\nfunction def (obj, key, val, enumerable) {\n  Object.defineProperty(obj, key, {\n    value: val,\n    enumerable: !!enumerable,\n    writable: true,\n    configurable: true\n  });\n}\n\n/**\n * Parse simple path.\n */\nvar bailRE = new RegExp((\"[^\" + (unicodeRegExp.source) + \".$_\\\\d]\"));\nfunction parsePath (path) {\n  if (bailRE.test(path)) {\n    return\n  }\n  var segments = path.split('.');\n  return function (obj) {\n    for (var i = 0; i < segments.length; i++) {\n      if (!obj) { return }\n      obj = obj[segments[i]];\n    }\n    return obj\n  }\n}\n\n/*  */\n\n// can we use __proto__?\nvar hasProto = '__proto__' in {};\n\n// Browser environment sniffing\nvar inBrowser = typeof window !== 'undefined';\nvar inWeex = typeof WXEnvironment !== 'undefined' && !!WXEnvironment.platform;\nvar weexPlatform = inWeex && WXEnvironment.platform.toLowerCase();\nvar UA = inBrowser && window.navigator && window.navigator.userAgent.toLowerCase();\nvar isIE = UA && /msie|trident/.test(UA);\nvar isIE9 = UA && UA.indexOf('msie 9.0') > 0;\nvar isEdge = UA && UA.indexOf('edge/') > 0;\nvar isAndroid = (UA && UA.indexOf('android') > 0) || (weexPlatform === 'android');\nvar isIOS = (UA && /iphone|ipad|ipod|ios/.test(UA)) || (weexPlatform === 'ios');\nvar isChrome = UA && /chrome\\/\\d+/.test(UA) && !isEdge;\nvar isPhantomJS = UA && /phantomjs/.test(UA);\nvar isFF = UA && UA.match(/firefox\\/(\\d+)/);\n\n// Firefox has a \"watch\" function on Object.prototype...\nvar nativeWatch = ({}).watch;\nif (inBrowser) {\n  try {\n    var opts = {};\n    Object.defineProperty(opts, 'passive', ({\n      get: function get () {\n      }\n    })); // https://github.com/facebook/flow/issues/285\n    window.addEventListener('test-passive', null, opts);\n  } catch (e) {}\n}\n\n// this needs to be lazy-evaled because vue may be required before\n// vue-server-renderer can set VUE_ENV\nvar _isServer;\nvar isServerRendering = function () {\n  if (_isServer === undefined) {\n    /* istanbul ignore if */\n    if (!inBrowser && !inWeex && typeof global !== 'undefined') {\n      // detect presence of vue-server-renderer and avoid\n      // Webpack shimming the process\n      _isServer = global['process'] && global['process'].env.VUE_ENV === 'server';\n    } else {\n      _isServer = false;\n    }\n  }\n  return _isServer\n};\n\n// detect devtools\nvar devtools = inBrowser && window.__VUE_DEVTOOLS_GLOBAL_HOOK__;\n\n/* istanbul ignore next */\nfunction isNative (Ctor) {\n  return typeof Ctor === 'function' && /native code/.test(Ctor.toString())\n}\n\nvar hasSymbol =\n  typeof Symbol !== 'undefined' && isNative(Symbol) &&\n  typeof Reflect !== 'undefined' && isNative(Reflect.ownKeys);\n\nvar _Set;\n/* istanbul ignore if */ // $flow-disable-line\nif (typeof Set !== 'undefined' && isNative(Set)) {\n  // use native Set when available.\n  _Set = Set;\n} else {\n  // a non-standard Set polyfill that only works with primitive keys.\n  _Set = /*@__PURE__*/(function () {\n    function Set () {\n      this.set = Object.create(null);\n    }\n    Set.prototype.has = function has (key) {\n      return this.set[key] === true\n    };\n    Set.prototype.add = function add (key) {\n      this.set[key] = true;\n    };\n    Set.prototype.clear = function clear () {\n      this.set = Object.create(null);\n    };\n\n    return Set;\n  }());\n}\n\n/*  */\n\nvar warn = noop;\nvar tip = noop;\nvar generateComponentTrace = (noop); // work around flow check\nvar formatComponentName = (noop);\n\nif (process.env.NODE_ENV !== 'production') {\n  var hasConsole = typeof console !== 'undefined';\n  var classifyRE = /(?:^|[-_])(\\w)/g;\n  var classify = function (str) { return str\n    .replace(classifyRE, function (c) { return c.toUpperCase(); })\n    .replace(/[-_]/g, ''); };\n\n  warn = function (msg, vm) {\n    var trace = vm ? generateComponentTrace(vm) : '';\n\n    if (config.warnHandler) {\n      config.warnHandler.call(null, msg, vm, trace);\n    } else if (hasConsole && (!config.silent)) {\n      console.error((\"[Vue warn]: \" + msg + trace));\n    }\n  };\n\n  tip = function (msg, vm) {\n    if (hasConsole && (!config.silent)) {\n      console.warn(\"[Vue tip]: \" + msg + (\n        vm ? generateComponentTrace(vm) : ''\n      ));\n    }\n  };\n\n  formatComponentName = function (vm, includeFile) {\n    if (vm.$root === vm) {\n      if (vm.$options && vm.$options.__file) { // fixed by xxxxxx\n        return ('') + vm.$options.__file\n      }\n      return '<Root>'\n    }\n    var options = typeof vm === 'function' && vm.cid != null\n      ? vm.options\n      : vm._isVue\n        ? vm.$options || vm.constructor.options\n        : vm;\n    var name = options.name || options._componentTag;\n    var file = options.__file;\n    if (!name && file) {\n      var match = file.match(/([^/\\\\]+)\\.vue$/);\n      name = match && match[1];\n    }\n\n    return (\n      (name ? (\"<\" + (classify(name)) + \">\") : \"<Anonymous>\") +\n      (file && includeFile !== false ? (\" at \" + file) : '')\n    )\n  };\n\n  var repeat = function (str, n) {\n    var res = '';\n    while (n) {\n      if (n % 2 === 1) { res += str; }\n      if (n > 1) { str += str; }\n      n >>= 1;\n    }\n    return res\n  };\n\n  generateComponentTrace = function (vm) {\n    if (vm._isVue && vm.$parent) {\n      var tree = [];\n      var currentRecursiveSequence = 0;\n      while (vm && vm.$options.name !== 'PageBody') {\n        if (tree.length > 0) {\n          var last = tree[tree.length - 1];\n          if (last.constructor === vm.constructor) {\n            currentRecursiveSequence++;\n            vm = vm.$parent;\n            continue\n          } else if (currentRecursiveSequence > 0) {\n            tree[tree.length - 1] = [last, currentRecursiveSequence];\n            currentRecursiveSequence = 0;\n          }\n        }\n        !vm.$options.isReserved && tree.push(vm);\n        vm = vm.$parent;\n      }\n      return '\\n\\nfound in\\n\\n' + tree\n        .map(function (vm, i) { return (\"\" + (i === 0 ? '---> ' : repeat(' ', 5 + i * 2)) + (Array.isArray(vm)\n            ? ((formatComponentName(vm[0])) + \"... (\" + (vm[1]) + \" recursive calls)\")\n            : formatComponentName(vm))); })\n        .join('\\n')\n    } else {\n      return (\"\\n\\n(found in \" + (formatComponentName(vm)) + \")\")\n    }\n  };\n}\n\n/*  */\n\nvar uid = 0;\n\n/**\n * A dep is an observable that can have multiple\n * directives subscribing to it.\n */\nvar Dep = function Dep () {\n  this.id = uid++;\n  this.subs = [];\n};\n\nDep.prototype.addSub = function addSub (sub) {\n  this.subs.push(sub);\n};\n\nDep.prototype.removeSub = function removeSub (sub) {\n  remove(this.subs, sub);\n};\n\nDep.prototype.depend = function depend () {\n  if (Dep.SharedObject.target) {\n    Dep.SharedObject.target.addDep(this);\n  }\n};\n\nDep.prototype.notify = function notify () {\n  // stabilize the subscriber list first\n  var subs = this.subs.slice();\n  if (process.env.NODE_ENV !== 'production' && !config.async) {\n    // subs aren't sorted in scheduler if not running async\n    // we need to sort them now to make sure they fire in correct\n    // order\n    subs.sort(function (a, b) { return a.id - b.id; });\n  }\n  for (var i = 0, l = subs.length; i < l; i++) {\n    subs[i].update();\n  }\n};\n\n// The current target watcher being evaluated.\n// This is globally unique because only one watcher\n// can be evaluated at a time.\n// fixed by xxxxxx (nvue shared vuex)\n/* eslint-disable no-undef */\nDep.SharedObject = {};\nDep.SharedObject.target = null;\nDep.SharedObject.targetStack = [];\n\nfunction pushTarget (target) {\n  Dep.SharedObject.targetStack.push(target);\n  Dep.SharedObject.target = target;\n  Dep.target = target;\n}\n\nfunction popTarget () {\n  Dep.SharedObject.targetStack.pop();\n  Dep.SharedObject.target = Dep.SharedObject.targetStack[Dep.SharedObject.targetStack.length - 1];\n  Dep.target = Dep.SharedObject.target;\n}\n\n/*  */\n\nvar VNode = function VNode (\n  tag,\n  data,\n  children,\n  text,\n  elm,\n  context,\n  componentOptions,\n  asyncFactory\n) {\n  this.tag = tag;\n  this.data = data;\n  this.children = children;\n  this.text = text;\n  this.elm = elm;\n  this.ns = undefined;\n  this.context = context;\n  this.fnContext = undefined;\n  this.fnOptions = undefined;\n  this.fnScopeId = undefined;\n  this.key = data && data.key;\n  this.componentOptions = componentOptions;\n  this.componentInstance = undefined;\n  this.parent = undefined;\n  this.raw = false;\n  this.isStatic = false;\n  this.isRootInsert = true;\n  this.isComment = false;\n  this.isCloned = false;\n  this.isOnce = false;\n  this.asyncFactory = asyncFactory;\n  this.asyncMeta = undefined;\n  this.isAsyncPlaceholder = false;\n};\n\nvar prototypeAccessors = { child: { configurable: true } };\n\n// DEPRECATED: alias for componentInstance for backwards compat.\n/* istanbul ignore next */\nprototypeAccessors.child.get = function () {\n  return this.componentInstance\n};\n\nObject.defineProperties( VNode.prototype, prototypeAccessors );\n\nvar createEmptyVNode = function (text) {\n  if ( text === void 0 ) text = '';\n\n  var node = new VNode();\n  node.text = text;\n  node.isComment = true;\n  return node\n};\n\nfunction createTextVNode (val) {\n  return new VNode(undefined, undefined, undefined, String(val))\n}\n\n// optimized shallow clone\n// used for static nodes and slot nodes because they may be reused across\n// multiple renders, cloning them avoids errors when DOM manipulations rely\n// on their elm reference.\nfunction cloneVNode (vnode) {\n  var cloned = new VNode(\n    vnode.tag,\n    vnode.data,\n    // #7975\n    // clone children array to avoid mutating original in case of cloning\n    // a child.\n    vnode.children && vnode.children.slice(),\n    vnode.text,\n    vnode.elm,\n    vnode.context,\n    vnode.componentOptions,\n    vnode.asyncFactory\n  );\n  cloned.ns = vnode.ns;\n  cloned.isStatic = vnode.isStatic;\n  cloned.key = vnode.key;\n  cloned.isComment = vnode.isComment;\n  cloned.fnContext = vnode.fnContext;\n  cloned.fnOptions = vnode.fnOptions;\n  cloned.fnScopeId = vnode.fnScopeId;\n  cloned.asyncMeta = vnode.asyncMeta;\n  cloned.isCloned = true;\n  return cloned\n}\n\n/*\n * not type checking this file because flow doesn't play well with\n * dynamically accessing methods on Array prototype\n */\n\nvar arrayProto = Array.prototype;\nvar arrayMethods = Object.create(arrayProto);\n\nvar methodsToPatch = [\n  'push',\n  'pop',\n  'shift',\n  'unshift',\n  'splice',\n  'sort',\n  'reverse'\n];\n\n/**\n * Intercept mutating methods and emit events\n */\nmethodsToPatch.forEach(function (method) {\n  // cache original method\n  var original = arrayProto[method];\n  def(arrayMethods, method, function mutator () {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var result = original.apply(this, args);\n    var ob = this.__ob__;\n    var inserted;\n    switch (method) {\n      case 'push':\n      case 'unshift':\n        inserted = args;\n        break\n      case 'splice':\n        inserted = args.slice(2);\n        break\n    }\n    if (inserted) { ob.observeArray(inserted); }\n    // notify change\n    ob.dep.notify();\n    return result\n  });\n});\n\n/*  */\n\nvar arrayKeys = Object.getOwnPropertyNames(arrayMethods);\n\n/**\n * In some cases we may want to disable observation inside a component's\n * update computation.\n */\nvar shouldObserve = true;\n\nfunction toggleObserving (value) {\n  shouldObserve = value;\n}\n\n/**\n * Observer class that is attached to each observed\n * object. Once attached, the observer converts the target\n * object's property keys into getter/setters that\n * collect dependencies and dispatch updates.\n */\nvar Observer = function Observer (value) {\n  this.value = value;\n  this.dep = new Dep();\n  this.vmCount = 0;\n  def(value, '__ob__', this);\n  if (Array.isArray(value)) {\n    if (hasProto) {\n      {// fixed by xxxxxx 微信小程序使用 plugins 之后，数组方法被直接挂载到了数组对象上，需要执行 copyAugment 逻辑\n        if(value.push !== value.__proto__.push){\n          copyAugment(value, arrayMethods, arrayKeys);\n        } else {\n          protoAugment(value, arrayMethods);\n        }\n      }\n    } else {\n      copyAugment(value, arrayMethods, arrayKeys);\n    }\n    this.observeArray(value);\n  } else {\n    this.walk(value);\n  }\n};\n\n/**\n * Walk through all properties and convert them into\n * getter/setters. This method should only be called when\n * value type is Object.\n */\nObserver.prototype.walk = function walk (obj) {\n  var keys = Object.keys(obj);\n  for (var i = 0; i < keys.length; i++) {\n    defineReactive$$1(obj, keys[i]);\n  }\n};\n\n/**\n * Observe a list of Array items.\n */\nObserver.prototype.observeArray = function observeArray (items) {\n  for (var i = 0, l = items.length; i < l; i++) {\n    observe(items[i]);\n  }\n};\n\n// helpers\n\n/**\n * Augment a target Object or Array by intercepting\n * the prototype chain using __proto__\n */\nfunction protoAugment (target, src) {\n  /* eslint-disable no-proto */\n  target.__proto__ = src;\n  /* eslint-enable no-proto */\n}\n\n/**\n * Augment a target Object or Array by defining\n * hidden properties.\n */\n/* istanbul ignore next */\nfunction copyAugment (target, src, keys) {\n  for (var i = 0, l = keys.length; i < l; i++) {\n    var key = keys[i];\n    def(target, key, src[key]);\n  }\n}\n\n/**\n * Attempt to create an observer instance for a value,\n * returns the new observer if successfully observed,\n * or the existing observer if the value already has one.\n */\nfunction observe (value, asRootData) {\n  if (!isObject(value) || value instanceof VNode) {\n    return\n  }\n  var ob;\n  if (hasOwn(value, '__ob__') && value.__ob__ instanceof Observer) {\n    ob = value.__ob__;\n  } else if (\n    shouldObserve &&\n    !isServerRendering() &&\n    (Array.isArray(value) || isPlainObject(value)) &&\n    Object.isExtensible(value) &&\n    !value._isVue &&\n    !value.__v_isMPComponent\n  ) {\n    ob = new Observer(value);\n  }\n  if (asRootData && ob) {\n    ob.vmCount++;\n  }\n  return ob\n}\n\n/**\n * Define a reactive property on an Object.\n */\nfunction defineReactive$$1 (\n  obj,\n  key,\n  val,\n  customSetter,\n  shallow\n) {\n  var dep = new Dep();\n\n  var property = Object.getOwnPropertyDescriptor(obj, key);\n  if (property && property.configurable === false) {\n    return\n  }\n\n  // cater for pre-defined getter/setters\n  var getter = property && property.get;\n  var setter = property && property.set;\n  if ((!getter || setter) && arguments.length === 2) {\n    val = obj[key];\n  }\n\n  var childOb = !shallow && observe(val);\n  Object.defineProperty(obj, key, {\n    enumerable: true,\n    configurable: true,\n    get: function reactiveGetter () {\n      var value = getter ? getter.call(obj) : val;\n      if (Dep.SharedObject.target) { // fixed by xxxxxx\n        dep.depend();\n        if (childOb) {\n          childOb.dep.depend();\n          if (Array.isArray(value)) {\n            dependArray(value);\n          }\n        }\n      }\n      return value\n    },\n    set: function reactiveSetter (newVal) {\n      var value = getter ? getter.call(obj) : val;\n      /* eslint-disable no-self-compare */\n      if (newVal === value || (newVal !== newVal && value !== value)) {\n        return\n      }\n      /* eslint-enable no-self-compare */\n      if (process.env.NODE_ENV !== 'production' && customSetter) {\n        customSetter();\n      }\n      // #7981: for accessor properties without setter\n      if (getter && !setter) { return }\n      if (setter) {\n        setter.call(obj, newVal);\n      } else {\n        val = newVal;\n      }\n      childOb = !shallow && observe(newVal);\n      dep.notify();\n    }\n  });\n}\n\n/**\n * Set a property on an object. Adds the new property and\n * triggers change notification if the property doesn't\n * already exist.\n */\nfunction set (target, key, val) {\n  if (process.env.NODE_ENV !== 'production' &&\n    (isUndef(target) || isPrimitive(target))\n  ) {\n    warn((\"Cannot set reactive property on undefined, null, or primitive value: \" + ((target))));\n  }\n  if (Array.isArray(target) && isValidArrayIndex(key)) {\n    target.length = Math.max(target.length, key);\n    target.splice(key, 1, val);\n    return val\n  }\n  if (key in target && !(key in Object.prototype)) {\n    target[key] = val;\n    return val\n  }\n  var ob = (target).__ob__;\n  if (target._isVue || (ob && ob.vmCount)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      'Avoid adding reactive properties to a Vue instance or its root $data ' +\n      'at runtime - declare it upfront in the data option.'\n    );\n    return val\n  }\n  if (!ob) {\n    target[key] = val;\n    return val\n  }\n  defineReactive$$1(ob.value, key, val);\n  ob.dep.notify();\n  return val\n}\n\n/**\n * Delete a property and trigger change if necessary.\n */\nfunction del (target, key) {\n  if (process.env.NODE_ENV !== 'production' &&\n    (isUndef(target) || isPrimitive(target))\n  ) {\n    warn((\"Cannot delete reactive property on undefined, null, or primitive value: \" + ((target))));\n  }\n  if (Array.isArray(target) && isValidArrayIndex(key)) {\n    target.splice(key, 1);\n    return\n  }\n  var ob = (target).__ob__;\n  if (target._isVue || (ob && ob.vmCount)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      'Avoid deleting properties on a Vue instance or its root $data ' +\n      '- just set it to null.'\n    );\n    return\n  }\n  if (!hasOwn(target, key)) {\n    return\n  }\n  delete target[key];\n  if (!ob) {\n    return\n  }\n  ob.dep.notify();\n}\n\n/**\n * Collect dependencies on array elements when the array is touched, since\n * we cannot intercept array element access like property getters.\n */\nfunction dependArray (value) {\n  for (var e = (void 0), i = 0, l = value.length; i < l; i++) {\n    e = value[i];\n    e && e.__ob__ && e.__ob__.dep.depend();\n    if (Array.isArray(e)) {\n      dependArray(e);\n    }\n  }\n}\n\n/*  */\n\n/**\n * Option overwriting strategies are functions that handle\n * how to merge a parent option value and a child option\n * value into the final value.\n */\nvar strats = config.optionMergeStrategies;\n\n/**\n * Options with restrictions\n */\nif (process.env.NODE_ENV !== 'production') {\n  strats.el = strats.propsData = function (parent, child, vm, key) {\n    if (!vm) {\n      warn(\n        \"option \\\"\" + key + \"\\\" can only be used during instance \" +\n        'creation with the `new` keyword.'\n      );\n    }\n    return defaultStrat(parent, child)\n  };\n}\n\n/**\n * Helper that recursively merges two data objects together.\n */\nfunction mergeData (to, from) {\n  if (!from) { return to }\n  var key, toVal, fromVal;\n\n  var keys = hasSymbol\n    ? Reflect.ownKeys(from)\n    : Object.keys(from);\n\n  for (var i = 0; i < keys.length; i++) {\n    key = keys[i];\n    // in case the object is already observed...\n    if (key === '__ob__') { continue }\n    toVal = to[key];\n    fromVal = from[key];\n    if (!hasOwn(to, key)) {\n      set(to, key, fromVal);\n    } else if (\n      toVal !== fromVal &&\n      isPlainObject(toVal) &&\n      isPlainObject(fromVal)\n    ) {\n      mergeData(toVal, fromVal);\n    }\n  }\n  return to\n}\n\n/**\n * Data\n */\nfunction mergeDataOrFn (\n  parentVal,\n  childVal,\n  vm\n) {\n  if (!vm) {\n    // in a Vue.extend merge, both should be functions\n    if (!childVal) {\n      return parentVal\n    }\n    if (!parentVal) {\n      return childVal\n    }\n    // when parentVal & childVal are both present,\n    // we need to return a function that returns the\n    // merged result of both functions... no need to\n    // check if parentVal is a function here because\n    // it has to be a function to pass previous merges.\n    return function mergedDataFn () {\n      return mergeData(\n        typeof childVal === 'function' ? childVal.call(this, this) : childVal,\n        typeof parentVal === 'function' ? parentVal.call(this, this) : parentVal\n      )\n    }\n  } else {\n    return function mergedInstanceDataFn () {\n      // instance merge\n      var instanceData = typeof childVal === 'function'\n        ? childVal.call(vm, vm)\n        : childVal;\n      var defaultData = typeof parentVal === 'function'\n        ? parentVal.call(vm, vm)\n        : parentVal;\n      if (instanceData) {\n        return mergeData(instanceData, defaultData)\n      } else {\n        return defaultData\n      }\n    }\n  }\n}\n\nstrats.data = function (\n  parentVal,\n  childVal,\n  vm\n) {\n  if (!vm) {\n    if (childVal && typeof childVal !== 'function') {\n      process.env.NODE_ENV !== 'production' && warn(\n        'The \"data\" option should be a function ' +\n        'that returns a per-instance value in component ' +\n        'definitions.',\n        vm\n      );\n\n      return parentVal\n    }\n    return mergeDataOrFn(parentVal, childVal)\n  }\n\n  return mergeDataOrFn(parentVal, childVal, vm)\n};\n\n/**\n * Hooks and props are merged as arrays.\n */\nfunction mergeHook (\n  parentVal,\n  childVal\n) {\n  var res = childVal\n    ? parentVal\n      ? parentVal.concat(childVal)\n      : Array.isArray(childVal)\n        ? childVal\n        : [childVal]\n    : parentVal;\n  return res\n    ? dedupeHooks(res)\n    : res\n}\n\nfunction dedupeHooks (hooks) {\n  var res = [];\n  for (var i = 0; i < hooks.length; i++) {\n    if (res.indexOf(hooks[i]) === -1) {\n      res.push(hooks[i]);\n    }\n  }\n  return res\n}\n\nLIFECYCLE_HOOKS.forEach(function (hook) {\n  strats[hook] = mergeHook;\n});\n\n/**\n * Assets\n *\n * When a vm is present (instance creation), we need to do\n * a three-way merge between constructor options, instance\n * options and parent options.\n */\nfunction mergeAssets (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  var res = Object.create(parentVal || null);\n  if (childVal) {\n    process.env.NODE_ENV !== 'production' && assertObjectType(key, childVal, vm);\n    return extend(res, childVal)\n  } else {\n    return res\n  }\n}\n\nASSET_TYPES.forEach(function (type) {\n  strats[type + 's'] = mergeAssets;\n});\n\n/**\n * Watchers.\n *\n * Watchers hashes should not overwrite one\n * another, so we merge them as arrays.\n */\nstrats.watch = function (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  // work around Firefox's Object.prototype.watch...\n  if (parentVal === nativeWatch) { parentVal = undefined; }\n  if (childVal === nativeWatch) { childVal = undefined; }\n  /* istanbul ignore if */\n  if (!childVal) { return Object.create(parentVal || null) }\n  if (process.env.NODE_ENV !== 'production') {\n    assertObjectType(key, childVal, vm);\n  }\n  if (!parentVal) { return childVal }\n  var ret = {};\n  extend(ret, parentVal);\n  for (var key$1 in childVal) {\n    var parent = ret[key$1];\n    var child = childVal[key$1];\n    if (parent && !Array.isArray(parent)) {\n      parent = [parent];\n    }\n    ret[key$1] = parent\n      ? parent.concat(child)\n      : Array.isArray(child) ? child : [child];\n  }\n  return ret\n};\n\n/**\n * Other object hashes.\n */\nstrats.props =\nstrats.methods =\nstrats.inject =\nstrats.computed = function (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  if (childVal && process.env.NODE_ENV !== 'production') {\n    assertObjectType(key, childVal, vm);\n  }\n  if (!parentVal) { return childVal }\n  var ret = Object.create(null);\n  extend(ret, parentVal);\n  if (childVal) { extend(ret, childVal); }\n  return ret\n};\nstrats.provide = mergeDataOrFn;\n\n/**\n * Default strategy.\n */\nvar defaultStrat = function (parentVal, childVal) {\n  return childVal === undefined\n    ? parentVal\n    : childVal\n};\n\n/**\n * Validate component names\n */\nfunction checkComponents (options) {\n  for (var key in options.components) {\n    validateComponentName(key);\n  }\n}\n\nfunction validateComponentName (name) {\n  if (!new RegExp((\"^[a-zA-Z][\\\\-\\\\.0-9_\" + (unicodeRegExp.source) + \"]*$\")).test(name)) {\n    warn(\n      'Invalid component name: \"' + name + '\". Component names ' +\n      'should conform to valid custom element name in html5 specification.'\n    );\n  }\n  if (isBuiltInTag(name) || config.isReservedTag(name)) {\n    warn(\n      'Do not use built-in or reserved HTML elements as component ' +\n      'id: ' + name\n    );\n  }\n}\n\n/**\n * Ensure all props option syntax are normalized into the\n * Object-based format.\n */\nfunction normalizeProps (options, vm) {\n  var props = options.props;\n  if (!props) { return }\n  var res = {};\n  var i, val, name;\n  if (Array.isArray(props)) {\n    i = props.length;\n    while (i--) {\n      val = props[i];\n      if (typeof val === 'string') {\n        name = camelize(val);\n        res[name] = { type: null };\n      } else if (process.env.NODE_ENV !== 'production') {\n        warn('props must be strings when using array syntax.');\n      }\n    }\n  } else if (isPlainObject(props)) {\n    for (var key in props) {\n      val = props[key];\n      name = camelize(key);\n      res[name] = isPlainObject(val)\n        ? val\n        : { type: val };\n    }\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\n      \"Invalid value for option \\\"props\\\": expected an Array or an Object, \" +\n      \"but got \" + (toRawType(props)) + \".\",\n      vm\n    );\n  }\n  options.props = res;\n}\n\n/**\n * Normalize all injections into Object-based format\n */\nfunction normalizeInject (options, vm) {\n  var inject = options.inject;\n  if (!inject) { return }\n  var normalized = options.inject = {};\n  if (Array.isArray(inject)) {\n    for (var i = 0; i < inject.length; i++) {\n      normalized[inject[i]] = { from: inject[i] };\n    }\n  } else if (isPlainObject(inject)) {\n    for (var key in inject) {\n      var val = inject[key];\n      normalized[key] = isPlainObject(val)\n        ? extend({ from: key }, val)\n        : { from: val };\n    }\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\n      \"Invalid value for option \\\"inject\\\": expected an Array or an Object, \" +\n      \"but got \" + (toRawType(inject)) + \".\",\n      vm\n    );\n  }\n}\n\n/**\n * Normalize raw function directives into object format.\n */\nfunction normalizeDirectives (options) {\n  var dirs = options.directives;\n  if (dirs) {\n    for (var key in dirs) {\n      var def$$1 = dirs[key];\n      if (typeof def$$1 === 'function') {\n        dirs[key] = { bind: def$$1, update: def$$1 };\n      }\n    }\n  }\n}\n\nfunction assertObjectType (name, value, vm) {\n  if (!isPlainObject(value)) {\n    warn(\n      \"Invalid value for option \\\"\" + name + \"\\\": expected an Object, \" +\n      \"but got \" + (toRawType(value)) + \".\",\n      vm\n    );\n  }\n}\n\n/**\n * Merge two option objects into a new one.\n * Core utility used in both instantiation and inheritance.\n */\nfunction mergeOptions (\n  parent,\n  child,\n  vm\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    checkComponents(child);\n  }\n\n  if (typeof child === 'function') {\n    child = child.options;\n  }\n\n  normalizeProps(child, vm);\n  normalizeInject(child, vm);\n  normalizeDirectives(child);\n\n  // Apply extends and mixins on the child options,\n  // but only if it is a raw options object that isn't\n  // the result of another mergeOptions call.\n  // Only merged options has the _base property.\n  if (!child._base) {\n    if (child.extends) {\n      parent = mergeOptions(parent, child.extends, vm);\n    }\n    if (child.mixins) {\n      for (var i = 0, l = child.mixins.length; i < l; i++) {\n        parent = mergeOptions(parent, child.mixins[i], vm);\n      }\n    }\n  }\n\n  var options = {};\n  var key;\n  for (key in parent) {\n    mergeField(key);\n  }\n  for (key in child) {\n    if (!hasOwn(parent, key)) {\n      mergeField(key);\n    }\n  }\n  function mergeField (key) {\n    var strat = strats[key] || defaultStrat;\n    options[key] = strat(parent[key], child[key], vm, key);\n  }\n  return options\n}\n\n/**\n * Resolve an asset.\n * This function is used because child instances need access\n * to assets defined in its ancestor chain.\n */\nfunction resolveAsset (\n  options,\n  type,\n  id,\n  warnMissing\n) {\n  /* istanbul ignore if */\n  if (typeof id !== 'string') {\n    return\n  }\n  var assets = options[type];\n  // check local registration variations first\n  if (hasOwn(assets, id)) { return assets[id] }\n  var camelizedId = camelize(id);\n  if (hasOwn(assets, camelizedId)) { return assets[camelizedId] }\n  var PascalCaseId = capitalize(camelizedId);\n  if (hasOwn(assets, PascalCaseId)) { return assets[PascalCaseId] }\n  // fallback to prototype chain\n  var res = assets[id] || assets[camelizedId] || assets[PascalCaseId];\n  if (process.env.NODE_ENV !== 'production' && warnMissing && !res) {\n    warn(\n      'Failed to resolve ' + type.slice(0, -1) + ': ' + id,\n      options\n    );\n  }\n  return res\n}\n\n/*  */\n\n\n\nfunction validateProp (\n  key,\n  propOptions,\n  propsData,\n  vm\n) {\n  var prop = propOptions[key];\n  var absent = !hasOwn(propsData, key);\n  var value = propsData[key];\n  // boolean casting\n  var booleanIndex = getTypeIndex(Boolean, prop.type);\n  if (booleanIndex > -1) {\n    if (absent && !hasOwn(prop, 'default')) {\n      value = false;\n    } else if (value === '' || value === hyphenate(key)) {\n      // only cast empty string / same name to boolean if\n      // boolean has higher priority\n      var stringIndex = getTypeIndex(String, prop.type);\n      if (stringIndex < 0 || booleanIndex < stringIndex) {\n        value = true;\n      }\n    }\n  }\n  // check default value\n  if (value === undefined) {\n    value = getPropDefaultValue(vm, prop, key);\n    // since the default value is a fresh copy,\n    // make sure to observe it.\n    var prevShouldObserve = shouldObserve;\n    toggleObserving(true);\n    observe(value);\n    toggleObserving(prevShouldObserve);\n  }\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    // skip validation for weex recycle-list child component props\n    !(false)\n  ) {\n    assertProp(prop, key, value, vm, absent);\n  }\n  return value\n}\n\n/**\n * Get the default value of a prop.\n */\nfunction getPropDefaultValue (vm, prop, key) {\n  // no default, return undefined\n  if (!hasOwn(prop, 'default')) {\n    return undefined\n  }\n  var def = prop.default;\n  // warn against non-factory defaults for Object & Array\n  if (process.env.NODE_ENV !== 'production' && isObject(def)) {\n    warn(\n      'Invalid default value for prop \"' + key + '\": ' +\n      'Props with type Object/Array must use a factory function ' +\n      'to return the default value.',\n      vm\n    );\n  }\n  // the raw prop value was also undefined from previous render,\n  // return previous default value to avoid unnecessary watcher trigger\n  if (vm && vm.$options.propsData &&\n    vm.$options.propsData[key] === undefined &&\n    vm._props[key] !== undefined\n  ) {\n    return vm._props[key]\n  }\n  // call factory function for non-Function types\n  // a value is Function if its prototype is function even across different execution context\n  return typeof def === 'function' && getType(prop.type) !== 'Function'\n    ? def.call(vm)\n    : def\n}\n\n/**\n * Assert whether a prop is valid.\n */\nfunction assertProp (\n  prop,\n  name,\n  value,\n  vm,\n  absent\n) {\n  if (prop.required && absent) {\n    warn(\n      'Missing required prop: \"' + name + '\"',\n      vm\n    );\n    return\n  }\n  if (value == null && !prop.required) {\n    return\n  }\n  var type = prop.type;\n  var valid = !type || type === true;\n  var expectedTypes = [];\n  if (type) {\n    if (!Array.isArray(type)) {\n      type = [type];\n    }\n    for (var i = 0; i < type.length && !valid; i++) {\n      var assertedType = assertType(value, type[i]);\n      expectedTypes.push(assertedType.expectedType || '');\n      valid = assertedType.valid;\n    }\n  }\n\n  if (!valid) {\n    warn(\n      getInvalidTypeMessage(name, value, expectedTypes),\n      vm\n    );\n    return\n  }\n  var validator = prop.validator;\n  if (validator) {\n    if (!validator(value)) {\n      warn(\n        'Invalid prop: custom validator check failed for prop \"' + name + '\".',\n        vm\n      );\n    }\n  }\n}\n\nvar simpleCheckRE = /^(String|Number|Boolean|Function|Symbol)$/;\n\nfunction assertType (value, type) {\n  var valid;\n  var expectedType = getType(type);\n  if (simpleCheckRE.test(expectedType)) {\n    var t = typeof value;\n    valid = t === expectedType.toLowerCase();\n    // for primitive wrapper objects\n    if (!valid && t === 'object') {\n      valid = value instanceof type;\n    }\n  } else if (expectedType === 'Object') {\n    valid = isPlainObject(value);\n  } else if (expectedType === 'Array') {\n    valid = Array.isArray(value);\n  } else {\n    valid = value instanceof type;\n  }\n  return {\n    valid: valid,\n    expectedType: expectedType\n  }\n}\n\n/**\n * Use function string name to check built-in types,\n * because a simple equality check will fail when running\n * across different vms / iframes.\n */\nfunction getType (fn) {\n  var match = fn && fn.toString().match(/^\\s*function (\\w+)/);\n  return match ? match[1] : ''\n}\n\nfunction isSameType (a, b) {\n  return getType(a) === getType(b)\n}\n\nfunction getTypeIndex (type, expectedTypes) {\n  if (!Array.isArray(expectedTypes)) {\n    return isSameType(expectedTypes, type) ? 0 : -1\n  }\n  for (var i = 0, len = expectedTypes.length; i < len; i++) {\n    if (isSameType(expectedTypes[i], type)) {\n      return i\n    }\n  }\n  return -1\n}\n\nfunction getInvalidTypeMessage (name, value, expectedTypes) {\n  var message = \"Invalid prop: type check failed for prop \\\"\" + name + \"\\\".\" +\n    \" Expected \" + (expectedTypes.map(capitalize).join(', '));\n  var expectedType = expectedTypes[0];\n  var receivedType = toRawType(value);\n  var expectedValue = styleValue(value, expectedType);\n  var receivedValue = styleValue(value, receivedType);\n  // check if we need to specify expected value\n  if (expectedTypes.length === 1 &&\n      isExplicable(expectedType) &&\n      !isBoolean(expectedType, receivedType)) {\n    message += \" with value \" + expectedValue;\n  }\n  message += \", got \" + receivedType + \" \";\n  // check if we need to specify received value\n  if (isExplicable(receivedType)) {\n    message += \"with value \" + receivedValue + \".\";\n  }\n  return message\n}\n\nfunction styleValue (value, type) {\n  if (type === 'String') {\n    return (\"\\\"\" + value + \"\\\"\")\n  } else if (type === 'Number') {\n    return (\"\" + (Number(value)))\n  } else {\n    return (\"\" + value)\n  }\n}\n\nfunction isExplicable (value) {\n  var explicitTypes = ['string', 'number', 'boolean'];\n  return explicitTypes.some(function (elem) { return value.toLowerCase() === elem; })\n}\n\nfunction isBoolean () {\n  var args = [], len = arguments.length;\n  while ( len-- ) args[ len ] = arguments[ len ];\n\n  return args.some(function (elem) { return elem.toLowerCase() === 'boolean'; })\n}\n\n/*  */\n\nfunction handleError (err, vm, info) {\n  // Deactivate deps tracking while processing error handler to avoid possible infinite rendering.\n  // See: https://github.com/vuejs/vuex/issues/1505\n  pushTarget();\n  try {\n    if (vm) {\n      var cur = vm;\n      while ((cur = cur.$parent)) {\n        var hooks = cur.$options.errorCaptured;\n        if (hooks) {\n          for (var i = 0; i < hooks.length; i++) {\n            try {\n              var capture = hooks[i].call(cur, err, vm, info) === false;\n              if (capture) { return }\n            } catch (e) {\n              globalHandleError(e, cur, 'errorCaptured hook');\n            }\n          }\n        }\n      }\n    }\n    globalHandleError(err, vm, info);\n  } finally {\n    popTarget();\n  }\n}\n\nfunction invokeWithErrorHandling (\n  handler,\n  context,\n  args,\n  vm,\n  info\n) {\n  var res;\n  try {\n    res = args ? handler.apply(context, args) : handler.call(context);\n    if (res && !res._isVue && isPromise(res) && !res._handled) {\n      res.catch(function (e) { return handleError(e, vm, info + \" (Promise/async)\"); });\n      // issue #9511\n      // avoid catch triggering multiple times when nested calls\n      res._handled = true;\n    }\n  } catch (e) {\n    handleError(e, vm, info);\n  }\n  return res\n}\n\nfunction globalHandleError (err, vm, info) {\n  if (config.errorHandler) {\n    try {\n      return config.errorHandler.call(null, err, vm, info)\n    } catch (e) {\n      // if the user intentionally throws the original error in the handler,\n      // do not log it twice\n      if (e !== err) {\n        logError(e, null, 'config.errorHandler');\n      }\n    }\n  }\n  logError(err, vm, info);\n}\n\nfunction logError (err, vm, info) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn((\"Error in \" + info + \": \\\"\" + (err.toString()) + \"\\\"\"), vm);\n  }\n  /* istanbul ignore else */\n  if ((inBrowser || inWeex) && typeof console !== 'undefined') {\n    console.error(err);\n  } else {\n    throw err\n  }\n}\n\n/*  */\n\nvar callbacks = [];\nvar pending = false;\n\nfunction flushCallbacks () {\n  pending = false;\n  var copies = callbacks.slice(0);\n  callbacks.length = 0;\n  for (var i = 0; i < copies.length; i++) {\n    copies[i]();\n  }\n}\n\n// Here we have async deferring wrappers using microtasks.\n// In 2.5 we used (macro) tasks (in combination with microtasks).\n// However, it has subtle problems when state is changed right before repaint\n// (e.g. #6813, out-in transitions).\n// Also, using (macro) tasks in event handler would cause some weird behaviors\n// that cannot be circumvented (e.g. #7109, #7153, #7546, #7834, #8109).\n// So we now use microtasks everywhere, again.\n// A major drawback of this tradeoff is that there are some scenarios\n// where microtasks have too high a priority and fire in between supposedly\n// sequential events (e.g. #4521, #6690, which have workarounds)\n// or even between bubbling of the same event (#6566).\nvar timerFunc;\n\n// The nextTick behavior leverages the microtask queue, which can be accessed\n// via either native Promise.then or MutationObserver.\n// MutationObserver has wider support, however it is seriously bugged in\n// UIWebView in iOS >= 9.3.3 when triggered in touch event handlers. It\n// completely stops working after triggering a few times... so, if native\n// Promise is available, we will use it:\n/* istanbul ignore next, $flow-disable-line */\nif (typeof Promise !== 'undefined' && isNative(Promise)) {\n  var p = Promise.resolve();\n  timerFunc = function () {\n    p.then(flushCallbacks);\n    // In problematic UIWebViews, Promise.then doesn't completely break, but\n    // it can get stuck in a weird state where callbacks are pushed into the\n    // microtask queue but the queue isn't being flushed, until the browser\n    // needs to do some other work, e.g. handle a timer. Therefore we can\n    // \"force\" the microtask queue to be flushed by adding an empty timer.\n    if (isIOS) { setTimeout(noop); }\n  };\n} else if (!isIE && typeof MutationObserver !== 'undefined' && (\n  isNative(MutationObserver) ||\n  // PhantomJS and iOS 7.x\n  MutationObserver.toString() === '[object MutationObserverConstructor]'\n)) {\n  // Use MutationObserver where native Promise is not available,\n  // e.g. PhantomJS, iOS7, Android 4.4\n  // (#6466 MutationObserver is unreliable in IE11)\n  var counter = 1;\n  var observer = new MutationObserver(flushCallbacks);\n  var textNode = document.createTextNode(String(counter));\n  observer.observe(textNode, {\n    characterData: true\n  });\n  timerFunc = function () {\n    counter = (counter + 1) % 2;\n    textNode.data = String(counter);\n  };\n} else if (typeof setImmediate !== 'undefined' && isNative(setImmediate)) {\n  // Fallback to setImmediate.\n  // Technically it leverages the (macro) task queue,\n  // but it is still a better choice than setTimeout.\n  timerFunc = function () {\n    setImmediate(flushCallbacks);\n  };\n} else {\n  // Fallback to setTimeout.\n  timerFunc = function () {\n    setTimeout(flushCallbacks, 0);\n  };\n}\n\nfunction nextTick (cb, ctx) {\n  var _resolve;\n  callbacks.push(function () {\n    if (cb) {\n      try {\n        cb.call(ctx);\n      } catch (e) {\n        handleError(e, ctx, 'nextTick');\n      }\n    } else if (_resolve) {\n      _resolve(ctx);\n    }\n  });\n  if (!pending) {\n    pending = true;\n    timerFunc();\n  }\n  // $flow-disable-line\n  if (!cb && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve) {\n      _resolve = resolve;\n    })\n  }\n}\n\n/*  */\n\n/* not type checking this file because flow doesn't play well with Proxy */\n\nvar initProxy;\n\nif (process.env.NODE_ENV !== 'production') {\n  var allowedGlobals = makeMap(\n    'Infinity,undefined,NaN,isFinite,isNaN,' +\n    'parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,' +\n    'Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,' +\n    'require' // for Webpack/Browserify\n  );\n\n  var warnNonPresent = function (target, key) {\n    warn(\n      \"Property or method \\\"\" + key + \"\\\" is not defined on the instance but \" +\n      'referenced during render. Make sure that this property is reactive, ' +\n      'either in the data option, or for class-based components, by ' +\n      'initializing the property. ' +\n      'See: https://vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.',\n      target\n    );\n  };\n\n  var warnReservedPrefix = function (target, key) {\n    warn(\n      \"Property \\\"\" + key + \"\\\" must be accessed with \\\"$data.\" + key + \"\\\" because \" +\n      'properties starting with \"$\" or \"_\" are not proxied in the Vue instance to ' +\n      'prevent conflicts with Vue internals. ' +\n      'See: https://vuejs.org/v2/api/#data',\n      target\n    );\n  };\n\n  var hasProxy =\n    typeof Proxy !== 'undefined' && isNative(Proxy);\n\n  if (hasProxy) {\n    var isBuiltInModifier = makeMap('stop,prevent,self,ctrl,shift,alt,meta,exact');\n    config.keyCodes = new Proxy(config.keyCodes, {\n      set: function set (target, key, value) {\n        if (isBuiltInModifier(key)) {\n          warn((\"Avoid overwriting built-in modifier in config.keyCodes: .\" + key));\n          return false\n        } else {\n          target[key] = value;\n          return true\n        }\n      }\n    });\n  }\n\n  var hasHandler = {\n    has: function has (target, key) {\n      var has = key in target;\n      var isAllowed = allowedGlobals(key) ||\n        (typeof key === 'string' && key.charAt(0) === '_' && !(key in target.$data));\n      if (!has && !isAllowed) {\n        if (key in target.$data) { warnReservedPrefix(target, key); }\n        else { warnNonPresent(target, key); }\n      }\n      return has || !isAllowed\n    }\n  };\n\n  var getHandler = {\n    get: function get (target, key) {\n      if (typeof key === 'string' && !(key in target)) {\n        if (key in target.$data) { warnReservedPrefix(target, key); }\n        else { warnNonPresent(target, key); }\n      }\n      return target[key]\n    }\n  };\n\n  initProxy = function initProxy (vm) {\n    if (hasProxy) {\n      // determine which proxy handler to use\n      var options = vm.$options;\n      var handlers = options.render && options.render._withStripped\n        ? getHandler\n        : hasHandler;\n      vm._renderProxy = new Proxy(vm, handlers);\n    } else {\n      vm._renderProxy = vm;\n    }\n  };\n}\n\n/*  */\n\nvar seenObjects = new _Set();\n\n/**\n * Recursively traverse an object to evoke all converted\n * getters, so that every nested property inside the object\n * is collected as a \"deep\" dependency.\n */\nfunction traverse (val) {\n  _traverse(val, seenObjects);\n  seenObjects.clear();\n}\n\nfunction _traverse (val, seen) {\n  var i, keys;\n  var isA = Array.isArray(val);\n  if ((!isA && !isObject(val)) || Object.isFrozen(val) || val instanceof VNode) {\n    return\n  }\n  if (val.__ob__) {\n    var depId = val.__ob__.dep.id;\n    if (seen.has(depId)) {\n      return\n    }\n    seen.add(depId);\n  }\n  if (isA) {\n    i = val.length;\n    while (i--) { _traverse(val[i], seen); }\n  } else {\n    keys = Object.keys(val);\n    i = keys.length;\n    while (i--) { _traverse(val[keys[i]], seen); }\n  }\n}\n\nvar mark;\nvar measure;\n\nif (process.env.NODE_ENV !== 'production') {\n  var perf = inBrowser && window.performance;\n  /* istanbul ignore if */\n  if (\n    perf &&\n    perf.mark &&\n    perf.measure &&\n    perf.clearMarks &&\n    perf.clearMeasures\n  ) {\n    mark = function (tag) { return perf.mark(tag); };\n    measure = function (name, startTag, endTag) {\n      perf.measure(name, startTag, endTag);\n      perf.clearMarks(startTag);\n      perf.clearMarks(endTag);\n      // perf.clearMeasures(name)\n    };\n  }\n}\n\n/*  */\n\nvar normalizeEvent = cached(function (name) {\n  var passive = name.charAt(0) === '&';\n  name = passive ? name.slice(1) : name;\n  var once$$1 = name.charAt(0) === '~'; // Prefixed last, checked first\n  name = once$$1 ? name.slice(1) : name;\n  var capture = name.charAt(0) === '!';\n  name = capture ? name.slice(1) : name;\n  return {\n    name: name,\n    once: once$$1,\n    capture: capture,\n    passive: passive\n  }\n});\n\nfunction createFnInvoker (fns, vm) {\n  function invoker () {\n    var arguments$1 = arguments;\n\n    var fns = invoker.fns;\n    if (Array.isArray(fns)) {\n      var cloned = fns.slice();\n      for (var i = 0; i < cloned.length; i++) {\n        invokeWithErrorHandling(cloned[i], null, arguments$1, vm, \"v-on handler\");\n      }\n    } else {\n      // return handler return value for single handlers\n      return invokeWithErrorHandling(fns, null, arguments, vm, \"v-on handler\")\n    }\n  }\n  invoker.fns = fns;\n  return invoker\n}\n\nfunction updateListeners (\n  on,\n  oldOn,\n  add,\n  remove$$1,\n  createOnceHandler,\n  vm\n) {\n  var name, def$$1, cur, old, event;\n  for (name in on) {\n    def$$1 = cur = on[name];\n    old = oldOn[name];\n    event = normalizeEvent(name);\n    if (isUndef(cur)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Invalid handler for event \\\"\" + (event.name) + \"\\\": got \" + String(cur),\n        vm\n      );\n    } else if (isUndef(old)) {\n      if (isUndef(cur.fns)) {\n        cur = on[name] = createFnInvoker(cur, vm);\n      }\n      if (isTrue(event.once)) {\n        cur = on[name] = createOnceHandler(event.name, cur, event.capture);\n      }\n      add(event.name, cur, event.capture, event.passive, event.params);\n    } else if (cur !== old) {\n      old.fns = cur;\n      on[name] = old;\n    }\n  }\n  for (name in oldOn) {\n    if (isUndef(on[name])) {\n      event = normalizeEvent(name);\n      remove$$1(event.name, oldOn[name], event.capture);\n    }\n  }\n}\n\n/*  */\n\n/*  */\n\n// fixed by xxxxxx (mp properties)\r\nfunction extractPropertiesFromVNodeData(data, Ctor, res, context) {\r\n  var propOptions = Ctor.options.mpOptions && Ctor.options.mpOptions.properties;\r\n  if (isUndef(propOptions)) {\r\n    return res\r\n  }\n  var externalClasses = Ctor.options.mpOptions.externalClasses || [];\r\n  var attrs = data.attrs;\n  var props = data.props;\r\n  if (isDef(attrs) || isDef(props)) {\r\n    for (var key in propOptions) {\r\n      var altKey = hyphenate(key);\n      var result = checkProp(res, props, key, altKey, true) ||\n          checkProp(res, attrs, key, altKey, false);\n      // externalClass\n      if (\n        result &&\n        res[key] &&\n        externalClasses.indexOf(altKey) !== -1 &&\n        context[camelize(res[key])]\n      ) {\n        // 赋值 externalClass 真正的值(模板里 externalClass 的值可能是字符串)\n        res[key] = context[camelize(res[key])];\n      }\r\n    }\r\n  }\r\n  return res\r\n}\n\nfunction extractPropsFromVNodeData (\n  data,\n  Ctor,\n  tag,\n  context// fixed by xxxxxx\n) {\n  // we are only extracting raw values here.\n  // validation and default values are handled in the child\n  // component itself.\n  var propOptions = Ctor.options.props;\n  if (isUndef(propOptions)) {\n    // fixed by xxxxxx\n    return extractPropertiesFromVNodeData(data, Ctor, {}, context)\n  }\n  var res = {};\n  var attrs = data.attrs;\n  var props = data.props;\n  if (isDef(attrs) || isDef(props)) {\n    for (var key in propOptions) {\n      var altKey = hyphenate(key);\n      if (process.env.NODE_ENV !== 'production') {\n        var keyInLowerCase = key.toLowerCase();\n        if (\n          key !== keyInLowerCase &&\n          attrs && hasOwn(attrs, keyInLowerCase)\n        ) {\n          tip(\n            \"Prop \\\"\" + keyInLowerCase + \"\\\" is passed to component \" +\n            (formatComponentName(tag || Ctor)) + \", but the declared prop name is\" +\n            \" \\\"\" + key + \"\\\". \" +\n            \"Note that HTML attributes are case-insensitive and camelCased \" +\n            \"props need to use their kebab-case equivalents when using in-DOM \" +\n            \"templates. You should probably use \\\"\" + altKey + \"\\\" instead of \\\"\" + key + \"\\\".\"\n          );\n        }\n      }\n      checkProp(res, props, key, altKey, true) ||\n      checkProp(res, attrs, key, altKey, false);\n    }\n  }\n  // fixed by xxxxxx\n  return extractPropertiesFromVNodeData(data, Ctor, res, context)\n}\n\nfunction checkProp (\n  res,\n  hash,\n  key,\n  altKey,\n  preserve\n) {\n  if (isDef(hash)) {\n    if (hasOwn(hash, key)) {\n      res[key] = hash[key];\n      if (!preserve) {\n        delete hash[key];\n      }\n      return true\n    } else if (hasOwn(hash, altKey)) {\n      res[key] = hash[altKey];\n      if (!preserve) {\n        delete hash[altKey];\n      }\n      return true\n    }\n  }\n  return false\n}\n\n/*  */\n\n// The template compiler attempts to minimize the need for normalization by\n// statically analyzing the template at compile time.\n//\n// For plain HTML markup, normalization can be completely skipped because the\n// generated render function is guaranteed to return Array<VNode>. There are\n// two cases where extra normalization is needed:\n\n// 1. When the children contains components - because a functional component\n// may return an Array instead of a single root. In this case, just a simple\n// normalization is needed - if any child is an Array, we flatten the whole\n// thing with Array.prototype.concat. It is guaranteed to be only 1-level deep\n// because functional components already normalize their own children.\nfunction simpleNormalizeChildren (children) {\n  for (var i = 0; i < children.length; i++) {\n    if (Array.isArray(children[i])) {\n      return Array.prototype.concat.apply([], children)\n    }\n  }\n  return children\n}\n\n// 2. When the children contains constructs that always generated nested Arrays,\n// e.g. <template>, <slot>, v-for, or when the children is provided by user\n// with hand-written render functions / JSX. In such cases a full normalization\n// is needed to cater to all possible types of children values.\nfunction normalizeChildren (children) {\n  return isPrimitive(children)\n    ? [createTextVNode(children)]\n    : Array.isArray(children)\n      ? normalizeArrayChildren(children)\n      : undefined\n}\n\nfunction isTextNode (node) {\n  return isDef(node) && isDef(node.text) && isFalse(node.isComment)\n}\n\nfunction normalizeArrayChildren (children, nestedIndex) {\n  var res = [];\n  var i, c, lastIndex, last;\n  for (i = 0; i < children.length; i++) {\n    c = children[i];\n    if (isUndef(c) || typeof c === 'boolean') { continue }\n    lastIndex = res.length - 1;\n    last = res[lastIndex];\n    //  nested\n    if (Array.isArray(c)) {\n      if (c.length > 0) {\n        c = normalizeArrayChildren(c, ((nestedIndex || '') + \"_\" + i));\n        // merge adjacent text nodes\n        if (isTextNode(c[0]) && isTextNode(last)) {\n          res[lastIndex] = createTextVNode(last.text + (c[0]).text);\n          c.shift();\n        }\n        res.push.apply(res, c);\n      }\n    } else if (isPrimitive(c)) {\n      if (isTextNode(last)) {\n        // merge adjacent text nodes\n        // this is necessary for SSR hydration because text nodes are\n        // essentially merged when rendered to HTML strings\n        res[lastIndex] = createTextVNode(last.text + c);\n      } else if (c !== '') {\n        // convert primitive to vnode\n        res.push(createTextVNode(c));\n      }\n    } else {\n      if (isTextNode(c) && isTextNode(last)) {\n        // merge adjacent text nodes\n        res[lastIndex] = createTextVNode(last.text + c.text);\n      } else {\n        // default key for nested array children (likely generated by v-for)\n        if (isTrue(children._isVList) &&\n          isDef(c.tag) &&\n          isUndef(c.key) &&\n          isDef(nestedIndex)) {\n          c.key = \"__vlist\" + nestedIndex + \"_\" + i + \"__\";\n        }\n        res.push(c);\n      }\n    }\n  }\n  return res\n}\n\n/*  */\n\nfunction initProvide (vm) {\n  var provide = vm.$options.provide;\n  if (provide) {\n    vm._provided = typeof provide === 'function'\n      ? provide.call(vm)\n      : provide;\n  }\n}\n\nfunction initInjections (vm) {\n  var result = resolveInject(vm.$options.inject, vm);\n  if (result) {\n    toggleObserving(false);\n    Object.keys(result).forEach(function (key) {\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        defineReactive$$1(vm, key, result[key], function () {\n          warn(\n            \"Avoid mutating an injected value directly since the changes will be \" +\n            \"overwritten whenever the provided component re-renders. \" +\n            \"injection being mutated: \\\"\" + key + \"\\\"\",\n            vm\n          );\n        });\n      } else {\n        defineReactive$$1(vm, key, result[key]);\n      }\n    });\n    toggleObserving(true);\n  }\n}\n\nfunction resolveInject (inject, vm) {\n  if (inject) {\n    // inject is :any because flow is not smart enough to figure out cached\n    var result = Object.create(null);\n    var keys = hasSymbol\n      ? Reflect.ownKeys(inject)\n      : Object.keys(inject);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n      // #6574 in case the inject object is observed...\n      if (key === '__ob__') { continue }\n      var provideKey = inject[key].from;\n      var source = vm;\n      while (source) {\n        if (source._provided && hasOwn(source._provided, provideKey)) {\n          result[key] = source._provided[provideKey];\n          break\n        }\n        source = source.$parent;\n      }\n      if (!source) {\n        if ('default' in inject[key]) {\n          var provideDefault = inject[key].default;\n          result[key] = typeof provideDefault === 'function'\n            ? provideDefault.call(vm)\n            : provideDefault;\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn((\"Injection \\\"\" + key + \"\\\" not found\"), vm);\n        }\n      }\n    }\n    return result\n  }\n}\n\n/*  */\n\n\n\n/**\n * Runtime helper for resolving raw children VNodes into a slot object.\n */\nfunction resolveSlots (\n  children,\n  context\n) {\n  if (!children || !children.length) {\n    return {}\n  }\n  var slots = {};\n  for (var i = 0, l = children.length; i < l; i++) {\n    var child = children[i];\n    var data = child.data;\n    // remove slot attribute if the node is resolved as a Vue slot node\n    if (data && data.attrs && data.attrs.slot) {\n      delete data.attrs.slot;\n    }\n    // named slots should only be respected if the vnode was rendered in the\n    // same context.\n    if ((child.context === context || child.fnContext === context) &&\n      data && data.slot != null\n    ) {\n      var name = data.slot;\n      var slot = (slots[name] || (slots[name] = []));\n      if (child.tag === 'template') {\n        slot.push.apply(slot, child.children || []);\n      } else {\n        slot.push(child);\n      }\n    } else {\n      // fixed by xxxxxx 临时 hack 掉 uni-app 中的异步 name slot page\n      if(child.asyncMeta && child.asyncMeta.data && child.asyncMeta.data.slot === 'page'){\n        (slots['page'] || (slots['page'] = [])).push(child);\n      }else{\n        (slots.default || (slots.default = [])).push(child);\n      }\n    }\n  }\n  // ignore slots that contains only whitespace\n  for (var name$1 in slots) {\n    if (slots[name$1].every(isWhitespace)) {\n      delete slots[name$1];\n    }\n  }\n  return slots\n}\n\nfunction isWhitespace (node) {\n  return (node.isComment && !node.asyncFactory) || node.text === ' '\n}\n\n/*  */\n\nfunction normalizeScopedSlots (\n  slots,\n  normalSlots,\n  prevSlots\n) {\n  var res;\n  var hasNormalSlots = Object.keys(normalSlots).length > 0;\n  var isStable = slots ? !!slots.$stable : !hasNormalSlots;\n  var key = slots && slots.$key;\n  if (!slots) {\n    res = {};\n  } else if (slots._normalized) {\n    // fast path 1: child component re-render only, parent did not change\n    return slots._normalized\n  } else if (\n    isStable &&\n    prevSlots &&\n    prevSlots !== emptyObject &&\n    key === prevSlots.$key &&\n    !hasNormalSlots &&\n    !prevSlots.$hasNormal\n  ) {\n    // fast path 2: stable scoped slots w/ no normal slots to proxy,\n    // only need to normalize once\n    return prevSlots\n  } else {\n    res = {};\n    for (var key$1 in slots) {\n      if (slots[key$1] && key$1[0] !== '$') {\n        res[key$1] = normalizeScopedSlot(normalSlots, key$1, slots[key$1]);\n      }\n    }\n  }\n  // expose normal slots on scopedSlots\n  for (var key$2 in normalSlots) {\n    if (!(key$2 in res)) {\n      res[key$2] = proxyNormalSlot(normalSlots, key$2);\n    }\n  }\n  // avoriaz seems to mock a non-extensible $scopedSlots object\n  // and when that is passed down this would cause an error\n  if (slots && Object.isExtensible(slots)) {\n    (slots)._normalized = res;\n  }\n  def(res, '$stable', isStable);\n  def(res, '$key', key);\n  def(res, '$hasNormal', hasNormalSlots);\n  return res\n}\n\nfunction normalizeScopedSlot(normalSlots, key, fn) {\n  var normalized = function () {\n    var res = arguments.length ? fn.apply(null, arguments) : fn({});\n    res = res && typeof res === 'object' && !Array.isArray(res)\n      ? [res] // single vnode\n      : normalizeChildren(res);\n    return res && (\n      res.length === 0 ||\n      (res.length === 1 && res[0].isComment) // #9658\n    ) ? undefined\n      : res\n  };\n  // this is a slot using the new v-slot syntax without scope. although it is\n  // compiled as a scoped slot, render fn users would expect it to be present\n  // on this.$slots because the usage is semantically a normal slot.\n  if (fn.proxy) {\n    Object.defineProperty(normalSlots, key, {\n      get: normalized,\n      enumerable: true,\n      configurable: true\n    });\n  }\n  return normalized\n}\n\nfunction proxyNormalSlot(slots, key) {\n  return function () { return slots[key]; }\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering v-for lists.\n */\nfunction renderList (\n  val,\n  render\n) {\n  var ret, i, l, keys, key;\n  if (Array.isArray(val) || typeof val === 'string') {\n    ret = new Array(val.length);\n    for (i = 0, l = val.length; i < l; i++) {\n      ret[i] = render(val[i], i, i, i); // fixed by xxxxxx\n    }\n  } else if (typeof val === 'number') {\n    ret = new Array(val);\n    for (i = 0; i < val; i++) {\n      ret[i] = render(i + 1, i, i, i); // fixed by xxxxxx\n    }\n  } else if (isObject(val)) {\n    if (hasSymbol && val[Symbol.iterator]) {\n      ret = [];\n      var iterator = val[Symbol.iterator]();\n      var result = iterator.next();\n      while (!result.done) {\n        ret.push(render(result.value, ret.length, i, i++)); // fixed by xxxxxx\n        result = iterator.next();\n      }\n    } else {\n      keys = Object.keys(val);\n      ret = new Array(keys.length);\n      for (i = 0, l = keys.length; i < l; i++) {\n        key = keys[i];\n        ret[i] = render(val[key], key, i, i); // fixed by xxxxxx\n      }\n    }\n  }\n  if (!isDef(ret)) {\n    ret = [];\n  }\n  (ret)._isVList = true;\n  return ret\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering <slot>\n */\nfunction renderSlot (\n  name,\n  fallback,\n  props,\n  bindObject\n) {\n  var scopedSlotFn = this.$scopedSlots[name];\n  var nodes;\n  if (scopedSlotFn) { // scoped slot\n    props = props || {};\n    if (bindObject) {\n      if (process.env.NODE_ENV !== 'production' && !isObject(bindObject)) {\n        warn(\n          'slot v-bind without argument expects an Object',\n          this\n        );\n      }\n      props = extend(extend({}, bindObject), props);\n    }\n    // fixed by xxxxxx app-plus scopedSlot\n    nodes = scopedSlotFn(props, this, props._i) || fallback;\n  } else {\n    nodes = this.$slots[name] || fallback;\n  }\n\n  var target = props && props.slot;\n  if (target) {\n    return this.$createElement('template', { slot: target }, nodes)\n  } else {\n    return nodes\n  }\n}\n\n/*  */\n\n/**\n * Runtime helper for resolving filters\n */\nfunction resolveFilter (id) {\n  return resolveAsset(this.$options, 'filters', id, true) || identity\n}\n\n/*  */\n\nfunction isKeyNotMatch (expect, actual) {\n  if (Array.isArray(expect)) {\n    return expect.indexOf(actual) === -1\n  } else {\n    return expect !== actual\n  }\n}\n\n/**\n * Runtime helper for checking keyCodes from config.\n * exposed as Vue.prototype._k\n * passing in eventKeyName as last argument separately for backwards compat\n */\nfunction checkKeyCodes (\n  eventKeyCode,\n  key,\n  builtInKeyCode,\n  eventKeyName,\n  builtInKeyName\n) {\n  var mappedKeyCode = config.keyCodes[key] || builtInKeyCode;\n  if (builtInKeyName && eventKeyName && !config.keyCodes[key]) {\n    return isKeyNotMatch(builtInKeyName, eventKeyName)\n  } else if (mappedKeyCode) {\n    return isKeyNotMatch(mappedKeyCode, eventKeyCode)\n  } else if (eventKeyName) {\n    return hyphenate(eventKeyName) !== key\n  }\n}\n\n/*  */\n\n/**\n * Runtime helper for merging v-bind=\"object\" into a VNode's data.\n */\nfunction bindObjectProps (\n  data,\n  tag,\n  value,\n  asProp,\n  isSync\n) {\n  if (value) {\n    if (!isObject(value)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        'v-bind without argument expects an Object or Array value',\n        this\n      );\n    } else {\n      if (Array.isArray(value)) {\n        value = toObject(value);\n      }\n      var hash;\n      var loop = function ( key ) {\n        if (\n          key === 'class' ||\n          key === 'style' ||\n          isReservedAttribute(key)\n        ) {\n          hash = data;\n        } else {\n          var type = data.attrs && data.attrs.type;\n          hash = asProp || config.mustUseProp(tag, type, key)\n            ? data.domProps || (data.domProps = {})\n            : data.attrs || (data.attrs = {});\n        }\n        var camelizedKey = camelize(key);\n        var hyphenatedKey = hyphenate(key);\n        if (!(camelizedKey in hash) && !(hyphenatedKey in hash)) {\n          hash[key] = value[key];\n\n          if (isSync) {\n            var on = data.on || (data.on = {});\n            on[(\"update:\" + key)] = function ($event) {\n              value[key] = $event;\n            };\n          }\n        }\n      };\n\n      for (var key in value) loop( key );\n    }\n  }\n  return data\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering static trees.\n */\nfunction renderStatic (\n  index,\n  isInFor\n) {\n  var cached = this._staticTrees || (this._staticTrees = []);\n  var tree = cached[index];\n  // if has already-rendered static tree and not inside v-for,\n  // we can reuse the same tree.\n  if (tree && !isInFor) {\n    return tree\n  }\n  // otherwise, render a fresh tree.\n  tree = cached[index] = this.$options.staticRenderFns[index].call(\n    this._renderProxy,\n    null,\n    this // for render fns generated for functional component templates\n  );\n  markStatic(tree, (\"__static__\" + index), false);\n  return tree\n}\n\n/**\n * Runtime helper for v-once.\n * Effectively it means marking the node as static with a unique key.\n */\nfunction markOnce (\n  tree,\n  index,\n  key\n) {\n  markStatic(tree, (\"__once__\" + index + (key ? (\"_\" + key) : \"\")), true);\n  return tree\n}\n\nfunction markStatic (\n  tree,\n  key,\n  isOnce\n) {\n  if (Array.isArray(tree)) {\n    for (var i = 0; i < tree.length; i++) {\n      if (tree[i] && typeof tree[i] !== 'string') {\n        markStaticNode(tree[i], (key + \"_\" + i), isOnce);\n      }\n    }\n  } else {\n    markStaticNode(tree, key, isOnce);\n  }\n}\n\nfunction markStaticNode (node, key, isOnce) {\n  node.isStatic = true;\n  node.key = key;\n  node.isOnce = isOnce;\n}\n\n/*  */\n\nfunction bindObjectListeners (data, value) {\n  if (value) {\n    if (!isPlainObject(value)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        'v-on without argument expects an Object value',\n        this\n      );\n    } else {\n      var on = data.on = data.on ? extend({}, data.on) : {};\n      for (var key in value) {\n        var existing = on[key];\n        var ours = value[key];\n        on[key] = existing ? [].concat(existing, ours) : ours;\n      }\n    }\n  }\n  return data\n}\n\n/*  */\n\nfunction resolveScopedSlots (\n  fns, // see flow/vnode\n  res,\n  // the following are added in 2.6\n  hasDynamicKeys,\n  contentHashKey\n) {\n  res = res || { $stable: !hasDynamicKeys };\n  for (var i = 0; i < fns.length; i++) {\n    var slot = fns[i];\n    if (Array.isArray(slot)) {\n      resolveScopedSlots(slot, res, hasDynamicKeys);\n    } else if (slot) {\n      // marker for reverse proxying v-slot without scope on this.$slots\n      if (slot.proxy) {\n        slot.fn.proxy = true;\n      }\n      res[slot.key] = slot.fn;\n    }\n  }\n  if (contentHashKey) {\n    (res).$key = contentHashKey;\n  }\n  return res\n}\n\n/*  */\n\nfunction bindDynamicKeys (baseObj, values) {\n  for (var i = 0; i < values.length; i += 2) {\n    var key = values[i];\n    if (typeof key === 'string' && key) {\n      baseObj[values[i]] = values[i + 1];\n    } else if (process.env.NODE_ENV !== 'production' && key !== '' && key !== null) {\n      // null is a special value for explicitly removing a binding\n      warn(\n        (\"Invalid value for dynamic directive argument (expected string or null): \" + key),\n        this\n      );\n    }\n  }\n  return baseObj\n}\n\n// helper to dynamically append modifier runtime markers to event names.\n// ensure only append when value is already string, otherwise it will be cast\n// to string and cause the type check to miss.\nfunction prependModifier (value, symbol) {\n  return typeof value === 'string' ? symbol + value : value\n}\n\n/*  */\n\nfunction installRenderHelpers (target) {\n  target._o = markOnce;\n  target._n = toNumber;\n  target._s = toString;\n  target._l = renderList;\n  target._t = renderSlot;\n  target._q = looseEqual;\n  target._i = looseIndexOf;\n  target._m = renderStatic;\n  target._f = resolveFilter;\n  target._k = checkKeyCodes;\n  target._b = bindObjectProps;\n  target._v = createTextVNode;\n  target._e = createEmptyVNode;\n  target._u = resolveScopedSlots;\n  target._g = bindObjectListeners;\n  target._d = bindDynamicKeys;\n  target._p = prependModifier;\n}\n\n/*  */\n\nfunction FunctionalRenderContext (\n  data,\n  props,\n  children,\n  parent,\n  Ctor\n) {\n  var this$1 = this;\n\n  var options = Ctor.options;\n  // ensure the createElement function in functional components\n  // gets a unique context - this is necessary for correct named slot check\n  var contextVm;\n  if (hasOwn(parent, '_uid')) {\n    contextVm = Object.create(parent);\n    // $flow-disable-line\n    contextVm._original = parent;\n  } else {\n    // the context vm passed in is a functional context as well.\n    // in this case we want to make sure we are able to get a hold to the\n    // real context instance.\n    contextVm = parent;\n    // $flow-disable-line\n    parent = parent._original;\n  }\n  var isCompiled = isTrue(options._compiled);\n  var needNormalization = !isCompiled;\n\n  this.data = data;\n  this.props = props;\n  this.children = children;\n  this.parent = parent;\n  this.listeners = data.on || emptyObject;\n  this.injections = resolveInject(options.inject, parent);\n  this.slots = function () {\n    if (!this$1.$slots) {\n      normalizeScopedSlots(\n        data.scopedSlots,\n        this$1.$slots = resolveSlots(children, parent)\n      );\n    }\n    return this$1.$slots\n  };\n\n  Object.defineProperty(this, 'scopedSlots', ({\n    enumerable: true,\n    get: function get () {\n      return normalizeScopedSlots(data.scopedSlots, this.slots())\n    }\n  }));\n\n  // support for compiled functional template\n  if (isCompiled) {\n    // exposing $options for renderStatic()\n    this.$options = options;\n    // pre-resolve slots for renderSlot()\n    this.$slots = this.slots();\n    this.$scopedSlots = normalizeScopedSlots(data.scopedSlots, this.$slots);\n  }\n\n  if (options._scopeId) {\n    this._c = function (a, b, c, d) {\n      var vnode = createElement(contextVm, a, b, c, d, needNormalization);\n      if (vnode && !Array.isArray(vnode)) {\n        vnode.fnScopeId = options._scopeId;\n        vnode.fnContext = parent;\n      }\n      return vnode\n    };\n  } else {\n    this._c = function (a, b, c, d) { return createElement(contextVm, a, b, c, d, needNormalization); };\n  }\n}\n\ninstallRenderHelpers(FunctionalRenderContext.prototype);\n\nfunction createFunctionalComponent (\n  Ctor,\n  propsData,\n  data,\n  contextVm,\n  children\n) {\n  var options = Ctor.options;\n  var props = {};\n  var propOptions = options.props;\n  if (isDef(propOptions)) {\n    for (var key in propOptions) {\n      props[key] = validateProp(key, propOptions, propsData || emptyObject);\n    }\n  } else {\n    if (isDef(data.attrs)) { mergeProps(props, data.attrs); }\n    if (isDef(data.props)) { mergeProps(props, data.props); }\n  }\n\n  var renderContext = new FunctionalRenderContext(\n    data,\n    props,\n    children,\n    contextVm,\n    Ctor\n  );\n\n  var vnode = options.render.call(null, renderContext._c, renderContext);\n\n  if (vnode instanceof VNode) {\n    return cloneAndMarkFunctionalResult(vnode, data, renderContext.parent, options, renderContext)\n  } else if (Array.isArray(vnode)) {\n    var vnodes = normalizeChildren(vnode) || [];\n    var res = new Array(vnodes.length);\n    for (var i = 0; i < vnodes.length; i++) {\n      res[i] = cloneAndMarkFunctionalResult(vnodes[i], data, renderContext.parent, options, renderContext);\n    }\n    return res\n  }\n}\n\nfunction cloneAndMarkFunctionalResult (vnode, data, contextVm, options, renderContext) {\n  // #7817 clone node before setting fnContext, otherwise if the node is reused\n  // (e.g. it was from a cached normal slot) the fnContext causes named slots\n  // that should not be matched to match.\n  var clone = cloneVNode(vnode);\n  clone.fnContext = contextVm;\n  clone.fnOptions = options;\n  if (process.env.NODE_ENV !== 'production') {\n    (clone.devtoolsMeta = clone.devtoolsMeta || {}).renderContext = renderContext;\n  }\n  if (data.slot) {\n    (clone.data || (clone.data = {})).slot = data.slot;\n  }\n  return clone\n}\n\nfunction mergeProps (to, from) {\n  for (var key in from) {\n    to[camelize(key)] = from[key];\n  }\n}\n\n/*  */\n\n/*  */\n\n/*  */\n\n/*  */\n\n// inline hooks to be invoked on component VNodes during patch\nvar componentVNodeHooks = {\n  init: function init (vnode, hydrating) {\n    if (\n      vnode.componentInstance &&\n      !vnode.componentInstance._isDestroyed &&\n      vnode.data.keepAlive\n    ) {\n      // kept-alive components, treat as a patch\n      var mountedNode = vnode; // work around flow\n      componentVNodeHooks.prepatch(mountedNode, mountedNode);\n    } else {\n      var child = vnode.componentInstance = createComponentInstanceForVnode(\n        vnode,\n        activeInstance\n      );\n      child.$mount(hydrating ? vnode.elm : undefined, hydrating);\n    }\n  },\n\n  prepatch: function prepatch (oldVnode, vnode) {\n    var options = vnode.componentOptions;\n    var child = vnode.componentInstance = oldVnode.componentInstance;\n    updateChildComponent(\n      child,\n      options.propsData, // updated props\n      options.listeners, // updated listeners\n      vnode, // new parent vnode\n      options.children // new children\n    );\n  },\n\n  insert: function insert (vnode) {\n    var context = vnode.context;\n    var componentInstance = vnode.componentInstance;\n    if (!componentInstance._isMounted) {\n      callHook(componentInstance, 'onServiceCreated');\n      callHook(componentInstance, 'onServiceAttached');\n      componentInstance._isMounted = true;\n      callHook(componentInstance, 'mounted');\n    }\n    if (vnode.data.keepAlive) {\n      if (context._isMounted) {\n        // vue-router#1212\n        // During updates, a kept-alive component's child components may\n        // change, so directly walking the tree here may call activated hooks\n        // on incorrect children. Instead we push them into a queue which will\n        // be processed after the whole patch process ended.\n        queueActivatedComponent(componentInstance);\n      } else {\n        activateChildComponent(componentInstance, true /* direct */);\n      }\n    }\n  },\n\n  destroy: function destroy (vnode) {\n    var componentInstance = vnode.componentInstance;\n    if (!componentInstance._isDestroyed) {\n      if (!vnode.data.keepAlive) {\n        componentInstance.$destroy();\n      } else {\n        deactivateChildComponent(componentInstance, true /* direct */);\n      }\n    }\n  }\n};\n\nvar hooksToMerge = Object.keys(componentVNodeHooks);\n\nfunction createComponent (\n  Ctor,\n  data,\n  context,\n  children,\n  tag\n) {\n  if (isUndef(Ctor)) {\n    return\n  }\n\n  var baseCtor = context.$options._base;\n\n  // plain options object: turn it into a constructor\n  if (isObject(Ctor)) {\n    Ctor = baseCtor.extend(Ctor);\n  }\n\n  // if at this stage it's not a constructor or an async component factory,\n  // reject.\n  if (typeof Ctor !== 'function') {\n    if (process.env.NODE_ENV !== 'production') {\n      warn((\"Invalid Component definition: \" + (String(Ctor))), context);\n    }\n    return\n  }\n\n  // async component\n  var asyncFactory;\n  if (isUndef(Ctor.cid)) {\n    asyncFactory = Ctor;\n    Ctor = resolveAsyncComponent(asyncFactory, baseCtor);\n    if (Ctor === undefined) {\n      // return a placeholder node for async component, which is rendered\n      // as a comment node but preserves all the raw information for the node.\n      // the information will be used for async server-rendering and hydration.\n      return createAsyncPlaceholder(\n        asyncFactory,\n        data,\n        context,\n        children,\n        tag\n      )\n    }\n  }\n\n  data = data || {};\n\n  // resolve constructor options in case global mixins are applied after\n  // component constructor creation\n  resolveConstructorOptions(Ctor);\n\n  // transform component v-model data into props & events\n  if (isDef(data.model)) {\n    transformModel(Ctor.options, data);\n  }\n\n  // extract props\n  var propsData = extractPropsFromVNodeData(data, Ctor, tag, context); // fixed by xxxxxx\n\n  // functional component\n  if (isTrue(Ctor.options.functional)) {\n    return createFunctionalComponent(Ctor, propsData, data, context, children)\n  }\n\n  // extract listeners, since these needs to be treated as\n  // child component listeners instead of DOM listeners\n  var listeners = data.on;\n  // replace with listeners with .native modifier\n  // so it gets processed during parent component patch.\n  data.on = data.nativeOn;\n\n  if (isTrue(Ctor.options.abstract)) {\n    // abstract components do not keep anything\n    // other than props & listeners & slot\n\n    // work around flow\n    var slot = data.slot;\n    data = {};\n    if (slot) {\n      data.slot = slot;\n    }\n  }\n\n  // install component management hooks onto the placeholder node\n  installComponentHooks(data);\n\n  // return a placeholder vnode\n  var name = Ctor.options.name || tag;\n  var vnode = new VNode(\n    (\"vue-component-\" + (Ctor.cid) + (name ? (\"-\" + name) : '')),\n    data, undefined, undefined, undefined, context,\n    { Ctor: Ctor, propsData: propsData, listeners: listeners, tag: tag, children: children },\n    asyncFactory\n  );\n\n  return vnode\n}\n\nfunction createComponentInstanceForVnode (\n  vnode, // we know it's MountedComponentVNode but flow doesn't\n  parent // activeInstance in lifecycle state\n) {\n  var options = {\n    _isComponent: true,\n    _parentVnode: vnode,\n    parent: parent\n  };\n  // check inline-template render functions\n  var inlineTemplate = vnode.data.inlineTemplate;\n  if (isDef(inlineTemplate)) {\n    options.render = inlineTemplate.render;\n    options.staticRenderFns = inlineTemplate.staticRenderFns;\n  }\n  return new vnode.componentOptions.Ctor(options)\n}\n\nfunction installComponentHooks (data) {\n  var hooks = data.hook || (data.hook = {});\n  for (var i = 0; i < hooksToMerge.length; i++) {\n    var key = hooksToMerge[i];\n    var existing = hooks[key];\n    var toMerge = componentVNodeHooks[key];\n    if (existing !== toMerge && !(existing && existing._merged)) {\n      hooks[key] = existing ? mergeHook$1(toMerge, existing) : toMerge;\n    }\n  }\n}\n\nfunction mergeHook$1 (f1, f2) {\n  var merged = function (a, b) {\n    // flow complains about extra args which is why we use any\n    f1(a, b);\n    f2(a, b);\n  };\n  merged._merged = true;\n  return merged\n}\n\n// transform component v-model info (value and callback) into\n// prop and event handler respectively.\nfunction transformModel (options, data) {\n  var prop = (options.model && options.model.prop) || 'value';\n  var event = (options.model && options.model.event) || 'input'\n  ;(data.attrs || (data.attrs = {}))[prop] = data.model.value;\n  var on = data.on || (data.on = {});\n  var existing = on[event];\n  var callback = data.model.callback;\n  if (isDef(existing)) {\n    if (\n      Array.isArray(existing)\n        ? existing.indexOf(callback) === -1\n        : existing !== callback\n    ) {\n      on[event] = [callback].concat(existing);\n    }\n  } else {\n    on[event] = callback;\n  }\n}\n\n/*  */\n\nvar SIMPLE_NORMALIZE = 1;\nvar ALWAYS_NORMALIZE = 2;\n\n// wrapper function for providing a more flexible interface\n// without getting yelled at by flow\nfunction createElement (\n  context,\n  tag,\n  data,\n  children,\n  normalizationType,\n  alwaysNormalize\n) {\n  if (Array.isArray(data) || isPrimitive(data)) {\n    normalizationType = children;\n    children = data;\n    data = undefined;\n  }\n  if (isTrue(alwaysNormalize)) {\n    normalizationType = ALWAYS_NORMALIZE;\n  }\n  return _createElement(context, tag, data, children, normalizationType)\n}\n\nfunction _createElement (\n  context,\n  tag,\n  data,\n  children,\n  normalizationType\n) {\n  if (isDef(data) && isDef((data).__ob__)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      \"Avoid using observed data object as vnode data: \" + (JSON.stringify(data)) + \"\\n\" +\n      'Always create fresh vnode data objects in each render!',\n      context\n    );\n    return createEmptyVNode()\n  }\n  // object syntax in v-bind\n  if (isDef(data) && isDef(data.is)) {\n    tag = data.is;\n  }\n  if (!tag) {\n    // in case of component :is set to falsy value\n    return createEmptyVNode()\n  }\n  // warn against non-primitive key\n  if (process.env.NODE_ENV !== 'production' &&\n    isDef(data) && isDef(data.key) && !isPrimitive(data.key)\n  ) {\n    {\n      warn(\n        'Avoid using non-primitive value as key, ' +\n        'use string/number value instead.',\n        context\n      );\n    }\n  }\n  // support single function children as default scoped slot\n  if (Array.isArray(children) &&\n    typeof children[0] === 'function'\n  ) {\n    data = data || {};\n    data.scopedSlots = { default: children[0] };\n    children.length = 0;\n  }\n  if (normalizationType === ALWAYS_NORMALIZE) {\n    children = normalizeChildren(children);\n  } else if (normalizationType === SIMPLE_NORMALIZE) {\n    children = simpleNormalizeChildren(children);\n  }\n  var vnode, ns;\n  if (typeof tag === 'string') {\n    var Ctor;\n    ns = (context.$vnode && context.$vnode.ns) || config.getTagNamespace(tag);\n    if (config.isReservedTag(tag)) {\n      // platform built-in elements\n      if (process.env.NODE_ENV !== 'production' && isDef(data) && isDef(data.nativeOn)) {\n        warn(\n          (\"The .native modifier for v-on is only valid on components but it was used on <\" + tag + \">.\"),\n          context\n        );\n      }\n      vnode = new VNode(\n        config.parsePlatformTagName(tag), data, children,\n        undefined, undefined, context\n      );\n    } else if ((!data || !data.pre) && isDef(Ctor = resolveAsset(context.$options, 'components', tag))) {\n      // component\n      vnode = createComponent(Ctor, data, context, children, tag);\n    } else {\n      // unknown or unlisted namespaced elements\n      // check at runtime because it may get assigned a namespace when its\n      // parent normalizes children\n      vnode = new VNode(\n        tag, data, children,\n        undefined, undefined, context\n      );\n    }\n  } else {\n    // direct component options / constructor\n    vnode = createComponent(tag, data, context, children);\n  }\n  if (Array.isArray(vnode)) {\n    return vnode\n  } else if (isDef(vnode)) {\n    if (isDef(ns)) { applyNS(vnode, ns); }\n    if (isDef(data)) { registerDeepBindings(data); }\n    return vnode\n  } else {\n    return createEmptyVNode()\n  }\n}\n\nfunction applyNS (vnode, ns, force) {\n  vnode.ns = ns;\n  if (vnode.tag === 'foreignObject') {\n    // use default namespace inside foreignObject\n    ns = undefined;\n    force = true;\n  }\n  if (isDef(vnode.children)) {\n    for (var i = 0, l = vnode.children.length; i < l; i++) {\n      var child = vnode.children[i];\n      if (isDef(child.tag) && (\n        isUndef(child.ns) || (isTrue(force) && child.tag !== 'svg'))) {\n        applyNS(child, ns, force);\n      }\n    }\n  }\n}\n\n// ref #5318\n// necessary to ensure parent re-render when deep bindings like :style and\n// :class are used on slot nodes\nfunction registerDeepBindings (data) {\n  if (isObject(data.style)) {\n    traverse(data.style);\n  }\n  if (isObject(data.class)) {\n    traverse(data.class);\n  }\n}\n\n/*  */\n\nfunction initRender (vm) {\n  vm._vnode = null; // the root of the child tree\n  vm._staticTrees = null; // v-once cached trees\n  var options = vm.$options;\n  var parentVnode = vm.$vnode = options._parentVnode; // the placeholder node in parent tree\n  var renderContext = parentVnode && parentVnode.context;\n  vm.$slots = resolveSlots(options._renderChildren, renderContext);\n  vm.$scopedSlots = emptyObject;\n  // bind the createElement fn to this instance\n  // so that we get proper render context inside it.\n  // args order: tag, data, children, normalizationType, alwaysNormalize\n  // internal version is used by render functions compiled from templates\n  vm._c = function (a, b, c, d) { return createElement(vm, a, b, c, d, false); };\n  // normalization is always applied for the public version, used in\n  // user-written render functions.\n  vm.$createElement = function (a, b, c, d) { return createElement(vm, a, b, c, d, true); };\n\n  // $attrs & $listeners are exposed for easier HOC creation.\n  // they need to be reactive so that HOCs using them are always updated\n  var parentData = parentVnode && parentVnode.data;\n\n  /* istanbul ignore else */\n  if (process.env.NODE_ENV !== 'production') {\n    defineReactive$$1(vm, '$attrs', parentData && parentData.attrs || emptyObject, function () {\n      !isUpdatingChildComponent && warn(\"$attrs is readonly.\", vm);\n    }, true);\n    defineReactive$$1(vm, '$listeners', options._parentListeners || emptyObject, function () {\n      !isUpdatingChildComponent && warn(\"$listeners is readonly.\", vm);\n    }, true);\n  } else {\n    defineReactive$$1(vm, '$attrs', parentData && parentData.attrs || emptyObject, null, true);\n    defineReactive$$1(vm, '$listeners', options._parentListeners || emptyObject, null, true);\n  }\n}\n\nvar currentRenderingInstance = null;\n\nfunction renderMixin (Vue) {\n  // install runtime convenience helpers\n  installRenderHelpers(Vue.prototype);\n\n  Vue.prototype.$nextTick = function (fn) {\n    return nextTick(fn, this)\n  };\n\n  Vue.prototype._render = function () {\n    var vm = this;\n    var ref = vm.$options;\n    var render = ref.render;\n    var _parentVnode = ref._parentVnode;\n\n    if (_parentVnode) {\n      vm.$scopedSlots = normalizeScopedSlots(\n        _parentVnode.data.scopedSlots,\n        vm.$slots,\n        vm.$scopedSlots\n      );\n    }\n\n    // set parent vnode. this allows render functions to have access\n    // to the data on the placeholder node.\n    vm.$vnode = _parentVnode;\n    // render self\n    var vnode;\n    try {\n      // There's no need to maintain a stack because all render fns are called\n      // separately from one another. Nested component's render fns are called\n      // when parent component is patched.\n      currentRenderingInstance = vm;\n      vnode = render.call(vm._renderProxy, vm.$createElement);\n    } catch (e) {\n      handleError(e, vm, \"render\");\n      // return error render result,\n      // or previous vnode to prevent render error causing blank component\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production' && vm.$options.renderError) {\n        try {\n          vnode = vm.$options.renderError.call(vm._renderProxy, vm.$createElement, e);\n        } catch (e) {\n          handleError(e, vm, \"renderError\");\n          vnode = vm._vnode;\n        }\n      } else {\n        vnode = vm._vnode;\n      }\n    } finally {\n      currentRenderingInstance = null;\n    }\n    // if the returned array contains only a single node, allow it\n    if (Array.isArray(vnode) && vnode.length === 1) {\n      vnode = vnode[0];\n    }\n    // return empty vnode in case the render function errored out\n    if (!(vnode instanceof VNode)) {\n      if (process.env.NODE_ENV !== 'production' && Array.isArray(vnode)) {\n        warn(\n          'Multiple root nodes returned from render function. Render function ' +\n          'should return a single root node.',\n          vm\n        );\n      }\n      vnode = createEmptyVNode();\n    }\n    // set parent\n    vnode.parent = _parentVnode;\n    return vnode\n  };\n}\n\n/*  */\n\nfunction ensureCtor (comp, base) {\n  if (\n    comp.__esModule ||\n    (hasSymbol && comp[Symbol.toStringTag] === 'Module')\n  ) {\n    comp = comp.default;\n  }\n  return isObject(comp)\n    ? base.extend(comp)\n    : comp\n}\n\nfunction createAsyncPlaceholder (\n  factory,\n  data,\n  context,\n  children,\n  tag\n) {\n  var node = createEmptyVNode();\n  node.asyncFactory = factory;\n  node.asyncMeta = { data: data, context: context, children: children, tag: tag };\n  return node\n}\n\nfunction resolveAsyncComponent (\n  factory,\n  baseCtor\n) {\n  if (isTrue(factory.error) && isDef(factory.errorComp)) {\n    return factory.errorComp\n  }\n\n  if (isDef(factory.resolved)) {\n    return factory.resolved\n  }\n\n  var owner = currentRenderingInstance;\n  if (owner && isDef(factory.owners) && factory.owners.indexOf(owner) === -1) {\n    // already pending\n    factory.owners.push(owner);\n  }\n\n  if (isTrue(factory.loading) && isDef(factory.loadingComp)) {\n    return factory.loadingComp\n  }\n\n  if (owner && !isDef(factory.owners)) {\n    var owners = factory.owners = [owner];\n    var sync = true;\n    var timerLoading = null;\n    var timerTimeout = null\n\n    ;(owner).$on('hook:destroyed', function () { return remove(owners, owner); });\n\n    var forceRender = function (renderCompleted) {\n      for (var i = 0, l = owners.length; i < l; i++) {\n        (owners[i]).$forceUpdate();\n      }\n\n      if (renderCompleted) {\n        owners.length = 0;\n        if (timerLoading !== null) {\n          clearTimeout(timerLoading);\n          timerLoading = null;\n        }\n        if (timerTimeout !== null) {\n          clearTimeout(timerTimeout);\n          timerTimeout = null;\n        }\n      }\n    };\n\n    var resolve = once(function (res) {\n      // cache resolved\n      factory.resolved = ensureCtor(res, baseCtor);\n      // invoke callbacks only if this is not a synchronous resolve\n      // (async resolves are shimmed as synchronous during SSR)\n      if (!sync) {\n        forceRender(true);\n      } else {\n        owners.length = 0;\n      }\n    });\n\n    var reject = once(function (reason) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Failed to resolve async component: \" + (String(factory)) +\n        (reason ? (\"\\nReason: \" + reason) : '')\n      );\n      if (isDef(factory.errorComp)) {\n        factory.error = true;\n        forceRender(true);\n      }\n    });\n\n    var res = factory(resolve, reject);\n\n    if (isObject(res)) {\n      if (isPromise(res)) {\n        // () => Promise\n        if (isUndef(factory.resolved)) {\n          res.then(resolve, reject);\n        }\n      } else if (isPromise(res.component)) {\n        res.component.then(resolve, reject);\n\n        if (isDef(res.error)) {\n          factory.errorComp = ensureCtor(res.error, baseCtor);\n        }\n\n        if (isDef(res.loading)) {\n          factory.loadingComp = ensureCtor(res.loading, baseCtor);\n          if (res.delay === 0) {\n            factory.loading = true;\n          } else {\n            timerLoading = setTimeout(function () {\n              timerLoading = null;\n              if (isUndef(factory.resolved) && isUndef(factory.error)) {\n                factory.loading = true;\n                forceRender(false);\n              }\n            }, res.delay || 200);\n          }\n        }\n\n        if (isDef(res.timeout)) {\n          timerTimeout = setTimeout(function () {\n            timerTimeout = null;\n            if (isUndef(factory.resolved)) {\n              reject(\n                process.env.NODE_ENV !== 'production'\n                  ? (\"timeout (\" + (res.timeout) + \"ms)\")\n                  : null\n              );\n            }\n          }, res.timeout);\n        }\n      }\n    }\n\n    sync = false;\n    // return in case resolved synchronously\n    return factory.loading\n      ? factory.loadingComp\n      : factory.resolved\n  }\n}\n\n/*  */\n\nfunction isAsyncPlaceholder (node) {\n  return node.isComment && node.asyncFactory\n}\n\n/*  */\n\nfunction getFirstComponentChild (children) {\n  if (Array.isArray(children)) {\n    for (var i = 0; i < children.length; i++) {\n      var c = children[i];\n      if (isDef(c) && (isDef(c.componentOptions) || isAsyncPlaceholder(c))) {\n        return c\n      }\n    }\n  }\n}\n\n/*  */\n\n/*  */\n\nfunction initEvents (vm) {\n  vm._events = Object.create(null);\n  vm._hasHookEvent = false;\n  // init parent attached events\n  var listeners = vm.$options._parentListeners;\n  if (listeners) {\n    updateComponentListeners(vm, listeners);\n  }\n}\n\nvar target;\n\nfunction add (event, fn) {\n  target.$on(event, fn);\n}\n\nfunction remove$1 (event, fn) {\n  target.$off(event, fn);\n}\n\nfunction createOnceHandler (event, fn) {\n  var _target = target;\n  return function onceHandler () {\n    var res = fn.apply(null, arguments);\n    if (res !== null) {\n      _target.$off(event, onceHandler);\n    }\n  }\n}\n\nfunction updateComponentListeners (\n  vm,\n  listeners,\n  oldListeners\n) {\n  target = vm;\n  updateListeners(listeners, oldListeners || {}, add, remove$1, createOnceHandler, vm);\n  target = undefined;\n}\n\nfunction eventsMixin (Vue) {\n  var hookRE = /^hook:/;\n  Vue.prototype.$on = function (event, fn) {\n    var vm = this;\n    if (Array.isArray(event)) {\n      for (var i = 0, l = event.length; i < l; i++) {\n        vm.$on(event[i], fn);\n      }\n    } else {\n      (vm._events[event] || (vm._events[event] = [])).push(fn);\n      // optimize hook:event cost by using a boolean flag marked at registration\n      // instead of a hash lookup\n      if (hookRE.test(event)) {\n        vm._hasHookEvent = true;\n      }\n    }\n    return vm\n  };\n\n  Vue.prototype.$once = function (event, fn) {\n    var vm = this;\n    function on () {\n      vm.$off(event, on);\n      fn.apply(vm, arguments);\n    }\n    on.fn = fn;\n    vm.$on(event, on);\n    return vm\n  };\n\n  Vue.prototype.$off = function (event, fn) {\n    var vm = this;\n    // all\n    if (!arguments.length) {\n      vm._events = Object.create(null);\n      return vm\n    }\n    // array of events\n    if (Array.isArray(event)) {\n      for (var i$1 = 0, l = event.length; i$1 < l; i$1++) {\n        vm.$off(event[i$1], fn);\n      }\n      return vm\n    }\n    // specific event\n    var cbs = vm._events[event];\n    if (!cbs) {\n      return vm\n    }\n    if (!fn) {\n      vm._events[event] = null;\n      return vm\n    }\n    // specific handler\n    var cb;\n    var i = cbs.length;\n    while (i--) {\n      cb = cbs[i];\n      if (cb === fn || cb.fn === fn) {\n        cbs.splice(i, 1);\n        break\n      }\n    }\n    return vm\n  };\n\n  Vue.prototype.$emit = function (event) {\n    var vm = this;\n    if (process.env.NODE_ENV !== 'production') {\n      var lowerCaseEvent = event.toLowerCase();\n      if (lowerCaseEvent !== event && vm._events[lowerCaseEvent]) {\n        tip(\n          \"Event \\\"\" + lowerCaseEvent + \"\\\" is emitted in component \" +\n          (formatComponentName(vm)) + \" but the handler is registered for \\\"\" + event + \"\\\". \" +\n          \"Note that HTML attributes are case-insensitive and you cannot use \" +\n          \"v-on to listen to camelCase events when using in-DOM templates. \" +\n          \"You should probably use \\\"\" + (hyphenate(event)) + \"\\\" instead of \\\"\" + event + \"\\\".\"\n        );\n      }\n    }\n    var cbs = vm._events[event];\n    if (cbs) {\n      cbs = cbs.length > 1 ? toArray(cbs) : cbs;\n      var args = toArray(arguments, 1);\n      var info = \"event handler for \\\"\" + event + \"\\\"\";\n      for (var i = 0, l = cbs.length; i < l; i++) {\n        invokeWithErrorHandling(cbs[i], vm, args, vm, info);\n      }\n    }\n    return vm\n  };\n}\n\n/*  */\n\nvar activeInstance = null;\nvar isUpdatingChildComponent = false;\n\nfunction setActiveInstance(vm) {\n  var prevActiveInstance = activeInstance;\n  activeInstance = vm;\n  return function () {\n    activeInstance = prevActiveInstance;\n  }\n}\n\nfunction initLifecycle (vm) {\n  var options = vm.$options;\n\n  // locate first non-abstract parent\n  var parent = options.parent;\n  if (parent && !options.abstract) {\n    while (parent.$options.abstract && parent.$parent) {\n      parent = parent.$parent;\n    }\n    parent.$children.push(vm);\n  }\n\n  vm.$parent = parent;\n  vm.$root = parent ? parent.$root : vm;\n\n  vm.$children = [];\n  vm.$refs = {};\n\n  vm._watcher = null;\n  vm._inactive = null;\n  vm._directInactive = false;\n  vm._isMounted = false;\n  vm._isDestroyed = false;\n  vm._isBeingDestroyed = false;\n}\n\nfunction lifecycleMixin (Vue) {\n  Vue.prototype._update = function (vnode, hydrating) {\n    var vm = this;\n    var prevEl = vm.$el;\n    var prevVnode = vm._vnode;\n    var restoreActiveInstance = setActiveInstance(vm);\n    vm._vnode = vnode;\n    // Vue.prototype.__patch__ is injected in entry points\n    // based on the rendering backend used.\n    if (!prevVnode) {\n      // initial render\n      vm.$el = vm.__patch__(vm.$el, vnode, hydrating, false /* removeOnly */);\n    } else {\n      // updates\n      vm.$el = vm.__patch__(prevVnode, vnode);\n    }\n    restoreActiveInstance();\n    // update __vue__ reference\n    if (prevEl) {\n      prevEl.__vue__ = null;\n    }\n    if (vm.$el) {\n      vm.$el.__vue__ = vm;\n    }\n    // if parent is an HOC, update its $el as well\n    if (vm.$vnode && vm.$parent && vm.$vnode === vm.$parent._vnode) {\n      vm.$parent.$el = vm.$el;\n    }\n    // updated hook is called by the scheduler to ensure that children are\n    // updated in a parent's updated hook.\n  };\n\n  Vue.prototype.$forceUpdate = function () {\n    var vm = this;\n    if (vm._watcher) {\n      vm._watcher.update();\n    }\n  };\n\n  Vue.prototype.$destroy = function () {\n    var vm = this;\n    if (vm._isBeingDestroyed) {\n      return\n    }\n    callHook(vm, 'beforeDestroy');\n    vm._isBeingDestroyed = true;\n    // remove self from parent\n    var parent = vm.$parent;\n    if (parent && !parent._isBeingDestroyed && !vm.$options.abstract) {\n      remove(parent.$children, vm);\n    }\n    // teardown watchers\n    if (vm._watcher) {\n      vm._watcher.teardown();\n    }\n    var i = vm._watchers.length;\n    while (i--) {\n      vm._watchers[i].teardown();\n    }\n    // remove reference from data ob\n    // frozen object may not have observer.\n    if (vm._data.__ob__) {\n      vm._data.__ob__.vmCount--;\n    }\n    // call the last hook...\n    vm._isDestroyed = true;\n    // invoke destroy hooks on current rendered tree\n    vm.__patch__(vm._vnode, null);\n    // fire destroyed hook\n    callHook(vm, 'destroyed');\n    // turn off all instance listeners.\n    vm.$off();\n    // remove __vue__ reference\n    if (vm.$el) {\n      vm.$el.__vue__ = null;\n    }\n    // release circular reference (#6759)\n    if (vm.$vnode) {\n      vm.$vnode.parent = null;\n    }\n  };\n}\n\nfunction updateChildComponent (\n  vm,\n  propsData,\n  listeners,\n  parentVnode,\n  renderChildren\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    isUpdatingChildComponent = true;\n  }\n\n  // determine whether component has slot children\n  // we need to do this before overwriting $options._renderChildren.\n\n  // check if there are dynamic scopedSlots (hand-written or compiled but with\n  // dynamic slot names). Static scoped slots compiled from template has the\n  // \"$stable\" marker.\n  var newScopedSlots = parentVnode.data.scopedSlots;\n  var oldScopedSlots = vm.$scopedSlots;\n  var hasDynamicScopedSlot = !!(\n    (newScopedSlots && !newScopedSlots.$stable) ||\n    (oldScopedSlots !== emptyObject && !oldScopedSlots.$stable) ||\n    (newScopedSlots && vm.$scopedSlots.$key !== newScopedSlots.$key)\n  );\n\n  // Any static slot children from the parent may have changed during parent's\n  // update. Dynamic scoped slots may also have changed. In such cases, a forced\n  // update is necessary to ensure correctness.\n  var needsForceUpdate = !!(\n    renderChildren ||               // has new static slots\n    vm.$options._renderChildren ||  // has old static slots\n    hasDynamicScopedSlot\n  );\n\n  vm.$options._parentVnode = parentVnode;\n  vm.$vnode = parentVnode; // update vm's placeholder node without re-render\n\n  if (vm._vnode) { // update child tree's parent\n    vm._vnode.parent = parentVnode;\n  }\n  vm.$options._renderChildren = renderChildren;\n\n  // update $attrs and $listeners hash\n  // these are also reactive so they may trigger child update if the child\n  // used them during render\n  vm.$attrs = parentVnode.data.attrs || emptyObject;\n  vm.$listeners = listeners || emptyObject;\n\n  // update props\n  if (propsData && vm.$options.props) {\n    toggleObserving(false);\n    var props = vm._props;\n    var propKeys = vm.$options._propKeys || [];\n    for (var i = 0; i < propKeys.length; i++) {\n      var key = propKeys[i];\n      var propOptions = vm.$options.props; // wtf flow?\n      props[key] = validateProp(key, propOptions, propsData, vm);\n    }\n    toggleObserving(true);\n    // keep a copy of raw propsData\n    vm.$options.propsData = propsData;\n  }\n  \n  // fixed by xxxxxx update properties(mp runtime)\n  vm._$updateProperties && vm._$updateProperties(vm);\n  \n  // update listeners\n  listeners = listeners || emptyObject;\n  var oldListeners = vm.$options._parentListeners;\n  vm.$options._parentListeners = listeners;\n  updateComponentListeners(vm, listeners, oldListeners);\n\n  // resolve slots + force update if has children\n  if (needsForceUpdate) {\n    vm.$slots = resolveSlots(renderChildren, parentVnode.context);\n    vm.$forceUpdate();\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    isUpdatingChildComponent = false;\n  }\n}\n\nfunction isInInactiveTree (vm) {\n  while (vm && (vm = vm.$parent)) {\n    if (vm._inactive) { return true }\n  }\n  return false\n}\n\nfunction activateChildComponent (vm, direct) {\n  if (direct) {\n    vm._directInactive = false;\n    if (isInInactiveTree(vm)) {\n      return\n    }\n  } else if (vm._directInactive) {\n    return\n  }\n  if (vm._inactive || vm._inactive === null) {\n    vm._inactive = false;\n    for (var i = 0; i < vm.$children.length; i++) {\n      activateChildComponent(vm.$children[i]);\n    }\n    callHook(vm, 'activated');\n  }\n}\n\nfunction deactivateChildComponent (vm, direct) {\n  if (direct) {\n    vm._directInactive = true;\n    if (isInInactiveTree(vm)) {\n      return\n    }\n  }\n  if (!vm._inactive) {\n    vm._inactive = true;\n    for (var i = 0; i < vm.$children.length; i++) {\n      deactivateChildComponent(vm.$children[i]);\n    }\n    callHook(vm, 'deactivated');\n  }\n}\n\nfunction callHook (vm, hook) {\n  // #7573 disable dep collection when invoking lifecycle hooks\n  pushTarget();\n  var handlers = vm.$options[hook];\n  var info = hook + \" hook\";\n  if (handlers) {\n    for (var i = 0, j = handlers.length; i < j; i++) {\n      invokeWithErrorHandling(handlers[i], vm, null, vm, info);\n    }\n  }\n  if (vm._hasHookEvent) {\n    vm.$emit('hook:' + hook);\n  }\n  popTarget();\n}\n\n/*  */\n\nvar MAX_UPDATE_COUNT = 100;\n\nvar queue = [];\nvar activatedChildren = [];\nvar has = {};\nvar circular = {};\nvar waiting = false;\nvar flushing = false;\nvar index = 0;\n\n/**\n * Reset the scheduler's state.\n */\nfunction resetSchedulerState () {\n  index = queue.length = activatedChildren.length = 0;\n  has = {};\n  if (process.env.NODE_ENV !== 'production') {\n    circular = {};\n  }\n  waiting = flushing = false;\n}\n\n// Async edge case #6566 requires saving the timestamp when event listeners are\n// attached. However, calling performance.now() has a perf overhead especially\n// if the page has thousands of event listeners. Instead, we take a timestamp\n// every time the scheduler flushes and use that for all event listeners\n// attached during that flush.\nvar currentFlushTimestamp = 0;\n\n// Async edge case fix requires storing an event listener's attach timestamp.\nvar getNow = Date.now;\n\n// Determine what event timestamp the browser is using. Annoyingly, the\n// timestamp can either be hi-res (relative to page load) or low-res\n// (relative to UNIX epoch), so in order to compare time we have to use the\n// same timestamp type when saving the flush timestamp.\n// All IE versions use low-res event timestamps, and have problematic clock\n// implementations (#9632)\nif (inBrowser && !isIE) {\n  var performance = window.performance;\n  if (\n    performance &&\n    typeof performance.now === 'function' &&\n    getNow() > document.createEvent('Event').timeStamp\n  ) {\n    // if the event timestamp, although evaluated AFTER the Date.now(), is\n    // smaller than it, it means the event is using a hi-res timestamp,\n    // and we need to use the hi-res version for event listener timestamps as\n    // well.\n    getNow = function () { return performance.now(); };\n  }\n}\n\n/**\n * Flush both queues and run the watchers.\n */\nfunction flushSchedulerQueue () {\n  currentFlushTimestamp = getNow();\n  flushing = true;\n  var watcher, id;\n\n  // Sort queue before flush.\n  // This ensures that:\n  // 1. Components are updated from parent to child. (because parent is always\n  //    created before the child)\n  // 2. A component's user watchers are run before its render watcher (because\n  //    user watchers are created before the render watcher)\n  // 3. If a component is destroyed during a parent component's watcher run,\n  //    its watchers can be skipped.\n  queue.sort(function (a, b) { return a.id - b.id; });\n\n  // do not cache length because more watchers might be pushed\n  // as we run existing watchers\n  for (index = 0; index < queue.length; index++) {\n    watcher = queue[index];\n    if (watcher.before) {\n      watcher.before();\n    }\n    id = watcher.id;\n    has[id] = null;\n    watcher.run();\n    // in dev build, check and stop circular updates.\n    if (process.env.NODE_ENV !== 'production' && has[id] != null) {\n      circular[id] = (circular[id] || 0) + 1;\n      if (circular[id] > MAX_UPDATE_COUNT) {\n        warn(\n          'You may have an infinite update loop ' + (\n            watcher.user\n              ? (\"in watcher with expression \\\"\" + (watcher.expression) + \"\\\"\")\n              : \"in a component render function.\"\n          ),\n          watcher.vm\n        );\n        break\n      }\n    }\n  }\n\n  // keep copies of post queues before resetting state\n  var activatedQueue = activatedChildren.slice();\n  var updatedQueue = queue.slice();\n\n  resetSchedulerState();\n\n  // call component updated and activated hooks\n  callActivatedHooks(activatedQueue);\n  callUpdatedHooks(updatedQueue);\n\n  // devtool hook\n  /* istanbul ignore if */\n  if (devtools && config.devtools) {\n    devtools.emit('flush');\n  }\n}\n\nfunction callUpdatedHooks (queue) {\n  var i = queue.length;\n  while (i--) {\n    var watcher = queue[i];\n    var vm = watcher.vm;\n    if (vm._watcher === watcher && vm._isMounted && !vm._isDestroyed) {\n      callHook(vm, 'updated');\n    }\n  }\n}\n\n/**\n * Queue a kept-alive component that was activated during patch.\n * The queue will be processed after the entire tree has been patched.\n */\nfunction queueActivatedComponent (vm) {\n  // setting _inactive to false here so that a render function can\n  // rely on checking whether it's in an inactive tree (e.g. router-view)\n  vm._inactive = false;\n  activatedChildren.push(vm);\n}\n\nfunction callActivatedHooks (queue) {\n  for (var i = 0; i < queue.length; i++) {\n    queue[i]._inactive = true;\n    activateChildComponent(queue[i], true /* true */);\n  }\n}\n\n/**\n * Push a watcher into the watcher queue.\n * Jobs with duplicate IDs will be skipped unless it's\n * pushed when the queue is being flushed.\n */\nfunction queueWatcher (watcher) {\n  var id = watcher.id;\n  if (has[id] == null) {\n    has[id] = true;\n    if (!flushing) {\n      queue.push(watcher);\n    } else {\n      // if already flushing, splice the watcher based on its id\n      // if already past its id, it will be run next immediately.\n      var i = queue.length - 1;\n      while (i > index && queue[i].id > watcher.id) {\n        i--;\n      }\n      queue.splice(i + 1, 0, watcher);\n    }\n    // queue the flush\n    if (!waiting) {\n      waiting = true;\n\n      if (process.env.NODE_ENV !== 'production' && !config.async) {\n        flushSchedulerQueue();\n        return\n      }\n      nextTick(flushSchedulerQueue);\n    }\n  }\n}\n\n/*  */\n\n\n\nvar uid$2 = 0;\n\n/**\n * A watcher parses an expression, collects dependencies,\n * and fires callback when the expression value changes.\n * This is used for both the $watch() api and directives.\n */\nvar Watcher = function Watcher (\n  vm,\n  expOrFn,\n  cb,\n  options,\n  isRenderWatcher\n) {\n  this.vm = vm;\n  if (isRenderWatcher) {\n    vm._watcher = this;\n  }\n  vm._watchers.push(this);\n  // options\n  if (options) {\n    this.deep = !!options.deep;\n    this.user = !!options.user;\n    this.lazy = !!options.lazy;\n    this.sync = !!options.sync;\n    this.before = options.before;\n  } else {\n    this.deep = this.user = this.lazy = this.sync = false;\n  }\n  this.cb = cb;\n  this.id = ++uid$2; // uid for batching\n  this.active = true;\n  this.dirty = this.lazy; // for lazy watchers\n  this.deps = [];\n  this.newDeps = [];\n  this.depIds = new _Set();\n  this.newDepIds = new _Set();\n  this.expression = process.env.NODE_ENV !== 'production'\n    ? expOrFn.toString()\n    : '';\n  // parse expression for getter\n  if (typeof expOrFn === 'function') {\n    this.getter = expOrFn;\n  } else {\n    this.getter = parsePath(expOrFn);\n    if (!this.getter) {\n      this.getter = noop;\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Failed watching path: \\\"\" + expOrFn + \"\\\" \" +\n        'Watcher only accepts simple dot-delimited paths. ' +\n        'For full control, use a function instead.',\n        vm\n      );\n    }\n  }\n  this.value = this.lazy\n    ? undefined\n    : this.get();\n};\n\n/**\n * Evaluate the getter, and re-collect dependencies.\n */\nWatcher.prototype.get = function get () {\n  pushTarget(this);\n  var value;\n  var vm = this.vm;\n  try {\n    value = this.getter.call(vm, vm);\n  } catch (e) {\n    if (this.user) {\n      handleError(e, vm, (\"getter for watcher \\\"\" + (this.expression) + \"\\\"\"));\n    } else {\n      throw e\n    }\n  } finally {\n    // \"touch\" every property so they are all tracked as\n    // dependencies for deep watching\n    if (this.deep) {\n      traverse(value);\n    }\n    popTarget();\n    this.cleanupDeps();\n  }\n  return value\n};\n\n/**\n * Add a dependency to this directive.\n */\nWatcher.prototype.addDep = function addDep (dep) {\n  var id = dep.id;\n  if (!this.newDepIds.has(id)) {\n    this.newDepIds.add(id);\n    this.newDeps.push(dep);\n    if (!this.depIds.has(id)) {\n      dep.addSub(this);\n    }\n  }\n};\n\n/**\n * Clean up for dependency collection.\n */\nWatcher.prototype.cleanupDeps = function cleanupDeps () {\n  var i = this.deps.length;\n  while (i--) {\n    var dep = this.deps[i];\n    if (!this.newDepIds.has(dep.id)) {\n      dep.removeSub(this);\n    }\n  }\n  var tmp = this.depIds;\n  this.depIds = this.newDepIds;\n  this.newDepIds = tmp;\n  this.newDepIds.clear();\n  tmp = this.deps;\n  this.deps = this.newDeps;\n  this.newDeps = tmp;\n  this.newDeps.length = 0;\n};\n\n/**\n * Subscriber interface.\n * Will be called when a dependency changes.\n */\nWatcher.prototype.update = function update () {\n  /* istanbul ignore else */\n  if (this.lazy) {\n    this.dirty = true;\n  } else if (this.sync) {\n    this.run();\n  } else {\n    queueWatcher(this);\n  }\n};\n\n/**\n * Scheduler job interface.\n * Will be called by the scheduler.\n */\nWatcher.prototype.run = function run () {\n  if (this.active) {\n    var value = this.get();\n    if (\n      value !== this.value ||\n      // Deep watchers and watchers on Object/Arrays should fire even\n      // when the value is the same, because the value may\n      // have mutated.\n      isObject(value) ||\n      this.deep\n    ) {\n      // set new value\n      var oldValue = this.value;\n      this.value = value;\n      if (this.user) {\n        try {\n          this.cb.call(this.vm, value, oldValue);\n        } catch (e) {\n          handleError(e, this.vm, (\"callback for watcher \\\"\" + (this.expression) + \"\\\"\"));\n        }\n      } else {\n        this.cb.call(this.vm, value, oldValue);\n      }\n    }\n  }\n};\n\n/**\n * Evaluate the value of the watcher.\n * This only gets called for lazy watchers.\n */\nWatcher.prototype.evaluate = function evaluate () {\n  this.value = this.get();\n  this.dirty = false;\n};\n\n/**\n * Depend on all deps collected by this watcher.\n */\nWatcher.prototype.depend = function depend () {\n  var i = this.deps.length;\n  while (i--) {\n    this.deps[i].depend();\n  }\n};\n\n/**\n * Remove self from all dependencies' subscriber list.\n */\nWatcher.prototype.teardown = function teardown () {\n  if (this.active) {\n    // remove self from vm's watcher list\n    // this is a somewhat expensive operation so we skip it\n    // if the vm is being destroyed.\n    if (!this.vm._isBeingDestroyed) {\n      remove(this.vm._watchers, this);\n    }\n    var i = this.deps.length;\n    while (i--) {\n      this.deps[i].removeSub(this);\n    }\n    this.active = false;\n  }\n};\n\n/*  */\n\nvar sharedPropertyDefinition = {\n  enumerable: true,\n  configurable: true,\n  get: noop,\n  set: noop\n};\n\nfunction proxy (target, sourceKey, key) {\n  sharedPropertyDefinition.get = function proxyGetter () {\n    return this[sourceKey][key]\n  };\n  sharedPropertyDefinition.set = function proxySetter (val) {\n    this[sourceKey][key] = val;\n  };\n  Object.defineProperty(target, key, sharedPropertyDefinition);\n}\n\nfunction initState (vm) {\n  vm._watchers = [];\n  var opts = vm.$options;\n  if (opts.props) { initProps(vm, opts.props); }\n  if (opts.methods) { initMethods(vm, opts.methods); }\n  if (opts.data) {\n    initData(vm);\n  } else {\n    observe(vm._data = {}, true /* asRootData */);\n  }\n  if (opts.computed) { initComputed(vm, opts.computed); }\n  if (opts.watch && opts.watch !== nativeWatch) {\n    initWatch(vm, opts.watch);\n  }\n}\n\nfunction initProps (vm, propsOptions) {\n  var propsData = vm.$options.propsData || {};\n  var props = vm._props = {};\n  // cache prop keys so that future props updates can iterate using Array\n  // instead of dynamic object key enumeration.\n  var keys = vm.$options._propKeys = [];\n  var isRoot = !vm.$parent;\n  // root instance props should be converted\n  if (!isRoot) {\n    toggleObserving(false);\n  }\n  var loop = function ( key ) {\n    keys.push(key);\n    var value = validateProp(key, propsOptions, propsData, vm);\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      var hyphenatedKey = hyphenate(key);\n      if (isReservedAttribute(hyphenatedKey) ||\n          config.isReservedAttr(hyphenatedKey)) {\n        warn(\n          (\"\\\"\" + hyphenatedKey + \"\\\" is a reserved attribute and cannot be used as component prop.\"),\n          vm\n        );\n      }\n      defineReactive$$1(props, key, value, function () {\n        if (!isRoot && !isUpdatingChildComponent) {\n          {\n            if(vm.mpHost === 'mp-baidu' || vm.mpHost === 'mp-kuaishou' || vm.mpHost === 'mp-xhs'){//百度、快手、小红书 observer 在 setData callback 之后触发，直接忽略该 warn\n                return\n            }\n            //fixed by xxxxxx __next_tick_pending,uni://form-field 时不告警\n            if(\n                key === 'value' && \n                Array.isArray(vm.$options.behaviors) &&\n                vm.$options.behaviors.indexOf('uni://form-field') !== -1\n              ){\n              return\n            }\n            if(vm._getFormData){\n              return\n            }\n            var $parent = vm.$parent;\n            while($parent){\n              if($parent.__next_tick_pending){\n                return  \n              }\n              $parent = $parent.$parent;\n            }\n          }\n          warn(\n            \"Avoid mutating a prop directly since the value will be \" +\n            \"overwritten whenever the parent component re-renders. \" +\n            \"Instead, use a data or computed property based on the prop's \" +\n            \"value. Prop being mutated: \\\"\" + key + \"\\\"\",\n            vm\n          );\n        }\n      });\n    } else {\n      defineReactive$$1(props, key, value);\n    }\n    // static props are already proxied on the component's prototype\n    // during Vue.extend(). We only need to proxy props defined at\n    // instantiation here.\n    if (!(key in vm)) {\n      proxy(vm, \"_props\", key);\n    }\n  };\n\n  for (var key in propsOptions) loop( key );\n  toggleObserving(true);\n}\n\nfunction initData (vm) {\n  var data = vm.$options.data;\n  data = vm._data = typeof data === 'function'\n    ? getData(data, vm)\n    : data || {};\n  if (!isPlainObject(data)) {\n    data = {};\n    process.env.NODE_ENV !== 'production' && warn(\n      'data functions should return an object:\\n' +\n      'https://vuejs.org/v2/guide/components.html#data-Must-Be-a-Function',\n      vm\n    );\n  }\n  // proxy data on instance\n  var keys = Object.keys(data);\n  var props = vm.$options.props;\n  var methods = vm.$options.methods;\n  var i = keys.length;\n  while (i--) {\n    var key = keys[i];\n    if (process.env.NODE_ENV !== 'production') {\n      if (methods && hasOwn(methods, key)) {\n        warn(\n          (\"Method \\\"\" + key + \"\\\" has already been defined as a data property.\"),\n          vm\n        );\n      }\n    }\n    if (props && hasOwn(props, key)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"The data property \\\"\" + key + \"\\\" is already declared as a prop. \" +\n        \"Use prop default value instead.\",\n        vm\n      );\n    } else if (!isReserved(key)) {\n      proxy(vm, \"_data\", key);\n    }\n  }\n  // observe data\n  observe(data, true /* asRootData */);\n}\n\nfunction getData (data, vm) {\n  // #7573 disable dep collection when invoking data getters\n  pushTarget();\n  try {\n    return data.call(vm, vm)\n  } catch (e) {\n    handleError(e, vm, \"data()\");\n    return {}\n  } finally {\n    popTarget();\n  }\n}\n\nvar computedWatcherOptions = { lazy: true };\n\nfunction initComputed (vm, computed) {\n  // $flow-disable-line\n  var watchers = vm._computedWatchers = Object.create(null);\n  // computed properties are just getters during SSR\n  var isSSR = isServerRendering();\n\n  for (var key in computed) {\n    var userDef = computed[key];\n    var getter = typeof userDef === 'function' ? userDef : userDef.get;\n    if (process.env.NODE_ENV !== 'production' && getter == null) {\n      warn(\n        (\"Getter is missing for computed property \\\"\" + key + \"\\\".\"),\n        vm\n      );\n    }\n\n    if (!isSSR) {\n      // create internal watcher for the computed property.\n      watchers[key] = new Watcher(\n        vm,\n        getter || noop,\n        noop,\n        computedWatcherOptions\n      );\n    }\n\n    // component-defined computed properties are already defined on the\n    // component prototype. We only need to define computed properties defined\n    // at instantiation here.\n    if (!(key in vm)) {\n      defineComputed(vm, key, userDef);\n    } else if (process.env.NODE_ENV !== 'production') {\n      if (key in vm.$data) {\n        warn((\"The computed property \\\"\" + key + \"\\\" is already defined in data.\"), vm);\n      } else if (vm.$options.props && key in vm.$options.props) {\n        warn((\"The computed property \\\"\" + key + \"\\\" is already defined as a prop.\"), vm);\n      }\n    }\n  }\n}\n\nfunction defineComputed (\n  target,\n  key,\n  userDef\n) {\n  var shouldCache = !isServerRendering();\n  if (typeof userDef === 'function') {\n    sharedPropertyDefinition.get = shouldCache\n      ? createComputedGetter(key)\n      : createGetterInvoker(userDef);\n    sharedPropertyDefinition.set = noop;\n  } else {\n    sharedPropertyDefinition.get = userDef.get\n      ? shouldCache && userDef.cache !== false\n        ? createComputedGetter(key)\n        : createGetterInvoker(userDef.get)\n      : noop;\n    sharedPropertyDefinition.set = userDef.set || noop;\n  }\n  if (process.env.NODE_ENV !== 'production' &&\n      sharedPropertyDefinition.set === noop) {\n    sharedPropertyDefinition.set = function () {\n      warn(\n        (\"Computed property \\\"\" + key + \"\\\" was assigned to but it has no setter.\"),\n        this\n      );\n    };\n  }\n  Object.defineProperty(target, key, sharedPropertyDefinition);\n}\n\nfunction createComputedGetter (key) {\n  return function computedGetter () {\n    var watcher = this._computedWatchers && this._computedWatchers[key];\n    if (watcher) {\n      if (watcher.dirty) {\n        watcher.evaluate();\n      }\n      if (Dep.SharedObject.target) {// fixed by xxxxxx\n        watcher.depend();\n      }\n      return watcher.value\n    }\n  }\n}\n\nfunction createGetterInvoker(fn) {\n  return function computedGetter () {\n    return fn.call(this, this)\n  }\n}\n\nfunction initMethods (vm, methods) {\n  var props = vm.$options.props;\n  for (var key in methods) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof methods[key] !== 'function') {\n        warn(\n          \"Method \\\"\" + key + \"\\\" has type \\\"\" + (typeof methods[key]) + \"\\\" in the component definition. \" +\n          \"Did you reference the function correctly?\",\n          vm\n        );\n      }\n      if (props && hasOwn(props, key)) {\n        warn(\n          (\"Method \\\"\" + key + \"\\\" has already been defined as a prop.\"),\n          vm\n        );\n      }\n      if ((key in vm) && isReserved(key)) {\n        warn(\n          \"Method \\\"\" + key + \"\\\" conflicts with an existing Vue instance method. \" +\n          \"Avoid defining component methods that start with _ or $.\"\n        );\n      }\n    }\n    vm[key] = typeof methods[key] !== 'function' ? noop : bind(methods[key], vm);\n  }\n}\n\nfunction initWatch (vm, watch) {\n  for (var key in watch) {\n    var handler = watch[key];\n    if (Array.isArray(handler)) {\n      for (var i = 0; i < handler.length; i++) {\n        createWatcher(vm, key, handler[i]);\n      }\n    } else {\n      createWatcher(vm, key, handler);\n    }\n  }\n}\n\nfunction createWatcher (\n  vm,\n  expOrFn,\n  handler,\n  options\n) {\n  if (isPlainObject(handler)) {\n    options = handler;\n    handler = handler.handler;\n  }\n  if (typeof handler === 'string') {\n    handler = vm[handler];\n  }\n  return vm.$watch(expOrFn, handler, options)\n}\n\nfunction stateMixin (Vue) {\n  // flow somehow has problems with directly declared definition object\n  // when using Object.defineProperty, so we have to procedurally build up\n  // the object here.\n  var dataDef = {};\n  dataDef.get = function () { return this._data };\n  var propsDef = {};\n  propsDef.get = function () { return this._props };\n  if (process.env.NODE_ENV !== 'production') {\n    dataDef.set = function () {\n      warn(\n        'Avoid replacing instance root $data. ' +\n        'Use nested data properties instead.',\n        this\n      );\n    };\n    propsDef.set = function () {\n      warn(\"$props is readonly.\", this);\n    };\n  }\n  Object.defineProperty(Vue.prototype, '$data', dataDef);\n  Object.defineProperty(Vue.prototype, '$props', propsDef);\n\n  Vue.prototype.$set = set;\n  Vue.prototype.$delete = del;\n\n  Vue.prototype.$watch = function (\n    expOrFn,\n    cb,\n    options\n  ) {\n    var vm = this;\n    if (isPlainObject(cb)) {\n      return createWatcher(vm, expOrFn, cb, options)\n    }\n    options = options || {};\n    options.user = true;\n    var watcher = new Watcher(vm, expOrFn, cb, options);\n    if (options.immediate) {\n      try {\n        cb.call(vm, watcher.value);\n      } catch (error) {\n        handleError(error, vm, (\"callback for immediate watcher \\\"\" + (watcher.expression) + \"\\\"\"));\n      }\n    }\n    return function unwatchFn () {\n      watcher.teardown();\n    }\n  };\n}\n\n/*  */\n\nvar uid$3 = 0;\n\nfunction initMixin (Vue) {\n  Vue.prototype._init = function (options) {\n    var vm = this;\n    // a uid\n    vm._uid = uid$3++;\n\n    var startTag, endTag;\n    /* istanbul ignore if */\n    if (process.env.NODE_ENV !== 'production' && config.performance && mark) {\n      startTag = \"vue-perf-start:\" + (vm._uid);\n      endTag = \"vue-perf-end:\" + (vm._uid);\n      mark(startTag);\n    }\n\n    // a flag to avoid this being observed\n    vm._isVue = true;\n    // merge options\n    if (options && options._isComponent) {\n      // optimize internal component instantiation\n      // since dynamic options merging is pretty slow, and none of the\n      // internal component options needs special treatment.\n      initInternalComponent(vm, options);\n    } else {\n      vm.$options = mergeOptions(\n        resolveConstructorOptions(vm.constructor),\n        options || {},\n        vm\n      );\n    }\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      initProxy(vm);\n    } else {\n      vm._renderProxy = vm;\n    }\n    // expose real self\n    vm._self = vm;\n    initLifecycle(vm);\n    initEvents(vm);\n    initRender(vm);\n    callHook(vm, 'beforeCreate');\n    !vm._$fallback && initInjections(vm); // resolve injections before data/props  \n    initState(vm);\n    !vm._$fallback && initProvide(vm); // resolve provide after data/props\n    !vm._$fallback && callHook(vm, 'created');      \n\n    /* istanbul ignore if */\n    if (process.env.NODE_ENV !== 'production' && config.performance && mark) {\n      vm._name = formatComponentName(vm, false);\n      mark(endTag);\n      measure((\"vue \" + (vm._name) + \" init\"), startTag, endTag);\n    }\n\n    if (vm.$options.el) {\n      vm.$mount(vm.$options.el);\n    }\n  };\n}\n\nfunction initInternalComponent (vm, options) {\n  var opts = vm.$options = Object.create(vm.constructor.options);\n  // doing this because it's faster than dynamic enumeration.\n  var parentVnode = options._parentVnode;\n  opts.parent = options.parent;\n  opts._parentVnode = parentVnode;\n\n  var vnodeComponentOptions = parentVnode.componentOptions;\n  opts.propsData = vnodeComponentOptions.propsData;\n  opts._parentListeners = vnodeComponentOptions.listeners;\n  opts._renderChildren = vnodeComponentOptions.children;\n  opts._componentTag = vnodeComponentOptions.tag;\n\n  if (options.render) {\n    opts.render = options.render;\n    opts.staticRenderFns = options.staticRenderFns;\n  }\n}\n\nfunction resolveConstructorOptions (Ctor) {\n  var options = Ctor.options;\n  if (Ctor.super) {\n    var superOptions = resolveConstructorOptions(Ctor.super);\n    var cachedSuperOptions = Ctor.superOptions;\n    if (superOptions !== cachedSuperOptions) {\n      // super option changed,\n      // need to resolve new options.\n      Ctor.superOptions = superOptions;\n      // check if there are any late-modified/attached options (#4976)\n      var modifiedOptions = resolveModifiedOptions(Ctor);\n      // update base extend options\n      if (modifiedOptions) {\n        extend(Ctor.extendOptions, modifiedOptions);\n      }\n      options = Ctor.options = mergeOptions(superOptions, Ctor.extendOptions);\n      if (options.name) {\n        options.components[options.name] = Ctor;\n      }\n    }\n  }\n  return options\n}\n\nfunction resolveModifiedOptions (Ctor) {\n  var modified;\n  var latest = Ctor.options;\n  var sealed = Ctor.sealedOptions;\n  for (var key in latest) {\n    if (latest[key] !== sealed[key]) {\n      if (!modified) { modified = {}; }\n      modified[key] = latest[key];\n    }\n  }\n  return modified\n}\n\nfunction Vue (options) {\n  if (process.env.NODE_ENV !== 'production' &&\n    !(this instanceof Vue)\n  ) {\n    warn('Vue is a constructor and should be called with the `new` keyword');\n  }\n  this._init(options);\n}\n\ninitMixin(Vue);\nstateMixin(Vue);\neventsMixin(Vue);\nlifecycleMixin(Vue);\nrenderMixin(Vue);\n\n/*  */\n\nfunction initUse (Vue) {\n  Vue.use = function (plugin) {\n    var installedPlugins = (this._installedPlugins || (this._installedPlugins = []));\n    if (installedPlugins.indexOf(plugin) > -1) {\n      return this\n    }\n\n    // additional parameters\n    var args = toArray(arguments, 1);\n    args.unshift(this);\n    if (typeof plugin.install === 'function') {\n      plugin.install.apply(plugin, args);\n    } else if (typeof plugin === 'function') {\n      plugin.apply(null, args);\n    }\n    installedPlugins.push(plugin);\n    return this\n  };\n}\n\n/*  */\n\nfunction initMixin$1 (Vue) {\n  Vue.mixin = function (mixin) {\n    this.options = mergeOptions(this.options, mixin);\n    return this\n  };\n}\n\n/*  */\n\nfunction initExtend (Vue) {\n  /**\n   * Each instance constructor, including Vue, has a unique\n   * cid. This enables us to create wrapped \"child\n   * constructors\" for prototypal inheritance and cache them.\n   */\n  Vue.cid = 0;\n  var cid = 1;\n\n  /**\n   * Class inheritance\n   */\n  Vue.extend = function (extendOptions) {\n    extendOptions = extendOptions || {};\n    var Super = this;\n    var SuperId = Super.cid;\n    var cachedCtors = extendOptions._Ctor || (extendOptions._Ctor = {});\n    if (cachedCtors[SuperId]) {\n      return cachedCtors[SuperId]\n    }\n\n    var name = extendOptions.name || Super.options.name;\n    if (process.env.NODE_ENV !== 'production' && name) {\n      validateComponentName(name);\n    }\n\n    var Sub = function VueComponent (options) {\n      this._init(options);\n    };\n    Sub.prototype = Object.create(Super.prototype);\n    Sub.prototype.constructor = Sub;\n    Sub.cid = cid++;\n    Sub.options = mergeOptions(\n      Super.options,\n      extendOptions\n    );\n    Sub['super'] = Super;\n\n    // For props and computed properties, we define the proxy getters on\n    // the Vue instances at extension time, on the extended prototype. This\n    // avoids Object.defineProperty calls for each instance created.\n    if (Sub.options.props) {\n      initProps$1(Sub);\n    }\n    if (Sub.options.computed) {\n      initComputed$1(Sub);\n    }\n\n    // allow further extension/mixin/plugin usage\n    Sub.extend = Super.extend;\n    Sub.mixin = Super.mixin;\n    Sub.use = Super.use;\n\n    // create asset registers, so extended classes\n    // can have their private assets too.\n    ASSET_TYPES.forEach(function (type) {\n      Sub[type] = Super[type];\n    });\n    // enable recursive self-lookup\n    if (name) {\n      Sub.options.components[name] = Sub;\n    }\n\n    // keep a reference to the super options at extension time.\n    // later at instantiation we can check if Super's options have\n    // been updated.\n    Sub.superOptions = Super.options;\n    Sub.extendOptions = extendOptions;\n    Sub.sealedOptions = extend({}, Sub.options);\n\n    // cache constructor\n    cachedCtors[SuperId] = Sub;\n    return Sub\n  };\n}\n\nfunction initProps$1 (Comp) {\n  var props = Comp.options.props;\n  for (var key in props) {\n    proxy(Comp.prototype, \"_props\", key);\n  }\n}\n\nfunction initComputed$1 (Comp) {\n  var computed = Comp.options.computed;\n  for (var key in computed) {\n    defineComputed(Comp.prototype, key, computed[key]);\n  }\n}\n\n/*  */\n\nfunction initAssetRegisters (Vue) {\n  /**\n   * Create asset registration methods.\n   */\n  ASSET_TYPES.forEach(function (type) {\n    Vue[type] = function (\n      id,\n      definition\n    ) {\n      if (!definition) {\n        return this.options[type + 's'][id]\n      } else {\n        /* istanbul ignore if */\n        if (process.env.NODE_ENV !== 'production' && type === 'component') {\n          validateComponentName(id);\n        }\n        if (type === 'component' && isPlainObject(definition)) {\n          definition.name = definition.name || id;\n          definition = this.options._base.extend(definition);\n        }\n        if (type === 'directive' && typeof definition === 'function') {\n          definition = { bind: definition, update: definition };\n        }\n        this.options[type + 's'][id] = definition;\n        return definition\n      }\n    };\n  });\n}\n\n/*  */\n\n\n\nfunction getComponentName (opts) {\n  return opts && (opts.Ctor.options.name || opts.tag)\n}\n\nfunction matches (pattern, name) {\n  if (Array.isArray(pattern)) {\n    return pattern.indexOf(name) > -1\n  } else if (typeof pattern === 'string') {\n    return pattern.split(',').indexOf(name) > -1\n  } else if (isRegExp(pattern)) {\n    return pattern.test(name)\n  }\n  /* istanbul ignore next */\n  return false\n}\n\nfunction pruneCache (keepAliveInstance, filter) {\n  var cache = keepAliveInstance.cache;\n  var keys = keepAliveInstance.keys;\n  var _vnode = keepAliveInstance._vnode;\n  for (var key in cache) {\n    var cachedNode = cache[key];\n    if (cachedNode) {\n      var name = getComponentName(cachedNode.componentOptions);\n      if (name && !filter(name)) {\n        pruneCacheEntry(cache, key, keys, _vnode);\n      }\n    }\n  }\n}\n\nfunction pruneCacheEntry (\n  cache,\n  key,\n  keys,\n  current\n) {\n  var cached$$1 = cache[key];\n  if (cached$$1 && (!current || cached$$1.tag !== current.tag)) {\n    cached$$1.componentInstance.$destroy();\n  }\n  cache[key] = null;\n  remove(keys, key);\n}\n\nvar patternTypes = [String, RegExp, Array];\n\nvar KeepAlive = {\n  name: 'keep-alive',\n  abstract: true,\n\n  props: {\n    include: patternTypes,\n    exclude: patternTypes,\n    max: [String, Number]\n  },\n\n  created: function created () {\n    this.cache = Object.create(null);\n    this.keys = [];\n  },\n\n  destroyed: function destroyed () {\n    for (var key in this.cache) {\n      pruneCacheEntry(this.cache, key, this.keys);\n    }\n  },\n\n  mounted: function mounted () {\n    var this$1 = this;\n\n    this.$watch('include', function (val) {\n      pruneCache(this$1, function (name) { return matches(val, name); });\n    });\n    this.$watch('exclude', function (val) {\n      pruneCache(this$1, function (name) { return !matches(val, name); });\n    });\n  },\n\n  render: function render () {\n    var slot = this.$slots.default;\n    var vnode = getFirstComponentChild(slot);\n    var componentOptions = vnode && vnode.componentOptions;\n    if (componentOptions) {\n      // check pattern\n      var name = getComponentName(componentOptions);\n      var ref = this;\n      var include = ref.include;\n      var exclude = ref.exclude;\n      if (\n        // not included\n        (include && (!name || !matches(include, name))) ||\n        // excluded\n        (exclude && name && matches(exclude, name))\n      ) {\n        return vnode\n      }\n\n      var ref$1 = this;\n      var cache = ref$1.cache;\n      var keys = ref$1.keys;\n      var key = vnode.key == null\n        // same constructor may get registered as different local components\n        // so cid alone is not enough (#3269)\n        ? componentOptions.Ctor.cid + (componentOptions.tag ? (\"::\" + (componentOptions.tag)) : '')\n        : vnode.key;\n      if (cache[key]) {\n        vnode.componentInstance = cache[key].componentInstance;\n        // make current key freshest\n        remove(keys, key);\n        keys.push(key);\n      } else {\n        cache[key] = vnode;\n        keys.push(key);\n        // prune oldest entry\n        if (this.max && keys.length > parseInt(this.max)) {\n          pruneCacheEntry(cache, keys[0], keys, this._vnode);\n        }\n      }\n\n      vnode.data.keepAlive = true;\n    }\n    return vnode || (slot && slot[0])\n  }\n};\n\nvar builtInComponents = {\n  KeepAlive: KeepAlive\n};\n\n/*  */\n\nfunction initGlobalAPI (Vue) {\n  // config\n  var configDef = {};\n  configDef.get = function () { return config; };\n  if (process.env.NODE_ENV !== 'production') {\n    configDef.set = function () {\n      warn(\n        'Do not replace the Vue.config object, set individual fields instead.'\n      );\n    };\n  }\n  Object.defineProperty(Vue, 'config', configDef);\n\n  // exposed util methods.\n  // NOTE: these are not considered part of the public API - avoid relying on\n  // them unless you are aware of the risk.\n  Vue.util = {\n    warn: warn,\n    extend: extend,\n    mergeOptions: mergeOptions,\n    defineReactive: defineReactive$$1\n  };\n\n  Vue.set = set;\n  Vue.delete = del;\n  Vue.nextTick = nextTick;\n\n  // 2.6 explicit observable API\n  Vue.observable = function (obj) {\n    observe(obj);\n    return obj\n  };\n\n  Vue.options = Object.create(null);\n  ASSET_TYPES.forEach(function (type) {\n    Vue.options[type + 's'] = Object.create(null);\n  });\n\n  // this is used to identify the \"base\" constructor to extend all plain-object\n  // components with in Weex's multi-instance scenarios.\n  Vue.options._base = Vue;\n\n  extend(Vue.options.components, builtInComponents);\n\n  initUse(Vue);\n  initMixin$1(Vue);\n  initExtend(Vue);\n  initAssetRegisters(Vue);\n}\n\ninitGlobalAPI(Vue);\n\nObject.defineProperty(Vue.prototype, '$isServer', {\n  get: isServerRendering\n});\n\nObject.defineProperty(Vue.prototype, '$ssrContext', {\n  get: function get () {\n    /* istanbul ignore next */\n    return this.$vnode && this.$vnode.ssrContext\n  }\n});\n\n// expose FunctionalRenderContext for ssr runtime helper installation\nObject.defineProperty(Vue, 'FunctionalRenderContext', {\n  value: FunctionalRenderContext\n});\n\nVue.version = '2.6.11';\n\n/**\n * https://raw.githubusercontent.com/Tencent/westore/master/packages/westore/utils/diff.js\n */\nvar ARRAYTYPE = '[object Array]';\nvar OBJECTTYPE = '[object Object]';\nvar NULLTYPE = '[object Null]';\nvar UNDEFINEDTYPE = '[object Undefined]';\n// const FUNCTIONTYPE = '[object Function]'\n\nfunction diff(current, pre) {\n    var result = {};\n    syncKeys(current, pre);\n    _diff(current, pre, '', result);\n    return result\n}\n\nfunction syncKeys(current, pre) {\n    if (current === pre) { return }\n    var rootCurrentType = type(current);\n    var rootPreType = type(pre);\n    if (rootCurrentType == OBJECTTYPE && rootPreType == OBJECTTYPE) {\n        if(Object.keys(current).length >= Object.keys(pre).length){\n            for (var key in pre) {\n                var currentValue = current[key];\n                if (currentValue === undefined) {\n                    current[key] = null;\n                } else {\n                    syncKeys(currentValue, pre[key]);\n                }\n            }\n        }\n    } else if (rootCurrentType == ARRAYTYPE && rootPreType == ARRAYTYPE) {\n        if (current.length >= pre.length) {\n            pre.forEach(function (item, index) {\n                syncKeys(current[index], item);\n            });\n        }\n    }\n}\n\nfunction nullOrUndefined(currentType, preType) {\n    if(\n        (currentType === NULLTYPE || currentType === UNDEFINEDTYPE) && \n        (preType === NULLTYPE || preType === UNDEFINEDTYPE)\n    ) {\n        return false\n    }\n    return true\n}\n\nfunction _diff(current, pre, path, result) {\n    if (current === pre) { return }\n    var rootCurrentType = type(current);\n    var rootPreType = type(pre);\n    if (rootCurrentType == OBJECTTYPE) {\n        if (rootPreType != OBJECTTYPE || Object.keys(current).length < Object.keys(pre).length) {\n            setResult(result, path, current);\n        } else {\n            var loop = function ( key ) {\n                var currentValue = current[key];\n                var preValue = pre[key];\n                var currentType = type(currentValue);\n                var preType = type(preValue);\n                if (currentType != ARRAYTYPE && currentType != OBJECTTYPE) {\n                    if (currentValue !== pre[key] && nullOrUndefined(currentType, preType)) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    }\n                } else if (currentType == ARRAYTYPE) {\n                    if (preType != ARRAYTYPE) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    } else {\n                        if (currentValue.length < preValue.length) {\n                            setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                        } else {\n                            currentValue.forEach(function (item, index) {\n                                _diff(item, preValue[index], (path == '' ? '' : path + \".\") + key + '[' + index + ']', result);\n                            });\n                        }\n                    }\n                } else if (currentType == OBJECTTYPE) {\n                    if (preType != OBJECTTYPE || Object.keys(currentValue).length < Object.keys(preValue).length) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    } else {\n                        for (var subKey in currentValue) {\n                            _diff(currentValue[subKey], preValue[subKey], (path == '' ? '' : path + \".\") + key + '.' + subKey, result);\n                        }\n                    }\n                }\n            };\n\n            for (var key in current) loop( key );\n        }\n    } else if (rootCurrentType == ARRAYTYPE) {\n        if (rootPreType != ARRAYTYPE) {\n            setResult(result, path, current);\n        } else {\n            if (current.length < pre.length) {\n                setResult(result, path, current);\n            } else {\n                current.forEach(function (item, index) {\n                    _diff(item, pre[index], path + '[' + index + ']', result);\n                });\n            }\n        }\n    } else {\n        setResult(result, path, current);\n    }\n}\n\nfunction setResult(result, k, v) {\n    // if (type(v) != FUNCTIONTYPE) {\n        result[k] = v;\n    // }\n}\n\nfunction type(obj) {\n    return Object.prototype.toString.call(obj)\n}\n\n/*  */\r\n\r\nfunction flushCallbacks$1(vm) {\r\n    if (vm.__next_tick_callbacks && vm.__next_tick_callbacks.length) {\r\n        if (process.env.VUE_APP_DEBUG) {\r\n            var mpInstance = vm.$scope;\r\n            console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + vm._uid +\r\n                ']:flushCallbacks[' + vm.__next_tick_callbacks.length + ']');\r\n        }\r\n        var copies = vm.__next_tick_callbacks.slice(0);\r\n        vm.__next_tick_callbacks.length = 0;\r\n        for (var i = 0; i < copies.length; i++) {\r\n            copies[i]();\r\n        }\r\n    }\r\n}\r\n\r\nfunction hasRenderWatcher(vm) {\r\n    return queue.find(function (watcher) { return vm._watcher === watcher; })\r\n}\r\n\r\nfunction nextTick$1(vm, cb) {\r\n    //1.nextTick 之前 已 setData 且 setData 还未回调完成\r\n    //2.nextTick 之前存在 render watcher\r\n    if (!vm.__next_tick_pending && !hasRenderWatcher(vm)) {\n        if(process.env.VUE_APP_DEBUG){\n            var mpInstance = vm.$scope;\n            console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + vm._uid +\n                ']:nextVueTick');\n        }\r\n        return nextTick(cb, vm)\r\n    }else{\n        if(process.env.VUE_APP_DEBUG){\n            var mpInstance$1 = vm.$scope;\n            console.log('[' + (+new Date) + '][' + (mpInstance$1.is || mpInstance$1.route) + '][' + vm._uid +\n                ']:nextMPTick');\n        }\n    }\r\n    var _resolve;\r\n    if (!vm.__next_tick_callbacks) {\r\n        vm.__next_tick_callbacks = [];\r\n    }\r\n    vm.__next_tick_callbacks.push(function () {\r\n        if (cb) {\r\n            try {\r\n                cb.call(vm);\r\n            } catch (e) {\r\n                handleError(e, vm, 'nextTick');\r\n            }\r\n        } else if (_resolve) {\r\n            _resolve(vm);\r\n        }\r\n    });\r\n    // $flow-disable-line\r\n    if (!cb && typeof Promise !== 'undefined') {\r\n        return new Promise(function (resolve) {\r\n            _resolve = resolve;\r\n        })\r\n    }\r\n}\n\n/*  */\r\n\r\nfunction clearInstance(key, value) {\r\n  // 简易去除 Vue 和小程序组件实例\r\n  if (value) {\r\n    if (value._isVue || value.__v_isMPComponent) {\r\n      return {}\r\n    }\r\n  }\r\n  return value\r\n}\r\n\r\nfunction cloneWithData(vm) {\r\n  // 确保当前 vm 所有数据被同步\r\n  var ret = Object.create(null);\r\n  var dataKeys = [].concat(\r\n    Object.keys(vm._data || {}),\r\n    Object.keys(vm._computedWatchers || {}));\r\n\r\n  dataKeys.reduce(function(ret, key) {\r\n    ret[key] = vm[key];\r\n    return ret\r\n  }, ret);\r\n\r\n  // vue-composition-api\r\n  var compositionApiState = vm.__composition_api_state__ || vm.__secret_vfa_state__;\r\n  var rawBindings = compositionApiState && compositionApiState.rawBindings;\r\n  if (rawBindings) {\r\n    Object.keys(rawBindings).forEach(function (key) {\r\n      ret[key] = vm[key];\r\n    });\r\n  }\r\n\r\n  //TODO 需要把无用数据处理掉，比如 list=>l0 则 list 需要移除，否则多传输一份数据\r\n  Object.assign(ret, vm.$mp.data || {});\r\n  if (\r\n    Array.isArray(vm.$options.behaviors) &&\r\n    vm.$options.behaviors.indexOf('uni://form-field') !== -1\r\n  ) { //form-field\r\n    ret['name'] = vm.name;\r\n    ret['value'] = vm.value;\r\n  }\r\n\r\n  return JSON.parse(JSON.stringify(ret, clearInstance))\r\n}\r\n\r\nvar patch = function(oldVnode, vnode) {\n  var this$1 = this;\n\r\n  if (vnode === null) { //destroy\r\n    return\r\n  }\r\n  if (this.mpType === 'page' || this.mpType === 'component') {\r\n    var mpInstance = this.$scope;\r\n    var data = Object.create(null);\r\n    try {\r\n      data = cloneWithData(this);\r\n    } catch (err) {\r\n      console.error(err);\r\n    }\r\n    data.__webviewId__ = mpInstance.data.__webviewId__;\r\n    var mpData = Object.create(null);\r\n    Object.keys(data).forEach(function (key) { //仅同步 data 中有的数据\r\n      mpData[key] = mpInstance.data[key];\r\n    });\r\n    var diffData = this.$shouldDiffData === false ? data : diff(data, mpData);\r\n    if (Object.keys(diffData).length) {\r\n      if (process.env.VUE_APP_DEBUG) {\r\n        console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + this._uid +\r\n          ']差量更新',\r\n          JSON.stringify(diffData));\r\n      }\r\n      this.__next_tick_pending = true;\r\n      mpInstance.setData(diffData, function () {\r\n        this$1.__next_tick_pending = false;\r\n        flushCallbacks$1(this$1);\r\n      });\r\n    } else {\r\n      flushCallbacks$1(this);\r\n    }\r\n  }\r\n};\n\n/*  */\n\nfunction createEmptyRender() {\n\n}\n\nfunction mountComponent$1(\n  vm,\n  el,\n  hydrating\n) {\n  if (!vm.mpType) {//main.js 中的 new Vue\n    return vm\n  }\n  if (vm.mpType === 'app') {\n    vm.$options.render = createEmptyRender;\n  }\n  if (!vm.$options.render) {\n    vm.$options.render = createEmptyRender;\n    if (process.env.NODE_ENV !== 'production') {\n      /* istanbul ignore if */\n      if ((vm.$options.template && vm.$options.template.charAt(0) !== '#') ||\n        vm.$options.el || el) {\n        warn(\n          'You are using the runtime-only build of Vue where the template ' +\n          'compiler is not available. Either pre-compile the templates into ' +\n          'render functions, or use the compiler-included build.',\n          vm\n        );\n      } else {\n        warn(\n          'Failed to mount component: template or render function not defined.',\n          vm\n        );\n      }\n    }\n  }\n  \n  !vm._$fallback && callHook(vm, 'beforeMount');\n\n  var updateComponent = function () {\n    vm._update(vm._render(), hydrating);\n  };\n\n  // we set this to vm._watcher inside the watcher's constructor\n  // since the watcher's initial patch may call $forceUpdate (e.g. inside child\n  // component's mounted hook), which relies on vm._watcher being already defined\n  new Watcher(vm, updateComponent, noop, {\n    before: function before() {\n      if (vm._isMounted && !vm._isDestroyed) {\n        callHook(vm, 'beforeUpdate');\n      }\n    }\n  }, true /* isRenderWatcher */);\n  hydrating = false;\n  return vm\n}\n\n/*  */\n\nfunction renderClass (\n  staticClass,\n  dynamicClass\n) {\n  if (isDef(staticClass) || isDef(dynamicClass)) {\n    return concat(staticClass, stringifyClass(dynamicClass))\n  }\n  /* istanbul ignore next */\n  return ''\n}\n\nfunction concat (a, b) {\n  return a ? b ? (a + ' ' + b) : a : (b || '')\n}\n\nfunction stringifyClass (value) {\n  if (Array.isArray(value)) {\n    return stringifyArray(value)\n  }\n  if (isObject(value)) {\n    return stringifyObject(value)\n  }\n  if (typeof value === 'string') {\n    return value\n  }\n  /* istanbul ignore next */\n  return ''\n}\n\nfunction stringifyArray (value) {\n  var res = '';\n  var stringified;\n  for (var i = 0, l = value.length; i < l; i++) {\n    if (isDef(stringified = stringifyClass(value[i])) && stringified !== '') {\n      if (res) { res += ' '; }\n      res += stringified;\n    }\n  }\n  return res\n}\n\nfunction stringifyObject (value) {\n  var res = '';\n  for (var key in value) {\n    if (value[key]) {\n      if (res) { res += ' '; }\n      res += key;\n    }\n  }\n  return res\n}\n\n/*  */\n\nvar parseStyleText = cached(function (cssText) {\n  var res = {};\n  var listDelimiter = /;(?![^(]*\\))/g;\n  var propertyDelimiter = /:(.+)/;\n  cssText.split(listDelimiter).forEach(function (item) {\n    if (item) {\n      var tmp = item.split(propertyDelimiter);\n      tmp.length > 1 && (res[tmp[0].trim()] = tmp[1].trim());\n    }\n  });\n  return res\n});\n\n// normalize possible array / string values into Object\nfunction normalizeStyleBinding (bindingStyle) {\n  if (Array.isArray(bindingStyle)) {\n    return toObject(bindingStyle)\n  }\n  if (typeof bindingStyle === 'string') {\n    return parseStyleText(bindingStyle)\n  }\n  return bindingStyle\n}\n\n/*  */\r\n\r\nvar MP_METHODS = ['createSelectorQuery', 'createIntersectionObserver', 'selectAllComponents', 'selectComponent'];\r\n\r\nfunction getTarget(obj, path) {\r\n  var parts = path.split('.');\r\n  var key = parts[0];\r\n  if (key.indexOf('__$n') === 0) { //number index\r\n    key = parseInt(key.replace('__$n', ''));\r\n  }\r\n  if (parts.length === 1) {\r\n    return obj[key]\r\n  }\r\n  return getTarget(obj[key], parts.slice(1).join('.'))\r\n}\r\n\r\nfunction internalMixin(Vue) {\r\n\r\n  Vue.config.errorHandler = function(err, vm, info) {\r\n    Vue.util.warn((\"Error in \" + info + \": \\\"\" + (err.toString()) + \"\\\"\"), vm);\r\n    console.error(err);\r\n    /* eslint-disable no-undef */\r\n    var app = typeof getApp === 'function' && getApp();\r\n    if (app && app.onError) {\r\n      app.onError(err);\r\n    }\r\n  };\r\n\r\n  var oldEmit = Vue.prototype.$emit;\r\n\r\n  Vue.prototype.$emit = function(event) {\r\n    if (this.$scope && event) {\r\n      var triggerEvent = this.$scope['_triggerEvent'] || this.$scope['triggerEvent'];\r\n      if (triggerEvent) {\r\n        try {\r\n          triggerEvent.call(this.$scope, event, {\r\n            __args__: toArray(arguments, 1)\r\n          });\r\n        } catch (error) {\r\n\r\n        }\r\n      }\r\n    }\r\n    return oldEmit.apply(this, arguments)\r\n  };\r\n\r\n  Vue.prototype.$nextTick = function(fn) {\r\n    return nextTick$1(this, fn)\r\n  };\r\n\r\n  MP_METHODS.forEach(function (method) {\r\n    Vue.prototype[method] = function(args) {\r\n      if (this.$scope && this.$scope[method]) {\r\n        return this.$scope[method](args)\r\n      }\r\n      // mp-alipay\r\n      if (typeof my === 'undefined') {\r\n        return\r\n      }\r\n      if (method === 'createSelectorQuery') {\r\n        /* eslint-disable no-undef */\r\n        return my.createSelectorQuery(args)\r\n      } else if (method === 'createIntersectionObserver') {\r\n        /* eslint-disable no-undef */\r\n        return my.createIntersectionObserver(args)\r\n      }\r\n      // TODO mp-alipay 暂不支持 selectAllComponents,selectComponent\r\n    };\r\n  });\r\n\r\n  Vue.prototype.__init_provide = initProvide;\r\n\r\n  Vue.prototype.__init_injections = initInjections;\r\n\r\n  Vue.prototype.__call_hook = function(hook, args) {\r\n    var vm = this;\r\n    // #7573 disable dep collection when invoking lifecycle hooks\r\n    pushTarget();\r\n    var handlers = vm.$options[hook];\r\n    var info = hook + \" hook\";\r\n    var ret;\r\n    if (handlers) {\r\n      for (var i = 0, j = handlers.length; i < j; i++) {\r\n        ret = invokeWithErrorHandling(handlers[i], vm, args ? [args] : null, vm, info);\r\n      }\r\n    }\r\n    if (vm._hasHookEvent) {\r\n      vm.$emit('hook:' + hook, args);\r\n    }\r\n    popTarget();\r\n    return ret\r\n  };\r\n\r\n  Vue.prototype.__set_model = function(target, key, value, modifiers) {\r\n    if (Array.isArray(modifiers)) {\r\n      if (modifiers.indexOf('trim') !== -1) {\r\n        value = value.trim();\r\n      }\r\n      if (modifiers.indexOf('number') !== -1) {\r\n        value = this._n(value);\r\n      }\r\n    }\r\n    if (!target) {\r\n      target = this;\r\n    }\r\n    // 解决动态属性添加\r\n    Vue.set(target, key, value);\r\n  };\r\n\r\n  Vue.prototype.__set_sync = function(target, key, value) {\r\n    if (!target) {\r\n      target = this;\r\n    }\r\n    // 解决动态属性添加\r\n    Vue.set(target, key, value);\r\n  };\r\n\r\n  Vue.prototype.__get_orig = function(item) {\r\n    if (isPlainObject(item)) {\r\n      return item['$orig'] || item\r\n    }\r\n    return item\r\n  };\r\n\r\n  Vue.prototype.__get_value = function(dataPath, target) {\r\n    return getTarget(target || this, dataPath)\r\n  };\r\n\r\n\r\n  Vue.prototype.__get_class = function(dynamicClass, staticClass) {\r\n    return renderClass(staticClass, dynamicClass)\r\n  };\r\n\r\n  Vue.prototype.__get_style = function(dynamicStyle, staticStyle) {\r\n    if (!dynamicStyle && !staticStyle) {\r\n      return ''\r\n    }\r\n    var dynamicStyleObj = normalizeStyleBinding(dynamicStyle);\r\n    var styleObj = staticStyle ? extend(staticStyle, dynamicStyleObj) : dynamicStyleObj;\r\n    return Object.keys(styleObj).map(function (name) { return ((hyphenate(name)) + \":\" + (styleObj[name])); }).join(';')\r\n  };\r\n\r\n  Vue.prototype.__map = function(val, iteratee) {\r\n    //TODO 暂不考虑 string\r\n    var ret, i, l, keys, key;\r\n    if (Array.isArray(val)) {\r\n      ret = new Array(val.length);\r\n      for (i = 0, l = val.length; i < l; i++) {\r\n        ret[i] = iteratee(val[i], i);\r\n      }\r\n      return ret\r\n    } else if (isObject(val)) {\r\n      keys = Object.keys(val);\r\n      ret = Object.create(null);\r\n      for (i = 0, l = keys.length; i < l; i++) {\r\n        key = keys[i];\r\n        ret[key] = iteratee(val[key], key, i);\r\n      }\r\n      return ret\r\n    } else if (typeof val === 'number') {\r\n      ret = new Array(val);\r\n      for (i = 0, l = val; i < l; i++) {\r\n        // 第一个参数暂时仍和小程序一致\r\n        ret[i] = iteratee(i, i);\r\n      }\r\n      return ret\r\n    }\r\n    return []\r\n  };\r\n\r\n}\n\n/*  */\r\n\r\nvar LIFECYCLE_HOOKS$1 = [\r\n    //App\r\n    'onLaunch',\r\n    'onShow',\r\n    'onHide',\r\n    'onUniNViewMessage',\r\n    'onPageNotFound',\r\n    'onThemeChange',\r\n    'onError',\r\n    'onUnhandledRejection',\r\n    //Page\r\n    'onInit',\r\n    'onLoad',\r\n    // 'onShow',\r\n    'onReady',\r\n    // 'onHide',\r\n    'onUnload',\r\n    'onPullDownRefresh',\r\n    'onReachBottom',\r\n    'onTabItemTap',\r\n    'onAddToFavorites',\r\n    'onShareTimeline',\r\n    'onShareAppMessage',\r\n    'onResize',\r\n    'onPageScroll',\r\n    'onNavigationBarButtonTap',\r\n    'onBackPress',\r\n    'onNavigationBarSearchInputChanged',\r\n    'onNavigationBarSearchInputConfirmed',\r\n    'onNavigationBarSearchInputClicked',\r\n    'onUploadDouyinVideo',\r\n    'onNFCReadMessage',\r\n    //Component\r\n    // 'onReady', // 兼容旧版本，应该移除该事件\r\n    'onPageShow',\r\n    'onPageHide',\r\n    'onPageResize'\r\n];\r\nfunction lifecycleMixin$1(Vue) {\r\n\r\n    //fixed vue-class-component\r\n    var oldExtend = Vue.extend;\r\n    Vue.extend = function(extendOptions) {\r\n        extendOptions = extendOptions || {};\r\n\r\n        var methods = extendOptions.methods;\r\n        if (methods) {\r\n            Object.keys(methods).forEach(function (methodName) {\r\n                if (LIFECYCLE_HOOKS$1.indexOf(methodName)!==-1) {\r\n                    extendOptions[methodName] = methods[methodName];\r\n                    delete methods[methodName];\r\n                }\r\n            });\r\n        }\r\n\r\n        return oldExtend.call(this, extendOptions)\r\n    };\r\n\r\n    var strategies = Vue.config.optionMergeStrategies;\r\n    var mergeHook = strategies.created;\r\n    LIFECYCLE_HOOKS$1.forEach(function (hook) {\r\n        strategies[hook] = mergeHook;\r\n    });\r\n\r\n    Vue.prototype.__lifecycle_hooks__ = LIFECYCLE_HOOKS$1;\r\n}\n\n/*  */\r\n\n// install platform patch function\r\nVue.prototype.__patch__ = patch;\r\n\r\n// public mount method\r\nVue.prototype.$mount = function(\r\n    el ,\r\n    hydrating \r\n) {\r\n    return mountComponent$1(this, el, hydrating)\r\n};\r\n\r\nlifecycleMixin$1(Vue);\r\ninternalMixin(Vue);\n\n/*  */\n\nexport default Vue;\n", "/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nexport default function normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode, /* vue-cli only */\n  components, // fixed by xxxxxx auto components\n  renderjs // fixed by xxxxxx renderjs\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // fixed by xxxxxx auto components\n  if (components) {\n    if (!options.components) {\n      options.components = {}\n    }\n    var hasOwn = Object.prototype.hasOwnProperty\n    for (var name in components) {\n      if (hasOwn.call(components, name) && !hasOwn.call(options.components, name)) {\n        options.components[name] = components[name]\n      }\n    }\n  }\n  // fixed by xxxxxx renderjs\n  if (renderjs) {\n    if(typeof renderjs.beforeCreate === 'function'){\n\t\t\trenderjs.beforeCreate = [renderjs.beforeCreate]\n\t\t}\n    (renderjs.beforeCreate || (renderjs.beforeCreate = [])).unshift(function() {\n      this[renderjs.__module] = this\n    });\n    (options.mixins || (options.mixins = [])).push(renderjs)\n  }\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () { injectStyles.call(this, this.$root.$options.shadowRoot) }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functioal component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n", "uni.addInterceptor({\n  returnValue (res) {\n    if (!(!!res && (typeof res === \"object\" || typeof res === \"function\") && typeof res.then === \"function\")) {\n      return res;\n    }\n    return new Promise((resolve, reject) => {\n      res.then((res) => res[0] ? reject(res[0]) : resolve(res[1]));\n    });\n  },\n});"], "names": ["_OverloadYield", "e", "d", "v", "k", "module", "exports", "__esModule", "_arrayLikeToArray", "r", "a", "length", "n", "Array", "default", "_arrayWithHoles", "isArray", "arrayLikeToArray", "_arrayWithoutHoles", "asyncGeneratorStep", "t", "o", "c", "i", "u", "value", "done", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_classCallCheck", "TypeError", "isNativeReflectConstruct", "setPrototypeOf", "_construct", "Reflect", "construct", "push", "p", "bind", "prototype", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "_defineProperty", "_isNativeReflectConstruct", "Boolean", "valueOf", "call", "_iterableToArray", "Symbol", "iterator", "from", "_iterableToArrayLimit", "l", "f", "next", "_nonIterableRest", "_nonIterableSpread", "_setPrototypeOf", "__proto__", "arrayWithHoles", "iterableToArrayLimit", "unsupportedIterableToArray", "nonIterableRest", "_slicedToArray", "arrayWithoutHoles", "iterableToArray", "nonIterableSpread", "_toConsumableArray", "_typeof", "toPrimitive", "String", "Number", "constructor", "_unsupportedIterableToArray", "toString", "slice", "name", "test", "regeneratorDefine", "require", "_regenerator", "toStringTag", "Generator", "create", "y", "G", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "displayName", "w", "m", "regeneratorAsyncGen", "_regeneratorAsync", "regenerator", "regeneratorAsyncIterator", "_regeneratorAsyncGen", "OverloadYield", "AsyncIterator", "asyncIterator", "_regeneratorDefine", "_invoke", "_regeneratorKeys", "unshift", "pop", "regeneratorAsync", "regeneratorKeys", "regeneratorValues", "_regeneratorRuntime", "stop", "_catch", "abrupt", "<PERSON><PERSON><PERSON>", "resultName", "finish", "_t", "prev", "sent", "wrap", "reverse", "isGeneratorFunction", "mark", "awrap", "async", "keys", "values", "_regeneratorValues", "isNaN", "isObject", "val", "defaultDelimiters", "BaseFormatter", "_caches", "interpolate", "message", "delimiters", "undefined", "tokens", "parse", "compile", "RE_TOKEN_LIST_VALUE", "RE_TOKEN_NAMED_VALUE", "format", "_ref", "_ref2", "startDelimiter", "endDelimiter", "position", "text", "char", "type", "sub", "isClosed", "compiled", "index", "mode", "token", "parseInt", "process", "env", "NODE_ENV", "console", "warn", "concat", "LOCALE_ZH_HANS", "LOCALE_ZH_HANT", "LOCALE_EN", "LOCALE_FR", "LOCALE_ES", "hasOwnProperty", "hasOwn", "defaultFormatter", "include", "str", "parts", "find", "part", "indexOf", "startsWith", "normalizeLocale", "locale", "messages", "trim", "replace", "toLowerCase", "locales", "lang", "I18n", "_ref3", "fallback<PERSON><PERSON><PERSON>", "watcher", "formater", "watchers", "setLocale", "watchLocale", "_this", "oldLocale", "for<PERSON>ach", "getLocale", "fn", "_this2", "splice", "add", "override", "curMessages", "assign", "join", "watchAppLocale", "appVm", "i18n", "$watchLocale", "newLocale", "$watch", "$locale", "getDefaultLocale", "uni", "global", "initVueI18n", "_ref4", "__uniConfig", "getApp", "isWatchedAppLocale", "$vm", "watch", "isString", "hasI18nJson", "jsonObj", "walkJsonObj", "isI18nStr", "parseI18nJson", "compileStr", "compileI18nJsonStr", "jsonStr", "_ref5", "localeValues", "JSON", "stringify", "compileJsonObj", "compileValue", "valueLocales", "localValue", "walk", "resolveLocale", "resolveLocaleChain", "chain", "split", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "realAtob", "b64", "b64re", "atob", "Error", "bitmap", "result", "r1", "r2", "char<PERSON>t", "fromCharCode", "b64DecodeUnicode", "decodeURIComponent", "map", "charCodeAt", "getCurrentUserInfo", "wx", "getStorageSync", "tokenArr", "uid", "role", "permission", "tokenExpired", "userInfo", "error", "exp", "iat", "uniIdMixin", "uniIDHasRole", "roleId", "_getCurrentUserInfo", "uniIDHasPermission", "permissionId", "_getCurrentUserInfo2", "uniIDTokenValid", "_getCurrentUserInfo3", "Date", "now", "_toString", "isFn", "isStr", "obj", "isPlainObject", "noop", "cached", "cache", "cachedFn", "hit", "camelizeRE", "camelize", "_", "toUpperCase", "sortObject", "sortObj", "sort", "HOOKS", "globalInterceptors", "scopedInterceptors", "mergeHook", "parentVal", "childVal", "res", "dedupe<PERSON><PERSON>s", "hooks", "removeH<PERSON>", "hook", "mergeInterceptorHook", "interceptor", "option", "removeInterceptorHook", "addInterceptor", "method", "removeInterceptor", "wrapperHook", "params", "data", "isPromise", "queue", "promise", "callback", "wrapperOptions", "options", "oldCallback", "callbackInterceptor", "wrapperReturnValue", "returnValue", "returnValueHooks", "getApiInterceptorHooks", "scopedInterceptor", "invokeApi", "api", "_len", "_key", "invoke", "promiseInterceptor", "reject", "SYNC_API_RE", "CONTEXT_API_RE", "CONTEXT_API_RE_EXC", "ASYNC_API", "CALLBACK_API_RE", "isContextApi", "isSyncApi", "isCallbackApi", "handlePromise", "catch", "err", "shouldPromise", "finally", "reason", "promisify", "promiseApi", "_len2", "_key2", "success", "fail", "complete", "EPS", "BASE_DEVICE_WIDTH", "isIOS", "deviceWidth", "deviceDPR", "checkDeviceWidth", "windowWidth", "pixelRatio", "platform", "windowInfo", "getWindowInfo", "getSystemInfoSync", "deviceInfo", "getDeviceInfo", "upx2px", "number", "newDeviceWidth", "Math", "floor", "getLocaleLanguage", "localeLanguage", "appBaseInfo", "getAppBaseInfo", "language", "initI18nMessages", "isEnableLocale", "localeKeys", "userMessages", "i18nMixin", "mixin", "beforeCreate", "unwatch", "$forceUpdate", "$once", "methods", "$$t", "initAppLocale", "state", "observable", "localeWatchers", "get", "set", "getLocale$1", "app", "allowDefault", "setLocale$1", "onLocaleChangeCallbacks", "onLocaleChange", "interceptors", "baseApi", "freeze", "rpx2px", "findExistsPageIndex", "url", "pages", "getCurrentPages", "len", "page", "$page", "fullPath", "redirectTo", "fromArgs", "exists", "delta", "args", "existsPageIndex", "previewImage", "currentIndex", "current", "urls", "filter", "item", "indicator", "loop", "UUID_KEY", "deviceId", "useDeviceId", "random", "setStorage", "addSafeAreaInsets", "safeArea", "safeAreaInsets", "top", "left", "right", "bottom", "screenHeight", "getOSInfo", "system", "osName", "osVersion", "toLocaleLowerCase", "populateParameters", "_result$brand", "brand", "_result$model", "model", "_result$system", "_result$language", "theme", "version", "fontSizeSetting", "SDKVersion", "deviceOrientation", "extraParam", "_getOSInfo", "hostVersion", "deviceType", "getGetDeviceType", "deviceBrand", "getDevice<PERSON>rand", "_hostName", "getHostName", "_deviceOrientation", "_devicePixelRatio", "_SDKVersion", "hostLanguage", "parameters", "appId", "UNI_APP_ID", "appName", "UNI_APP_NAME", "appVersion", "UNI_APP_VERSION_NAME", "appVersionCode", "UNI_APP_VERSION_CODE", "appLanguage", "getAppLanguage", "uniCompileVersion", "UNI_COMPILER_VERSION", "uniCompilerVersion", "uniRuntimeVersion", "uniPlatform", "UNI_SUB_PLATFORM", "UNI_PLATFORM", "deviceModel", "devicePixelRatio", "hostTheme", "hostName", "hostSDKVersion", "hostFontSizeSetting", "windowTop", "windowBottom", "osLanguage", "osTheme", "ua", "hostPackageName", "browserName", "browserVersion", "isUniAppX", "deviceTypeMaps", "ipad", "windows", "mac", "deviceTypeMapsKeys", "_model", "_m", "defaultLanguage", "_platform", "environment", "host", "getSystemInfo", "showActionSheet", "alertText", "title", "_result", "_result2", "_result2$system", "_result2$platform", "_getOSInfo2", "getAppAuthorizeSetting", "locationReducedAccuracy", "locationAccuracy", "compressImage", "compressedHeight", "compressHeight", "compressedWidth", "compressWidth", "protocols", "todos", "canIUses", "CALLBACKS", "processCallback", "methodName", "processReturnValue", "processArgs", "argsOption", "keepFromArgs", "<PERSON><PERSON><PERSON><PERSON>", "keyOption", "keepReturnValue", "wrapper", "protocol", "arg1", "arg2", "todo<PERSON><PERSON>", "TODOS", "createTodoApi", "todoApi", "errMsg", "providers", "o<PERSON>h", "share", "payment", "get<PERSON><PERSON><PERSON>", "service", "provider", "extraApi", "getEmitter", "Emitter", "getUniEmitter", "ctx", "$on", "$off", "$emit", "eventApi", "tryCatch", "getApiCallbacks", "apiCallbacks", "param", "cid", "cidErrMsg", "enabled", "normalizePushMessage", "invoke<PERSON><PERSON><PERSON><PERSON><PERSON>", "invokeGetPushCidCallbacks", "onPushMessageCallbacks", "stopped", "getPushCidCallbacks", "getPushClientId", "_getApiCallbacks", "hasSuccess", "hasFail", "hasComplete", "onPushMessage", "offPushMessage", "__f__", "_len3", "_key3", "baseInfo", "shareVideoMessage", "miniapp", "mocks", "findVmByVueId", "vm", "vuePid", "$children", "childVm", "$scope", "_$vueId", "parentVm", "init<PERSON>eh<PERSON>or", "Behavior", "isPage", "route", "initRelation", "detail", "triggerEvent", "selectAllComponents", "mpInstance", "selector", "$refs", "components", "component", "ref", "dataset", "toSkip", "vueGeneric", "scopedComponent", "syncRefs", "refs", "newRefs", "oldKeys", "Set", "newKeys", "oldValue", "newValue", "every", "includes", "delete", "initRefs", "forComponents", "handleLink", "event", "vueOptions", "parent", "markMPComponent", "IS_MP", "OB", "SKIP", "isExtensible", "WORKLET_RE", "initWorkletMethods", "mpMethods", "vueMethods", "matches", "match", "workletName", "MPPage", "Page", "MPComponent", "Component", "customizeRE", "customize", "initTriggerEvent", "oldTriggerEvent", "newTriggerEvent", "_len4", "_key4", "comType", "newEvent", "_triggerEvent", "initHook", "isComponent", "oldHook", "_len5", "_key5", "__$wrappered", "after", "PAGE_EVENT_HOOKS", "initMocks", "$mp", "mpType", "mock", "hasH<PERSON>", "extendOptions", "super", "mixins", "initHooks", "mpOptions", "__call_hook", "initUnknownHooks", "excludes", "find<PERSON>ooks", "initHook$1", "initVueComponent", "VueComponent", "extend", "initSlots", "vueSlots", "$slots", "slotName", "$scopedSlots", "initVueIds", "vueIds", "_$vuePid", "initData", "context", "VUE_APP_DEBUG", "__lifecycle_hooks__", "PROP_TYPES", "createObserver", "observer", "newVal", "oldVal", "initBehaviors", "vueBehaviors", "behaviors", "vueExtends", "extends", "vueMixins", "vueProps", "props", "behavior", "properties", "initProperties", "vueMixin", "parsePropType", "defaultValue", "file", "is<PERSON>eh<PERSON>or", "vueId", "virtualHost", "virtualHostStyle", "virtualHostClass", "scopedSlotsCompiler", "setData", "opts", "wrapper$1", "mp", "stopPropagation", "preventDefault", "target", "markerId", "getExtraValue", "dataPathsArray", "dataPathArray", "dataPath", "prop<PERSON>ath", "valuePath", "vFor", "isInteger", "substr", "__get_value", "vForItem", "vForKey", "processEventExtra", "extra", "__args__", "extraObj", "getObjByArray", "arr", "element", "processEventArgs", "isCustom", "isCustomMPEvent", "currentTarget", "ret", "arg", "ONCE", "CUSTOM", "isMatchEventType", "eventType", "optType", "getContextVm", "$parent", "$options", "generic", "handleEvent", "eventOpts", "eventOpt", "eventsArray", "isOnce", "eventArray", "handlerCtx", "handler", "path", "is", "once", "eventChannels", "getEventChannel", "id", "eventChannel", "initEventChannel", "getOpenerEventChannel", "callHook", "__id__", "__eventChannel__", "initScopedSlotsParams", "center", "parents", "currentId", "propsData", "$hasSSP", "slot", "$getSSP", "needAll", "$setSSP", "$initSSP", "$callSSP", "destroyed", "parseBaseApp", "store", "$store", "mpHost", "$i18n", "_i18n", "appOptions", "onLaunch", "canIUse", "globalData", "_isMounted", "getLocaleLanguage$1", "parseApp", "createApp", "App", "encodeReserveRE", "encodeReserveReplacer", "commaRE", "encode", "encodeURIComponent", "stringifyQuery", "encodeStr", "val2", "x", "parseBaseComponent", "vueComponentOptions", "needVueOptions", "_initVueComponent", "_initVueComponent2", "_objectSpread", "multipleSlots", "addGlobalClass", "componentOptions", "__file", "lifetimes", "attached", "$mount", "ready", "detached", "$destroy", "pageLifetimes", "show", "hide", "resize", "size", "__l", "__e", "externalClasses", "wxsCallMethods", "callMethod", "parseComponent", "hooks$1", "parseBasePage", "vuePageOptions", "_parseComponent", "_parseComponent2", "pageOptions", "onLoad", "query", "copyQuery", "parsePage", "createPage", "createComponent", "createSubpackageApp", "onShow", "onAppShow", "_len6", "_key6", "onHide", "onAppHide", "_len7", "_key7", "getLaunchOptionsSync", "createPlugin", "_len8", "_key8", "_len9", "_key9", "canIUseApi", "apiName", "Proxy", "uni$1", "objectKeys", "singlePageDisableKey", "globalThis", "oldWx", "launchOption", "isWxKey", "scene", "initWx", "newWx"], "sourceRoot": ""}