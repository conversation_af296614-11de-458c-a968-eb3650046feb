// Mock uni object for browser environment
// This must be loaded before any uni-app modules

if (typeof window !== 'undefined' && typeof uni === 'undefined') {
  window.uni = {
    getStorageSync: (key) => {
      try {
        return localStorage.getItem(key) || ''
      } catch (e) {
        console.warn('localStorage not available:', e)
        return ''
      }
    },
    setStorageSync: (key, value) => {
      try {
        localStorage.setItem(key, value)
      } catch (e) {
        console.warn('localStorage not available:', e)
      }
    },
    removeStorageSync: (key) => {
      try {
        localStorage.removeItem(key)
      } catch (e) {
        console.warn('localStorage not available:', e)
      }
    },
    showLoading: (options) => {
      console.log('showLoading:', options)
    },
    hideLoading: () => {
      console.log('hideLoading')
    },
    showToast: (options) => {
      console.log('showToast:', options)
      if (options && (options.title || options.message)) {
        alert(options.title || options.message)
      }
    },
    hideToast: () => {
      console.log('hideToast')
    },
    request: (options) => {
      if (!options || !options.url) {
        console.error('uni.request: url is required')
        return
      }

      const config = {
        method: options.method || 'GET',
        headers: options.header || {},
      }
      
      if (options.data && (options.method === 'POST' || options.method === 'PUT')) {
        if (options.header && options.header['content-type'] && options.header['content-type'].includes('application/json')) {
          config.body = JSON.stringify(options.data)
        } else {
          const formData = new URLSearchParams()
          Object.keys(options.data).forEach(key => {
            formData.append(key, options.data[key])
          })
          config.body = formData
        }
      }
      
      fetch(options.url, config)
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
          return response.json()
        })
        .then(data => {
          if (options.success) {
            options.success({ 
              data,
              statusCode: 200,
              header: {}
            })
          }
        })
        .catch(error => {
          console.error('Request failed:', error)
          if (options.fail) {
            options.fail({ 
              errMsg: error.message,
              statusCode: 0
            })
          }
        })
        .finally(() => {
          if (options.complete) {
            options.complete()
          }
        })
    },
    // Navigation APIs
    navigateTo: (options) => {
      console.log('navigateTo:', options)
    },
    redirectTo: (options) => {
      console.log('redirectTo:', options)
    },
    navigateBack: (options) => {
      console.log('navigateBack:', options)
      if (window.history.length > 1) {
        window.history.back()
      }
    },
    // System info
    getSystemInfoSync: () => {
      return {
        platform: 'web',
        system: navigator.platform,
        version: navigator.appVersion,
        screenWidth: window.screen.width,
        screenHeight: window.screen.height,
        windowWidth: window.innerWidth,
        windowHeight: window.innerHeight
      }
    },
    // Placeholder for $u object (will be set by uview-ui)
    $u: {}
  }

  // Make uni globally available
  if (typeof global !== 'undefined') {
    global.uni = window.uni
  }
}

export default window.uni
