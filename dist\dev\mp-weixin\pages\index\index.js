(global["webpackChunkjavamcpview"] = global["webpackChunkjavamcpview"] || []).push([["pages/index/index"],{

/***/ "./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/index/index.vue?vue&type=template&id=57280228&scoped=true&":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/index/index.vue?vue&type=template&id=57280228&scoped=true& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   components: function() { return /* binding */ components; },
/* harmony export */   recyclableRender: function() { return /* binding */ recyclableRender; },
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/index/index.vue?vue&type=script&lang=js&":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/index/index.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/regenerator */ "./node_modules/@dcloudio/vue-cli-plugin-uni/packages/@babel/runtime/regenerator/index.js");
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2__);
/* provided dependency */ var uni = __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ "./node_modules/@dcloudio/uni-mp-weixin/dist/index.js")["default"];



//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ __webpack_exports__["default"] = ({
  data: function data() {
    return {
      // MCP连接相关
      // 本地测试地址
      mcpApiBase: 'http://localhost:8081',
      // 线上地址
      // mcpApiBase: 'https://b55b-183-250-154-98.ngrok-free.app',
      mcpConnected: false,
      connectionStatus: 'disconnected',
      // connected, disconnected, connecting

      // 搜索相关
      searchQuery: '',
      isLoading: false,
      showResult: false,
      queryResult: '',
      // 加载动画相关
      loadingMessage: '正在智能分析您的问题',
      loadingSubMessage: 'AI正在理解查询意图',
      loadingMessages: [{
        main: '正在智能分析您的问题',
        sub: 'AI正在理解查询意图'
      }, {
        main: '正在连接数据库',
        sub: '建立与SQL Server的安全连接'
      }, {
        main: '正在查询数据',
        sub: '从StoreRevenue表中检索相关数据'
      }, {
        main: '正在处理结果',
        sub: '对查询结果进行智能分析和格式化'
      }],
      loadingInterval: null,
      // 语音识别相关
      isRecording: false,
      aliyunAppKey: 'DzVzUYbM1vG83nxm',
      aliyunToken: '74902900e45c44a497f830c29522ede0',
      aliyunWs: null,
      aliyunRecorder: null,
      aliyunAudioContext: null,
      aliyunStream: null,
      aliyunAnalyser: null,
      aliyunWaveAnimationId: null,
      silenceStartTime: null,
      silenceThreshold: 0.02,
      silenceTimeout: 1000,
      // 热门搜索标签
      hotTags: ['本月平台收入总共多少', '本月平台收入明细', '上个月平台收入', '平台收入排名前5', '去年营业额', '今年营业额', '门店营业额排名'],
      // 搜索历史
      searchHistory: ['本月平台收入总共多少', '列一个本月平台收入的明细给我', '上个月的平台收入总共有多少', '看看去年的营业额', '今年的营业额', '门店营业额前十门店营业额', '去年营业额前十门店同比情况', '门店日期间3天利润趋势'],
      isH5: false,
      _waveAnimId: null
    };
  },
  computed: {
    connectionStatusClass: function connectionStatusClass() {
      return "connection-status ".concat(this.connectionStatus);
    },
    connectionStatusText: function connectionStatusText() {
      var statusMap = {
        'connected': '🟢 已连接',
        'disconnected': '🔴 未连接',
        'connecting': '🟡 连接中...'
      };
      return statusMap[this.connectionStatus] || '🔴 未连接';
    },
    formattedResult: function formattedResult() {
      if (!this.queryResult) return '';
      var formatted = this.queryResult.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>').replace(/\*(.*?)\*/g, '<em>$1</em>').replace(/`(.*?)`/g, '<code style="background: #f1f3f4; padding: 2px 4px; border-radius: 3px;">$1</code>');

      // 数字高亮
      formatted = formatted.replace(/(\d+\.?\d*)(元|万元|亿元)/g, '<span style="color: #4A90E2; font-weight: bold;">$1$2</span>');
      return "<div style=\"line-height: 1.8;\">".concat(formatted, "</div>");
    }
  },
  methods: {
    // MCP连接管理
    connectToMCP: function connectToMCP() {
      var _this = this;
      return (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__["default"])(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee() {
        var _yield$uni$request, _yield$uni$request2, err, response, _t;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _this.updateConnectionStatus('connecting');
              console.log('正在连接MCP服务器...');
              _context.next = 1;
              return uni.request({
                url: "".concat(_this.mcpApiBase, "/mcp/connect"),
                method: 'POST',
                header: {
                  'Content-Type': 'application/json'
                }
              });
            case 1:
              _yield$uni$request = _context.sent;
              _yield$uni$request2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__["default"])(_yield$uni$request, 2);
              err = _yield$uni$request2[0];
              response = _yield$uni$request2[1];
              if (!(response && response.data && response.data.success)) {
                _context.next = 2;
                break;
              }
              _this.mcpConnected = true;
              _this.updateConnectionStatus('connected');
              console.log('MCP服务器连接成功');
              _context.next = 3;
              break;
            case 2:
              throw new Error(response && response.data && response.data.message || '连接失败');
            case 3:
              _context.next = 5;
              break;
            case 4:
              _context.prev = 4;
              _t = _context["catch"](0);
              console.error('MCP连接错误:', _t);
              _this.mcpConnected = false;
              _this.updateConnectionStatus('disconnected');
              setTimeout(function () {
                uni.showModal({
                  title: '连接失败',
                  content: "MCP\u670D\u52A1\u5668\u8FDE\u63A5\u5931\u8D25: ".concat(_t.message, "\n\n\u8BF7\u786E\u4FDD:\n1. MCP\u670D\u52A1\u5668\u6B63\u5728\u8FD0\u884C (\u7AEF\u53E38080)\n2. MCP\u5BA2\u6237\u7AEF\u6B63\u5728\u8FD0\u884C (\u7AEF\u53E38081)"),
                  showCancel: false
                });
              }, 1000);
            case 5:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 4]]);
      }))();
    },
    updateConnectionStatus: function updateConnectionStatus(status) {
      this.connectionStatus = status;
    },
    reconnectMCP: function reconnectMCP() {
      var _this2 = this;
      return (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__["default"])(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee2() {
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().wrap(function (_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              if (_this2.mcpConnected) {
                _context2.next = 1;
                break;
              }
              console.log('尝试重新连接MCP...');
              _context2.next = 1;
              return _this2.connectToMCP();
            case 1:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    },
    // 搜索功能
    performSearch: function performSearch() {
      var _this3 = this;
      return (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__["default"])(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee3() {
        var query;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().wrap(function (_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              query = _this3.searchQuery.trim();
              if (query) {
                _context3.next = 1;
                break;
              }
              uni.showToast({
                title: '请输入查询内容',
                icon: 'none'
              });
              return _context3.abrupt("return");
            case 1:
              if (_this3.mcpConnected) {
                _context3.next = 2;
                break;
              }
              uni.showToast({
                title: 'MCP服务器未连接，请稍后重试',
                icon: 'none'
              });
              return _context3.abrupt("return");
            case 2:
              _context3.next = 3;
              return _this3.executeQuery(query);
            case 3:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }))();
    },
    searchTag: function searchTag(query) {
      var _this4 = this;
      return (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__["default"])(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee4() {
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().wrap(function (_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              _this4.searchQuery = query;
              _context4.next = 1;
              return _this4.executeQuery(query);
            case 1:
            case "end":
              return _context4.stop();
          }
        }, _callee4);
      }))();
    },
    executeQuery: function executeQuery(query) {
      var _this5 = this;
      return (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__["default"])(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee5() {
        var _yield$uni$request3, _yield$uni$request4, err, response, content, errorMsg, errorMessage, _t2;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().wrap(function (_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              _this5.isLoading = true;
              _this5.showResult = false;
              _this5.showLoadingWithMessages();
              console.log('开始执行查询:', query);
              _context5.prev = 1;
              _context5.next = 2;
              return uni.request({
                url: "".concat(_this5.mcpApiBase, "/mcp/tools/business-query?query=").concat(encodeURIComponent(query)),
                method: 'POST',
                header: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json'
                }
              });
            case 2:
              _yield$uni$request3 = _context5.sent;
              _yield$uni$request4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__["default"])(_yield$uni$request3, 2);
              err = _yield$uni$request4[0];
              response = _yield$uni$request4[1];
              console.log('API响应状态:', response);
              if (response && response.data && response.data.success && response.data.data && response.data.data.content) {
                content = response.data.data.content[0];
                if (content && content.text) {
                  _this5.queryResult = content.text;
                  _this5.showResult = true;
                  _this5.addToHistory(query);
                  console.log('查询成功，结果已显示');
                } else {
                  _this5.queryResult = '查询结果为空，请尝试其他问题';
                  _this5.showResult = true;
                }
              } else {
                errorMsg = response && response.data && (response.data.message || response.data.error) || '查询失败，请稍后重试';
                _this5.queryResult = "\u274C ".concat(errorMsg);
                _this5.showResult = true;
              }
              _context5.next = 4;
              break;
            case 3:
              _context5.prev = 3;
              _t2 = _context5["catch"](1);
              console.error('查询错误:', _t2);
              errorMessage = '';
              if (_t2.message && _t2.message.includes('Failed to fetch')) {
                errorMessage = '❌ 无法连接到MCP服务，请检查服务是否正在运行';
              } else if (_t2.message && _t2.message.includes('HTTP 404')) {
                errorMessage = '❌ API接口不存在，请检查MCP客户端是否正确启动';
              } else if (_t2.message && _t2.message.includes('HTTP 500')) {
                errorMessage = '❌ 服务器内部错误，请检查MCP服务器日志';
              } else {
                errorMessage = "\u274C \u67E5\u8BE2\u51FA\u9519: ".concat(_t2.message);
              }
              _this5.queryResult = "".concat(errorMessage, "\n\t\t\t\t<div style=\"margin-top: 10px; font-size: 12px; color: #666;\">\n\t\t\t\t\t<strong>\u6545\u969C\u6392\u9664\u5EFA\u8BAE\uFF1A</strong><br>\n\t\t\t\t\t1. \u786E\u4FDDMCP\u670D\u52A1\u5668\u6B63\u5728\u8FD0\u884C (\u7AEF\u53E38080)<br>\n\t\t\t\t\t2. \u786E\u4FDDMCP\u5BA2\u6237\u7AEF\u6B63\u5728\u8FD0\u884C (\u7AEF\u53E38081)<br>\n\t\t\t\t\t3. \u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5<br>\n\t\t\t\t\t4. \u5237\u65B0\u9875\u9762\u91CD\u65B0\u8FDE\u63A5\n\t\t\t\t</div>");
              _this5.showResult = true;
            case 4:
              _context5.prev = 4;
              _this5.hideLoading();
              return _context5.finish(4);
            case 5:
            case "end":
              return _context5.stop();
          }
        }, _callee5, null, [[1, 3, 4, 5]]);
      }))();
    },
    // 加载动画管理
    showLoadingWithMessages: function showLoadingWithMessages() {
      var _this6 = this;
      var currentIndex = 0;
      this.updateLoadingMessage(this.loadingMessages[currentIndex]);
      this.loadingInterval = setInterval(function () {
        currentIndex = (currentIndex + 1) % _this6.loadingMessages.length;
        _this6.updateLoadingMessage(_this6.loadingMessages[currentIndex]);
      }, 1500);
    },
    updateLoadingMessage: function updateLoadingMessage(message) {
      this.loadingMessage = message.main;
      this.loadingSubMessage = message.sub;
    },
    hideLoading: function hideLoading() {
      this.isLoading = false;
      if (this.loadingInterval) {
        clearInterval(this.loadingInterval);
        this.loadingInterval = null;
      }
    },
    // 历史记录管理
    addToHistory: function addToHistory(query) {
      // 避免重复
      this.searchHistory = this.searchHistory.filter(function (item) {
        return item !== query;
      });

      // 添加到开头
      this.searchHistory.unshift(query);

      // 限制历史记录数量
      if (this.searchHistory.length > 10) {
        this.searchHistory = this.searchHistory.slice(0, 10);
      }

      // 保存到本地存储
      uni.setStorageSync('searchHistory', JSON.stringify(this.searchHistory));
    },
    loadSearchHistory: function loadSearchHistory() {
      try {
        var history = uni.getStorageSync('searchHistory');
        if (history) {
          this.searchHistory = JSON.parse(history);
        }
      } catch (error) {
        console.error('加载搜索历史失败:', error);
      }
    },
    // 键盘事件
    onKeyPress: function onKeyPress(e) {
      if (e.keyCode === 13) {
        // Enter键
        this.performSearch();
      }
    },
    // 语音识别功能（简化版，uniapp环境下可能需要特殊处理）
    toggleVoiceRecognition: function toggleVoiceRecognition() {
      if (this.isRecording) {
        this.stopVoiceRecognition();
      } else {
        this.startVoiceRecognition();
      }
    },
    startVoiceRecognition: function startVoiceRecognition() {
      var _this7 = this;
      var systemInfo = uni.getSystemInfoSync();
      var platform = systemInfo.platform;
      if (platform === 'devtools' || platform === 'android' || platform === 'ios') {
        this.isRecording = true;
        var plugin = requirePlugin('WechatSI');
        plugin.getRecordRecognitionManager().start({
          lang: 'zh_CN',
          duration: 60000
        });
        plugin.getRecordRecognitionManager().onRecognize = function (res) {
          if (res.result) {
            _this7.searchQuery = res.result;
            _this7.stopVoiceRecognition();
            _this7.performSearch();
          }
        };
        plugin.getRecordRecognitionManager().onStop = function (res) {
          if (res.result) {
            _this7.searchQuery = res.result;
            _this7.performSearch();
          }
          _this7.stopVoiceRecognition();
        };
        plugin.getRecordRecognitionManager().onError = function (res) {
          _this7.stopVoiceRecognition();
          uni.showToast({
            title: '语音识别失败',
            icon: 'none'
          });
        };
        return;
      }

      // 其他平台
      uni.showToast({
        title: '当前平台暂不支持语音识别',
        icon: 'none',
        duration: 2000
      });
    },
    stopVoiceRecognition: function stopVoiceRecognition() {
      this.isRecording = false;
      try {
        var plugin = requirePlugin('WechatSI');
        plugin.getRecordRecognitionManager().stop();
      } catch (e) {}
    },
    // H5端波形动画
    _startWaveAnim: function _startWaveAnim() {
      var _this8 = this;
      var canvas = document.getElementById('voiceWaveCanvas');
      if (!canvas) return;
      var ctx = canvas.getContext('2d');
      var W = canvas.width = 120;
      var H = canvas.height = 40;
      var t = 0;
      var _draw = function draw() {
        ctx.clearRect(0, 0, W, H);
        ctx.save();
        ctx.strokeStyle = '#4A90E2';
        ctx.lineWidth = 3;
        ctx.beginPath();
        for (var x = 0; x <= W; x += 2) {
          var y = H / 2 + Math.sin(x / 12 + t) * (10 + 8 * Math.sin(t / 2));
          if (x === 0) ctx.moveTo(x, y);else ctx.lineTo(x, y);
        }
        ctx.stroke();
        ctx.restore();
        t += 0.08;
        if (_this8.isRecording) _this8._waveAnimId = requestAnimationFrame(_draw);
      };
      _draw();
    }
  },
  mounted: function mounted() {
    console.log('AI智能问数页面已加载');
    console.log('MCP API地址:', this.mcpApiBase);
    // 判断平台
    this.isH5 = typeof window !== 'undefined' && !!window.SpeechRecognition;
  },
  onLoad: function onLoad(options) {
    // 自动建立MCP连接
    this.connectToMCP();

    // 加载搜索历史
    this.loadSearchHistory();
  },
  onUnload: function onUnload() {
    // 清理定时器
    if (this.loadingInterval) {
      clearInterval(this.loadingInterval);
    }

    // 停止语音识别
    if (this.isRecording) {
      this.stopVoiceRecognition();
    }
  }
});

/***/ }),

/***/ "./node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-12[0].rules[0].use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-12[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/index/index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-12[0].rules[0].use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-12[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/index/index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin

/***/ }),

/***/ "./src/main.js?{\"page\":\"pages%2Findex%2Findex\"}":
/*!******************************************************!*\
  !*** ./src/main.js?{"page":"pages%2Findex%2Findex"} ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var uni_pages__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uni-pages */ "./src/pages.json");
/* harmony import */ var uni_pages__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(uni_pages__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vue */ "./node_modules/@dcloudio/vue-cli-plugin-uni/packages/mp-vue/dist/mp.runtime.esm.js");
/* harmony import */ var _pages_index_index_vue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./pages/index/index.vue */ "./src/pages/index/index.vue");
/* provided dependency */ var wx = __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ "./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js")["default"];
/* provided dependency */ var createPage = __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ "./node_modules/@dcloudio/uni-mp-weixin/dist/index.js")["createPage"];

// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;


createPage(_pages_index_index_vue__WEBPACK_IMPORTED_MODULE_2__["default"]);

/***/ }),

/***/ "./src/pages/index/index.vue":
/*!***********************************!*\
  !*** ./src/pages/index/index.vue ***!
  \***********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=57280228&scoped=true& */ "./src/pages/index/index.vue?vue&type=template&id=57280228&scoped=true&");
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ "./src/pages/index/index.vue?vue&type=script&lang=js&");
/* harmony import */ var _index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css& */ "./src/pages/index/index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&");
/* harmony import */ var _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js");

var renderjs
;

;


/* normalize component */

var component = (0,_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__.render,
  _index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  "57280228",
  null,
  false,
  _index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__.components,
  renderjs
)

component.options.__file = "pages/index/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/pages/index/index.vue?vue&type=script&lang=js&":
/*!************************************************************!*\
  !*** ./src/pages/index/index.vue?vue&type=script&lang=js& ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_0_rules_0_use_0_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_40_0_rules_0_use_1_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/index/index.vue?vue&type=script&lang=js&");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_40_0_rules_0_use_0_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_40_0_rules_0_use_1_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/pages/index/index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&":
/*!********************************************************************************************!*\
  !*** ./src/pages/index/index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css& ***!
  \********************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_mini_css_extract_plugin_dist_loader_js_clonedRuleSet_12_0_rules_0_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_0_rules_0_use_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_12_0_rules_0_use_2_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_0_rules_0_use_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-12[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-12[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css& */ "./node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-12[0].rules[0].use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-12[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-12[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/index/index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&");
/* harmony import */ var _node_modules_mini_css_extract_plugin_dist_loader_js_clonedRuleSet_12_0_rules_0_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_0_rules_0_use_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_12_0_rules_0_use_2_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_0_rules_0_use_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_mini_css_extract_plugin_dist_loader_js_clonedRuleSet_12_0_rules_0_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_0_rules_0_use_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_12_0_rules_0_use_2_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_0_rules_0_use_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_mini_css_extract_plugin_dist_loader_js_clonedRuleSet_12_0_rules_0_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_0_rules_0_use_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_12_0_rules_0_use_2_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_0_rules_0_use_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _node_modules_mini_css_extract_plugin_dist_loader_js_clonedRuleSet_12_0_rules_0_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_0_rules_0_use_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_12_0_rules_0_use_2_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_0_rules_0_use_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);
 /* harmony default export */ __webpack_exports__["default"] = ((_node_modules_mini_css_extract_plugin_dist_loader_js_clonedRuleSet_12_0_rules_0_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_12_0_rules_0_use_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_12_0_rules_0_use_2_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_12_0_rules_0_use_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default())); 

/***/ }),

/***/ "./src/pages/index/index.vue?vue&type=template&id=57280228&scoped=true&":
/*!******************************************************************************!*\
  !*** ./src/pages/index/index.vue?vue&type=template&id=57280228&scoped=true& ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   components: function() { return /* reexport safe */ _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_42_0_rules_0_use_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__.components; },
/* harmony export */   recyclableRender: function() { return /* reexport safe */ _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_42_0_rules_0_use_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__.recyclableRender; },
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_42_0_rules_0_use_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_42_0_rules_0_use_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_42_0_rules_0_use_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true& */ "./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/index/index.vue?vue&type=template&id=57280228&scoped=true&");


/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["common/vendor"], function() { return __webpack_exec__("./src/main.js?{\"page\":\"pages%2Findex%2Findex\"}"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/pages/index/index.js.map