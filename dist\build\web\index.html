<!doctype html><html lang="zh-CN"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><title>javaMCPView</title><script>var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
            document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />')</script><link rel="stylesheet" href="/static/index.2da1efab.css"/><script defer="defer" src="/static/js/chunk-vendors.f52bca04.js"></script><script defer="defer" src="/static/js/index.a41f5203.js"></script></head><body><noscript><strong>Please enable JavaScript to continue.</strong></noscript><div id="app"></div></body></html>