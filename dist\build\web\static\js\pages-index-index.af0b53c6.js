(self["webpackChunkjavamcpview"]=self["webpackChunkjavamcpview"]||[]).push([[329],{5759:function(t,e,n){var a=n(6841);a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.id,a,""]]),a.locals&&(t.exports=a.locals);var i=n(9333).A;i("21c90f16",a,!0,{sourceMap:!1,shadowMode:!1})},6841:function(t,e,n){"use strict";n.r(e);var a=n(1601),i=n.n(a),o=n(6314),r=n.n(o),s=r()(i());s.push([t.id,'.ai-query-container[data-v-63ad4ead]{min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.connection-status[data-v-63ad4ead]{position:fixed;top:60px;right:20px;padding:8px 16px;border-radius:20px;font-size:12px;font-weight:500;z-index:1000;transition:all .3s ease;&.connected{background:#d4edda;color:#155724;border:1px solid #c3e6cb}&.disconnected{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb}&.connecting{background:#fff3cd;color:#856404;border:1px solid #ffeaa7}}.container[data-v-63ad4ead]{max-width:800px;margin:0 auto;background:hsla(0,0%,100%,.95);border-radius:20px;padding:40px;box-shadow:0 20px 40px rgba(0,0,0,.1);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.title[data-v-63ad4ead]{display:block;text-align:center;font-size:32px;font-weight:600;color:#4a90e2;margin-bottom:40px;letter-spacing:2px}.search-container[data-v-63ad4ead]{display:flex;align-items:center;background:#fff;border-radius:50px;padding:8px 16px;box-shadow:0 4px 16px rgba(74,144,226,.08);margin-bottom:40px;position:relative;min-height:56px}.search-input[data-v-63ad4ead]{flex:1;min-width:0;border:none;outline:none;font-size:16px;background:transparent;padding:8px 0 8px 0;margin-right:12px;box-shadow:none;border-radius:0}.search-btn[data-v-63ad4ead]{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;border:none;background:#e6edfa;color:#4a90e2;margin-left:8px;transition:background .2s,color .2s;font-size:18px;cursor:pointer;box-shadow:0 2px 8px rgba(74,144,226,.08)}.search-btn[data-v-63ad4ead]:active, .search-btn[data-v-63ad4ead]:hover{background:#4a90e2;color:#fff}.search-btn.voice-btn[data-v-63ad4ead]{background:#fff;border:2px solid #4a90e2;color:#4a90e2}.search-btn.voice-btn.recording[data-v-63ad4ead]{background:#4a90e2;color:#fff}.voice-wave-canvas[data-v-63ad4ead]{position:absolute;right:100px;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);z-index:2;background:transparent;border-radius:8px;pointer-events:none;height:40px;width:120px;display:block}.section[data-v-63ad4ead]{margin-bottom:30px}.section-title[data-v-63ad4ead]{display:block;font-size:16px;color:#666;margin-bottom:15px;font-weight:500}.tags-container[data-v-63ad4ead]{display:flex;flex-wrap:wrap;gap:10px}.tag[data-v-63ad4ead]{padding:8px 16px;background:#f8f9fa;border:1px solid #e9ecef;border-radius:20px;font-size:14px;color:#495057;cursor:pointer;transition:all .3s ease;white-space:nowrap;&:hover{background:#4a90e2;color:#fff;border-color:#4a90e2;-webkit-transform:translateY(-2px);transform:translateY(-2px)}}.history-container[data-v-63ad4ead]{display:flex;flex-direction:column;gap:8px}.history-item[data-v-63ad4ead]{display:flex;align-items:center;padding:8px 16px;background:#f8f9fa;border:1px solid #e9ecef;border-radius:20px;font-size:14px;color:#495057;cursor:pointer;transition:all .3s ease;&:hover{background:#e3f2fd;border-color:#4a90e2}&::before{content:"🕐";margin-right:8px}}.loading[data-v-63ad4ead]{text-align:center;padding:25px;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:15px;margin:20px 0;color:#fff;box-shadow:0 10px 30px rgba(0,0,0,.2);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid hsla(0,0%,100%,.2)}.loading-content[data-v-63ad4ead]{display:flex;align-items:center;justify-content:center;flex-direction:column;gap:15px}.spinner[data-v-63ad4ead]{width:40px;height:40px;border:4px solid hsla(0,0%,100%,.3);border-top:4px solid #fff;border-radius:50%;-webkit-animation:spin-data-v-63ad4ead 1s linear infinite;animation:spin-data-v-63ad4ead 1s linear infinite}.loading-text[data-v-63ad4ead]{font-size:16px;font-weight:500;letter-spacing:.5px}.loading-sub-text[data-v-63ad4ead]{font-size:12px;opacity:.8}.loading-dots[data-v-63ad4ead]{display:inline-block;-webkit-animation:dots-data-v-63ad4ead 1.5s infinite;animation:dots-data-v-63ad4ead 1.5s infinite}.result-container[data-v-63ad4ead]{margin:20px 0;padding:25px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:15px;border-left:4px solid #4a90e2;box-shadow:0 8px 25px rgba(0,0,0,.1);border:1px solid #e9ecef;position:relative;overflow:hidden;&::before{content:"";position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient(90deg,#4a90e2,#667eea,#764ba2)}}.result-title[data-v-63ad4ead]{display:block;font-size:18px;font-weight:600;color:#333;margin-bottom:15px}.result-content[data-v-63ad4ead]{font-size:14px;line-height:1.6;color:#666}@-webkit-keyframes spin-data-v-63ad4ead{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes spin-data-v-63ad4ead{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes dots-data-v-63ad4ead{0%,\n\t20%{content:""}40%{content:"."}60%{content:".."}80%,\n\t100%{content:"..."}}@keyframes dots-data-v-63ad4ead{0%,\n\t20%{content:""}40%{content:"."}60%{content:".."}80%,\n\t100%{content:"..."}}\n\n/* 响应式设计 */@media (max-width:768px){.connection-status[data-v-63ad4ead]{top:44px;padding:6px 10px;font-size:11px}.container[data-v-63ad4ead]{padding:20px;margin:10px}.title[data-v-63ad4ead]{font-size:24px}.tags-container[data-v-63ad4ead]{gap:8px}.tag[data-v-63ad4ead]{font-size:12px;padding:6px 12px}}.search-btn.voice-btn.recording[data-v-63ad4ead]{-webkit-animation:voice-breath-data-v-63ad4ead 1.2s infinite alternate;animation:voice-breath-data-v-63ad4ead 1.2s infinite alternate;box-shadow:0 0 0 8px rgba(74,144,226,.12)}@-webkit-keyframes voice-breath-data-v-63ad4ead{0%{-webkit-transform:scale(1);transform:scale(1);box-shadow:0 0 0 8px rgba(74,144,226,.12)}100%{-webkit-transform:scale(1.12);transform:scale(1.12);box-shadow:0 0 0 16px rgba(74,144,226,.18)}}@keyframes voice-breath-data-v-63ad4ead{0%{-webkit-transform:scale(1);transform:scale(1);box-shadow:0 0 0 8px rgba(74,144,226,.12)}100%{-webkit-transform:scale(1.12);transform:scale(1.12);box-shadow:0 0 0 16px rgba(74,144,226,.18)}}',""]),e["default"]=s},9431:function(t,e,n){"use strict";n.r(e),n.d(e,{default:function(){return u}});n(2675),n(9463),n(6280),n(6918),n(875),n(287),n(6099),n(4185);function a(t,e,n,i){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}a=function(t,e,n,i){if(e)o?o(t,e,{value:n,enumerable:!i,configurable:!i,writable:!i}):t[e]=n;else{var r=function(e,n){a(t,e,function(t){return this._invoke(e,n,t)})};r("next",0),r("throw",1),r("return",2)}},a(t,e,n,i)}function i(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */
var t,e,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",r=n.toStringTag||"@@toStringTag";function s(n,i,o,r){var s=i&&i.prototype instanceof d?i:d,l=Object.create(s.prototype);return a(l,"_invoke",function(n,a,i){var o,r,s,d=0,l=i||[],u=!1,p={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,n){return o=e,r=0,s=t,p.n=n,c}};function f(n,a){for(r=n,s=a,e=0;!u&&d&&!i&&e<l.length;e++){var i,o=l[e],f=p.p,g=o[2];n>3?(i=g===a)&&(s=o[(r=o[4])?5:(r=3,3)],o[4]=o[5]=t):o[0]<=f&&((i=n<2&&f<o[1])?(r=0,p.v=a,p.n=o[1]):f<g&&(i=n<3||o[0]>a||a>g)&&(o[4]=n,o[5]=a,p.n=g,r=0))}if(i||n>1)return c;throw u=!0,a}return function(i,l,g){if(d>1)throw TypeError("Generator is already running");for(u&&1===l&&f(l,g),r=l,s=g;(e=r<2?t:s)||!u;){o||(r?r<3?(r>1&&(p.n=-1),f(r,s)):p.n=s:p.v=s);try{if(d=2,o){if(r||(i="next"),e=o[i]){if(!(e=e.call(o,s)))throw TypeError("iterator result is not an object");if(!e.done)return e;s=e.value,r<2&&(r=0)}else 1===r&&(e=o["return"])&&e.call(o),r<2&&(s=TypeError("The iterator does not provide a '"+i+"' method"),r=1);o=t}else if((e=(u=p.n<0)?s:n.call(a,p))!==c)break}catch(e){o=t,r=1,s=e}finally{d=1}}return{value:e,done:u}}}(n,o,r),!0),l}var c={};function d(){}function l(){}function u(){}e=Object.getPrototypeOf;var p=[][o]?e(e([][o]())):(a(e={},o,function(){return this}),e),f=u.prototype=d.prototype=Object.create(p);function g(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,u):(t.__proto__=u,a(t,r,"GeneratorFunction")),t.prototype=Object.create(f),t}return l.prototype=u,a(f,"constructor",u),a(u,"constructor",l),l.displayName="GeneratorFunction",a(u,r,"GeneratorFunction"),a(f),a(f,r,"Generator"),a(f,o,function(){return this}),a(f,"toString",function(){return"[object Generator]"}),(i=function(){return{w:s,m:g}})()}function o(t,e,n,a,i,o,r){try{var s=t[o](r),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(a,i)}function r(t){return function(){var e=this,n=arguments;return new Promise(function(a,i){var r=t.apply(e,n);function s(t){o(r,a,i,s,c,"next",t)}function c(t){o(r,a,i,s,c,"throw",t)}s(void 0)})}}n(8706),n(2008),n(4423),n(4782),n(3609),n(739),n(8111),n(2489),n(3110),n(9432),n(7495),n(1699),n(5440),n(2762);var s={data:function(){return{mcpApiBase:"https://dataseekapi.xiaoyujia.com",mcpConnected:!1,connectionStatus:"disconnected",searchQuery:"",isLoading:!1,showResult:!1,queryResult:"",loadingMessage:"正在智能分析您的问题",loadingSubMessage:"AI正在理解查询意图",loadingMessages:[{main:"正在智能分析您的问题",sub:"AI正在理解查询意图"},{main:"正在连接数据库",sub:"建立与SQL Server的安全连接"},{main:"正在查询数据",sub:"从表中检索相关数据"},{main:"正在处理结果",sub:"对查询结果进行智能分析和格式化"}],loadingInterval:null,isRecording:!1,aliyunAppKey:"DzVzUYbM1vG83nxm",aliyunToken:"1361a388bbf14c018caa704b7e5c2c5f",aliyunWs:null,aliyunRecorder:null,aliyunAudioContext:null,aliyunStream:null,aliyunAnalyser:null,aliyunWaveAnimationId:null,silenceStartTime:null,silenceThreshold:.02,silenceTimeout:1e3,hotTags:["本月销售业绩","上周交付业绩","本月异地交付业绩","本周驻场业绩","本周政府收入","全国门店收入","本月全国三嫂业绩","本月平台收入","上月软件销售","大学收入"],searchHistory:["本月平台收入总共多少","列一个本月平台收入的明细给我","上个月的平台收入总共有多少","看看去年的营业额","今年的营业额","门店营业额前十门店营业额","去年营业额前十门店同比情况","门店日期间3天利润趋势"],isH5:!1,_waveAnimId:null}},computed:{connectionStatusClass:function(){return"connection-status ".concat(this.connectionStatus)},connectionStatusText:function(){return{connected:"🟢 已连接",disconnected:"🔴 未连接",connecting:"🟡 连接中..."}[this.connectionStatus]||"🔴 未连接"},formattedResult:function(){if(!this.queryResult)return"";var t=this.queryResult.replace(/\n/g,"<br>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/`(.*?)`/g,'<code style="background: #f1f3f4; padding: 2px 4px; border-radius: 3px;">$1</code>');return t=t.replace(/(\d+\.?\d*)(元|万元|亿元)/g,'<span style="color: #4A90E2; font-weight: bold;">$1$2</span>'),'<div style="line-height: 1.8;">'.concat(t,"</div>")}},methods:{connectToMCP:function(){var t=this;return r(i().m(function e(){var n,a,o,r;return i().w(function(e){while(1)switch(e.n){case 0:return e.p=0,t.updateConnectionStatus("connecting"),console.log("正在连接MCP服务器..."),n="".concat(t.mcpApiBase,"/mcp/connect"),"https://dataseekapi.xiaoyujia.com/mcp/connect"===n&&(n+="?serverUrl=ws://mcp-server:8080/mcp"),e.n=1,uni.request({url:n,method:"POST",header:{"Content-Type":"application/json"}});case 1:if(a=e.v,o=Array.isArray(a)?a[1]:a,!(o&&o.data&&o.data.serverInfo)){e.n=2;break}t.mcpConnected=!0,t.updateConnectionStatus("connected"),console.log("MCP服务器连接成功"),e.n=3;break;case 2:throw new Error(o&&o.data&&o.data.message||"连接失败");case 3:e.n=5;break;case 4:e.p=4,r=e.v,console.error("MCP连接错误:",r),t.mcpConnected=!1,t.updateConnectionStatus("disconnected"),setTimeout(function(){uni.showModal({title:"连接失败",content:"MCP服务器连接失败: ".concat(r.message,"\n\n请确保:\n1. MCP服务器正在运行 (端口8080)\n2. MCP客户端正在运行 (端口8081)"),showCancel:!1})},1e3);case 5:return e.a(2)}},e,null,[[0,4]])}))()},updateConnectionStatus:function(t){this.connectionStatus=t},reconnectMCP:function(){var t=this;return r(i().m(function e(){return i().w(function(e){while(1)switch(e.n){case 0:if(t.mcpConnected){e.n=1;break}return console.log("尝试重新连接MCP..."),e.n=1,t.connectToMCP();case 1:return e.a(2)}},e)}))()},performSearch:function(){var t=this;return r(i().m(function e(){var n;return i().w(function(e){while(1)switch(e.n){case 0:if(n=t.searchQuery.trim(),n){e.n=1;break}return uni.showToast({title:"请输入查询内容",icon:"none"}),e.a(2);case 1:if(t.mcpConnected){e.n=2;break}return uni.showToast({title:"MCP服务器未连接，请稍后重试",icon:"none"}),e.a(2);case 2:return e.n=3,t.executeQuery(n);case 3:return e.a(2)}},e)}))()},searchTag:function(t){var e=this;return r(i().m(function n(){return i().w(function(n){while(1)switch(n.n){case 0:return e.searchQuery=t,n.n=1,e.executeQuery(t);case 1:return n.a(2)}},n)}))()},executeQuery:function(t){var e=this;return r(i().m(function n(){var a,o,r,s,c,d,l;return i().w(function(n){while(1)switch(n.n){case 0:return e.isLoading=!0,e.showResult=!1,e.showLoadingWithMessages(),console.log("开始执行查询:",t),n.p=1,n.n=2,uni.request({url:"".concat(e.mcpApiBase,"/mcp/tools/business-query?query=").concat(encodeURIComponent(t)),method:"POST",header:{"Content-Type":"application/json",Accept:"application/json"}});case 2:a=n.v,o=Array.isArray(a)?a[1]:a,console.log("API响应状态:",o),r=o&&o.data,r&&!0===r.success&&r.data&&Array.isArray(r.data.content)&&r.data.content.length>0&&!1===r.data.isError?(s=r.data.content[0],s&&s.text?(e.queryResult=s.text,e.showResult=!0,e.addToHistory(t),console.log("查询成功，结果已显示")):(e.queryResult="查询结果为空，请尝试其他问题",e.showResult=!0)):(c=r&&(r.message||r.error)||"查询失败，请稍后重试",e.queryResult="❌ ".concat(c),e.showResult=!0),n.n=4;break;case 3:n.p=3,l=n.v,console.error("查询错误:",l),d="",d=l.message&&l.message.includes("Failed to fetch")?"❌ 无法连接到MCP服务，请检查服务是否正在运行":l.message&&l.message.includes("HTTP 404")?"❌ API接口不存在，请检查MCP客户端是否正确启动":l.message&&l.message.includes("HTTP 500")?"❌ 服务器内部错误，请检查MCP服务器日志":"❌ 查询出错: ".concat(l.message),e.queryResult="".concat(d,'\n\t\t\t\t<div style="margin-top: 10px; font-size: 12px; color: #666;">\n\t\t\t\t\t<strong>故障排除建议：</strong><br>\n\t\t\t\t\t1. 确保MCP服务器正在运行 (端口8080)<br>\n\t\t\t\t\t2. 确保MCP客户端正在运行 (端口8081)<br>\n\t\t\t\t\t3. 检查网络连接<br>\n\t\t\t\t\t4. 刷新页面重新连接\n\t\t\t\t</div>'),e.showResult=!0;case 4:return n.p=4,e.hideLoading(),n.f(4);case 5:return n.a(2)}},n,null,[[1,3,4,5]])}))()},showLoadingWithMessages:function(){var t=this,e=0;this.updateLoadingMessage(this.loadingMessages[e]),this.loadingInterval=setInterval(function(){e=(e+1)%t.loadingMessages.length,t.updateLoadingMessage(t.loadingMessages[e])},1500)},updateLoadingMessage:function(t){this.loadingMessage=t.main,this.loadingSubMessage=t.sub},hideLoading:function(){this.isLoading=!1,this.loadingInterval&&(clearInterval(this.loadingInterval),this.loadingInterval=null)},addToHistory:function(t){this.searchHistory=this.searchHistory.filter(function(e){return e!==t}),this.searchHistory.unshift(t),this.searchHistory.length>10&&(this.searchHistory=this.searchHistory.slice(0,10)),uni.setStorageSync("searchHistory",JSON.stringify(this.searchHistory))},loadSearchHistory:function(){try{var t=uni.getStorageSync("searchHistory");t&&(this.searchHistory=JSON.parse(t))}catch(e){console.error("加载搜索历史失败:",e)}},onKeyPress:function(t){13===t.keyCode&&this.performSearch()},toggleVoiceRecognition:function(){this.isRecording?this.stopVoiceRecognition():this.startVoiceRecognition()},startVoiceRecognition:function(){var t=this,e=uni.getSystemInfoSync();e.platform;if("undefined"!==typeof window&&(window.SpeechRecognition||window.webkitSpeechRecognition)){this.isRecording=!0;var n=window.SpeechRecognition||window.webkitSpeechRecognition;return this._recognition=new n,this._recognition.lang="zh-CN",this._recognition.continuous=!1,this._recognition.interimResults=!1,this._recognition.onresult=function(e){var n=e.results[0][0].transcript;t.searchQuery=n,t.stopVoiceRecognition(),t.performSearch()},this._recognition.onerror=function(e){t.stopVoiceRecognition(),uni.showToast({title:"语音识别失败",icon:"none"})},this._recognition.onend=function(){t.stopVoiceRecognition()},this._recognition.start(),void this.$nextTick(function(){t._startWaveAnim()})}uni.showToast({title:"当前平台暂不支持语音识别",icon:"none",duration:2e3})},stopVoiceRecognition:function(){if(this.isRecording=!1,this._recognition&&(this._recognition.stop(),this._recognition=null),this._waveAnimId){cancelAnimationFrame(this._waveAnimId),this._waveAnimId=null;var t=document.getElementById("voiceWaveCanvas");if(t){var e=t.getContext("2d");e.clearRect(0,0,t.width,t.height)}}},_startWaveAnim:function(){var t=this,e=document.getElementById("voiceWaveCanvas");if(e){var n=e.getContext("2d"),a=e.width=120,i=e.height=40,o=0,r=function(){n.clearRect(0,0,a,i),n.save(),n.strokeStyle="#4A90E2",n.lineWidth=3,n.beginPath();for(var e=0;e<=a;e+=2){var s=i/2+Math.sin(e/12+o)*(10+8*Math.sin(o/2));0===e?n.moveTo(e,s):n.lineTo(e,s)}n.stroke(),n.restore(),o+=.08,t.isRecording&&(t._waveAnimId=requestAnimationFrame(r))};r()}}},mounted:function(){console.log("AI智能问数页面已加载"),console.log("MCP API地址:",this.mcpApiBase),this.isH5="undefined"!==typeof window&&!!window.SpeechRecognition},onLoad:function(t){this.connectToMCP(),this.loadSearchHistory()},onUnload:function(){this.loadingInterval&&clearInterval(this.loadingInterval),this.isRecording&&this.stopVoiceRecognition()}},c=s,d=(n(5759),n(8535)),l=(0,d.A)(c,function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"ai-query-container"},[n("v-uni-view",{staticClass:"connection-status",class:t.connectionStatusClass,on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.reconnectMCP.apply(void 0,arguments)}}},[t._v(t._s(t.connectionStatusText))]),n("v-uni-view",{staticClass:"container"},[n("v-uni-text",{staticClass:"title"},[t._v("AI智能问数")]),n("v-uni-view",{staticClass:"search-container"},[n("v-uni-input",{staticClass:"search-input",attrs:{type:"text",placeholder:"请输入您的业务查询，如：本月销售业绩、本月平台收入、本周交付业绩等"},on:{keypress:function(e){arguments[0]=e=t.$handleEvent(e),t.onKeyPress.apply(void 0,arguments)}},model:{value:t.searchQuery,callback:function(e){t.searchQuery=e},expression:"searchQuery"}}),n("v-uni-button",{staticClass:"search-btn voice-btn",class:{recording:t.isRecording},attrs:{title:t.isRecording?"正在聆听...点击停止":"语音输入"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggleVoiceRecognition.apply(void 0,arguments)}}},[n("v-uni-text",{staticClass:"voice-icon"},[t._v("🎤")])],1),n("v-uni-button",{staticClass:"search-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.performSearch.apply(void 0,arguments)}}},[n("v-uni-text",{staticClass:"search-icon"},[t._v("🔍")])],1),t.isRecording&&t.isH5?n("v-uni-canvas",{staticClass:"voice-wave-canvas",attrs:{id:"voiceWaveCanvas"}}):t._e()],1),t.isLoading?n("v-uni-view",{staticClass:"loading"},[n("v-uni-view",{staticClass:"loading-content"},[n("v-uni-view",{staticClass:"spinner"}),n("v-uni-text",{staticClass:"loading-text"},[t._v(t._s(t.loadingMessage)),n("v-uni-text",{staticClass:"loading-dots"})],1),n("v-uni-text",{staticClass:"loading-sub-text"},[t._v(t._s(t.loadingSubMessage))])],1)],1):t._e(),t.showResult?n("v-uni-view",{staticClass:"result-container"},[n("v-uni-text",{staticClass:"result-title"},[t._v("📊 查询结果")]),n("v-uni-rich-text",{staticClass:"result-content",attrs:{nodes:t.formattedResult}})],1):t._e(),n("v-uni-view",{staticClass:"section"},[n("v-uni-text",{staticClass:"section-title"},[t._v("热门搜索")]),n("v-uni-view",{staticClass:"tags-container"},t._l(t.hotTags,function(e,a){return n("v-uni-view",{key:a,staticClass:"tag",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.searchTag(e)}}},[t._v(t._s(e))])}),1)],1),n("v-uni-view",{staticClass:"section"},[n("v-uni-text",{staticClass:"section-title"},[t._v("历史搜索")]),n("v-uni-view",{staticClass:"history-container"},t._l(t.searchHistory,function(e,a){return n("v-uni-view",{key:a,staticClass:"history-item",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.searchTag(e)}}},[t._v(t._s(e))])}),1)],1)],1)],1)},[],!1,null,"63ad4ead",null,!1,undefined,undefined),u=l.exports}}]);